<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>run after 1 min</description>
    <environments>Default</environments>
    <interviewLabel>trigger load balancing - cases {!$Flow.CurrentDateTime}</interviewLabel>
    <label>trigger load balancing - cases</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsCurrentState</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>UserServicePresence</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <name>X1m_delay</name>
            <connector>
                <targetReference>load_balance_for_all_new_cases_created_by_email2case</targetReference>
            </connector>
            <label>1m delay</label>
            <offsetNumber>1</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <timeSource>RecordTriggerEvent</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>load_balance_for_all_new_cases_created_by_email2case</name>
        <label>load balance for all new cases created by email2case</label>
        <locationX>308</locationX>
        <locationY>276</locationY>
        <flowName>Load_Balance_Premium_Emails</flowName>
    </subflows>
</Flow>
