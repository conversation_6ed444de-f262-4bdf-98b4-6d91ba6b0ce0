<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Is_Campaign_Active</name>
        <label>Is Campaign Active</label>
        <locationX>182</locationX>
        <locationY>395</locationY>
        <defaultConnector>
            <targetReference>isHostedEvent</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Not_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.IsActive</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>setCampaignAsActive</targetReference>
            </connector>
            <label>Is Not Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>isHostedEvent</name>
        <label>Is Hosted Event</label>
        <locationX>182</locationX>
        <locationY>695</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decYes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Is_Hosted_Event__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Ticket_Setting_1</targetReference>
            </connector>
            <label>Is Hosted</label>
        </rules>
    </decisions>
    <description>SBET-1063 - New Campaign Record Type</description>
    <environments>Default</environments>
    <interviewLabel>Campaign Triggered Flow - On Create {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Campaign Triggered Flow - On Create</label>
    <migratedFromWorkflowRuleName>ON CREATE SET ACTIVE</migratedFromWorkflowRuleName>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Set_Ticket_Setting_1</name>
        <label>Set Ticket Setting 1</label>
        <locationX>50</locationX>
        <locationY>803</locationY>
        <inputAssignments>
            <field>Details_Required_for_Ticket_Allocation__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>setCampaignAsActive</name>
        <label>setCampaignAsActive</label>
        <locationX>50</locationX>
        <locationY>503</locationY>
        <connector>
            <targetReference>isHostedEvent</targetReference>
        </connector>
        <inputAssignments>
            <field>IsActive</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>ticketSetting2</name>
        <label>Set Ticket Setting 2 as Active</label>
        <locationX>182</locationX>
        <locationY>287</locationY>
        <connector>
            <targetReference>Is_Campaign_Active</targetReference>
        </connector>
        <inputAssignments>
            <field>Allow_Reallocation_of_Cancelled_Attendee__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Waitlist_Capacity__c</field>
            <value>
                <numberValue>10.0</numberValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>ticketSetting2</targetReference>
        </connector>
        <filterFormula>{!$Record.RecordType.Name}=&apos;Event&apos;</filterFormula>
        <object>Campaign</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
