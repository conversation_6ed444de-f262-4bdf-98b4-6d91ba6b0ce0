<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Checks if the requested date of the work item is within business hours of the given day</description>
        <name>Check_Within_Business_Hours</name>
        <label>Check Within Business Hours</label>
        <locationX>506</locationX>
        <locationY>1092</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Update_Agent_Work_Record</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Business_Hours_of_Customer_Portfolio.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Record.RequestDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Customer_Portfolio_At_Time_For_Case_Record</name>
        <label>Assign Customer Portfolio At Time For Case Record</label>
        <locationX>110</locationX>
        <locationY>492</locationY>
        <assignmentItems>
            <assignToReference>varCustomerPortfolioAtTime</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Case_Record.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Customer_Portfolio</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Customer_Portfolio_At_Time_For_Messaging_Session_Record</name>
        <label>Assign Customer Portfolio At Time For Messaging Session Record</label>
        <locationX>638</locationX>
        <locationY>492</locationY>
        <assignmentItems>
            <assignToReference>varCustomerPortfolioAtTime</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Messaging_Session_Record.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Customer_Portfolio</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Customer_Portfolio_At_Time_For_Voice_Call_Record</name>
        <label>Assign Customer Portfolio At Time For Voice Call Record</label>
        <locationX>374</locationX>
        <locationY>492</locationY>
        <assignmentItems>
            <assignToReference>varCustomerPortfolioAtTime</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Voice_Record.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Customer_Portfolio</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Grow_Business_Hour_Variable</name>
        <label>Assign Grow Business Hour Variable</label>
        <locationX>506</locationX>
        <locationY>792</locationY>
        <assignmentItems>
            <assignToReference>varPortfolioBusinessHourName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>National Grow</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Business_Hours_of_Customer_Portfolio</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Premium_Managed_Business_Hours</name>
        <label>Assign Premium Managed Business Hours</label>
        <locationX>770</locationX>
        <locationY>792</locationY>
        <assignmentItems>
            <assignToReference>varPortfolioBusinessHourName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Premium Managed Business Hours</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Business_Hours_of_Customer_Portfolio</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Protect_Business_Hour_Variable</name>
        <label>Assign Protect Business Hour Variable</label>
        <locationX>242</locationX>
        <locationY>792</locationY>
        <assignmentItems>
            <assignToReference>varPortfolioBusinessHourName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>National Protect</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Business_Hours_of_Customer_Portfolio</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_Agent_Work_Channel</name>
        <label>Check Agent Work Channel</label>
        <locationX>506</locationX>
        <locationY>276</locationY>
        <defaultConnector>
            <targetReference>Check_Customer_Portfolio</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Case</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WorkItemId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>500</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Case_Record</targetReference>
            </connector>
            <label>Is Case</label>
        </rules>
        <rules>
            <name>Is_Voice</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WorkItemId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>0LQ</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Voice_Record</targetReference>
            </connector>
            <label>Is Voice</label>
        </rules>
        <rules>
            <name>Is_Messaging_Session</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WorkItemId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>0Mw</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Messaging_Session_Record</targetReference>
            </connector>
            <label>Is Messaging Session</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Customer_Portfolio</name>
        <label>Check Customer Portfolio</label>
        <locationX>506</locationX>
        <locationY>684</locationY>
        <defaultConnector>
            <targetReference>Assign_Premium_Managed_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Customer_Is_Protect</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>varCustomerPortfolioAtTime</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Protect</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Protect_Business_Hour_Variable</targetReference>
            </connector>
            <label>Customer Is Protect</label>
        </rules>
        <rules>
            <name>Customer_Is_Grow</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varCustomerPortfolioAtTime</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Grow</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Grow_Business_Hour_Variable</targetReference>
            </connector>
            <label>Customer Is Grow</label>
        </rules>
    </decisions>
    <description>Assigns values to Agent Work record to log if request was sent inside business hours and if not logs next business date time. This is used for reporting on SLAs with Agent Work object. 

This also stamps the customer portfolio at the time of the Agent Work record creation to assist with SLA reporting.</description>
    <environments>Default</environments>
    <formulas>
        <description>Calculates next start date or request date depending on if the request was sent inside business hours or not</description>
        <name>formulaNextStartDate</name>
        <dataType>DateTime</dataType>
        <expression>IF({!Check_Within_Business_Hours.isWithin}, {!$Record.RequestDateTime},{!Check_Within_Business_Hours.nextStartDate})</expression>
    </formulas>
    <formulas>
        <description>Output from Apex action is true or false</description>
        <name>formulaWithinBusinessHours</name>
        <dataType>Boolean</dataType>
        <expression>IF({!Check_Within_Business_Hours.isWithin}, TRUE, FALSE)</expression>
    </formulas>
    <interviewLabel>Agent Work: Assign Business Hours Values {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Agent Work: Assign Business Hours Values</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the business hours of the customer portfolio</description>
        <name>Get_Business_Hours_of_Customer_Portfolio</name>
        <label>Get Business Hours of Customer Portfolio</label>
        <locationX>506</locationX>
        <locationY>984</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varPortfolioBusinessHourName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Case_Record</name>
        <label>Get Case Record</label>
        <locationX>110</locationX>
        <locationY>384</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Customer_Portfolio_At_Time_For_Case_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WorkItemId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Messaging_Session_Record</name>
        <label>Get Messaging Session Record</label>
        <locationX>638</locationX>
        <locationY>384</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Customer_Portfolio_At_Time_For_Messaging_Session_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WorkItemId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingSession</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Voice_Record</name>
        <label>Get Voice Record</label>
        <locationX>374</locationX>
        <locationY>384</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Customer_Portfolio_At_Time_For_Voice_Call_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WorkItemId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates Agent Work record with values from Apex Action</description>
        <name>Update_Agent_Work_Record</name>
        <label>Update Agent Work Record</label>
        <locationX>506</locationX>
        <locationY>1200</locationY>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>varCustomerPortfolioAtTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Next_Business_Start_Date_Time__c</field>
            <value>
                <elementReference>formulaNextStartDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>formulaWithinBusinessHours</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>or</filterLogic>
        <filters>
            <field>WorkItemId</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>500</stringValue>
            </value>
        </filters>
        <filters>
            <field>WorkItemId</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>0LQ</stringValue>
            </value>
        </filters>
        <filters>
            <field>WorkItemId</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>0Mw</stringValue>
            </value>
        </filters>
        <object>AgentWork</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Check_Agent_Work_Channel</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>varCustomerPortfolioAtTime</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varPortfolioBusinessHourName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
