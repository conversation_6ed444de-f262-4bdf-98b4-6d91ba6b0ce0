<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Contact_ID</name>
        <label>Assign Contact ID</label>
        <locationX>857</locationX>
        <locationY>54</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetAccount.PersonContactId</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <decisions>
        <name>CheckPersonContactID</name>
        <label>Check Person ContactID</label>
        <locationX>642</locationX>
        <locationY>54</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetAccount.PersonContactId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Contact_ID</targetReference>
            </connector>
            <label>Is Not Null</label>
        </rules>
    </decisions>
    <decisions>
        <name>If_Account_ID_was_Set</name>
        <label>If Account ID was Set</label>
        <locationX>249</locationX>
        <locationY>52</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Was_Set</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.AccountId</leftValueReference>
                <operator>WasSet</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetAccount</targetReference>
            </connector>
            <label>Was Set</label>
        </rules>
    </decisions>
    <interviewLabel>Case Flow Trigger {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case Flow Trigger</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetAccount</name>
        <label>GetAccount</label>
        <locationX>459</locationX>
        <locationY>52</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckPersonContactID</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>PersonContactId</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>49</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>If_Account_ID_was_Set</targetReference>
        </connector>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
