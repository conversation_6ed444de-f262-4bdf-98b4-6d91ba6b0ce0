<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Send Email to <PERSON> &amp; <PERSON> to advise to check Group Membership manually</description>
        <name>Send_Email_to_<PERSON>_<PERSON></name>
        <label>Send Email to <PERSON> &amp; <PERSON></label>
        <locationX>314</locationX>
        <locationY>2856</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL>, <EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Queue: Daily Schedule Change Host SMS Queue Membership Flow Failed</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <stringValue>Queue: Daily Schedule Change Host SMS Queue Membership Flow Failed. Please manually check queue membership for Host SMS Queue is correct for any of todays hosted events.</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns Campaign Ids to text collection for the Get Hosts element</description>
        <name>Assign_Campaign_Ids</name>
        <label>Assign Campaign Ids</label>
        <locationX>666</locationX>
        <locationY>1356</locationY>
        <assignmentItems>
            <assignToReference>varCampaignIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Campaign_Events.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Campaign_Events</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns values for new Host Queue Membership records and add to record collection to be created</description>
        <name>Assign_Host_Queue_Member_Values</name>
        <label>Assign Host Queue Member Values</label>
        <locationX>138</locationX>
        <locationY>2556</locationY>
        <assignmentItems>
            <assignToReference>varQueueMembershipSingle.UserOrGroupId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Hosts.Host__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varQueueMembershipSingle.GroupId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Host_SMS_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varQueueMembershipCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>varQueueMembershipSingle</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Hosts</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns values for new Host Victoria Queue Membership records and add to record collection to be created</description>
        <name>Assign_Host_Victoria_Queue_Member_Values</name>
        <label>Assign Host Victoria Queue Member Values</label>
        <locationX>294</locationX>
        <locationY>1956</locationY>
        <assignmentItems>
            <assignToReference>varQueueMembershipSingle.UserOrGroupId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Victoria_Hosts.Host__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varQueueMembershipSingle.GroupId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Host_Victoria_SMS_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varQueueMembershipCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>varQueueMembershipSingle</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Victoria_Hosts</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Victoria Campaign Ids to text collection for the Get Hosts element</description>
        <name>Assign_Victoria_Campaign_Ids</name>
        <label>Assign Victoria Campaign Ids</label>
        <locationX>402</locationX>
        <locationY>1356</locationY>
        <assignmentItems>
            <assignToReference>varCampaignIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Campaign_Events.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varCampaignIdsVictoria</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Campaign_Events.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Campaign_Events</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Are there any Campaign Events today?</description>
        <name>Any_Events_Today</name>
        <label>Any Events Today?</label>
        <locationX>842</locationX>
        <locationY>1032</locationY>
        <defaultConnectorLabel>No Events</defaultConnectorLabel>
        <rules>
            <name>Events_Today</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Hosted_Campaign_Event</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Hosted_Campaign_Event</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Campaign_Events</targetReference>
            </connector>
            <label>Events Today</label>
        </rules>
    </decisions>
    <decisions>
        <description>Are there Host records for events today?</description>
        <name>Are_there_hosts</name>
        <label>Are there hosts?</label>
        <locationX>314</locationX>
        <locationY>2340</locationY>
        <defaultConnectorLabel>No Hosts</defaultConnectorLabel>
        <rules>
            <name>Hosts</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Hosts</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Hosts</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Hosts</targetReference>
            </connector>
            <label>Hosts</label>
        </rules>
    </decisions>
    <decisions>
        <description>Are there any queue members in Premium Managed Host SMS Queue?</description>
        <name>Are_There_Queue_Members</name>
        <label>Are There Queue Members?</label>
        <locationX>842</locationX>
        <locationY>576</locationY>
        <defaultConnector>
            <targetReference>Get_Hosted_Campaign_Event</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Queue Members</defaultConnectorLabel>
        <rules>
            <name>Queue_Members</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Host_SMS_Queue_Membership</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Host_SMS_Queue_Membership</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Remove_Queue_Members</targetReference>
            </connector>
            <label>Queue Members</label>
        </rules>
    </decisions>
    <decisions>
        <description>Are there Host records for Victoria events today?</description>
        <name>Are_there_Victoria_hosts</name>
        <label>Are there Victoria hosts?</label>
        <locationX>314</locationX>
        <locationY>1740</locationY>
        <defaultConnector>
            <targetReference>Get_Hosts</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Hosts</defaultConnectorLabel>
        <rules>
            <name>Victoria_Hosts</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Victoria_Hosts</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Victoria_Hosts</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Victoria_Hosts</targetReference>
            </connector>
            <label>Victoria Hosts</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is the event based in Victoria</description>
        <name>Event_in_Victoria</name>
        <label>Event in Victoria</label>
        <locationX>534</locationX>
        <locationY>1248</locationY>
        <defaultConnector>
            <targetReference>Assign_Campaign_Ids</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Other State</defaultConnectorLabel>
        <rules>
            <name>Victoria_Event</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Campaign_Events.State__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VIC</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Victoria_Campaign_Ids</targetReference>
            </connector>
            <label>Victoria Event</label>
        </rules>
    </decisions>
    <description>Daily Scheduled Flow that updates the queue membership of the Premium Managed Host SMS queue to add or remove users based on if they are the host of an event that is on this day. This is to allow customer who are attending the event to be able to send SMS about the event and have them responded to by hosts specifically. Works in conjunction with Customer Attendee section of the Messaging Session: Premium SMS Routing Omnichannel Flow</description>
    <environments>Default</environments>
    <formulas>
        <description>Calculates tomorrows date from Current Date</description>
        <name>formulaDateTomorrow</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate} + 1</expression>
    </formulas>
    <interviewLabel>Queue: Daily Schedule Change Host SMS Queue Membership {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Queue: Daily Schedule Change Host SMS Queue Membership</label>
    <loops>
        <description>Loops through campaign events to get Ids of Campaigns</description>
        <name>Loop_Campaign_Events</name>
        <label>Loop Campaign Events</label>
        <locationX>314</locationX>
        <locationY>1140</locationY>
        <collectionReference>Get_Hosted_Campaign_Event</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Event_in_Victoria</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Victoria_Hosts</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops through Hosts to get Ids of users</description>
        <name>Loop_Hosts</name>
        <label>Loop Hosts</label>
        <locationX>50</locationX>
        <locationY>2448</locationY>
        <collectionReference>Get_Hosts</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Host_Queue_Member_Values</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Create_Host_SMS_Queue_Membership</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops through Hosts to get Ids of users</description>
        <name>Loop_Victoria_Hosts</name>
        <label>Loop Victoria Hosts</label>
        <locationX>206</locationX>
        <locationY>1848</locationY>
        <collectionReference>Get_Victoria_Hosts</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Host_Victoria_Queue_Member_Values</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Hosts</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>Creates the membership records for the Premium Managed Host SMS queue</description>
        <name>Create_Host_SMS_Queue_Membership</name>
        <label>Create Host SMS Queue Membership</label>
        <locationX>50</locationX>
        <locationY>2748</locationY>
        <faultConnector>
            <targetReference>Send_Email_to_Jenny_Joel</targetReference>
        </faultConnector>
        <inputReference>varQueueMembershipCollection</inputReference>
    </recordCreates>
    <recordDeletes>
        <description>Removes all group members from Premium Managed Host SMS queue</description>
        <name>Remove_Queue_Members</name>
        <label>Remove Queue Members</label>
        <locationX>578</locationX>
        <locationY>684</locationY>
        <connector>
            <targetReference>Get_Hosted_Campaign_Event</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Send_Email_to_Jenny_Joel</targetReference>
        </faultConnector>
        <inputReference>Get_Host_SMS_Queue_Membership</inputReference>
    </recordDeletes>
    <recordLookups>
        <description>Gets the Queue for the Premium Managed Host SMS</description>
        <name>Get_Host_SMS_Queue</name>
        <label>Get Host SMS Queue</label>
        <locationX>842</locationX>
        <locationY>252</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Host_Victoria_SMS_Queue</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Send_Email_to_Jenny_Joel</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_Host_SMS</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the membership of the Premium Managed Host SMS queue</description>
        <name>Get_Host_SMS_Queue_Membership</name>
        <label>Get Host SMS Queue Membership</label>
        <locationX>842</locationX>
        <locationY>468</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Are_There_Queue_Members</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Send_Email_to_Jenny_Joel</targetReference>
        </faultConnector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>GroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Host_SMS_Queue.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>GroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Host_Victoria_SMS_Queue.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GroupMember</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Queue for the Premium Managed Host Victoria SMS Queue</description>
        <name>Get_Host_Victoria_SMS_Queue</name>
        <label>Get Host Victoria SMS Queue</label>
        <locationX>842</locationX>
        <locationY>360</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Host_SMS_Queue_Membership</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Send_Email_to_Jenny_Joel</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_Host_Victoria_SMS</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Campaign Event records for hosted Events starting today</description>
        <name>Get_Hosted_Campaign_Event</name>
        <label>Get Hosted Campaign Event</label>
        <locationX>842</locationX>
        <locationY>924</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Any_Events_Today</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Send_Email_to_Jenny_Joel</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Start_Date__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </filters>
        <filters>
            <field>Start_Date__c</field>
            <operator>LessThan</operator>
            <value>
                <elementReference>formulaDateTomorrow</elementReference>
            </value>
        </filters>
        <filters>
            <field>Is_Hosted_Event__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Campaign</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Host records for the Campaigns found in Campaign Ids</description>
        <name>Get_Hosts</name>
        <label>Get Hosts</label>
        <locationX>314</locationX>
        <locationY>2232</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Are_there_hosts</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Send_Email_to_Jenny_Joel</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Campaign__c</field>
            <operator>In</operator>
            <value>
                <elementReference>varCampaignIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Host__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Host records for the Campaigns found in Campaign Ids Victoria</description>
        <name>Get_Victoria_Hosts</name>
        <label>Get Victoria Hosts</label>
        <locationX>314</locationX>
        <locationY>1632</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Are_there_Victoria_hosts</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Send_Email_to_Jenny_Joel</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Campaign__c</field>
            <operator>In</operator>
            <value>
                <elementReference>varCampaignIdsVictoria</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Host__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>716</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Host_SMS_Queue</targetReference>
        </connector>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2024-08-27</startDate>
            <startTime>06:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>currentItemFromSourceCollection</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Host__c</objectType>
    </variables>
    <variables>
        <description>Stores Campaign Ids for Event to use with Get Hosts</description>
        <name>varCampaignIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores campaigns Ids for just Victoria events</description>
        <name>varCampaignIdsVictoria</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the Id of the users that are hosts</description>
        <name>varHostUserIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores Host Queue Ids to use for getting the members of those queues</description>
        <name>varQueueIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the collection of Queue Membership records</description>
        <name>varQueueMembershipCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>GroupMember</objectType>
    </variables>
    <variables>
        <description>Stores single Queue Membership record</description>
        <name>varQueueMembershipSingle</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>GroupMember</objectType>
    </variables>
</Flow>
