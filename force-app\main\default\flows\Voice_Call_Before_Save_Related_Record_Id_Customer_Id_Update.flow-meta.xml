<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Inbound, Outbound or Transfer</description>
        <name>Call_Type</name>
        <label>Call Type</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Inbound_Transfer</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CallType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Inbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CallType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Transfer</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Related_Record</targetReference>
            </connector>
            <label>Inbound &amp; Transfer</label>
        </rules>
        <rules>
            <name>Outbound</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CallType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Outbound</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Customer_Field</targetReference>
            </connector>
            <label>Outbound</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is this a newly created record</description>
        <name>Is_Record_New</name>
        <label>Is Record New</label>
        <locationX>776</locationX>
        <locationY>323</locationY>
        <defaultConnector>
            <targetReference>Related_Record_or_Customer_Changed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>New</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formulaIsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Call_Type</targetReference>
            </connector>
            <label>New</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was the RelatedRecordId or Customer fields changed</description>
        <name>Related_Record_or_Customer_Changed</name>
        <label>Related Record or Customer Changed</label>
        <locationX>1238</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RelatedRecordId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Customer__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Which_Field_Changed</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>RelatedRecordId or Customer field</description>
        <name>Which_Field_Changed</name>
        <label>Which Field Changed</label>
        <locationX>1018</locationX>
        <locationY>539</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RelatedRecordId</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RelatedRecordId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Customer_Id</targetReference>
            </connector>
            <label>RelatedRecordId</label>
        </rules>
        <rules>
            <name>Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Customer__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RelatedRecordId</targetReference>
            </connector>
            <label>Customer</label>
        </rules>
    </decisions>
    <description>Updates the Related Record Id &amp; Customer Id fields</description>
    <environments>Default</environments>
    <formulas>
        <name>formulaIsNew</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <interviewLabel>Voice Call: Before Save Related Record Id &amp; Customer Id Update {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Voice Call: Before Save Related Record Id &amp; Customer Id Update</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Updates Customer Id with RelatedRecordId</description>
        <name>Update_Customer_Field</name>
        <label>Update Customer Field</label>
        <locationX>314</locationX>
        <locationY>539</locationY>
        <inputAssignments>
            <field>Customer__c</field>
            <value>
                <elementReference>$Record.RelatedRecordId</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates Customer Id with RelatedRecordId</description>
        <name>Update_Customer_Id</name>
        <label>Update Customer Id</label>
        <locationX>754</locationX>
        <locationY>647</locationY>
        <inputAssignments>
            <field>Customer__c</field>
            <value>
                <elementReference>$Record.RelatedRecordId</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates RelatedRecordId with Customer Id</description>
        <name>Update_Related_Record</name>
        <label>Update Related Record</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <inputAssignments>
            <field>RelatedRecordId</field>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates RelatedRecordId with Customer Id</description>
        <name>Update_RelatedRecordId</name>
        <label>Update RelatedRecordId</label>
        <locationX>1018</locationX>
        <locationY>647</locationY>
        <inputAssignments>
            <field>RelatedRecordId</field>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>650</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_Record_New</targetReference>
        </connector>
        <object>VoiceCall</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
