<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>57.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>AddUpdatedTasksToCollection</name>
        <label>Add Updated Tasks to Collection</label>
        <locationX>138</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>varTasksToBeUpdated</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>LoopAllTasks</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>LoopAllTasks</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignTaskUpdates</name>
        <label>Assign Task Updates</label>
        <locationX>138</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>LoopAllTasks.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>LoopAllTasks.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Customer account is not active. Task closed by System.</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>AddUpdatedTasksToCollection</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>TasksAvailable</name>
        <label>Tasks Available</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decYes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varOpenTasks</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>LoopAllTasks</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>Subflow for automating the closure of tasks when an account status is either Suspended, Closed, Timeout or if Self Excluded equals Yes
- Includes status of &quot;Not Started&quot; for tasks</description>
    <environments>Default</environments>
    <interviewLabel>Inactive User Task Closure {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Inactive User Task Closure</label>
    <loops>
        <name>LoopAllTasks</name>
        <label>LoopAllTasks</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <collectionReference>varOpenTasks</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>AssignTaskUpdates</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>UpdateTasks</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Retrieves tasks with the following Subject lines:
- Customer Birthday
- Biggest Win Last 12 Months
- Alert: Introduction to Customer
- Alert: Expiring Bonus/Deposit Offer
- RG Alert - (all)
- Alert - Viewed Sky Racing but no Racing Bet in last 7 days</description>
        <name>GetOpenTasks</name>
        <label>Get Open Tasks</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>TasksAvailable</targetReference>
        </connector>
        <filterLogic>(1 OR 10) AND (2 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9) AND 3</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Customer Birthday</stringValue>
            </value>
        </filters>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Biggest Win Last 12 Months</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Alert: Introduction to Customer</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Alert: Expiring Bonus/Deposit Offer</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Alert - Viewed Sky Racing but no Racing Bet in last 7 days</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>RG Alert - </stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Alert - Logged in and hasn&apos;t bet for 7+ days</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Not Started</stringValue>
            </value>
        </filters>
        <object>Task</object>
        <outputReference>varOpenTasks</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Status</queriedFields>
        <queriedFields>Description</queriedFields>
    </recordLookups>
    <recordUpdates>
        <name>UpdateTasks</name>
        <label>Update Tasks</label>
        <locationX>50</locationX>
        <locationY>758</locationY>
        <inputReference>varTasksToBeUpdated</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetOpenTasks</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varOpenTasks</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>varTasksToBeUpdated</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
