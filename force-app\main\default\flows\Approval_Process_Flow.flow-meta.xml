<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>BB_Adj_Amount_GT20K_LTE40K</name>
        <label>BB Adj Amount GT20K LTE40K</label>
        <locationX>2690</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Amt_GT20K_LTE40K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Submitted with Amont GT20K LTE40K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>BB_Amount_GT40K</name>
        <label>BB Amount GT40K</label>
        <locationX>2954</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Amt_GT40K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Approval request for amount GT40K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Expense_Approval_Submit</name>
        <label>Expense Approval Submit</label>
        <locationX>4802</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Expense_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Expense Appoval Submit</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>ManualAdj_Amount_GT10K_LTE25K</name>
        <label>ManualAdj Amount GT10K LTE25K</label>
        <locationX>4010</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Manual_Adjustment_Amt_GT10K_LTE25K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Submitted for Approval with Amount GT10K LTE25K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>ManualAdj_Amount_GT25K</name>
        <label>ManualAdj Amount GT25K</label>
        <locationX>4274</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Manual_Adjustment_Amt_GT25K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>SUbmitted for Approval with Amount GT25K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Other_Role_GT25K_ManualAdj_Approval</name>
        <label>Other Role GT25K ManualAdj Approval</label>
        <locationX>5594</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Manual_Adjustment_Amt_GT25K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>{!$Record.CreatedBy.UserRole.Name} submitted for approval with amount {!$Record.Amount__c}</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Other_Role_GT40K_BonusBet_Approval</name>
        <label>Other Role GT40K BonusBet Approval</label>
        <locationX>5858</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Amt_GT40K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>{!$Record.CreatedBy.UserRole.Name} Submitted For Approval with Amount GT40K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Other_Roles_LT25K_ManualAdj_Approval</name>
        <label>Other Roles LT25K ManualAdj Approval</label>
        <locationX>5330</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Manual_Adjustment_Amt_LT25K_Other_Roles</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Other role submitted for approval with amount LTE25K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Other_Roles_LT40K_BonusBet_Approval</name>
        <label>Other Roles LT40K BonusBet Approval</label>
        <locationX>5066</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Amt_LT40K_Other_Roles</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Other Role Submitted with Amount LTE40K submitted for approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>RM_NSW_ManualAdj_GT5K_LTE10K</name>
        <label>RM NSW ManualAdj GT5K LTE10K</label>
        <locationX>3218</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>RM_NSW_Manual_Adjustment_Amt_GT5K_LTE10K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Relationship Manager NSW submitted for Approval with amount GT5K LTE10K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>RM_QLD_approval_GT10_LTE20K</name>
        <label>RM QLD approval GT10 LTE20K</label>
        <locationX>2162</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>RM_QLD_Adjustment_Amt_GT10K_LTE20K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Relationship Manager QLD/WA</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>RM_QLD_ManualAdj_GT5K_LTE10K</name>
        <label>RM QLD ManualAdj GT5K LTE10K</label>
        <locationX>3482</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>RM_QLD_Manual_Adjustment_Amt_GT5K_LTE10K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Relationship Manager QLD/WA submitted for approval with Amount GT5K LTE10K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>RM_submit_GT10K_LTE20K</name>
        <label>RM submit GT10K LTE20K</label>
        <locationX>1898</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>RM_NSW_Adjustment_Amt_GT10K_LTE20K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Relationship Manager NSW Submitted for Approval, Amount GT10K LTE20K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>RM_VIC_GT10K_LTE20K_approval</name>
        <label>RM VIC GT10K LTE20K approval</label>
        <locationX>2426</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>RM_VIC_Adjustment_Amt_GT10K_LTE20K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Relationship Manager VIC submitted for approval Amount GT10K LTE20K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>RM_VIC_ManualAdj_GT5K_LTE10K</name>
        <label>RM VIC ManualAdj GT5K LTE10K</label>
        <locationX>3746</locationX>
        <locationY>455</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>RM_VIC_Manual_Adjustment_Amt_GT5K_LTE10K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Relationship Manager VIC submitted for Approval  with Amount GT5K LTE10K</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>57.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>RecordTypeCustomerAdj</name>
        <dataType>String</dataType>
        <value>
            <stringValue>0122y000000bpLyAAI</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Check for different approval process and</description>
        <name>Different_Approval_Process</name>
        <label>Different Approval Process?</label>
        <locationX>3350</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Remove_Customer_submitted_if_Status_Rejected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rejected</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Expense</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Remove_Customer_Pending_Approval</targetReference>
            </connector>
            <label>Remove Customer submitted if Status Rejected</label>
        </rules>
        <rules>
            <name>RM_BonusBet_LTE10K_Auto_Approve</name>
            <conditionLogic>1 AND 2 AND 3 AND (4 OR 5 OR 6 OR 7)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_10K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_BB_LTE10K_Auto_Approve</targetReference>
            </connector>
            <label>RM BonusBet LTE10K Auto Approve</label>
        </rules>
        <rules>
            <name>PTM_BonusBet_LTE20K_Auto_Approve</name>
            <conditionLogic>1 AND 2 AND 3 AND (4 OR 5 OR 6 OR 7 OR 8)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_20K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_PTM_BB_LTE20K_Auto_Approve</targetReference>
            </connector>
            <label>PTM BonusBet LTE20K Auto Approve</label>
        </rules>
        <rules>
            <name>PSM_BonusBet_LTE40K_Auto_Approve</name>
            <conditionLogic>1 AND 2 AND 3 AND (4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_40K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_PSM_BB_LTE40K_Auto_Approve</targetReference>
            </connector>
            <label>PSM BonusBet LTE40K Auto Approve</label>
        </rules>
        <rules>
            <name>RM_ManualAdj_LTE5K_Auto_Approve</name>
            <conditionLogic>1 AND 2 AND 3 AND (4 OR 5 OR 6 OR 7)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_5K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_MA_LTE5K_Auto_Approve</targetReference>
            </connector>
            <label>RM ManualAdj LTE5K Auto Approve</label>
        </rules>
        <rules>
            <name>PTM_ManualAdj_LTE10K_Auto_Approve</name>
            <conditionLogic>1 AND 2 AND 3 AND (4 OR 5 OR 6 OR 7 OR 8)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_10K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_PTM_MA_LTE10K_Auto_Approve</targetReference>
            </connector>
            <label>PTM ManualAdj LTE10K Auto Approve</label>
        </rules>
        <rules>
            <name>PSM_ManualAdj_LTE25K_Auto_Approve</name>
            <conditionLogic>1 AND 2 AND 3 AND (4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_25K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_PSM_MA_LTE20K_Auto_Approve</targetReference>
            </connector>
            <label>PSM ManualAdj LTE25K Auto Approve</label>
        </rules>
        <rules>
            <name>RM_NSW_GT10K_LTE20K_BonusBet</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_10K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_20K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RM_submit_GT10K_LTE20K</targetReference>
            </connector>
            <label>RM NSW GT10K LTE20K BonusBet</label>
        </rules>
        <rules>
            <name>RM_QLD_GT10K_LTE20K_BonusBet</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND (5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_10K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_20K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RM_QLD_approval_GT10_LTE20K</targetReference>
            </connector>
            <label>RM QLD GT10K LTE20K BonusBet</label>
        </rules>
        <rules>
            <name>RM_VIC_GT10K_LTE20K_BonusBet</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_10K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_20K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RM_VIC_GT10K_LTE20K_approval</targetReference>
            </connector>
            <label>RM VIC GT10K LTE20K BonusBet</label>
        </rules>
        <rules>
            <name>BonusBet_GT20K_LTE40K</name>
            <conditionLogic>1 AND 12 AND 2 AND 3 AND (4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 13)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_20K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_40K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>BB_Adj_Amount_GT20K_LTE40K</targetReference>
            </connector>
            <label>BonusBet GT20K LTE40K</label>
        </rules>
        <rules>
            <name>BonusBet_GT40K</name>
            <conditionLogic>1 AND 2 AND 3 AND (4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13 OR 14 OR 15)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_40K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>BB_Amount_GT40K</targetReference>
            </connector>
            <label>BonusBet GT40K</label>
        </rules>
        <rules>
            <name>RM_NSW_MA_GT5K_LTE10K_ManualAdj</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_5K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_10K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RM_NSW_ManualAdj_GT5K_LTE10K</targetReference>
            </connector>
            <label>RM NSW MA GT5K LTE10K ManualAdj</label>
        </rules>
        <rules>
            <name>RM_QLD_GT5K_LTE10K_ManualAdj</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND (5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_5K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_10K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RM_QLD_ManualAdj_GT5K_LTE10K</targetReference>
            </connector>
            <label>RM QLD GT5K LTE10K ManualAdj</label>
        </rules>
        <rules>
            <name>RM_VIC_GT5K_LTE10K_ManualAdj</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_5K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_10K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RM_VIC_ManualAdj_GT5K_LTE10K</targetReference>
            </connector>
            <label>RM VIC GT5K LTE10K ManualAdj</label>
        </rules>
        <rules>
            <name>RM_PTM_GT10K_LTE25K_ManualAdj</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND (5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_10K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_25K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ManualAdj_Amount_GT10K_LTE25K</targetReference>
            </connector>
            <label>RM PTM GT10K LTE25K ManualAdj</label>
        </rules>
        <rules>
            <name>ManualAdj_GT25K</name>
            <conditionLogic>1 AND 2 AND 3 AND (4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13 OR 14 OR 15)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_25K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ManualAdj_Amount_GT25K</targetReference>
            </connector>
            <label>ManualAdj GT25K</label>
        </rules>
        <rules>
            <name>Expense_Submitted_LT400_Auto_Approve</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Submitted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>400.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Expense</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Expense_Submitted_Auto_Approved</targetReference>
            </connector>
            <label>Expense Submitted LT400 Auto Approve</label>
        </rules>
        <rules>
            <name>Expense_Approval</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>400.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Expense</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Submitted</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Expense_Approval_Submit</targetReference>
            </connector>
            <label>Expense Approval</label>
        </rules>
        <rules>
            <name>Other_Roles_LT40K_BonusBet</name>
            <conditionLogic>1 AND 2 AND 3 AND NOT(4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13 OR 14 OR 15 OR 16)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_40K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.General_Manager__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Other_Roles_LT40K_BonusBet_Approval</targetReference>
            </connector>
            <label>Other Roles LT40K BonusBet</label>
        </rules>
        <rules>
            <name>Other_Roles_LT25K_ManualAdj</name>
            <conditionLogic>1 AND 13 AND 14 AND NOT(2 OR 3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 15 OR 16)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_25K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.General_Manager__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Other_Roles_LT25K_ManualAdj_Approval</targetReference>
            </connector>
            <label>Other Roles LT25K ManualAdj</label>
        </rules>
        <rules>
            <name>Other_Roles_GT25K_ManualAdj</name>
            <conditionLogic>1 AND 2 AND 3 AND NOT(4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13 OR 14 OR 15 OR 16)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_25K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Manual Adjustment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.General_Manager__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Other_Role_GT25K_ManualAdj_Approval</targetReference>
            </connector>
            <label>Other Roles GT25K ManualAdj</label>
        </rules>
        <rules>
            <name>Other_Roles_GT40K_BonusBet</name>
            <conditionLogic>1 AND 2 AND 3 AND NOT(4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13 OR 14 OR 15 OR 16)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Adjustment_Amt_40K__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bonus Bet</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Relationship_Manager_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_WA__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PTM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_NSW__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_QLD__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.PSM_VIC__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.Premium_Performance__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.General_Manager__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Other_Role_GT40K_BonusBet_Approval</targetReference>
            </connector>
            <label>Other Roles GT40K BonusBet</label>
        </rules>
        <rules>
            <name>General_Manger_Submitted</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>RecordTypeCustomerAdj</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Setup.AdjApprovalProcessSetting__c.General_Manager__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>General_manager_auto_Approve</targetReference>
            </connector>
            <label>General Manger Submitted?</label>
        </rules>
        <rules>
            <name>Is_Adjustment_Status_Submitted</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Submitted</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Customer_Pending_Approval_to_Customer</targetReference>
            </connector>
            <label>Is Adjustment Status Submitted</label>
        </rules>
    </decisions>
    <description>Flow triggers the respective approval process based on criteria</description>
    <environments>Default</environments>
    <interviewLabel>Approval Process Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Approval Process Flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Expense_Submitted_Auto_Approved</name>
        <label>Expense Submitted Auto Approved</label>
        <locationX>4538</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Approval_Notes__c</field>
            <value>
                <stringValue>Auto Approved</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Pending_Approval__c</field>
            <value>
                <stringValue></stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>General_manager_auto_Approve</name>
        <label>General manager auto Approve</label>
        <locationX>6122</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Remove_Customer_Pending_Approval</name>
        <label>Remove Customer Pending Approval</label>
        <locationX>50</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Customer_Pending_Approval__c</field>
            <value>
                <stringValue></stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Notes__c</field>
            <value>
                <stringValue>Removed Customer Submitted(dgdgdgd)</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Customer_Pending_Approval_to_Customer</name>
        <label>Update Customer Pending Approval to Customer</label>
        <locationX>6386</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Customer_Pending_Approval__c</field>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_PSM_BB_LTE40K_Auto_Approve</name>
        <label>Update PSM BB LTE40K Auto Approve</label>
        <locationX>842</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_PSM_MA_LTE20K_Auto_Approve</name>
        <label>Update PSM MA LTE20K Auto Approve</label>
        <locationX>1634</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_PTM_BB_LTE20K_Auto_Approve</name>
        <label>Update PTM BB LTE20K Auto Approve</label>
        <locationX>578</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_PTM_MA_LTE10K_Auto_Approve</name>
        <label>Update PTM MA LTE10K Auto Approve</label>
        <locationX>1370</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_BB_LTE10K_Auto_Approve</name>
        <label>Update RM BB LTE10K Auto Approve</label>
        <locationX>314</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_MA_LTE5K_Auto_Approve</name>
        <label>Update RM MA LTE5K Auto Approve</label>
        <locationX>1106</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>3224</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Different_Approval_Process</targetReference>
        </connector>
        <object>Adjustment__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
