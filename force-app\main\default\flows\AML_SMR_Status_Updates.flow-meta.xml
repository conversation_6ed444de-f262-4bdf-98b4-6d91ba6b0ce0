<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Count</name>
        <label>Assign Count</label>
        <locationX>776</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>CountFiles</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>DocCollectionVar</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Count_files_attached</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>If Standard SMR Case Status is QC review then change the Case Owner to Senior Analyst Queue</description>
        <name>Case_Status_QC_review</name>
        <label>Case Status = QC review?</label>
        <locationX>182</locationX>
        <locationY>947</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>QC Review</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Queue_Dev_Name</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>If Fast Track SMR Case Status is in Submit to Austrac then the Case Owner should be changed to Senior Analyst Queue.</description>
        <name>Case_Status_Submit_to_Austrac</name>
        <label>Case Status = Submit to Austrac?</label>
        <locationX>710</locationX>
        <locationY>947</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Submit to Austrac</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Get_Queue_Dev_Name</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Documents_found</name>
        <label>Documents found?</label>
        <locationX>644</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Assign_Count</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Documents Found</defaultConnectorLabel>
        <rules>
            <name>No_Documents_found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>DocCollectionVar</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Count_files_attached_to_0</targetReference>
            </connector>
            <label>No Documents found</label>
        </rules>
    </decisions>
    <decisions>
        <name>Standard_or_Fast_Track_SMR</name>
        <label>Standard or Fast Track SMR</label>
        <locationX>644</locationX>
        <locationY>839</locationY>
        <defaultConnectorLabel>Default</defaultConnectorLabel>
        <rules>
            <name>Standard_SMR</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Standard_SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Case_Status_QC_review</targetReference>
            </connector>
            <label>Standard SMR</label>
        </rules>
        <rules>
            <name>Fast_Track_SMR</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fast_Track_SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Case_Status_Submit_to_Austrac</targetReference>
            </connector>
            <label>Fast Track SMR</label>
        </rules>
    </decisions>
    <description>This flow updates the statuses of Standard or Fast Track SMR to Senior Queue.
Standard SMR - IF Case Status = QC review, then Case Owner = Senior Analyst Queue.
Fast Track SMR - IF Case Status = Submit to Austrac, then Case Owner = Senior Analyst Queue

It also checks if the Documents have been uploaded before a case is Closed.</description>
    <environments>Default</environments>
    <interviewLabel>AML SMR Status Updates {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML SMR Status Updates &amp; File Upload Check</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Copy_1_of_Get_Queue_Dev_Name</name>
        <label>Get Queue Dev Name</label>
        <locationX>578</locationX>
        <locationY>1055</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Change_Case_Owner_to_Senior_Analyst_Queue_for_Fast_Track_SMR</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Senior_Analyst</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <object>Group</object>
        <outputAssignments>
            <assignToReference>SeniorAnalystQueue</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get attached documents on the case.</description>
        <name>Get_Files_on_SMR_case</name>
        <label>Get Files on SMR case</label>
        <locationX>644</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Documents_found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>LinkedEntityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <object>ContentDocumentLink</object>
        <outputReference>DocCollectionVar</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>Get_Queue_Dev_Name</name>
        <label>Get Queue Dev Name</label>
        <locationX>50</locationX>
        <locationY>1055</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Change_Case_Owner_to_Senior_Analyst_Queue_for_Standard_SMR</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Senior_Analyst</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <object>Group</object>
        <outputAssignments>
            <assignToReference>SeniorAnalystQueue</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordUpdates>
        <name>Change_Case_Owner_to_Senior_Analyst_Queue_for_Fast_Track_SMR</name>
        <label>Change Case Owner to Senior Analyst Queue for Fast Track SMR</label>
        <locationX>578</locationX>
        <locationY>1163</locationY>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>SeniorAnalystQueue</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Change_Case_Owner_to_Senior_Analyst_Queue_for_Standard_SMR</name>
        <label>Change Case Owner to Senior Analyst Queue for Standard SMR</label>
        <locationX>50</locationX>
        <locationY>1163</locationY>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>SeniorAnalystQueue</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update case field Count_Files_attached__c
This field is checked in the ValidationRule - Case.Require_Attachment_SMR_Case_Close</description>
        <name>Update_Count_files_attached</name>
        <label>Update Count files attached</label>
        <locationX>776</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Standard_or_Fast_Track_SMR</targetReference>
        </connector>
        <inputAssignments>
            <field>Count_Files_attached__c</field>
            <value>
                <elementReference>CountFiles</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update case field Count_Files_attached__c
This field is checked in the ValidationRule - Case.Require_Attachment_SMR_Case_Close</description>
        <name>Update_Count_files_attached_to_0</name>
        <label>Update Count files attached to 0</label>
        <locationX>512</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Standard_or_Fast_Track_SMR</targetReference>
        </connector>
        <inputAssignments>
            <field>Count_Files_attached__c</field>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>518</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Files_on_SMR_case</targetReference>
        </connector>
        <filterFormula>OR(
{!$Record.RecordType.DeveloperName} = &quot;Standard_SMR&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Fast_Track_SMR&quot;
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>CountFiles</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>DocCollectionVar</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContentDocumentLink</objectType>
    </variables>
    <variables>
        <name>SeniorAnalystQueue</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
