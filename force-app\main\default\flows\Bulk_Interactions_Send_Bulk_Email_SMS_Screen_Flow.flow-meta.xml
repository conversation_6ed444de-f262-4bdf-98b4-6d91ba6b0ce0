<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionCalls>
        <name>Launch_Bulk_Email_Action</name>
        <label>Launch Bulk Email Action</label>
        <locationX>50</locationX>
        <locationY>1754</locationY>
        <actionName>BulkEmailInvocable</actionName>
        <actionType>apex</actionType>
        <faultConnector>
            <targetReference>Error_Screen</targetReference>
        </faultConnector>
        <flowTransactionModel>NewTransaction</flowTransactionModel>
        <inputParameters>
            <name>caseSubject</name>
            <value>
                <elementReference>EmailSubjectInput</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>caseSummary</name>
            <value>
                <elementReference>CaseSummaryInput</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>createInteractions</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>reasonForContact</name>
            <value>
                <elementReference>Reason_For_Contact_Input</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIdList</name>
            <value>
                <elementReference>Get_ContactId_List_to_Send</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>template</name>
            <value>
                <elementReference>EmailTemplateDataTable.firstSelectedRow</elementReference>
            </value>
        </inputParameters>
        <nameSegment>BulkEmailInvocable</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Launch_Bulk_Updater_Job</name>
        <label>Launch Bulk Updater Job</label>
        <locationX>1194</locationX>
        <locationY>2870</locationY>
        <actionName>MessagingSessionBulkUpdaterInvocable</actionName>
        <actionType>apex</actionType>
        <faultConnector>
            <targetReference>Results_Screen_SMS</targetReference>
        </faultConnector>
        <flowTransactionModel>NewTransaction</flowTransactionModel>
        <inputParameters>
            <name>messagingSessionIds</name>
            <value>
                <elementReference>Send_Bulk_Conversation_Messages.messageIdentifiers</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingSessionReason</name>
            <value>
                <elementReference>Message_Reason_Input</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingSessionSummary</name>
            <value>
                <elementReference>SMS_Message_Input</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>runningUserId</name>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MessagingSessionBulkUpdaterInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Send_Bulk_Conversation_Messages</name>
        <label>Send Bulk Conversation Messages</label>
        <locationX>1194</locationX>
        <locationY>2762</locationY>
        <actionName>sendConversationMessages</actionName>
        <actionType>sendConversationMessages</actionType>
        <connector>
            <targetReference>Launch_Bulk_Updater_Job</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Error_Screen</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>messageDefinitionName</name>
            <value>
                <stringValue>Premium_Managed_Outbound_SMS</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingDefinitionInputParameters</name>
            <value>
                <apexValue>[
  {
    &quot;name&quot;: &quot;Flow_SMS_Message_Body&quot;,
    &quot;textValue&quot;: &quot;{!SMS_Message_Input}&quot;
  }
]</apexValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>requestType</name>
            <value>
                <stringValue>SendNotificationMessages</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isEnforceMessagingChannelConsent</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>allowedSessionStatus</name>
            <value>
                <stringValue>Any</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingEndUserIds</name>
            <value>
                <elementReference>Get_Messaging_End_User_Id_List</elementReference>
            </value>
        </inputParameters>
        <nameSegment>sendConversationMessages</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_Opt_Out_Contacts_to_RecipientFailOutput</name>
        <label>Add Opt Out Contacts to RecipientFailOutput</label>
        <locationX>1194</locationX>
        <locationY>2546</locationY>
        <assignmentItems>
            <assignToReference>RecipientsFailOutput</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>RecipientsSMSOptOut</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Messaging_End_User_Id_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_to_RecipientsFail</name>
        <label>Add to RecipientsFail</label>
        <locationX>1942</locationX>
        <locationY>1970</locationY>
        <assignmentItems>
            <assignToReference>RecipientsFailOutput</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Contact_Recipients</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>DisplayRecipientsFailSMSTable</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Contact_Recipients</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_to_RecipientsOptOut</name>
        <label>Add to RecipientsOptOut</label>
        <locationX>2206</locationX>
        <locationY>1862</locationY>
        <assignmentItems>
            <assignToReference>RecipientsSMSOptOut</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Contact_Recipients</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>DisplaySMSOptOutMessage</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Messaging_End_Users</assignToReference>
            <operator>RemoveAll</operator>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Contact_Recipients</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_to_RecipientsSuccess</name>
        <label>Add to RecipientsSuccess</label>
        <locationX>1678</locationX>
        <locationY>1970</locationY>
        <assignmentItems>
            <assignToReference>RecipientsSuccessOutput</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Contact_Recipients</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Contact_Recipients</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Count_Variable</name>
        <label>Assign Count Variable</label>
        <locationX>1546</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>NumRecipients</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>RecipientsInput</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Email_Template_Folders</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_MessageTypeOutput_variable</name>
        <label>Assign MessageTypeOutput variable</label>
        <locationX>1920</locationX>
        <locationY>998</locationY>
        <assignmentItems>
            <assignToReference>MessageTypeOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>SMS</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Recipients_that_are_SMS_Opt_In</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_output_variables</name>
        <label>Assign output variables</label>
        <locationX>314</locationX>
        <locationY>1322</locationY>
        <assignmentItems>
            <assignToReference>RecipientsWithoutEmail</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Filter_Contacts_without_Email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>RecipientsSuccessOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Filter_Contacts_to_send_Email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>RecipientsFailOutput</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Filter_Contacts_without_Email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>MessageTypeOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>RecipientsFailOutput</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Filter_Contacts_Not_Opt_In_Email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>RecipientsOptOutEmailCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Filter_Contacts_Not_Opt_In_Email</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Recipients_have_Emails</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Email_Choice</name>
        <choiceText>Email</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Email</stringValue>
        </value>
    </choices>
    <choices>
        <name>SMS_Choice</name>
        <choiceText>SMS</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>SMS</stringValue>
        </value>
    </choices>
    <collectionProcessors>
        <name>Filter_Contacts_Not_Opt_In_Email</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Contacts Not Opt In Email</label>
        <locationX>314</locationX>
        <locationY>1214</locationY>
        <assignNextValueToReference>currentItem_Filter_Contacts_Not_Opt_In_Email</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>RecipientsInput</collectionReference>
        <conditionLogic>1 AND (2 OR 3)</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Contacts_Not_Opt_In_Email.Bulk_Email_Opt_In__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <booleanValue>false</booleanValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_Filter_Contacts_Not_Opt_In_Email.Email</leftValueReference>
            <operator>IsBlank</operator>
            <rightValue>
                <booleanValue>false</booleanValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_Filter_Contacts_Not_Opt_In_Email.Email</leftValueReference>
            <operator>IsNull</operator>
            <rightValue>
                <booleanValue>false</booleanValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Assign_output_variables</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>Filter_Contacts_to_send_Email</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Contacts to send Email</label>
        <locationX>314</locationX>
        <locationY>998</locationY>
        <assignNextValueToReference>currentItem_Filter_Contacts_with_Email</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>RecipientsInput</collectionReference>
        <conditionLogic>(1 OR 2) AND 3</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Contacts_with_Email.Email</leftValueReference>
            <operator>IsNull</operator>
            <rightValue>
                <booleanValue>false</booleanValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_Filter_Contacts_with_Email.Email</leftValueReference>
            <operator>IsBlank</operator>
            <rightValue>
                <booleanValue>false</booleanValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_Filter_Contacts_with_Email.Bulk_Email_Opt_In__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <booleanValue>true</booleanValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Filter_Contacts_without_Email</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>Filter_Contacts_without_Email</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Contacts without Email</label>
        <locationX>314</locationX>
        <locationY>1106</locationY>
        <assignNextValueToReference>currentItem_Filter_Contacts_with_Email</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>RecipientsInput</collectionReference>
        <conditionLogic>or</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Contacts_with_Email.Email</leftValueReference>
            <operator>IsNull</operator>
            <rightValue>
                <booleanValue>true</booleanValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_Filter_Contacts_with_Email.Email</leftValueReference>
            <operator>IsBlank</operator>
            <rightValue>
                <booleanValue>true</booleanValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Filter_Contacts_Not_Opt_In_Email</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>Get_Recipients_that_are_SMS_Opt_In</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Get Recipients that are SMS Opt In</label>
        <locationX>1920</locationX>
        <locationY>1106</locationY>
        <assignNextValueToReference>currentItem_Get_Recipients_that_are_SMS_Opt_In</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>RecipientsInput</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Get_Recipients_that_are_SMS_Opt_In.Bulk_SMS_Opt_In__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <booleanValue>true</booleanValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Get_ContactIds_from_Recipients_Input</targetReference>
        </connector>
    </collectionProcessors>
    <constants>
        <name>BULK_CLASSIC_EMAIL_TEMPLATES_FOLDER_NAME</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Bulk_Email_Templates</stringValue>
        </value>
    </constants>
    <constants>
        <name>BULK_LIGHTNING_EMAIL_TEMPLATES_FOLDER_NAME</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BulkEmailTemplates</stringValue>
        </value>
    </constants>
    <constants>
        <name>SMS_BULK_MESSAGING_LIMIT</name>
        <dataType>Number</dataType>
        <value>
            <numberValue>100.0</numberValue>
        </value>
    </constants>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Does_Contact_Have_Associated_Messaging_User</name>
        <label>Does Contact Have Associated Messaging User?</label>
        <locationX>1810</locationX>
        <locationY>1862</locationY>
        <defaultConnector>
            <targetReference>Add_to_RecipientsFail</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes_Messaging_User_found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_ContactIds_from_Messaging_Users</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>Loop_Contact_Recipients.Id</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_to_RecipientsSuccess</targetReference>
            </connector>
            <label>Yes, Messaging User found</label>
        </rules>
    </decisions>
    <decisions>
        <name>Email_or_SMS</name>
        <label>Email or SMS?</label>
        <locationX>1546</locationX>
        <locationY>890</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Template_Select_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>None</defaultConnectorLabel>
        <rules>
            <name>Email</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>TemplateTypeInput</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Email_Choice</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Filter_Contacts_to_send_Email</targetReference>
            </connector>
            <label>Email</label>
        </rules>
        <rules>
            <name>SMS</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>TemplateTypeInput</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>SMS_Choice</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_MessageTypeOutput_variable</targetReference>
            </connector>
            <label>SMS</label>
        </rules>
    </decisions>
    <decisions>
        <name>Found_Messaging_End_Users</name>
        <label>Found Messaging End Users?</label>
        <locationX>1920</locationX>
        <locationY>1430</locationY>
        <defaultConnector>
            <targetReference>No_Messaging_Users_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>None returned</defaultConnectorLabel>
        <rules>
            <name>Yes_found_records</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Messaging_End_Users</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Messaging_End_Users</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_ContactIds_from_Messaging_Users</targetReference>
            </connector>
            <label>Yes, found records</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Customer_SMS_Opt_In</name>
        <label>Is Customer SMS Opt In?</label>
        <locationX>2008</locationX>
        <locationY>1754</locationY>
        <defaultConnector>
            <targetReference>Add_to_RecipientsOptOut</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Opt Out</defaultConnectorLabel>
        <rules>
            <name>Opt_In_SMS</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Contact_Recipients.Bulk_SMS_Opt_In__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Does_Contact_Have_Associated_Messaging_User</targetReference>
            </connector>
            <label>Opt In SMS</label>
        </rules>
    </decisions>
    <decisions>
        <name>Ready_to_send_to_any_Recipients</name>
        <label>Ready to send to any Recipients?</label>
        <locationX>1590</locationX>
        <locationY>2330</locationY>
        <defaultConnector>
            <targetReference>No_Users_Opt_In_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No recipients available to send</defaultConnectorLabel>
        <rules>
            <name>Yes_ready_to_send</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>RecipientsSuccessOutput</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>RecipientsSuccessOutput</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Confirmation_Page_SMS</targetReference>
            </connector>
            <label>Yes, ready to send</label>
        </rules>
    </decisions>
    <decisions>
        <name>Received_Recipients</name>
        <label>Received Recipients?</label>
        <locationX>2558</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>No_Customers_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>RecipientsInput</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>RecipientsInput</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Count_Variable</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Recipients_have_Emails</name>
        <label>Recipients have Emails?</label>
        <locationX>314</locationX>
        <locationY>1430</locationY>
        <defaultConnector>
            <targetReference>No_Emails_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>All Recipients have No Emails</defaultConnectorLabel>
        <rules>
            <name>Yes_Recipients_Have_Emails</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Filter_Contacts_to_send_Email</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Filter_Contacts_to_send_Email</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Confirmation_Page_Email</targetReference>
            </connector>
            <label>Yes, Recipients Have Emails</label>
        </rules>
    </decisions>
    <decisions>
        <name>Running_User_Has_Permission_to_Run_Bulk_Interactions</name>
        <label>Running User Has Permission to Run Bulk Interactions?</label>
        <locationX>3196</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>No_Access_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Access</defaultConnectorLabel>
        <rules>
            <name>Yes_User_Has_Permission</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Permission.Bulk_Interactions</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Received_Recipients</targetReference>
            </connector>
            <label>Yes, User Has Permission</label>
        </rules>
    </decisions>
    <dynamicChoiceSets>
        <name>MessageReasonChoiceSet</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Message_Reason__c</picklistField>
        <picklistObject>MessagingSession</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>ReasonForContactChoiceSet</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Reason_for_Contact__c</picklistField>
        <picklistObject>Task</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>SelectedTemplateName</name>
        <dataType>String</dataType>
        <expression>CASE({!TemplateTypeInput},
{!Email_Choice}, {!EmailTemplateDataTable.firstSelectedRow.Name},
{!SMS_Choice}, {!SMS_Message_Input},
&apos;&apos;)</expression>
    </formulas>
    <formulas>
        <name>SMSCharacterCount</name>
        <dataType>Number</dataType>
        <expression>IF(ISBLANK({!SMS_Message_Input}), 0, LEN(TRIM({!SMS_Message_Input})))</expression>
        <scale>0</scale>
    </formulas>
    <interviewLabel>Bulk Interactions - Send Bulk Email/SMS Screen Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Bulk Interactions - Send Bulk Email/SMS Screen Flow</label>
    <loops>
        <name>Loop_Contact_Recipients</name>
        <label>Loop Contact Recipients</label>
        <locationX>1590</locationX>
        <locationY>1646</locationY>
        <collectionReference>RecipientsInput</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Is_Customer_SMS_Opt_In</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Ready_to_send_to_any_Recipients</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Bulk_Email_Templates</name>
        <label>Get Bulk Email Templates</label>
        <locationX>1546</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Template_Select_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>1 AND 2 AND (3 OR 4)</filterLogic>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>FolderId</field>
            <operator>In</operator>
            <value>
                <elementReference>Get_Folder_Id_List</elementReference>
            </value>
        </filters>
        <filters>
            <field>RelatedEntityType</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>RelatedEntityType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <sortField>LastModifiedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Email_Template_Folders</name>
        <label>Get Email Template Folders</label>
        <locationX>1546</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Folder_Id_List</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>BULK_LIGHTNING_EMAIL_TEMPLATES_FOLDER_NAME</elementReference>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>BULK_CLASSIC_EMAIL_TEMPLATES_FOLDER_NAME</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Folder</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Messaging_End_Users</name>
        <label>Get Messaging End Users</label>
        <locationX>1920</locationX>
        <locationY>1322</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Found_Messaging_End_Users</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ContactId</field>
            <operator>In</operator>
            <value>
                <elementReference>Get_ContactIds_from_Recipients_Input</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <limit>
            <elementReference>SMS_BULK_MESSAGING_LIMIT</elementReference>
        </limit>
        <object>MessagingEndUser</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>DefaultMode</runInMode>
    <screens>
        <name>Confirmation_Page_Email</name>
        <label>Confirmation Page - Email</label>
        <locationX>50</locationX>
        <locationY>1538</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <backButtonLabel>No, Go Back</backButtonLabel>
        <connector>
            <targetReference>Get_ContactId_List_to_Send</targetReference>
        </connector>
        <fields>
            <name>ConfirmationDisplayText</name>
            <fieldText>&lt;p&gt;Confirm you would like to send the {!TemplateTypeInput} message &apos;&lt;strong&gt;{!SelectedTemplateName}&lt;/strong&gt;&apos; to the following recipients?:&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>ReceipientsConfirmation</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Filter_Contacts_to_send_Email</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-934e&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Email&quot;,&quot;guid&quot;:&quot;column-82bb&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Standard Email&quot;,&quot;type&quot;:&quot;email&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>RecipientsWithNoEmailDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(255, 0, 0);&quot;&gt;Please note&lt;/strong&gt;: the following recipients do not have an Email on their record and the system will &lt;b&gt;NOT &lt;/b&gt;attempt to send an email to the following:&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>RecipientsWithoutEmail</leftValueReference>
                    <operator>GreaterThan</operator>
                    <rightValue>
                        <numberValue>0.0</numberValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ReceipientsNoEmail</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Filter_Contacts_without_Email</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-50ef&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Email&quot;,&quot;guid&quot;:&quot;column-95fd&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Standard Email&quot;,&quot;type&quot;:&quot;email&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>RecipientsWithoutEmail</leftValueReference>
                    <operator>GreaterThan</operator>
                    <rightValue>
                        <numberValue>0.0</numberValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>NotOptInDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Please note&lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;: while the following recipients do have an Email populated on their record, they have opted &lt;/span&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;OUT&lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt; of receiving email and hence the system will &lt;/span&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;NOT &lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;attempt to send an email to the following:&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>RecipientsOptOutEmailCount</leftValueReference>
                    <operator>GreaterThan</operator>
                    <rightValue>
                        <numberValue>0.0</numberValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>RecipientsOptOutEmail</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Filter_Contacts_Not_Opt_In_Email</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-3e02&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Email&quot;,&quot;guid&quot;:&quot;column-a70f&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Standard Email&quot;,&quot;type&quot;:&quot;email&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>RecipientsOptOutEmailCount</leftValueReference>
                    <operator>GreaterThan</operator>
                    <rightValue>
                        <numberValue>0.0</numberValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Yes, Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Confirmation_Page_SMS</name>
        <label>Confirmation Page - SMS</label>
        <locationX>1194</locationX>
        <locationY>2438</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <backButtonLabel>No, Go Back</backButtonLabel>
        <connector>
            <targetReference>Add_Opt_Out_Contacts_to_RecipientFailOutput</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_ConfirmationDisplayText</name>
            <fieldText>&lt;p&gt;Confirm you would like to send the {!TemplateTypeInput} message &apos;&lt;strong&gt;{!SelectedTemplateName}&lt;/strong&gt;&apos; to the below recipients?:&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Copy_1_of_SMSMaxWarningDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(255, 0, 0);&quot;&gt;Please note&lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;: bulk SMS only supports {!SMS_BULK_MESSAGING_LIMIT} messages at a time. If more than this has been selected, &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;he system will only send an SMS to the first &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!SMS_BULK_MESSAGING_LIMIT} &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;customers.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>NumRecipients</leftValueReference>
                    <operator>GreaterThan</operator>
                    <rightValue>
                        <elementReference>SMS_BULK_MESSAGING_LIMIT</elementReference>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>TemplateTypeInput</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>SMS_Choice</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>MessagingUserConfirmation</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>RecipientsSuccessOutput</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-05a2&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;MobilePhone&quot;,&quot;guid&quot;:&quot;column-764e&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Standard Mobile&quot;,&quot;type&quot;:&quot;phone&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>NoMobileDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(255, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;Please note&lt;/strong&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;: the following recipients do not have a Messaging User record associated and the system will &lt;/span&gt;&lt;strong style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;NOT &lt;/strong&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;attempt send an SMS to the following:&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DisplayRecipientsFailSMSTable</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>NoMessagingUser</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>RecipientsFailOutput</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-702c&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;MobilePhone&quot;,&quot;guid&quot;:&quot;column-6583&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Standard Mobile&quot;,&quot;type&quot;:&quot;phone&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DisplayRecipientsFailSMSTable</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>OptOutDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(255, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;Please note&lt;/strong&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;: the following recipients have opted &lt;/span&gt;&lt;strong style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;OUT &lt;/strong&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;of receiving SMS, and the system will &lt;/span&gt;&lt;strong style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;NOT &lt;/strong&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;attempt send an SMS to the following:&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DisplaySMSOptOutMessage</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>OptOutDataTableSMS</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>RecipientsSMSOptOut</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-3770&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DisplaySMSOptOutMessage</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Yes, Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Error_Screen</name>
        <label>Error Screen</label>
        <locationX>314</locationX>
        <locationY>1862</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>ErrorDisplay</name>
            <fieldText>&lt;p&gt;Oops! Something went wrong.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Error message:&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-family: courier;&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_Access_Screen</name>
        <label>No Access Screen</label>
        <locationX>3834</locationX>
        <locationY>242</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>NoAccessDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Error&lt;/strong&gt;: Sorry, you do not have access to use the &apos;Bulk Interactions&apos; feature.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_Customers_Screen</name>
        <label>No Customers Screen</label>
        <locationX>3570</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>NoRecordsDisplay</name>
            <fieldText>&lt;p&gt;No Customers were received as input. &lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Please select records from the list view by selecting the checkbox on the rows of the relevant records, to send a message, and try again.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_Emails_Screen</name>
        <label>No Emails Screen</label>
        <locationX>578</locationX>
        <locationY>1538</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>NoEmailsDisplay</name>
            <fieldText>&lt;p&gt;Could not proceed with sending bulk emails as no recipient selected has an Email Address.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Please ensure all Customer or Contact records have an Email, and try again.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_Messaging_Users_Screen</name>
        <label>No Messaging Users Screen</label>
        <locationX>2250</locationX>
        <locationY>1538</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>NoUsersDisplay</name>
            <fieldText>&lt;p&gt;Could not send any SMS messages as recipients selected do not have &lt;strong&gt;Messaging End User&lt;/strong&gt; records created. Please contact your system administrator for more information.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;Please ensure each recipient has an up-to-date Mobile Number, has an associated Messaging User record, and has not opted out of SMS.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_Users_Opt_In_Screen</name>
        <label>No Users Opt In Screen</label>
        <locationX>1986</locationX>
        <locationY>2438</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Copy_1_of_NoUsersDisplay</name>
            <fieldText>&lt;p&gt;Could not send any SMS messages as all recipients selected have opted &lt;strong&gt;OUT&lt;/strong&gt; of receiving SMS messages. Please contact your system administrator for more information.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Please ensure each recipient has an up-to-date Mobile Number, has an associated Messaging User record, and has not opted out of SMS.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Results_Screen_SMS</name>
        <label>Results Screen - SMS</label>
        <locationX>1458</locationX>
        <locationY>2978</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>SMSResultsDisplay</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;A total of &lt;/span&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;{!NumSmsMessages}&lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt; SMS messages were queued to send!&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Request ID: &lt;/span&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68); font-family: courier;&quot;&gt;{!Send_Bulk_Conversation_Messages.requestId}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Message IDs: &lt;/span&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68); font-family: courier;&quot;&gt;{!Send_Bulk_Conversation_Messages.messageIdentifiers}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>SMSErrorDisplay</name>
            <fieldText>&lt;p&gt;If this is less than the expected number of recipients, please ensure each recipient has an up-to-date Mobile Number, has an associated Messaging User record, and has not opted out of SMS.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>NumSmsMessages</leftValueReference>
                    <operator>NotEqualTo</operator>
                    <rightValue>
                        <elementReference>NumRecipients</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ErrorJobDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(255, 0, 0);&quot;&gt;Technical error&lt;/strong&gt;&lt;strong&gt;:&lt;/strong&gt; something went wrong when scheduling the &lt;span style=&quot;color: rgb(78, 201, 176);&quot;&gt;MessagingSessionBulkUpdaterQueueable&lt;/span&gt; job. Give your Salesforce Admin these details:&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-family: courier;&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;Please note that your SMS messages have still been queued to send.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>ContinueDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Please click &apos;Continue&apos; to complete any required processing and display results.&lt;/strong&gt; The MessagingSessionBulkUpdaterQueueable&amp;nbsp;job is not required to send outbound messages.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Continue</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Template_Select_Screen</name>
        <label>Template Select Screen</label>
        <locationX>1546</locationX>
        <locationY>782</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Email_or_SMS</targetReference>
        </connector>
        <fields>
            <name>TemplateTypeInput</name>
            <choiceReferences>Email_Choice</choiceReferences>
            <choiceReferences>SMS_Choice</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Please select a type</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>EmailTemplateDataTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>EmailTemplate</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Available Email Templates</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Bulk_Email_Templates</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-a033&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Email Template Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Subject&quot;,&quot;guid&quot;:&quot;column-8650&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Email Subject&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Subject&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Description&quot;,&quot;guid&quot;:&quot;column-fd00&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;Interaction Description&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Description&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;LastModifiedDate&quot;,&quot;guid&quot;:&quot;column-9c08&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Last Modified Date&quot;,&quot;type&quot;:&quot;customDateTime&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>TemplateTypeInput</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>Email_Choice</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Template_Select_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Template_Select_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>EmailSubjectInput</name>
                    <dataType>String</dataType>
                    <fieldText>Subject</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>Reason_For_Contact_Input</name>
                    <choiceReferences>ReasonForContactChoiceSet</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Case Reason</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>TemplateTypeInput</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>Email_Choice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>CaseSummaryInput</name>
                    <fieldText>Case Summary</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>TemplateTypeInput</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>Email_Choice</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Template_Select_Screen_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Template_Select_Screen_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>SMSCharCountDisplay</name>
                    <fieldText>&lt;p&gt;{!SMSCharacterCount} / 320&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>TemplateTypeInput</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>SMS_Choice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>SMS_Message_Input</name>
                    <fieldText>SMS Message</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <validationRule>
                        <errorMessage>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;SMS character count must be 320 or below for a single message.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                        <formulaExpression>LEN({!SMS_Message_Input}) &lt;= 320</formulaExpression>
                    </validationRule>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>TemplateTypeInput</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>SMS_Choice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Message_Reason_Input</name>
                    <choiceReferences>MessageReasonChoiceSet</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Message Reason</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>TemplateTypeInput</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>SMS_Choice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>TemplateTypeInput</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>SMS_Choice</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>3070</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Running_User_Has_Permission_to_Run_Bulk_Interactions</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <transforms>
        <name>Get_ContactId_List_to_Send</name>
        <label>Get ContactId List to Send</label>
        <locationX>50</locationX>
        <locationY>1646</locationY>
        <connector>
            <targetReference>Launch_Bulk_Email_Action</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Filter_Contacts_to_send_Email[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>Get_ContactIds_from_Messaging_Users</name>
        <label>Get ContactId&apos;s from Messaging Users</label>
        <locationX>1590</locationX>
        <locationY>1538</locationY>
        <connector>
            <targetReference>Loop_Contact_Recipients</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Messaging_End_Users[$EachItem].ContactId</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>Get_ContactIds_from_Recipients_Input</name>
        <label>Get ContactId&apos;s from Recipients Input</label>
        <locationX>1920</locationX>
        <locationY>1214</locationY>
        <connector>
            <targetReference>Get_Messaging_End_Users</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Recipients_that_are_SMS_Opt_In[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>Get_Folder_Id_List</name>
        <label>Get Folder Id List</label>
        <locationX>1546</locationX>
        <locationY>566</locationY>
        <connector>
            <targetReference>Get_Bulk_Email_Templates</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Email_Template_Folders[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>Get_Messaging_End_User_Id_List</name>
        <label>Get Messaging End User Id List</label>
        <locationX>1194</locationX>
        <locationY>2654</locationY>
        <connector>
            <targetReference>Send_Bulk_Conversation_Messages</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Messaging_End_Users[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <variables>
        <name>currentItem_Filter_Contacts_Not_Opt_In_Email</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>currentItem_Filter_Contacts_with_Email</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>currentItem_Filter_Contacts_with_Messaging_Users</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>currentItem_Get_Recipients_that_are_SMS_Opt_In</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>DisplayRecipientsFailSMSTable</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>DisplaySMSOptOutMessage</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>MessageTypeOutput</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>MessagingSessionCollectionToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>MessagingSession</objectType>
    </variables>
    <variables>
        <name>MessagingSessionRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>MessagingSession</objectType>
    </variables>
    <variables>
        <name>NumRecipients</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>NumSmsMessages</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>RecipientsFailOutput</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>RecipientsInput</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>RecipientsOptOutEmailCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>RecipientsSMSOptOut</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>RecipientsSuccessOutput</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>RecipientsWithoutEmail</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
</Flow>
