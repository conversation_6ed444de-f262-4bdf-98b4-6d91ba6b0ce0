<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns Customer Id to text collection variable</description>
        <name>Assign_Customer_Id</name>
        <label>Assign Customer Id</label>
        <locationX>264</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>varCustomerIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Attendees.Account__r.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Attendees</targetReference>
        </connector>
    </assignments>
    <description>Used to uncheck Customer Attendee checkbox on Attendee&apos;s Customer record after an event is completed</description>
    <environments>Default</environments>
    <interviewLabel>Campaign: On Update - Customer Attendee Checkbox on Event Completed {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Campaign: On Update - Customer Attendee Checkbox on Event Completed</label>
    <loops>
        <description>Loops through Attendee records to get Customer Ids</description>
        <name>Loop_Attendees</name>
        <label>Loop Attendees</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <collectionReference>Get_Attendee_Records</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Customer_Id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Customer_Attendee_Checkbox</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets all attendee records on Campaign</description>
        <name>Get_Attendee_Records</name>
        <label>Get Attendee Records</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Attendees</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Marks the customer attendee checkbox on customer record as False</description>
        <name>Update_Customer_Attendee_Checkbox</name>
        <label>Update Customer Attendee Checkbox</label>
        <locationX>176</locationX>
        <locationY>731</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>varCustomerIds</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Attendee__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Attendee_Records</targetReference>
        </connector>
        <filterFormula>AND(
{!$Record.RecordType.DeveloperName} = &apos;Event&apos;,
ISCHANGED({!$Record.Status}),
ISPICKVAL({!$Record.Status}, &apos;Completed&apos;)
)</filterFormula>
        <object>Campaign</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>currentItemFromSourceCollection</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
    <variables>
        <description>Collections of Customer Ids from Attendee records</description>
        <name>varCustomerIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
