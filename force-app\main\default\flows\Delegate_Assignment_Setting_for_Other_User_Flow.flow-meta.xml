<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>AssignNewValue</name>
        <label>AssignNewValue</label>
        <locationX>527</locationX>
        <locationY>529</locationY>
        <assignmentItems>
            <assignToReference>varInputUser.Delegate_Assignment_Start_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen2inputStartDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varInputUser.Delegate_Assignment_End_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen2inputEndDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varInputUser.Delegate_Assignment_User_ID__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>screen2inputUserId.recordId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>UpdateUserRecord</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>RemoveValue</name>
        <label>RemoveValue</label>
        <locationX>985</locationX>
        <locationY>618</locationY>
        <assignmentItems>
            <assignToReference>varInputUser.Delegate_Assignment_Start_Date__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varInputUser.Delegate_Assignment_End_Date__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varInputUser.Delegate_Assignment_User_ID__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>screen2inputUserId.recordName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>UpdateUserRecord</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Remove</name>
        <choiceText>Remove Current Delegate Setting</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Remove</stringValue>
        </value>
    </choices>
    <choices>
        <name>Update</name>
        <choiceText>Update Delegate Setting</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Update</stringValue>
        </value>
    </choices>
    <decisions>
        <name>CheckRemoveUpdate</name>
        <label>CheckRemoveUpdate</label>
        <locationX>982</locationX>
        <locationY>153</locationY>
        <defaultConnector>
            <targetReference>Screen2SetDelegateAssignment</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>IsUpdate</defaultConnectorLabel>
        <rules>
            <name>IsRemove</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Screen1_Remove_Update</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Remove</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RemoveValue</targetReference>
            </connector>
            <label>IsRemove</label>
        </rules>
    </decisions>
    <decisions>
        <name>IsUserInDelegatePeriod</name>
        <label>IsUserInDelegatePeriod</label>
        <locationX>394</locationX>
        <locationY>52</locationY>
        <defaultConnector>
            <targetReference>Screen2SetDelegateAssignment</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not in period</defaultConnectorLabel>
        <rules>
            <name>Is_in_period_or_future_period</name>
            <conditionLogic>((1 AND 4) OR (2 AND 5)) AND 3</conditionLogic>
            <conditions>
                <leftValueReference>varInputUser.Delegate_Assignment_Start_Date__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varInputUser.Delegate_Assignment_End_Date__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varInputUser.Delegate_Assignment_User_ID__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varInputUser.Delegate_Assignment_Start_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varInputUser.Delegate_Assignment_End_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetDelegateUserRecord</targetReference>
            </connector>
            <label>Is in period or future period</label>
        </rules>
    </decisions>
    <decisions>
        <name>UserIdCheck</name>
        <label>UserIdCheck</label>
        <locationX>810</locationX>
        <locationY>530</locationY>
        <defaultConnector>
            <targetReference>AssignNewValue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>False</defaultConnectorLabel>
        <rules>
            <name>True</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>DelegateUserCheck</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Screen2SetDelegateAssignment</targetReference>
            </connector>
            <label>True</label>
        </rules>
    </decisions>
    <formulas>
        <name>CurrentUserId</name>
        <dataType>String</dataType>
        <expression>{!$User.Id}</expression>
    </formulas>
    <formulas>
        <name>DelegateUserCheck</name>
        <dataType>Boolean</dataType>
        <expression>{!varInputUser.Id}=={!screen2inputUserId.recordId}</expression>
    </formulas>
    <interviewLabel>Delegate Assignment Setting for other User Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Delegate Assignment Setting for Other User Flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>GetDelegateUserRecord</name>
        <label>GetDelegateUserRecord</label>
        <locationX>664</locationX>
        <locationY>42</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Show_Current_Delegated_Assignment</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Display_Error</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varInputUser.Delegate_Assignment_User_ID__c</elementReference>
            </value>
        </filters>
        <object>User</object>
        <outputReference>varDelegateUser</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Name</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>GetUserRecord</name>
        <label>GetUserRecord</label>
        <locationX>270</locationX>
        <locationY>13</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>IsUserInDelegatePeriod</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Display_Error</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Screen0inputUserId.recordId</elementReference>
            </value>
        </filters>
        <object>User</object>
        <outputReference>varInputUser</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Delegate_Assignment_User_ID__c</queriedFields>
        <queriedFields>Delegate_Assignment_End_Date__c</queriedFields>
        <queriedFields>Delegate_Assignment_Start_Date__c</queriedFields>
    </recordLookups>
    <recordUpdates>
        <name>UpdateUserRecord</name>
        <label>UpdateUserRecord</label>
        <locationX>143</locationX>
        <locationY>537</locationY>
        <connector>
            <targetReference>Show_Current_Delegated_Assignment_0</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Display_Error</targetReference>
        </faultConnector>
        <inputReference>varInputUser</inputReference>
    </recordUpdates>
    <runInMode>DefaultMode</runInMode>
    <screens>
        <name>Display_Error</name>
        <label>Display Error</label>
        <locationX>453</locationX>
        <locationY>308</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Screen0</targetReference>
        </connector>
        <fields>
            <name>FaultErrorMessage</name>
            <fieldText>&lt;p&gt;Error occurs. Please contact your System Administrator with below error message:&lt;/p&gt;&lt;p&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Screen0</name>
        <label>Screen0</label>
        <locationX>143</locationX>
        <locationY>50</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>GetUserRecord</targetReference>
        </connector>
        <fields>
            <name>Screen0HeaderDisplay</name>
            <fieldText>&lt;p&gt;&lt;b&gt;My Teams Salesforce Delegation&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Please Select a User to view/update the delegation&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Screen0inputUserId</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>ManagerId</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>User</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>User</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Screen2SetDelegateAssignment</name>
        <label>Set Delegate Assignment</label>
        <locationX>816</locationX>
        <locationY>271</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>UserIdCheck</targetReference>
        </connector>
        <fields>
            <name>Screen2Display</name>
            <fieldText>&lt;p&gt;&lt;b&gt;My Team Members Salesforce Delegation:&lt;/b&gt;&lt;/p&gt;&lt;p&gt;{!Screen0inputUserId.recordName}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Screen2inputStartDate</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>$Flow.CurrentDate</elementReference>
            </defaultValue>
            <fieldText>Start Date</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;Start Date must be future date&lt;/p&gt;</errorMessage>
                <formulaExpression>{!Screen2inputStartDate}&gt;={!$Flow.CurrentDate}</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>Screen2inputEndDate</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>$Flow.CurrentDate</elementReference>
            </defaultValue>
            <fieldText>End Date</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;End Date must be a future date, and greater than or equal to Start Date&lt;/p&gt;</errorMessage>
                <formulaExpression>{!Screen2inputEndDate}&gt;={!$Flow.CurrentDate} &amp;&amp; {!Screen2inputEndDate}&gt;={!Screen2inputStartDate}</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>screen2inputUserId</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>ManagerId</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Delegate Assignment User</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>User</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Screen2ErrorMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Delegate User must be other user&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DelegateUserCheck</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Show_Current_Delegated_Assignment</name>
        <label>Show Current Delegated Assignment</label>
        <locationX>817</locationX>
        <locationY>42</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>CheckRemoveUpdate</targetReference>
        </connector>
        <fields>
            <name>DisplayCurrentDelegateAssignment</name>
            <fieldText>&lt;p&gt;&lt;b&gt;My Team Member Salesforce Delegation:&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(0, 0, 0);&quot;&gt;User:&lt;/b&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; {!Screen0inputUserId.recordName}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Current Salesforce Delegated&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt; Detail:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(0, 0, 0);&quot;&gt;Start Date:&lt;/b&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; {!varInputUser.Delegate_Assignment_Start_Date__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(0, 0, 0);&quot;&gt;End Date: &lt;/b&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!varInputUser.Delegate_Assignment_End_Date__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(0, 0, 0);&quot;&gt;Delegate User: &lt;/b&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!varDelegateUser.Name}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Click &quot;Next&quot; to update.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Screen1_Remove_Update</name>
            <choiceReferences>Remove</choiceReferences>
            <choiceReferences>Update</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>Remove</defaultSelectedChoiceReference>
            <fieldText>Select the action below then &quot;Next&quot;</fieldText>
            <fieldType>RadioButtons</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Show_Current_Delegated_Assignment_0</name>
        <label>Show Current Delegated Assignment</label>
        <locationX>142</locationX>
        <locationY>283</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Screen0</targetReference>
        </connector>
        <fields>
            <name>DisplayCurrentDelegateAssignment_0</name>
            <fieldText>&lt;p&gt;&lt;b&gt;My Team Members Salesforce Delegation:&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(0, 0, 0);&quot;&gt;User:&lt;/b&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; {!Screen0inputUserId.recordName}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Update &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;Delegate Case Assignment Detail:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(0, 0, 0);&quot;&gt;Start Date:&lt;/b&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; {!varInputUser.Delegate_Assignment_Start_Date__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(0, 0, 0);&quot;&gt;End Date: &lt;/b&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!varInputUser.Delegate_Assignment_End_Date__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(0, 0, 0);&quot;&gt;Delegate User: &lt;/b&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!screen2inputUserId.recordName}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Click &quot;Next&quot; to update another user.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>Screen0</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>DelegateUser</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
    <variables>
        <name>varDelegateUser</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
    <variables>
        <name>varInputUser</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
</Flow>
