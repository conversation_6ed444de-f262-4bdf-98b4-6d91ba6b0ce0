<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <description>Display custom error message when Updating Parent Record fails</description>
        <name>Error_Message</name>
        <label>Error Message</label>
        <locationX>440</locationX>
        <locationY>539</locationY>
        <customErrorMessages>
            <errorMessage>There was a fault in the Rebase Review: Update Parent Rebase Comments &amp; Review Flow. 

Please try again. If the error persists, contact the Performance Team with the below Error Message.

Flow Fault Message: {!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <description>Used to assign any team comments and review values that have changed on the Rebase Review record to the equivalent parent Rebase record</description>
    <environments>Default</environments>
    <interviewLabel>Rebase Review: Update Parent Rebase Comments &amp; Review {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rebase Review: Update Parent Rebase Comments &amp; Review</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Updates the parent Rebase Review record with the values from the Rebase Review that changed</description>
        <name>Update_Parent_Rebase</name>
        <label>Update Parent Rebase</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <faultConnector>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Rebase_Record__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Integrity_Comment__c</field>
            <value>
                <elementReference>$Record.Customer_Integrity_Comment__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Integrity_Confidential_Info__c</field>
            <value>
                <elementReference>$Record.Customer_Integrity_Confidential_Info__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Integrity_Review__c</field>
            <value>
                <elementReference>$Record.Customer_Integrity_Review__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Relationship_Manager_Comment__c</field>
            <value>
                <elementReference>$Record.Relationship_Manager_Comment__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Relationship_Manager_Review__c</field>
            <value>
                <elementReference>$Record.Relationship_Manager_Comment__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Relationship_Manager_to_Reallocate_to__c</field>
            <value>
                <elementReference>$Record.Relationship_Manager_to_Reallocate_to__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Risk_Trade_Comment__c</field>
            <value>
                <elementReference>$Record.Risk_Trade_Comment__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Risk_Trade_Review__c</field>
            <value>
                <elementReference>$Record.Risk_Trade_Review__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Safer_Gambling_Comment__c</field>
            <value>
                <elementReference>$Record.Safer_Gambling_Comment__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Safer_Gambling_Confidential_Info__c</field>
            <value>
                <elementReference>$Record.Safer_Gambling_Confidential_Info__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Safer_Gambling_Review__c</field>
            <value>
                <elementReference>$Record.Safer_Gambling_Review__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Rebase_Record__r</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Parent_Rebase</targetReference>
        </connector>
        <filterFormula>OR(
    ISCHANGED({!$Record.Relationship_Manager_Comment__c}),
    ISCHANGED({!$Record.Relationship_Manager_Review__c}),
    ISCHANGED({!$Record.Relationship_Manager_to_Reallocate_to__c}),
    ISCHANGED({!$Record.Risk_Trade_Comment__c}),
    ISCHANGED({!$Record.Risk_Trade_Review__c}),
    ISCHANGED({!$Record.Safer_Gambling_Comment__c}),
    ISCHANGED({!$Record.Safer_Gambling_Confidential_Info__c}),
    ISCHANGED({!$Record.Safer_Gambling_Review__c}),
    ISCHANGED({!$Record.Customer_Integrity_Comment__c}),
    ISCHANGED({!$Record.Customer_Integrity_Confidential_Info__c}),
    ISCHANGED({!$Record.Customer_Integrity_Review__c})
)</filterFormula>
        <object>Rebase_Review__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <description>Subflow to handle Flow Faults, sends email to Performance Team members</description>
        <name>Rebase_Flow_Fault_Handler</name>
        <label>Rebase Flow Fault Handler</label>
        <locationX>440</locationX>
        <locationY>431</locationY>
        <connector>
            <targetReference>Error_Message</targetReference>
        </connector>
        <flowName>Rebase_Flow_Fault_Handler</flowName>
        <inputAssignments>
            <name>varFlowCurrentDateTime</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowFaultMessage</name>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowName</name>
            <value>
                <stringValue>Rebase Review: Update Parent Rebase Comments &amp; Review</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowUser</name>
            <value>
                <elementReference>$User.Username</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <triggerOrder>100</triggerOrder>
</Flow>
