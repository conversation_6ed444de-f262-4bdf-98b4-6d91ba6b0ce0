<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>User_Deactivated</name>
        <label>User Deactivated</label>
        <locationX>543</locationX>
        <locationY>182</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>User_is_Inactive</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.IsActive</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Deactivated_Date_to_Today</targetReference>
            </connector>
            <label>User is Inactive</label>
        </rules>
        <rules>
            <name>User_is_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.IsActive</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Deactivation_Date_to_Null</targetReference>
            </connector>
            <label>User is Active</label>
        </rules>
    </decisions>
    <interviewLabel>User {!$Flow.CurrentDateTime}</interviewLabel>
    <label>User : Update Deactivation Date</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Deactivated_Date_to_Today</name>
        <label>Update Deactivated Date to Today</label>
        <locationX>855</locationX>
        <locationY>157</locationY>
        <inputAssignments>
            <field>User_Deactivated_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Deactivation_Date_to_Null</name>
        <label>Update Deactivation Date to Null</label>
        <locationX>543</locationX>
        <locationY>490</locationY>
        <inputAssignments>
            <field>User_Deactivated_Date__c</field>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>User_Deactivated</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsActive</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>User</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
