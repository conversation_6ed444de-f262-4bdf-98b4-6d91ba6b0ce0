<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Sends a summary Attendee Last 2 Weeks to Prompt Template</description>
        <name>Attendee_Last_2_Weeks_to_Prompt_Template</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Attendee Last 2 Weeks to Prompt Template</label>
        <locationX>138</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Event Name: {!Loop_Attendee_Last_2_Weeks.Campaign__r.Name}
Event Date: {!Loop_Attendee_Last_2_Weeks.Status__c}
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Attendee_Last_2_Weeks</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sends message that there was a fault in the flow and it couldn&apos;t find the records</description>
        <name>Flow_Fault_Prompt_Instructions</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Flow Fault Prompt Instructions</label>
        <locationX>578</locationX>
        <locationY>242</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>There was a fault in the flow when retrieving records
</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sends message that no Attendee Last 2 Weeks were found to Prompt Template</description>
        <name>No_Attendee_Last_2_Weeks</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>No Attendee Last 2 Weeks</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Customers attended no events in the last 2 weeks
</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <decisions>
        <description>Are there any Attendee records of the customer for the last 2 Weeks</description>
        <name>Attendee_Records_Last_2_Weeks_Found</name>
        <label>Attendee Records Last 2 Weeks Found?</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>No_Attendee_Last_2_Weeks</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Attendance</defaultConnectorLabel>
        <rules>
            <name>Attended_Last_2_Weeks</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Attendee_Records_Last_2_Weeks</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Attendee_Records_Last_2_Weeks</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Attendee_Last_2_Weeks</targetReference>
            </connector>
            <label>Attended Last 2 Weeks</label>
        </rules>
    </decisions>
    <description>Used by Customer Summary Prompt Template to generate additional prompt instructions related to the customers Attendance in the last two weeks</description>
    <environments>Default</environments>
    <formulas>
        <description>Stores date 14 days ago</description>
        <name>formulaDateLast14Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 14</expression>
    </formulas>
    <formulas>
        <description>Stores date 365 days ago</description>
        <name>formulaDateLast365Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 365</expression>
    </formulas>
    <formulas>
        <description>Stores date 14 days in future</description>
        <name>formulaDateNext14Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() + 14</expression>
    </formulas>
    <formulas>
        <description>Stores value for today&apos;s date</description>
        <name>formulaDateToday</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <interviewLabel>Prompt Template: Instructions for Attendee Last 2 Weeks {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Prompt Template: Instructions for Attendee Last 2 Weeks</label>
    <loops>
        <description>Loops through Attendee records for last 2 weeks</description>
        <name>Loop_Attendee_Last_2_Weeks</name>
        <label>Loop Attendee Last 2 Weeks</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_Attendee_Records_Last_2_Weeks</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Attendee_Last_2_Weeks_to_Prompt_Template</targetReference>
        </nextValueConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>PromptFlow</processType>
    <recordLookups>
        <description>Gets the Attendee records of the customer for the last 2 Weeks with specific statuses</description>
        <name>Get_Attendee_Records_Last_2_Weeks</name>
        <label>Get Attendee Records Last 2 Weeks</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Attendee_Records_Last_2_Weeks_Found</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Flow_Fault_Prompt_Instructions</targetReference>
        </faultConnector>
        <filterLogic>1 AND 2 AND (3 OR 4) AND 5</filterLogic>
        <filters>
            <field>Campaign_Date__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>formulaDateLast14Days</elementReference>
            </value>
        </filters>
        <filters>
            <field>Campaign_Date__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>formulaDateToday</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attended</stringValue>
            </value>
        </filters>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Input.objectToSummarize.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <capabilityTypes>
            <name>PromptTemplateType://einstein_gpt__recordSummary</name>
            <capabilityName>PromptTemplateType://einstein_gpt__recordSummary</capabilityName>
            <inputs>
                <name>objectToSummarize</name>
                <capabilityInputName>objectToSummarize</capabilityInputName>
                <dataType>SOBJECT://Account</dataType>
                <isCollection>false</isCollection>
            </inputs>
        </capabilityTypes>
        <connector>
            <targetReference>Get_Attendee_Records_Last_2_Weeks</targetReference>
        </connector>
        <triggerType>Capability</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>currentItem_Filter_Attendee_Last_2_Weeks</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
</Flow>
