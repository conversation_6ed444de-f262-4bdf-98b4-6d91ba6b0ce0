<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns &apos;Error&apos; to the Relationship Manager To Reallocate To variable for when the Get Records element executes but doesn&apos;t return records</description>
        <name>Assign_Error_for_Relationship_Manager</name>
        <label>Assign Error for Relationship Manager</label>
        <locationX>842</locationX>
        <locationY>600</locationY>
        <assignmentItems>
            <assignToReference>varRelationshipManagerToReallocateTo</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Error</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Customer_Outcome_Group_RM_PM</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the value for New Customer Group based on formula output</description>
        <name>Assign_New_Customer_Group</name>
        <label>Assign New Customer Group</label>
        <locationX>1040</locationX>
        <locationY>984</locationY>
        <assignmentItems>
            <assignToReference>$Record.Customer_Outcome_Group__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Assign_Customer_Outcome_Group_RM_PM.varNewCustomerGroupOutcome</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Rebase_Record_Customer_Group_Outcome</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Relationship Manager To Reallocate To to variable to use in formula</description>
        <name>Assign_Relationship_Manager</name>
        <label>Assign Relationship Manager</label>
        <locationX>578</locationX>
        <locationY>600</locationY>
        <assignmentItems>
            <assignToReference>varRelationshipManagerToReallocateTo</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Relationship_Manager.Name</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Customer_Outcome_Group_RM_PM</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the Review By field to the user that triggered the Flow</description>
        <name>Assign_Reviewed_By_User</name>
        <label>Assign Reviewed By User</label>
        <locationX>50</locationX>
        <locationY>276</locationY>
        <assignmentItems>
            <assignToReference>$Record.Reviewed_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Rebase_Record_Reviewed_By</targetReference>
        </connector>
    </assignments>
    <customErrors>
        <description>Display custom error message when there is a Flow fault</description>
        <name>Error_Message</name>
        <label>Error Message</label>
        <locationX>1546</locationX>
        <locationY>1308</locationY>
        <customErrorMessages>
            <errorMessage>There was a fault in the Rebase Review: Assign Customer Outcome Group - SG, CI, R&amp;T


Flow Fault Message: {!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <description>Check if the Relationship Manager To Reallocate To field is populated</description>
        <name>Is_RM_To_Reallocate_To_Populated</name>
        <label>Is RM To Reallocate To Populated</label>
        <locationX>1040</locationX>
        <locationY>276</locationY>
        <defaultConnector>
            <targetReference>Assign_Customer_Outcome_Group_RM_PM</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Blank</defaultConnectorLabel>
        <rules>
            <name>Relationship_Manager</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Relationship_Manager_to_Reallocate_to__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Relationship_Manager</targetReference>
            </connector>
            <label>Relationship Manager</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was the User record for the Relationship Manager found?</description>
        <name>Null_Check_RM_Found</name>
        <label>Null Check RM Found</label>
        <locationX>710</locationX>
        <locationY>492</locationY>
        <defaultConnector>
            <targetReference>Assign_Error_for_Relationship_Manager</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Record</defaultConnectorLabel>
        <rules>
            <name>User_Record</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Relationship_Manager</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Relationship_Manager</targetReference>
            </connector>
            <label>User Record</label>
        </rules>
    </decisions>
    <description>Assign value Reviewed By and the final Customer Outcome Group when Status is Completed by Performance Team</description>
    <environments>Default</environments>
    <interviewLabel>Rebase: Assign Reviewed By &amp; Customer Outcome Group on Completion {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rebase: Assign Reviewed By &amp; Customer Outcome Group on Completion</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the user record for the Relationship Manager To Reallocate To as it needs the derived field Full Name to use in formula variable</description>
        <name>Get_Relationship_Manager</name>
        <label>Get Relationship Manager</label>
        <locationX>710</locationX>
        <locationY>384</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_RM_Found</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Relationship_Manager_to_Reallocate_to__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Name</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates the parent Rebase record Customer Group Outcome field</description>
        <name>Update_Rebase_Record_Customer_Group_Outcome</name>
        <label>Update Rebase Record Customer Group Outcome</label>
        <locationX>1040</locationX>
        <locationY>1092</locationY>
        <faultConnector>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the parent Rebase record Reviewed By field</description>
        <name>Update_Rebase_Record_Reviewed_By</name>
        <label>Update Rebase Record Reviewed By</label>
        <locationX>50</locationX>
        <locationY>384</locationY>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>419</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assign_Reviewed_By_User</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Rebase_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </filters>
        <object>Rebase__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Is_RM_To_Reallocate_To_Populated</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <description>Subflow that stores the formula for the rules and if else logic to assign value to the Customer Outcome Group following reviews from Relationship Manager &amp; Performance Team</description>
        <name>Assign_Customer_Outcome_Group_RM_PM</name>
        <label>Assign Customer Outcome Group - RM &amp; PM</label>
        <locationX>1040</locationX>
        <locationY>876</locationY>
        <connector>
            <targetReference>Assign_New_Customer_Group</targetReference>
        </connector>
        <flowName>Rebase_Subflow_Assign_Customer_Outcome_Group_RM_PM</flowName>
        <inputAssignments>
            <name>varCustomerGroupOutcome</name>
            <value>
                <elementReference>$Record.Customer_Outcome_Group__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varCustomerIntegrityReview</name>
            <value>
                <elementReference>$Record.Customer_Integrity_Review__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varPerformanceTeamCustomerGroupOutcome</name>
            <value>
                <elementReference>$Record.Performance_Team_Customer_Group_Outcome__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varPerformanceTeamReview</name>
            <value>
                <elementReference>$Record.Performance_Team_Review__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varRBNewCustomerGroup</name>
            <value>
                <elementReference>$Record.RB_Customer_Group__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varRelationshipManagerReview</name>
            <value>
                <elementReference>$Record.Relationship_Manager_Review__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varRelationshipManagertoReallocateto</name>
            <value>
                <elementReference>varRelationshipManagerToReallocateTo</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varSaferGamblingReview</name>
            <value>
                <elementReference>$Record.Safer_Gambling_Review__c</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <subflows>
        <description>Subflow to handle Flow Faults, sends email to Performance Team members</description>
        <name>Rebase_Flow_Fault_Handler</name>
        <label>Rebase Flow Fault Handler</label>
        <locationX>1546</locationX>
        <locationY>1200</locationY>
        <connector>
            <targetReference>Error_Message</targetReference>
        </connector>
        <flowName>Rebase_Flow_Fault_Handler</flowName>
        <inputAssignments>
            <name>varFlowCurrentDateTime</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowFaultMessage</name>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowName</name>
            <value>
                <stringValue>Rebase Review: Assign Customer Outcome Group - SG, CI, R&amp;T</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowUser</name>
            <value>
                <elementReference>$User.Username</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <description>Stores the value for the Relationship Manager To Reallocate To that is used in the formula variable</description>
        <name>varRelationshipManagerToReallocateTo</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
