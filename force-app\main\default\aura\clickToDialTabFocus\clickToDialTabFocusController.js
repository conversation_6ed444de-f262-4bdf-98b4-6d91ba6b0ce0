({
    onTabCreated : function(cmp, evt, hlp) {
        let newTabId = evt.getParam('tabId');
        let workspaceAPI = cmp.find('workspace');

        console.log('onTabCreated() tabId', newTabId);

        if (newTabId && workspaceAPI) {
            console.log('calling focusTab...');
            workspaceAPI.focusTab({tabId: newTabId})
                .then((response) => {
                    console.log('focusTab response', response);
                })
                .catch((error) => {
                    console.error('focusTab error', error);
                });
        }
    }
})