<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Post to chatter in the parent case on which the QA is being done. This chatter post will also send an email notification to the analyst since the ChatterPost texttemplate @ mentions the Rework Recipient.</description>
        <name>Post_to_chatter</name>
        <label>Post to chatter</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <actionName>chatterPost</actionName>
        <actionType>chatterPost</actionType>
        <connector>
            <targetReference>Send_Email_to_Rework_Recipient</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>text</name>
            <value>
                <elementReference>texttemplateChatterPost</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>subjectNameOrId</name>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>type</name>
            <value>
                <stringValue>user</stringValue>
            </value>
        </inputParameters>
        <nameSegment>chatterPost</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Send email to Rework Recipient</description>
        <name>Send_Email_to_Rework_Recipient</name>
        <label>Send Email to Rework Recipient</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>create_Task</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>$Record.Rework_Recipient__r.Email</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>CurrentUser</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>texttemplateEmailSubject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>texttemplateEmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>This flow triggers when:
- The Case Record Type is set to “QA/QC”
- The “Rework_Required__c” field is set to “Yes”

Actions:
- Posts a Chatter message on the parent case (the case under QA), informing the Responsible Analyst to complete their QA rework.
- Creates a Task on the QA/QC case to remind the QA Analyst to follow up on the QA rework.</description>
    <environments>Default</environments>
    <formulas>
        <name>ActivityDateFormula</name>
        <dataType>Date</dataType>
        <expression>IF(TEXT({!$Record.QA_Outcome__c})= &quot;Unsatisfactory-Major&quot;, {!$Flow.CurrentDate} + 1, {!$Flow.CurrentDate} + 5)</expression>
    </formulas>
    <formulas>
        <name>ActvityDate</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate} + 7</expression>
    </formulas>
    <formulas>
        <name>AlertType</name>
        <dataType>String</dataType>
        <expression>IF({!$Record.Parent.RecordType.DeveloperName} = &quot;Transaction_Monitoring&quot;, &quot;TM_Alert_CC&quot;,&quot;Other&quot;)</expression>
    </formulas>
    <formulas>
        <name>Days</name>
        <dataType>String</dataType>
        <expression>IF(TEXT({!$Record.QA_Outcome__c})= &quot;Unsatisfactory-Major&quot;, &quot;1 business day&quot;, &quot;5 business days&quot;)</expression>
    </formulas>
    <formulas>
        <description>Gets the current QA/QC Case.</description>
        <name>formulaCaseURL</name>
        <dataType>String</dataType>
        <expression>LEFT({!$Api.Partner_Server_URL_550}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_550})) &amp; {!$Record.Id}</expression>
    </formulas>
    <formulas>
        <description>Gets the parent case URL.</description>
        <name>formulaParentCaseURL</name>
        <dataType>String</dataType>
        <expression>LEFT({!$Api.Partner_Server_URL_260}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_260})) &amp; {!$Record.ParentId}</expression>
    </formulas>
    <interviewLabel>AML Case - QA reworked required {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Case - QA reworked required</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>Create Task on the QA/QC case reminding the QA analyst to follow up with the responsible analyst.</description>
        <name>create_Task</name>
        <label>create Task</label>
        <locationX>176</locationX>
        <locationY>539</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>ActivityDateFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Follow up on Rework Required for QA Case</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Post_to_chatter</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterFormula>AND(
{!$Record.RecordType.DeveloperName} = &quot;QA_QC&quot;,
TEXT({!$Record.Rework_Required__c}) = &quot;Yes&quot;,
NOT(ISBLANK({!$Record.Rework_Recipient__c})) 
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>Chatter Post to be posted on the Parent Case.</description>
        <name>texttemplateChatterPost</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>@[{!$Record.Rework_Recipient__c}]
A rework has been assigned to your case, please complete this within {!Days} and advise the QA/QC Analyst that this has been completed by replying to this chatter post.

Please review the below for required actions.
Case Number: {!$Record.Parent.CaseNumber}
QA outcome: {!$Record.QA_Outcome__c}
QA comment: {!$Record.QAQC_Comments__c}

Case Link: 
{!formulaParentCaseURL}</text>
    </textTemplates>
    <textTemplates>
        <name>texttemplateEmailBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;p&gt;Hi {!$Record.Rework_Recipient__r.FirstName} {!$Record.Rework_Recipient__r.LastName},&lt;/p&gt;
&lt;p&gt;A rework has been assigned to your case. Please complete the required actions within &lt;strong&gt;{!Days} &lt;/strong&gt; and notify the QA/QC Analyst by replying to the Chatter post in the case.&lt;/p&gt;

&lt;p&gt;&lt;strong&gt;Case Details:&lt;/strong&gt;&lt;/p&gt;
&lt;ul&gt;
  &lt;li&gt;&lt;strong&gt;Case Number&lt;/strong&gt;: {!$Record.Parent.CaseNumber}&lt;/li&gt;
  &lt;li&gt;&lt;strong&gt;QA Outcome&lt;/strong&gt;: {!$Record.QA_Outcome__c}&lt;/li&gt;
  &lt;li&gt;&lt;strong&gt;QA Comments&lt;/strong&gt;: {!$Record.QAQC_Comments__c}&lt;/li&gt;
&lt;/ul&gt;

&lt;p&gt;Click &lt;a href=&quot;{!formulaParentCaseURL}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;&lt;strong&gt;HERE&lt;/strong&gt;&lt;/a&gt; to view the Case: {!$Record.Parent.CaseNumber}.&lt;/p&gt;

&lt;p&gt;Best regards,&lt;br&gt;
{!$Record.Owner:User.FirstName} {!$Record.Owner:User.LastName}&lt;br&gt;
QA Analyst&lt;br&gt;
Sportsbet&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>texttemplateEmailSubject</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Action Required: Rework Assigned to Your Case: {!$Record__Prior.Parent.CaseNumber}.</text>
    </textTemplates>
</Flow>
