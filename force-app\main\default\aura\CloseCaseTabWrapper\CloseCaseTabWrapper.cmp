<!--
  @description       : 
  <AUTHOR> <PERSON>
  @team              : 
  @last modified on  : 11-04-2025
  @last modified by  : <PERSON>
-->
<aura:component implements="flexipage:availableForAllPageTypes,force:hasRecordId" access="global">
    <aura:attribute name="recordId" type="Id" />
    <lightning:workspaceAPI aura:id="workspace" />

    <c:closeCaseTabButton recordId="{!v.recordId}" onclosecasetab="{!c.handleCloseTab}" />
</aura:component>