<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <description>Display Error Message when there is a fault in the Flow</description>
        <name>Error_Message</name>
        <label>Error Message</label>
        <locationX>440</locationX>
        <locationY>600</locationY>
        <customErrorMessages>
            <errorMessage>There was an error in the Messaging Session: Outbound SMS Update Task Flow, please try again or contact your Salesforce Administrator 
Flow Error: {!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <description>Did the Get Records return any records</description>
        <name>Task_Records</name>
        <label>Task Records</label>
        <locationX>440</locationX>
        <locationY>384</locationY>
        <defaultConnectorLabel>No Records</defaultConnectorLabel>
        <rules>
            <name>Records</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Matching_Task</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Matching_Task</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Task</targetReference>
            </connector>
            <label>Records</label>
        </rules>
    </decisions>
    <description>Used to update the Task record with the related Messaging Session once the Message Identifier has been added to the Messaging Session when it is asynchronously created in the Outbound SMS Flow</description>
    <environments>Default</environments>
    <interviewLabel>Messaging Session: Outbound SMS Update Task {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Messaging Session: Outbound SMS Update Task</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the Task record related to the Messaging Session with a matching Message Identifier</description>
        <name>Get_Matching_Task</name>
        <label>Get Matching Task</label>
        <locationX>440</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Task_Records</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Error_Message</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Message_Identifier__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Message_Identifier__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates Task with related Messaging Session</description>
        <name>Update_Task</name>
        <label>Update Task</label>
        <locationX>176</locationX>
        <locationY>492</locationY>
        <faultConnector>
            <targetReference>Error_Message</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Matching_Task.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Messaging_Session__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Origin</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>TriggeredOutbound</stringValue>
            </value>
        </filters>
        <filters>
            <field>Message_Identifier__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>MessagingSession</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Get_Matching_Task</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
