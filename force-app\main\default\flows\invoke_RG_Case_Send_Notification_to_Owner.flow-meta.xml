<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Custom_Notification</name>
        <label>Send Custom Notification</label>
        <locationX>1759</locationX>
        <locationY>146</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>GetRGCaseNotification.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>NotificationBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>formulaNotificationTitle</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>UserIds</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>inputCase.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Send_RG_Alert_Email_Notificaiton</name>
        <label>Send RG Alert Email Notificaiton</label>
        <locationX>1076</locationX>
        <locationY>44</locationY>
        <actionName>Case.RG_Alert_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>AssignUserIds</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>inputCase.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.RG_Alert_Case_Email_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Send_RG_Alert_Email_Notificaiton_0</name>
        <label>Send RG Alert Email Notificaiton</label>
        <locationX>1021</locationX>
        <locationY>239</locationY>
        <actionName>Case.RG_Alert_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>CheckOwnerManager</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>inputCase.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.RG_Alert_Case_Email_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Send_RG_Alert_Email_Notification_for_Manager</name>
        <label>Send RG Alert Email Notification for Manager</label>
        <locationX>1316</locationX>
        <locationY>408</locationY>
        <actionName>Case.RG_Alert_Case_Email_Notification_for_Manager</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>AssignUserIds_0</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>inputCase.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.RG_Alert_Case_Email_Notification_for_Manager</nameSegment>
    </actionCalls>
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>AssignManagerEmail</name>
        <label>AssignManagerEmail</label>
        <locationX>677</locationX>
        <locationY>235</locationY>
        <assignmentItems>
            <assignToReference>inputCase.RM_Manager_Email__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaInputUserManagerEmail</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>UpdateCase_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignUserIds</name>
        <label>AssignUserIds</label>
        <locationX>1389</locationX>
        <locationY>44</locationY>
        <assignmentItems>
            <assignToReference>UserIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>inputUserId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetRGCaseNotification</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignUserIds_0</name>
        <label>AssignUserIds</label>
        <locationX>1497</locationX>
        <locationY>292</locationY>
        <assignmentItems>
            <assignToReference>UserIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>GetInputOwner.ManagerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>AssignUserIds</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>ChangeCaseOwner</name>
        <label>ChangeCaseOwner</label>
        <locationX>390</locationX>
        <locationY>50</locationY>
        <assignmentItems>
            <assignToReference>inputCase.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputUserId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>If_Case_Critical</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>CheckOwnerManager</name>
        <label>CheckOwnerManager</label>
        <locationX>1194</locationX>
        <locationY>245</locationY>
        <defaultConnector>
            <targetReference>AssignUserIds</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Null</defaultConnectorLabel>
        <rules>
            <name>Is_Not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetInputOwner.ManagerId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_RG_Alert_Email_Notification_for_Manager</targetReference>
            </connector>
            <label>Is Not Null</label>
        </rules>
    </decisions>
    <decisions>
        <name>If_Case_Critical</name>
        <label>If Case Critical</label>
        <locationX>532</locationX>
        <locationY>52</locationY>
        <defaultConnector>
            <targetReference>UpdateCase_0_0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Other Priority</defaultConnectorLabel>
        <rules>
            <name>Is_Critical</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>inputCase.Priority</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>inputCase.Case_Name__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer Deposit over Avg Threshold</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetInputOwner</targetReference>
            </connector>
            <label>Is Critical</label>
        </rules>
        <rules>
            <name>Is_High</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>inputCase.Priority</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateCase</targetReference>
            </connector>
            <label>Is High</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>formulaInputUserManagerEmail</name>
        <dataType>String</dataType>
        <expression>If(!ISBLANK({!GetInputOwner.ManagerId}),{!GetInputOwner.Manager.Email},&apos;&apos;)</expression>
    </formulas>
    <formulas>
        <name>formulaNotificationTitle</name>
        <dataType>String</dataType>
        <expression>&quot;A New &quot; + Text({!inputCase.Priority}) + &quot; Responsible Gambling Alert for Customer - &quot; + {!inputCase.Customer_Name__c}</expression>
    </formulas>
    <interviewLabel>invoke RG Case Send Notification to Owner {!$Flow.CurrentDateTime}</interviewLabel>
    <label>invoke RG Case Send Notification to Owner</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetInputOwner</name>
        <label>GetInputOwner</label>
        <locationX>536</locationX>
        <locationY>235</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>AssignManagerEmail</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputUserId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>ManagerId</queriedFields>
        <queriedFields>Delegate_Assignment_End_Date__c</queriedFields>
        <queriedFields>Delegate_Assignment_Start_Date__c</queriedFields>
        <queriedFields>Delegate_Assignment_User_ID__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetRGCaseNotification</name>
        <label>GetRGCaseNotification</label>
        <locationX>1610</locationX>
        <locationY>147</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Send_Custom_Notification</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>RG_Case_Notification</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>UpdateCase</name>
        <label>UpdateCase</label>
        <locationX>836</locationX>
        <locationY>44</locationY>
        <connector>
            <targetReference>Send_RG_Alert_Email_Notificaiton</targetReference>
        </connector>
        <inputReference>inputCase</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateCase_0</name>
        <label>UpdateCase</label>
        <locationX>845</locationX>
        <locationY>262</locationY>
        <connector>
            <targetReference>Send_RG_Alert_Email_Notificaiton_0</targetReference>
        </connector>
        <inputReference>inputCase</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateCase_0_0</name>
        <label>UpdateCase</label>
        <locationX>837</locationX>
        <locationY>151</locationY>
        <inputReference>inputCase</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>ChangeCaseOwner</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>NotificationBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>Customer Name: {!inputCase.Customer_Name__c}
Case Number: {!inputCase.CaseNumber}
Subject: {!inputCase.Subject}
Alert Date/Time: {!inputCase.Alert_Date_Time__c}
Alert Level: {!inputCase.Priority}
Case Type: {!inputCase.Type}</text>
    </textTemplates>
    <variables>
        <name>inputCase</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>inputUserId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>UserIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
