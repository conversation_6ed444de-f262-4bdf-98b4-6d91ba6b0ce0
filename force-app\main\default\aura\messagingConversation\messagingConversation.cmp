<aura:component implements="flexipage:availableForRecordHome,force:hasRecordId,force:hasSObjectName">
    <!-- force:hasRecordId      => component.get("v.recordId") -->
    <!-- force:hasSObjectName   => component.get("v.sObjectName") -->

    <aura:attribute name="tabId" type="String" />
    <aura:attribute name="parentTabId" type="String" />
    <aura:attribute name="timerRef" type="String" />
    <aura:attribute name="tabInfoDelay" type="Integer" description="Delay in milliseconds for GetTabInfo call" required="true" default="1500"/>
    <aura:attribute name="showSpinner" type="Boolean" default="false" />
    <lightning:conversationToolkitAPI aura:id="newconversationAPI" />
    <lightning:omniToolkitAPI aura:id="omniAPI" />
    <lightning:workspaceAPI aura:id="workspaceAPI" />	
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <aura:handler event="lightning:conversationAgentSend" action="{!c.onAgentSend}" />
    <aura:handler event="lightning:conversationNewMessage" action="{!c.onNewMessage}" />
    <aura:handler event="lightning:conversationCustomEvent" action="{!c.onCustomEvent}" />
    <aura:handler event="lightning:conversationChatEnded" action="{!c.onChatEnded}" />
    <aura:handler event="lightning:tabFocused" action="{!c.onTabFocused}"/>
    <aura:handler event="lightning:tabClosed" action="{!c.onTabClosed}" />

    <aura:html tag="style">
        scrt-conversation-body {
            display: none;
            visibility: hidden;
        }
    </aura:html>

    <aura:if isTrue="{!v.showSpinner}">
        <lightning:spinner size="medium" variant="brand" title="Loading Conversation History..." alternativeText="Loading Conversation History..." />
    </aura:if>

    <aura:if isTrue="{!v.sObjectName == 'MessagingSession'}">
    <c:conversationMessageLWC
        aura:id="lwc"
        omg="{!v.recordId}"
        onfirechatlog="{!c.handleChatLogFired}"
        onfiremsgsend="{!c.handleMsgSendFired}"
        onfireendchat="{!c.handleEndChatFired}"
        onfireloadfinished="{!c.handleLoadFinished}"
    ></c:conversationMessageLWC>
</aura:if> 
</aura:component>