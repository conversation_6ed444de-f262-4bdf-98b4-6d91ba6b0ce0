<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assign Customer to current loop item</description>
        <name>Assign_Customer_to_Messaging_User</name>
        <label>Assign Customer to Messaging User</label>
        <locationX>270</locationX>
        <locationY>1382</locationY>
        <assignmentItems>
            <assignToReference>For_each_selected_Row.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Customer_Record.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>For_each_selected_Row.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Customer_Record.PersonContact.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>messagingUsersToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>For_each_selected_Row</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>For_each_selected_Row</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Set variable to show existing records</description>
        <name>Show_existing_records_in_next_screen</name>
        <label>Show existing records in next screen</label>
        <locationX>314</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>showExistingRecordsTable</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>PhoneNumberFoundScreen</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Determine if Messaging User records were found that match customer record</description>
        <name>AlreadyHadMessagingUser</name>
        <label>Does Customer already have Messaging User records?</label>
        <locationX>446</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Get_Existing_Messaging_User_Records_for_Phone_Number</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Existing_Messaging_User_Records</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Customer_Already_Has_Messaging_User_Records</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if user entered a new mobile number to create a new Messaging End User with</description>
        <name>Has_a_new_mobile_number_been_provided</name>
        <label>Has a new mobile number been provided?</label>
        <locationX>446</locationX>
        <locationY>1814</locationY>
        <defaultConnector>
            <targetReference>Success_in_Messaging_User_Update</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>New_Mobile_Number</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Messaging</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if any rows were selected for reassignment to the current customer.</description>
        <name>Were_any_Rows_Selected</name>
        <label>Were any Rows Selected?</label>
        <locationX>446</locationX>
        <locationY>1166</locationY>
        <defaultConnector>
            <targetReference>Has_a_new_mobile_number_been_provided</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>unassignedMessagingUserRecords.selectedRows</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Existing_Messaging_User_Records_for_Phone_Number</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>For_each_selected_Row</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if records were found that match this phone number</description>
        <name>Were_records_found_that_have_this_phone_number</name>
        <label>Were records found that have this phone number?</label>
        <locationX>446</locationX>
        <locationY>758</locationY>
        <defaultConnector>
            <targetReference>PhoneNumberFoundScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Existing_Messaging_User_Records_for_Phone_Number</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Show_existing_records_in_next_screen</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>Screen flow to create new messaging end user, or to re-assign existing end users with the same phone number as the customer.</description>
    <environments>Default</environments>
    <interviewLabel>Screen Flow - Create New Messaging User from Customer page {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Screen Flow - Create New Messaging User from Customer page</label>
    <loops>
        <description>Loop through each selected row</description>
        <name>For_each_selected_Row</name>
        <label>For each selected Row</label>
        <locationX>182</locationX>
        <locationY>1274</locationY>
        <collectionReference>unassignedMessagingUserRecords.selectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Customer_to_Messaging_User</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Existing_Messaging_User_Records</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Create a Messaging User record for this customer with the input mobile number from the previous screen</description>
        <name>Create_Messaging_User_Record_with_Input_Mobile_Number</name>
        <label>Create Messaging User Record with Input Mobile Number</label>
        <locationX>50</locationX>
        <locationY>2030</locationY>
        <connector>
            <targetReference>Success_in_Messaging_User_Update</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>Get_Customer_Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>Get_Customer_Record.PersonContact.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessageType</field>
            <value>
                <stringValue>Text</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessagingChannelId</field>
            <value>
                <elementReference>Get_Messaging.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessagingConsentStatus</field>
            <value>
                <stringValue>ImplicitlyOptedIn</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessagingPlatformKey</field>
            <value>
                <elementReference>New_Mobile_Number</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>New_Mobile_Number</elementReference>
            </value>
        </inputAssignments>
        <object>MessagingEndUser</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <description>Get Customer Record related to the recordID variable of the flow</description>
        <name>Get_Customer_Record</name>
        <label>Get Customer Record</label>
        <locationX>446</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Existing_Messaging_User_Records</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get all Messaging Users associated with the customer record input by the flow.</description>
        <name>Get_Existing_Messaging_User_Records</name>
        <label>Get Existing Messaging User Records for Customer</label>
        <locationX>446</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>AlreadyHadMessagingUser</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer_Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>MessagingEndUser</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Messaging User records associated with the same phone number as the customer but not the same Customer record</description>
        <name>Get_Existing_Messaging_User_Records_for_Phone_Number</name>
        <label>Get Existing Messaging User Records for Phone Number</label>
        <locationX>446</locationX>
        <locationY>650</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Were_records_found_that_have_this_phone_number</targetReference>
        </connector>
        <faultConnector>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MessagingPlatformKey</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer_Record.PersonMobilePhone</elementReference>
            </value>
        </filters>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer_Record.PersonMobilePhone</elementReference>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>Get_Customer_Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>MessagingEndUser</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Messaging Channel for this instance</description>
        <name>Get_Messaging</name>
        <label>Get Messaging Channel</label>
        <locationX>50</locationX>
        <locationY>1922</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Messaging_User_Record_with_Input_Mobile_Number</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MessageType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Text</stringValue>
            </value>
        </filters>
        <filters>
            <field>MessagingPlatformKey</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>+614</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingChannel</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update records post customer reassignment</description>
        <name>Update_Existing_Messaging_User_Records</name>
        <label>Update Existing Messaging User Records</label>
        <locationX>182</locationX>
        <locationY>1574</locationY>
        <connector>
            <targetReference>Has_a_new_mobile_number_been_provided</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <inputReference>messagingUsersToUpdate</inputReference>
    </recordUpdates>
    <screens>
        <description>Screen that shows the existing Messaging User records for this customer</description>
        <name>Customer_Already_Has_Messaging_User_Records</name>
        <label>Customer Already Has Messaging User Records</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Get_Existing_Messaging_User_Records_for_Phone_Number</targetReference>
        </connector>
        <fields>
            <name>messagingUserFoundText</name>
            <fieldText>&lt;p&gt;This customer record already has messaging user record(s):&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>existingMessagingUserRecords</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>MessagingEndUser</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Existing_Messaging_User_Records</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-b4cd&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Phone Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Messaging User Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>promptToContinue</name>
            <fieldText>&lt;p&gt;If the messaging end user you are searching for is not above, click Next to search for End Users that exist for this phone number but not for this Customer, or create a new Messaging End User from scratch. Otherwise, close this window.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Next</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Display flow error message for end user</description>
        <name>errorScreen</name>
        <label>errorScreen</label>
        <locationX>1018</locationX>
        <locationY>758</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>errorMessage</name>
            <fieldText>&lt;p&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen that shows the existing Messaging User records for this phone number</description>
        <name>PhoneNumberFoundScreen</name>
        <label>Phone Number found on Other Messaging User Records</label>
        <locationX>446</locationX>
        <locationY>1058</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Were_any_Rows_Selected</targetReference>
        </connector>
        <fields>
            <name>PhoneNumberFoundScreen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>PhoneNumberFoundScreen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>messagingUserFoundText2</name>
                    <fieldText>&lt;p&gt;This phone number has been found on the following Messaging Users:&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>unassignedMessagingUserRecords</name>
                    <dataTypeMappings>
                        <typeName>T</typeName>
                        <typeValue>MessagingEndUser</typeValue>
                    </dataTypeMappings>
                    <extensionName>flowruntime:datatable</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Data Table</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>selectionMode</name>
                        <value>
                            <stringValue>MULTI_SELECT</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>minRowSelection</name>
                        <value>
                            <numberValue>1.0</numberValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>tableData</name>
                        <value>
                            <elementReference>Get_Existing_Messaging_User_Records_for_Phone_Number</elementReference>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>columns</name>
                        <value>
                            <stringValue>[{&quot;apiName&quot;:&quot;AccountId&quot;,&quot;guid&quot;:&quot;column-2cb9&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Customer&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Customer&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-b4cd&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Phone Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Messaging User Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>Copy_1_of_promptToContinue</name>
                    <fieldText>&lt;p&gt;If any of these phone numbers belong to this customer, select the messaging users you want to reassign to this customer and click next. You can also add a new phone number in the field below to create a new one:&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>showExistingRecordsTable</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>noRecordsFoundText</name>
            <fieldText>&lt;p&gt;No records were found that match this customer&apos;s phone number on their account record. Enter the phone number you want to create a messaging user for below:&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>showExistingRecordsTable</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>New_Mobile_Number</name>
            <dataType>String</dataType>
            <fieldText>New Mobile Number</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;The mobile number needs to be in the format of +61000000000&lt;/p&gt;</errorMessage>
                <formulaExpression>AND(
    BEGINS({!New_Mobile_Number}, &quot;+61&quot;),
    LEN({!New_Mobile_Number}) = 12
)</formulaExpression>
            </validationRule>
        </fields>
        <nextOrFinishButtonLabel>Next</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Show success in Messaging User Update</description>
        <name>Success_in_Messaging_User_Update</name>
        <label>Success in Messaging User Update</label>
        <locationX>446</locationX>
        <locationY>2270</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Success_in_Messaging_User_Update_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Success_in_Messaging_User_Update_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>successfulUpdateText</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Success&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;You have updated the following Messaging User Records. They are now associated with this Customer:&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>updatedRecords</name>
                    <dataTypeMappings>
                        <typeName>T</typeName>
                        <typeValue>MessagingEndUser</typeValue>
                    </dataTypeMappings>
                    <extensionName>flowruntime:datatable</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Data Table</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>selectionMode</name>
                        <value>
                            <stringValue>NO_SELECTION</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>minRowSelection</name>
                        <value>
                            <numberValue>0.0</numberValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>tableData</name>
                        <value>
                            <elementReference>messagingUsersToUpdate</elementReference>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>maxRowSelection</name>
                        <value>
                            <numberValue>0.0</numberValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>columns</name>
                        <value>
                            <stringValue>[{&quot;apiName&quot;:&quot;AccountId&quot;,&quot;guid&quot;:&quot;column-dbcf&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Customer&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Customer&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;ContactId&quot;,&quot;guid&quot;:&quot;column-17b9&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Contact&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Contact ID&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-8831&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Mobile Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Messaging User Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>updatedRecords.firstSelectedRow.Id</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Success_in_Messaging_User_Update_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Success_in_Messaging_User_Update_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>successNewMessagingUser</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Success&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;You have created a new messaging user. This will now be available on the Customer record for selection when starting an outbound conversation.&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>New_Mobile_Number</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Customer_Record</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>Messaging users to update after reassignment to current customer</description>
        <name>messagingUsersToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>MessagingEndUser</objectType>
    </variables>
    <variables>
        <description>Variable to store values of fields for creating Messaging End User created at end of flow</description>
        <name>messagingUserToCreate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>MessagingEndUser</objectType>
    </variables>
    <variables>
        <description>recordId available for input on screen flow launch</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to determine visibility of existing records</description>
        <name>showExistingRecordsTable</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
</Flow>
