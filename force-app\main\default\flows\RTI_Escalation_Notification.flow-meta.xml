<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_RTI_Escalation_Notification</name>
        <label>Send RTI Escalation Notification</label>
        <locationX>674</locationX>
        <locationY>296</locationY>
        <actionName>Case.RTI_Alert_Case_Escalation_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.RTI_Alert_Case_Escalation_Email_Notification</nameSegment>
    </actionCalls>
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Notification Email after 1 hour</description>
    <environments>Default</environments>
    <interviewLabel>RTI Escalation Notification {!$Flow.CurrentDateTime}</interviewLabel>
    <label>RTI Escalation Notification</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Escalated_Checkbox</name>
        <label>Escalated Checkbox</label>
        <locationX>515</locationX>
        <locationY>296</locationY>
        <connector>
            <targetReference>Send_RTI_Escalation_Notification</targetReference>
        </connector>
        <inputAssignments>
            <field>IsEscalated</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>110</locationX>
        <locationY>48</locationY>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterFormula>!ISBLANK({!$Record.RecordTypeId}) 
&amp;&amp;
{!$Record.RecordType.DeveloperName} == &apos;RG_Alert_Case&apos;
&amp;&amp;
ISPICKVAL({!$Record.Case_Name__c}, &apos;RTI Premium&apos;) 
&amp;&amp;
ISPICKVAL({!$Record.Status}, &apos;New&apos;)
&amp;&amp;
!ISBLANK({!$Record.AccountId})
&amp;&amp;
ISBLANK({!$Record.ClosedDate})</filterFormula>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <scheduledPaths>
            <name>Send_Email_Escalation_Notification_after_1_hour</name>
            <connector>
                <targetReference>Escalated_Checkbox</targetReference>
            </connector>
            <label>Send Email Escalation Notification after 1 hour</label>
            <offsetNumber>1</offsetNumber>
            <offsetUnit>Hours</offsetUnit>
            <timeSource>RecordTriggerEvent</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
