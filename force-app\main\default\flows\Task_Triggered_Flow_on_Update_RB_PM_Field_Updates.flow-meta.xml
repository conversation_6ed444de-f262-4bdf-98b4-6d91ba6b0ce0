<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Determine the action name value to define the new values of the RB PB Review and RB PM Comment fields</description>
        <name>Action_Name_value</name>
        <label>Action Name value</label>
        <locationX>446</locationX>
        <locationY>395</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_Escalation</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RB_Action__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No Escalation</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_for_No_Escalation</targetReference>
            </connector>
            <label>No Escalation</label>
        </rules>
        <rules>
            <name>X1E_18R_RULE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RB_Action__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>#1E-18R_RULE</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Update_for_No_Escalation</targetReference>
            </connector>
            <label>#1E-18R_RULE</label>
        </rules>
        <rules>
            <name>X19R_Review_RM_Down_Tier</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RB_Action__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>19R_Review RM - Down Tier</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Update_for_1E_18R_RULE</targetReference>
            </connector>
            <label>19R_Review RM - Down Tier</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine what the new value of the RB_Review field is and its previous value to determine the next steps</description>
        <name>New_Value_of_RB_Review_field</name>
        <label>New Value of RB Review field</label>
        <locationX>864</locationX>
        <locationY>287</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>New_Value_is_Agree</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RB_Review__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agree</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Action_Name_value</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>New Value is Agree</label>
        </rules>
        <rules>
            <name>Previous_value_was_Agree</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.RB_Review__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agree</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RB_Review__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Agree</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Update_for_19R_Review_RM_Down_Tier</targetReference>
            </connector>
            <label>Previous value was Agree</label>
        </rules>
    </decisions>
    <description>SBET-1109 - Flow to update the RB PM Review and RB PM Review Comments fields based on updates to the RB Review field where the new or previous value of the field is Agree</description>
    <environments>Default</environments>
    <interviewLabel>Task Triggered Flow on Update - RB PM Field Updates {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Task Triggered Flow on Update - RB PM Field Updates</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Update fields for RB PM Review and Review Comments based on Change to RB Review field</description>
        <name>Copy_1_of_Update_for_19R_Review_RM_Down_Tier</name>
        <label>Clear Field Values</label>
        <locationX>1018</locationX>
        <locationY>395</locationY>
        <inputAssignments>
            <field>RB_PM_Review_Comments__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>RB_PM_Review__c</field>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update fields for RB PM Review and Review Comments based on Action</description>
        <name>Copy_2_of_Update_for_1E_18R_RULE</name>
        <label>Update for 19R_Review RM - Down Tier</label>
        <locationX>578</locationX>
        <locationY>503</locationY>
        <inputAssignments>
            <field>RB_PM_Review_Comments__c</field>
            <value>
                <stringValue>Premium Service</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RB_PM_Review__c</field>
            <value>
                <stringValue>Remove</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update fields for RB PM Review and Review Comments based on Action</description>
        <name>Copy_2_of_Update_for_No_Escalation</name>
        <label>Update for #1E-18R_RULE</label>
        <locationX>314</locationX>
        <locationY>503</locationY>
        <inputAssignments>
            <field>RB_PM_Review_Comments__c</field>
            <value>
                <stringValue>Standard</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RB_PM_Review__c</field>
            <value>
                <stringValue>Remove</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update fields for RB PM Review and Review Comments based on Action</description>
        <name>Update_for_No_Escalation</name>
        <label>Update for No Escalation</label>
        <locationX>50</locationX>
        <locationY>503</locationY>
        <inputAssignments>
            <field>RB_PM_Review_Comments__c</field>
            <value>
                <stringValue>Keep</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RB_PM_Review__c</field>
            <value>
                <stringValue>Keep</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>738</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>New_Value_of_RB_Review_field</targetReference>
        </connector>
        <filterFormula>{!$Record.RecordType.Name}= &apos;Rebase&apos;
&amp;&amp;
ISCHANGED({!$Record.RB_Review__c})
&amp;&amp;
ISPICKVAL({!$Record.Status}, &apos;Open&apos;)</filterFormula>
        <object>Task</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Obsolete</status>
    <variables>
        <description>Variable to store new values to the RB PM Review and RB PM Comments field on the triggering task</description>
        <name>taskFieldUpdates</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
