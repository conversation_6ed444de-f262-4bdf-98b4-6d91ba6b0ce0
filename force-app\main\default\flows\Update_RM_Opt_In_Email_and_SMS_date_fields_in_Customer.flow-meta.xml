<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>RM_Opt_In_Email_and_SMS_fields_updated</name>
        <label>RM Opt In Email and SMS fields updated</label>
        <locationX>446</locationX>
        <locationY>311</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RM_Opt_In_Email_SMS_Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Opt_In_Email_SMS_fields</targetReference>
            </connector>
            <label>RM Opt In Email &amp; SMS Updated</label>
        </rules>
        <rules>
            <name>RM_Opt_In_Email_Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_In_Email_Date_field</targetReference>
            </connector>
            <label>RM Opt In Email Updated</label>
        </rules>
        <rules>
            <name>RM_Opt_In_SMS_Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_In_SMS_Date_field</targetReference>
            </connector>
            <label>RM Opt In SMS Updated</label>
        </rules>
    </decisions>
    <description>Flow updates RM Opt in Email and RM Opt in SMS date fields base don RM Opt in Email and RM Opt in SMS fields</description>
    <interviewLabel>Update RM Opt In Email and SMS date fields in Customer {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update RM Opt In Email and SMS date fields in Customer</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Opt_In_Email_SMS_fields</name>
        <label>Update Opt In Email &amp; SMS fields</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_Opt_In_Email_Date_field</name>
        <label>Update RM Opt In Email Date field</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_Opt_In_SMS_Date_field</name>
        <label>Update RM Opt In SMS Date field</label>
        <locationX>578</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email_and_SMS_fields_updated</targetReference>
        </connector>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Obsolete</status>
    <variables>
        <name>TodaysDate</name>
        <dataType>Date</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Flow.CurrentDate</elementReference>
        </value>
    </variables>
</Flow>
