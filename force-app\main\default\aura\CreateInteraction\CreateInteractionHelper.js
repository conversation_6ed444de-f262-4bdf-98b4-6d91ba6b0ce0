({
    getTaskRecord : function(component){
        console.log('Initialization of Create Interaction >>> ');
        var action = component.get("c.getTask");
        action.setParams({
            'recordId': component.get("v.recordId")
        });
        action.setCallback(this, function(a) {
            var state = a.getState();
            if (state === "SUCCESS") {
                var task = a.getReturnValue();
                component.set("v.interaction", task);
                component.set("v.newInteraction.Subject", component.get("v.interaction.Subject"));
                component.set("v.newInteraction.RecordTypeId", component.get("v.interaction.RecordTypeId"));
                component.set("v.newInteraction.Channel__c", component.get("v.interaction.Channel__c"));
                component.set("v.newInteraction.Direction__c", component.get("v.interaction.Direction__c"));
                component.set("v.newInteraction.ActivityDate", component.get("v.interaction.ActivityDate"));
                component.set("v.newInteraction.Reason_for_Contact__c", component.get("v.interaction.Reason_for_Contact__c"));
                component.set("v.newInteraction.Status", component.get("v.interaction.Status"));
                component.set("v.newInteraction.Log_Method__c", component.get("v.interaction.Log_Method__c"));
                component.set("v.newInteraction.WhoId", component.get("v.interaction.WhoId"));
                component.set("v.newInteraction.WhatId", component.get("v.interaction.WhatId"));
            }
        });
        $A.enqueueAction(action);
        
    },
    
    fetchChannelPicklist : function(component){
        var action = component.get("c.getPicklistvalues");
        action.setParams({
            'objectName': 'Task',
            'field_apiname': 'Channel__c',
            'nullRequired': false // includes --None--
        });
        action.setCallback(this, function(a) {
            var state = a.getState();
            if (state === "SUCCESS"){
                var result = a.getReturnValue();
                var channelMap = [];
                for(var key in result){
                    channelMap.push({key: key, value: result[key]});
                }
                component.set("v.channelPicklist", channelMap);
            } 
        });
        $A.enqueueAction(action);
    },  
    
    fetchDirectionPicklist : function(component){
        var action = component.get("c.getPicklistvalues");
        action.setParams({
            'objectName': 'Task',
            'field_apiname': 'Direction__c',
            'nullRequired': false
        });
        action.setCallback(this, function(a) {
            var state = a.getState();
            if (state === "SUCCESS"){
                var result = a.getReturnValue();
                var directionMap = [];
                for(var key in result){
                    directionMap.push({key: key, value: result[key]});
                }
                component.set("v.directionPicklist", directionMap);
            }
        });
        $A.enqueueAction(action);
    }, 
    
    createNewInteraction : function (component){
        
        let subject = component.find("subject"), 			
            channel = component.find("channel"), 			
            direction = component.find("direction"),
            dateField = component.find("date"),
            timeField = component.find("time");
        
        if(subject.get("v.validity").valid && channel.get("v.validity").valid &&
           direction.get("v.validity").valid && dateField.get("v.validity").valid && timeField.get("v.validity").valid){
            
            console.log('All Valid!');
            
            var action = component.get("c.createInteraction");
            action.setParams({ 
                "interaction": component.get("v.newInteraction"),
                "taskId": component.get("v.recordId")
            });
            action.setCallback(this, function(response) {
                console.log('In Set Callback');
                var state = response.getState();
                console.log('Return value::: ' +JSON.stringify(response.getReturnValue()));
                if (state === "SUCCESS") {
                    
                    var closeTask = component.get("v.closeTask");
                    if (closeTask){
                        this.closeOriginTask(component);
                    }
                    
                    // record is saved successfully
                    var resultsToast = $A.get("e.force:showToast");
                    resultsToast.setParams({
                        "title": "Saved",
                        "type": "success",
                        "message": "New Interaction successfully created!"
                    });
                    resultsToast.fire();
                    
                    // Close the action panel
                    var dismissActionPanel = $A.get("e.force:closeQuickAction");
                    dismissActionPanel.fire();
                    
                    var navEvt = $A.get("e.force:navigateToSObject");
                    navEvt.setParams({
                        "recordId": component.get("v.newInteraction.WhatId"),
                        "slideDevName": "related"
                    });
                    navEvt.fire();
                }
                else {
                     console.log('Problem Saving Interaction ' + a.state + ', error: ' + JSON.stringify(response.error));
                }
            });
            
            $A.enqueueAction(action);
            
        }else{
            if (!subject.get("v.validity").valid)   subject.showHelpMessageIfInvalid();
            if (!channel.get("v.validity").valid) 	channel.showHelpMessageIfInvalid(); 
            if (!direction.get("v.validity").valid) direction.showHelpMessageIfInvalid(); 
            if (!dateField.get("v.validity").valid) dateField.showHelpMessageIfInvalid(); 
            if (!timeField.get("v.validity").valid) timeField.showHelpMessageIfInvalid(); 
            
            return;
        }
        
    }, 
    
    closeOriginTask : function (component){
        var action = component.get("c.closeTask");
        action.setParams({ 
            "taskId": component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            console.log('In Set Callback');
            var state = response.getState();
            console.log('Return value::: ' +JSON.stringify(response.getReturnValue()));
        });
        
        $A.enqueueAction(action);
    }
 })