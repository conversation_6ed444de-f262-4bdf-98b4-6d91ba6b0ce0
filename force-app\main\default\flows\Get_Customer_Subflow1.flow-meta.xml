<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Find_Customers</name>
        <label>Find Customers</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <actionName>findMatchingIndividuals</actionName>
        <actionType>findMatchingIndividuals</actionType>
        <connector>
            <targetReference>Get_Customer_From_Email</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>searchTerm</name>
            <value>
                <stringValue>{!varSuppliedEmail}</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>searchFields</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>searchObject</name>
            <value>
                <stringValue>Contact</stringValue>
            </value>
        </inputParameters>
        <nameSegment>findMatchingIndividuals</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>AssignCustomer</name>
        <label>AssignCustomer</label>
        <locationX>138</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>varCase.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Non_Elite.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Non_Elite</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignEliteCustomer</name>
        <label>AssignEliteCustomer</label>
        <locationX>490</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>varCase.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Elite.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Elite</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Count_of_Customer_Records</name>
        <label>Count of Customer Records</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>numberCustomerRecords</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Customer_From_Email</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Record_Number</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_AccountId</name>
        <label>Set AccountId</label>
        <locationX>402</locationX>
        <locationY>1190</locationY>
        <assignmentItems>
            <assignToReference>varAccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varCase.AccountId</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>Set_Non_Elite_AccountId</name>
        <label>Set Non Elite AccountId</label>
        <locationX>50</locationX>
        <locationY>1190</locationY>
        <assignmentItems>
            <assignToReference>varAccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varCase.AccountId</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <collectionProcessors>
        <name>Filter_for_Elite_Status</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter for Elite Status</label>
        <locationX>226</locationX>
        <locationY>566</locationY>
        <assignNextValueToReference>currentItem_Filter_for_Elite_Status</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_Customer_From_Email</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_for_Elite_Status.Elite_Status__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Elite</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Elite_Customers</targetReference>
        </connector>
    </collectionProcessors>
    <decisions>
        <name>Elite_Customers</name>
        <label>Elite Customers?</label>
        <locationX>226</locationX>
        <locationY>674</locationY>
        <defaultConnector>
            <targetReference>Loop_Elite</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>1 or More</defaultConnectorLabel>
        <rules>
            <name>No_Elite_Customer</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Filter_for_Elite_Status</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Filter_for_Elite_Status</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Non_Elite</targetReference>
            </connector>
            <label>No Elite Customer</label>
        </rules>
    </decisions>
    <decisions>
        <name>Record_Number</name>
        <label>Record Number</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <defaultConnectorLabel>1</defaultConnectorLabel>
        <rules>
            <name>Less_Than_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>numberCustomerRecords</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <label>Less Than 1</label>
        </rules>
        <rules>
            <name>More_Than_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>numberCustomerRecords</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Filter_for_Elite_Status</targetReference>
            </connector>
            <label>More Than 1</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Get Customer Subflow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Get Customer Subflow</label>
    <loops>
        <name>Loop_Elite</name>
        <label>Loop Elite</label>
        <locationX>402</locationX>
        <locationY>782</locationY>
        <collectionReference>Filter_for_Elite_Status</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>AssignEliteCustomer</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Elite_Case_Record</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Non_Elite</name>
        <label>Loop Non Elite</label>
        <locationX>50</locationX>
        <locationY>782</locationY>
        <collectionReference>Get_Customer_From_Email</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>AssignCustomer</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Case_Record</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Customer_From_Email</name>
        <label>Get Customer From Email</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Count_of_Customer_Records</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Count_of_Customer_Records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>PersonContactId</field>
            <operator>In</operator>
            <value>
                <elementReference>Find_Customers.contactIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Account</object>
        <sortField>LastActivityDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Case_Record</name>
        <label>Update Case Record</label>
        <locationX>50</locationX>
        <locationY>1082</locationY>
        <connector>
            <targetReference>Set_Non_Elite_AccountId</targetReference>
        </connector>
        <inputReference>varCase</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Elite_Case_Record</name>
        <label>Update Elite Case Record</label>
        <locationX>402</locationX>
        <locationY>1082</locationY>
        <connector>
            <targetReference>Set_AccountId</targetReference>
        </connector>
        <inputReference>varCase</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Find_Customers</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>currentItem_Filter_for_Elite_Status</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>numberCustomerRecords</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>varAccountId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>varCase</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>varCustomerAccountId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varSuppliedEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>varCase.SuppliedEmail</elementReference>
        </value>
    </variables>
</Flow>
