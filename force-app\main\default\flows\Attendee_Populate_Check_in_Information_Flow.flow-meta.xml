<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <environments>Default</environments>
    <formulas>
        <name>Updatecheckininformation</name>
        <dataType>String</dataType>
        <expression>&quot;Number of Seats&quot; + &quot; &quot; + TEXT({!$Record.Number_Of_Seats__c}) + &quot;,&quot; + &quot;Seat/Table Number:&quot; + &quot; &quot; + TEXT({!$Record.Seat_Table_Number__c})</expression>
    </formulas>
    <interviewLabel>Attendee-Populate Check in Information Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Attendee-Populate Check in Information Flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Check_in_Information</name>
        <label>Update Check-in Information</label>
        <locationX>690</locationX>
        <locationY>436</locationY>
        <inputAssignments>
            <field>Attendee_Check_in_Information__c</field>
            <value>
                <elementReference>Updatecheckininformation</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>First_Name_Text__c</field>
            <value>
                <elementReference>$Record.Account__r.FirstName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Last_Name_Text__c</field>
            <value>
                <elementReference>$Record.Account__r.LastName</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>562</locationX>
        <locationY>48</locationY>
        <connector>
            <targetReference>Update_Check_in_Information</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>Attendee__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
