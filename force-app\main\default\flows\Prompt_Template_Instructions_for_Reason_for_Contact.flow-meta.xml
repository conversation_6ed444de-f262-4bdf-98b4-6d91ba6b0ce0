<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Sends message that there was a fault in the flow and it couldn&apos;t find the records</description>
        <name>Flow_Fault_Prompt_Instructions</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Flow Fault Prompt Instructions</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>There was a fault in the flow when retrieving records
</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sends message that no Tasks were found to Prompt Template</description>
        <name>No_Tasks</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>No Tasks</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Customers has had no interactions in the last 90 days
</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sends a summary of the customers most frequent reasons for contact to Prompt Template</description>
        <name>Send_Reason_For_Contact_to_Prompt_Template</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Send Reason For Contact to Prompt Template</label>
        <locationX>138</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Reason for Contact - {!Loop_Tasks.Reason_for_Contact__c} 
Date - {!Loop_Tasks.ActivityDate}
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Tasks</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Are there any interaction tasks?</description>
        <name>Tasks_Found</name>
        <label>Tasks Found?</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>No_Tasks</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Tasks</defaultConnectorLabel>
        <rules>
            <name>Tasks</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Interaction_Tasks</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Interaction_Tasks</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Tasks</targetReference>
            </connector>
            <label>Tasks</label>
        </rules>
    </decisions>
    <description>Used by Customer Summary Prompt Template to generate additional prompt instructions about the reasons for contact for a customer</description>
    <environments>Default</environments>
    <formulas>
        <name>formulaDateFilterValue</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 30</expression>
    </formulas>
    <interviewLabel>Prompt Template: Instructions for Reason for Contact {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Prompt Template: Instructions for Reason for Contact</label>
    <loops>
        <description>Loop through all customers Task records</description>
        <name>Loop_Tasks</name>
        <label>Loop Tasks</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <collectionReference>Get_Interaction_Tasks</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Send_Reason_For_Contact_to_Prompt_Template</targetReference>
        </nextValueConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>PromptFlow</processType>
    <recordLookups>
        <description>Gets the record type details for interaction Tasks</description>
        <name>Get_Interaction_Task_Record_Type</name>
        <label>Get Interaction Task Record Type</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Interaction_Tasks</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Fault_Prompt_Instructions</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Interaction</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets all Interaction Tasks from last 90 days</description>
        <name>Get_Interaction_Tasks</name>
        <label>Get Interaction Tasks</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Tasks_Found</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Flow_Fault_Prompt_Instructions</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Interaction_Task_Record_Type.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>WhoId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Input.objectToSummarize.PersonContactId</elementReference>
            </value>
        </filters>
        <filters>
            <field>ActivityDate</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>formulaDateFilterValue</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <capabilityTypes>
            <name>PromptTemplateType://einstein_gpt__recordSummary</name>
            <capabilityName>PromptTemplateType://einstein_gpt__recordSummary</capabilityName>
            <inputs>
                <name>objectToSummarize</name>
                <capabilityInputName>objectToSummarize</capabilityInputName>
                <dataType>SOBJECT://Account</dataType>
                <isCollection>false</isCollection>
            </inputs>
        </capabilityTypes>
        <connector>
            <targetReference>Get_Interaction_Task_Record_Type</targetReference>
        </connector>
        <triggerType>Capability</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>currentItem_Filter_Attendee_Last_2_Weeks</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
</Flow>
