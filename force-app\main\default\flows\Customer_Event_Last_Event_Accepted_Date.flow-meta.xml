<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Check_if_it_s_not_null</name>
        <label>Check if it&apos;s not null</label>
        <locationX>566</locationX>
        <locationY>72</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>NewAttendeeId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Customer</targetReference>
            </connector>
            <label>Not Null</label>
        </rules>
    </decisions>
    <description>- Populates the last event accepted date in Customer record based on various criteria</description>
    <interviewLabel>Customer: Last Event Accepted Date {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer: Event Last Event Accepted Date</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Attendee</name>
        <label>Get Attendee</label>
        <locationX>434</locationX>
        <locationY>70</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_if_it_s_not_null</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CustomerId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <filters>
            <field>Parent_Attendee__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Event_Offer_Accepted_Date__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>Attendee__c</object>
        <outputAssignments>
            <assignToReference>AttendeeEventOfferAcceptedDate</assignToReference>
            <field>Event_Offer_Accepted_Date__c</field>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>NewAttendeeId</assignToReference>
            <field>Id</field>
        </outputAssignments>
        <sortField>Event_Offer_Accepted_Date__c</sortField>
        <sortOrder>Desc</sortOrder>
    </recordLookups>
    <recordUpdates>
        <name>Update_Customer</name>
        <label>Update Customer</label>
        <locationX>580</locationX>
        <locationY>247</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CustomerId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Event_Accepted_Date__c</field>
            <value>
                <elementReference>AttendeeEventOfferAcceptedDate</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>65</locationX>
        <locationY>53</locationY>
        <connector>
            <targetReference>Get_Attendee</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>AttendeeEventOfferAcceptedDate</name>
        <dataType>Date</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CustomerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>NewAttendeeId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
