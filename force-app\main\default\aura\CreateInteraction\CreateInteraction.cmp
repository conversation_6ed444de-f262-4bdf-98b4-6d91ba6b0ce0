<aura:component implements="flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,force:lightningQuickActionWithoutHeader" 
                controller="CreateInteractionController"
                access="global" >
    
    <!--Declare Attribute-->   
    <aura:attribute name="channelPicklist" type="String[]"/>
    <aura:attribute name="directionPicklist" type="String[]"/>
    <aura:attribute name="interaction" type="Task" />
    <aura:attribute name="closeTask" type="Boolean"/>

    <aura:attribute name="newInteraction" type="Task"
                    default="{ 'sobjectType': 'Task',
                             'RecordTypeId':'',
                             'Status':'Open',
                             'Subject':'',
                             'Channel__c':'',
                             'Direction__c':'',
                             'Description':'',
                             'ActivityDate':'',
                             'Time__c':'',
                             'Log__Method__c':'',
                             'Status':'',
                             'WhatId':'',
                             'WhoId':''
                             }"/>
    
   	<aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    
    <!--Custom Styles for Modal Header and Footer--> 
    <aura:html tag="style">
        .cuf-content {
        padding: 0 0rem !important;
        }
        .slds-p-around--medium {
        padding: 0rem !important;
        }
        .slds-modal__container { 
        height : auto; 
        width: 85%; 
        max-width: 70vh; 
        } 
        .slds-modal__content{
        overflow-y:scroll !important;       
        }
        .modal-body{
        height : 80vh !important; 
        max-height: 80vh !important;
        }
    </aura:html>
    
    <!--Modal Header-->  
    <div class="modal-header slds-modal__header slds-size_1-of-1">
        <h4 class="title slds-text-heading--medium">Create Interaction</h4>
    </div>
    <!--End Modal Header--> 
    
    <!--Modal Body-->   
    <div class="slds-modal__content slds-modal__container  slds-p-around--x-small slds-m-top_medium slds-m-bottom_medium slds-size_1-of-1 slds-is-relative">
        
        <lightning:layout multipleRows="true">
            <lightning:layoutItem size="12" padding="around-small">
                <lightning:input name="Subject" aura:id="subject" label="Subject" value="{!v.newInteraction.Subject}" required="true" messageWhenValueMissing="Please input subject" disabled="true"/> 
            </lightning:layoutItem>
            <lightning:layoutItem size="12" padding="around-small">
                <lightning:select name="channel" label="Channel" aura:id="channel" value="{!v.newInteraction.Channel__c}" onchange="{!c.handleChannelChange}" required="true" messageWhenValueMissing="Please choose a channel">
                    <aura:iteration items="{!v.channelPicklist}" var="channel" indexVar="key">
                        <option text="{!channel.value}" value="{!channel.key}" selected="{!channel.key==v.newInteraction.Channel__c}"/>
                    </aura:iteration>
                </lightning:select>
            </lightning:layoutItem>
            <lightning:layoutItem size="12" padding="around-small">
                <lightning:select name="direction" label="Direction" aura:id="direction" value="{!v.newInteraction.Direction__c}" onchange="{!c.handleDirectionChange}" required="true" messageWhenValueMissing="Please choose a direction">
                    <aura:iteration items="{!v.directionPicklist}" var="direction" indexVar="key">
                        <option text="{!direction.value}" value="{!direction.key}" selected="{!direction.key==v.newInteraction.Direction__c}"/>
                    </aura:iteration>
                </lightning:select>
            </lightning:layoutItem>
            <lightning:layoutItem size="12" padding="around-small">
                <lightning:input name="reason" aura:id="reason" label="Reason for Contact" value="{!v.newInteraction.Reason_for_Contact__c}" required="true" messageWhenValueMissing="Please choose a reason for contact" disabled="true"/>
            </lightning:layoutItem>
            <lightning:layoutItem size="12" padding="around-small">
                <lightning:textarea name="comments" aura:id="comments" label="Comments" value="{!v.newInteraction.Description}"/>
            </lightning:layoutItem>
            <lightning:layoutItem size="6" padding="around-small">
                <lightning:input type="date" name="date" aura:id="date" label="Date" value="{!v.newInteraction.ActivityDate}" required="true" messageWhenValueMissing="Please choose a date"/>
            </lightning:layoutItem>
            <lightning:layoutItem size="6" padding="around-small">
                <lightning:input type="time" name="time" aura:id="time" label="Time" value="{!v.newInteraction.Time__c}" required="true" messageWhenValueMissing="Please choose a time"/>
            </lightning:layoutItem>
            <lightning:layoutItem size="6" padding="around-small">
                <lightning:input type="checkbox" aura:id="closeTask" label="{!$Label.c.UI_Close_Current_Task}" checked="{v.closeTask}" onchange="{!c.handleCheck}"/>
            </lightning:layoutItem>
        </lightning:layout>
    </div>
    <!--End of Modal Body--> 
    
    <!--Modal Footer-->
    <div class="modal-footer slds-modal__footer slds-size_1-of-1">
        <lightning:button variant="Brand" class="slds-button" label="Save" onclick="{!c.handleSubmit}"/>
    </div> 
    <!--End of Modal Footer-->
</aura:component>