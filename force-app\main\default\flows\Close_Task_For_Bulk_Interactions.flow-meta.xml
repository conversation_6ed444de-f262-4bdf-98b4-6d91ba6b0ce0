<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Close Task For Bulk Interactions with a Delay</description>
    <environments>Default</environments>
    <interviewLabel>Close Task For Bulk Interactions {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Close Task For Bulk Interactions with a Delay</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>update Tasks after Delay to break the 101 SOQL error</description>
        <name>update_Tasks_after_Delay</name>
        <label>update Tasks after Delay</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>tasksList</elementReference>
            </value>
        </filters>
        <filters>
            <field>WhoId</field>
            <operator>In</operator>
            <value>
                <elementReference>ContactIdList</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Bulk_Interactions__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Created__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Wait_One_Minute</targetReference>
        </connector>
    </start>
    <status>Obsolete</status>
    <variables>
        <name>ContactIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>tasksList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <waits>
        <name>Wait_One_Minute</name>
        <elementSubtype>WaitDuration</elementSubtype>
        <label>Wait One Minute</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <defaultConnectorLabel>Default Path</defaultConnectorLabel>
        <waitEvents>
            <conditionLogic>and</conditionLogic>
            <connector>
                <targetReference>update_Tasks_after_Delay</targetReference>
            </connector>
            <label>el_0</label>
            <offset>1</offset>
            <offsetUnit>Minutes</offsetUnit>
        </waitEvents>
    </waits>
</Flow>
