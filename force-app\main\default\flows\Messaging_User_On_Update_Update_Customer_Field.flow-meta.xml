<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_to_Collection</name>
        <label>Add to Collection</label>
        <locationX>264</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>messagingSessionsToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>For_Each_Session</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>For_Each_Session</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_new_Customer_Value</name>
        <label>Assign new Customer Value</label>
        <locationX>264</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>For_Each_Session.Customer__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Account.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_to_Collection</targetReference>
        </connector>
    </assignments>
    <description>Flow to update all messaging sessions for a messaging user with the correct customer upon update to the messaging user</description>
    <environments>Default</environments>
    <interviewLabel>Messaging User: On Update - {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Messaging User: On Update - Update Customer Field on Messaging Sessions</label>
    <loops>
        <name>For_Each_Session</name>
        <label>For Each Session</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <collectionReference>Get_Messaging_Sessions</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_new_Customer_Value</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Collection</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Messaging_Sessions</name>
        <label>Get Messaging Sessions</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>For_Each_Session</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MessagingEndUserId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>MessagingSession</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Customer__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Collection</name>
        <label>Update Collection</label>
        <locationX>176</locationX>
        <locationY>839</locationY>
        <inputReference>messagingSessionsToUpdate</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Messaging_Sessions</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>ContactId</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>MessagingEndUser</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>messagingSessionsToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>MessagingSession</objectType>
    </variables>
    <variables>
        <name>newCustomerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Record.Account.Id</elementReference>
        </value>
    </variables>
</Flow>
