<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>56.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Case_Customer_Details_Are_Present</name>
        <label>Case Customer Details Are Present</label>
        <locationX>336</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Case_Customer_Details_available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Case_Customer_Details.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Case_Customer_Details.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0052y000003CNOPAA4</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Case_customer_owner_NSW_premium_Team_managed</targetReference>
            </connector>
            <label>Case Customer Details available</label>
        </rules>
    </decisions>
    <decisions>
        <name>Case_customer_owner_NSW_premium_Team_managed</name>
        <label>Case customer owner NSW premium Team managed</label>
        <locationX>182</locationX>
        <locationY>551</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_NSW_Premium_Team_managed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>0122y000000c3F3AAI</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Queue</targetReference>
            </connector>
            <label>Is NSW Premium Team managed?</label>
        </rules>
    </decisions>
    <description>Flow assigns Case to QUeue when Customer Owner is NSW Premium Team Managed</description>
    <environments>Default</environments>
    <interviewLabel>Assign NSW Premium Team Managed Cases to Queue Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Assign NSW Premium Team Managed Cases to Queue Flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Case_Customer_Details</name>
        <label>Get Case Customer Details</label>
        <locationX>336</locationX>
        <locationY>311</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Case_Customer_Details_Are_Present</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Queue</name>
        <label>Get Queue</label>
        <locationX>50</locationX>
        <locationY>671</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Case_Owner_to_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>NSW_Premium_Team_Managed</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Case_Owner_to_Queue</name>
        <label>Update Case Owner to Queue</label>
        <locationX>50</locationX>
        <locationY>791</locationY>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Queue.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>210</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case_Customer_Details</targetReference>
        </connector>
        <object>Case</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
