<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assign values for a task record that will then be created at the end of the flow</description>
        <name>Assign_Values_for_this_Attendee_s_Task</name>
        <label>Assign Values for this Attendee&apos;s Task</label>
        <locationX>138</locationX>
        <locationY>792</locationY>
        <assignmentItems>
            <assignToReference>currentTaskVariable.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>subjectTemplate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentTaskVariable.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>descriptionText</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentTaskVariable.WhoId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>For_Each_Attendee.Account__r.PersonContact.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentTaskVariable.WhatId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>For_Each_Attendee.Account__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentTaskVariable.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>For_Each_Attendee.Account__r.OwnerId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentTaskVariable.ActivityDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>todayPlusTwoDays</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentTaskVariable.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentTaskVariable.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Task_Record_Type.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>currentTaskVariable.Priority</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>tasksToCreate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>currentTaskVariable</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>For_Each_Attendee</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Determine if Event start date is in 5 days to determine if flow needs to run for this event.</description>
        <name>EventIn5Days</name>
        <label>Is Event in 5 Days</label>
        <locationX>182</locationX>
        <locationY>252</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsStartDate5DaysLater</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Task_Record_Type</targetReference>
            </connector>
            <label>Event Start Date is in 5 Days</label>
        </rules>
    </decisions>
    <description>Flow that runs daily to identify open campaigns commencing in 5 days, and create tasks for customer owners with incomplete guest details on customer attendees. 
SBET-1158 - Alert system for hosted events where a customer doesn&apos;t have guest details
SBET-1158 - Updated the formula to check if the start date is 5 days after today</description>
    <environments>Default</environments>
    <formulas>
        <name>IsStartDate5DaysLater</name>
        <dataType>Boolean</dataType>
        <expression>DATEVALUE({!$Record.Start_Date__c}) = TODAY() + 5</expression>
    </formulas>
    <formulas>
        <description>Formula to calculate the date 5 days from today.</description>
        <name>todayPlus5Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() + 5</expression>
    </formulas>
    <formulas>
        <description>Formula to calculate date in 2 days</description>
        <name>todayPlusTwoDays</name>
        <dataType>Date</dataType>
        <expression>TODAY() + 2</expression>
    </formulas>
    <interviewLabel>Scheduled Flow - Create Incomplete Guest Details Tasks {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Scheduled Flow - Create Incomplete Guest Details Tasks</label>
    <loops>
        <description>Loop through all Attendees found that meet requirements to create a task for their Customer Owner</description>
        <name>For_Each_Attendee</name>
        <label>For Each Attendee</label>
        <locationX>50</locationX>
        <locationY>684</locationY>
        <collectionReference>Get_Attendee_Records_for_Task_Creation</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Values_for_this_Attendee_s_Task</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Create_Tasks</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>Create tasks at end of the loop through all Attendees</description>
        <name>Create_Tasks</name>
        <label>Create Tasks</label>
        <locationX>50</locationX>
        <locationY>984</locationY>
        <inputReference>tasksToCreate</inputReference>
    </recordCreates>
    <recordLookups>
        <description>Get all customer attendee records with incomplete guest details greater than 0</description>
        <name>Get_Attendee_Records_for_Task_Creation</name>
        <label>Get Attendee Records for Task Creation</label>
        <locationX>50</locationX>
        <locationY>576</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>For_Each_Attendee</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Incomplete_Guest_Details__c</field>
            <operator>GreaterThan</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer_Attendee_Record_Type.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the ID of the Customer Attendee record type for later use in the flow.</description>
        <name>Get_Customer_Attendee_Record_Type</name>
        <label>Get Customer Attendee Record Type</label>
        <locationX>50</locationX>
        <locationY>468</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Attendee_Records_for_Task_Creation</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Customer_Attendee</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the Task Record type for the Task object for use later in the flow.</description>
        <name>Get_Task_Record_Type</name>
        <label>Get Task Record Type</label>
        <locationX>50</locationX>
        <locationY>360</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Customer_Attendee_Record_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>EventIn5Days</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Is_Hosted_Event__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <object>Campaign</object>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2024-08-28</startDate>
            <startTime>08:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Draft</status>
    <textTemplates>
        <description>Text to be populated on the Task Description (does not vary between attendees)</description>
        <name>descriptionText</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>This event is starting in 5 days and the customer has not submitted their guest details, please contact them and confirm their guest details and if they are still attending. 
If the customer is unable to attend please change their status to Cancelled and fill their spot ASAP.</text>
    </textTemplates>
    <textTemplates>
        <description>Template for the Task subject</description>
        <name>subjectTemplate</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Action Required - Guest details not completed for {!$Record.Name} - {!For_Each_Attendee.Account__r.Customer_Id__c}</text>
    </textTemplates>
    <variables>
        <name>currentTaskVariable</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <description>Collection variable of tasks to create once loop has ended</description>
        <name>tasksToCreate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
