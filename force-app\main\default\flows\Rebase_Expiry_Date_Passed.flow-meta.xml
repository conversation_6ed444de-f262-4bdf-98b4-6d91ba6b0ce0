<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Null checks if any expired child records are returned</description>
        <name>Null_Check_Expired_Child_Records</name>
        <label>Null Check Expired Child Records</label>
        <locationX>314</locationX>
        <locationY>684</locationY>
        <defaultConnector>
            <targetReference>Assign_Closed_by_System_Value_To_Rebase_Records</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Records</defaultConnectorLabel>
        <rules>
            <name>Expired_Child_Records</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Child_Rebase_Review_Records</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Closed_by_System_Value_to_Child_Records</targetReference>
            </connector>
            <label>Expired Child Records</label>
        </rules>
    </decisions>
    <decisions>
        <description>Null checks if any expired records are returned</description>
        <name>Null_Check_Expired_Records</name>
        <label>Null Check Expired Records</label>
        <locationX>798</locationX>
        <locationY>360</locationY>
        <defaultConnectorLabel>No Records</defaultConnectorLabel>
        <rules>
            <name>Expired_Records</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Expired_Rebase_Records</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Extract_Rebase_Record_Ids</targetReference>
            </connector>
            <label>Expired Records</label>
        </rules>
    </decisions>
    <description>Scheduled Flow that runs daily to check if the Expiry Date has passed on any Rebase records. Rebase Status field is updated to Closed By System if it has</description>
    <environments>Default</environments>
    <formulas>
        <description>Stores the specific date for yesterday</description>
        <name>formulaYesterdayDate</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 1</expression>
    </formulas>
    <interviewLabel>Rebase: Expiry Date Passed {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rebase: Expiry Date Passed</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets all child Rebase Review records that have expired</description>
        <name>Get_Child_Rebase_Review_Records</name>
        <label>Get Child Rebase Review Records</label>
        <locationX>314</locationX>
        <locationY>576</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_Expired_Child_Records</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <filterLogic>1 AND (2 OR 3)</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>Extract_Rebase_Record_Ids</elementReference>
            </value>
        </filters>
        <filters>
            <field>Rebase_Review_Status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Rebase_Review_Status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Closed By System</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Rebase_Review__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Rebase_Review_Status__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets all Rebase records with an expiry date of yesterday</description>
        <name>Get_Expired_Rebase_Records</name>
        <label>Get Expired Rebase Records</label>
        <locationX>798</locationX>
        <locationY>252</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_Expired_Records</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <filterLogic>1 AND (2 OR 3)</filterLogic>
        <filters>
            <field>Expiry_Date__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaYesterdayDate</elementReference>
            </value>
        </filters>
        <filters>
            <field>Rebase_Status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Rebase_Status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Closed By System</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Rebase__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Rebase_Status__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates the Rebase records with Closed By System value for expired Rebase records</description>
        <name>Update_Rebase_Record_Collection</name>
        <label>Update Rebase Record Collection</label>
        <locationX>314</locationX>
        <locationY>1248</locationY>
        <faultConnector>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <inputReference>Assign_Closed_by_System_Value_To_Rebase_Records</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the Rebase Reviews records with Closed By System value for expired Rebase Review records</description>
        <name>Update_Rebase_Review_Record_Collection</name>
        <label>Update Rebase Review Record Collection</label>
        <locationX>50</locationX>
        <locationY>900</locationY>
        <connector>
            <targetReference>Assign_Closed_by_System_Value_To_Rebase_Records</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <inputReference>Assign_Closed_by_System_Value_to_Child_Records</inputReference>
    </recordUpdates>
    <start>
        <locationX>672</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Expired_Rebase_Records</targetReference>
        </connector>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2025-05-01</startDate>
            <startTime>00:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <description>Subflow to handle Flow Faults, sends email to Performance Team members</description>
        <name>Rebase_Flow_Fault_Handler</name>
        <label>Rebase Flow Fault Handler</label>
        <locationX>754</locationX>
        <locationY>1356</locationY>
        <flowName>Rebase_Flow_Fault_Handler</flowName>
        <inputAssignments>
            <name>varFlowCurrentDateTime</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowFaultMessage</name>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowName</name>
            <value>
                <stringValue>Rebase: Expiry Date Passed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowUser</name>
            <value>
                <elementReference>$User.Username</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <transforms>
        <description>Assigns Closed By System to Rebase Review Status field for all expired child Rebase Review records</description>
        <name>Assign_Closed_by_System_Value_to_Child_Records</name>
        <label>Assign Closed by System Value to Child Records</label>
        <locationX>50</locationX>
        <locationY>792</locationY>
        <connector>
            <targetReference>Update_Rebase_Review_Record_Collection</targetReference>
        </connector>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <objectType>Rebase_Review__c</objectType>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <outputFieldApiName>Rebase_Review_Status__c</outputFieldApiName>
                <transformType>Map</transformType>
                <value>
                    <stringValue>Closed By System</stringValue>
                </value>
            </transformValueActions>
            <transformValueActions>
                <outputFieldApiName>Id</outputFieldApiName>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Child_Rebase_Review_Records[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <description>Assigns Closed By System to Rebase Status field for all Expired Rebase Records</description>
        <name>Assign_Closed_by_System_Value_To_Rebase_Records</name>
        <label>Assign Closed by System Value To Rebase Records</label>
        <locationX>314</locationX>
        <locationY>1140</locationY>
        <connector>
            <targetReference>Update_Rebase_Record_Collection</targetReference>
        </connector>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <objectType>Rebase__c</objectType>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <outputFieldApiName>Id</outputFieldApiName>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Expired_Rebase_Records[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
            <transformValueActions>
                <outputFieldApiName>Rebase_Status__c</outputFieldApiName>
                <transformType>Map</transformType>
                <value>
                    <stringValue>Closed By System</stringValue>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <description>Extracts the Rebase Record Ids for expired records and stores in text collection</description>
        <name>Extract_Rebase_Record_Ids</name>
        <label>Extract Rebase Record Ids</label>
        <locationX>314</locationX>
        <locationY>468</locationY>
        <connector>
            <targetReference>Get_Child_Rebase_Review_Records</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Expired_Rebase_Records[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
</Flow>
