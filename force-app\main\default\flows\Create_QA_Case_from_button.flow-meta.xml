<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Case_Origin</name>
        <label>Assign Case Origin</label>
        <locationX>314</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>CaseOrigin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>TM Alert QA</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>qa_Analyst_Queue</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_Origin_to_ECDD</name>
        <label>Assign Case Origin to ECDD</label>
        <locationX>842</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>CaseOrigin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>ECDD Analyst QA</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>qa_Analyst_Queue</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_Origin_to_LER</name>
        <label>Assign Case Origin to LER</label>
        <locationX>578</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>CaseOrigin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>LER Investigation QA</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>qa_Analyst_Queue</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_Origin_to_SMR</name>
        <label>Assign Case Origin to SMR</label>
        <locationX>1106</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>CaseOrigin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>SMR Case QA</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>qa_Analyst_Queue</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Decides whether a QA Case already exists based on the previous “Get Records” result. It directs the flow to either show the existing QA Case information or proceed to create a new one.</description>
        <name>check_if_QA_is_existing</name>
        <label>check if QA is existing</label>
        <locationX>446</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Parent_Case_Type_check</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Open QA doesn&apos;t exists</defaultConnectorLabel>
        <rules>
            <name>Open_QA_Exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_QA_case_if_already_present</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Show_existing_QA_info</targetReference>
            </connector>
            <label>Open QA Exists</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check Record Types of Parent Case.</description>
        <name>Parent_Case_Type_check</name>
        <label>Parent Case Type check</label>
        <locationX>842</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>qa_Analyst_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>TM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_parent_record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_Origin</targetReference>
            </connector>
            <label>TM</label>
        </rules>
        <rules>
            <name>LER</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_parent_record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non_Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_Origin_to_LER</targetReference>
            </connector>
            <label>LER</label>
        </rules>
        <rules>
            <name>ECDD</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_parent_record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_Origin_to_ECDD</targetReference>
            </connector>
            <label>ECDD</label>
        </rules>
        <rules>
            <name>SMR</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_parent_record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Standard_SMR</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_parent_record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fast_Track_SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_Origin_to_SMR</targetReference>
            </connector>
            <label>SMR</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Create QA Case from button {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Create QA Case from button</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Creates the new QA Case with the gathered information and assignments. This is the final step in the flow where the new case is actually generated and saved in Salesforce.</description>
        <name>Create_QA_record</name>
        <label>Create QA record</label>
        <locationX>842</locationX>
        <locationY>974</locationY>
        <connector>
            <targetReference>Confirmation_Screen</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Fault_Message_screen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>Get_parent_record.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <elementReference>CaseOrigin</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>qa_Analyst_Queue.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_QA_Recordtype.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <description>Retrieves the parent case record that the QA Case will be associated with. This step is crucial for ensuring the new QA Case is linked to the correct parent case.</description>
        <name>Get_parent_record</name>
        <label>Get parent record</label>
        <locationX>446</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_QA_Recordtype</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Searches for an existing QA Case related to the parent case. This step helps to determine if a QA Case has already been created, preventing duplication.</description>
        <name>Get_QA_case_if_already_present</name>
        <label>Get QA case if already present</label>
        <locationX>446</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>check_if_QA_is_existing</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ParentId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_QA_Recordtype.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsClosed</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Retrieves the Record Type ID for the “QA_QC” record type. This is needed to correctly assign the Record Type when creating the QA Case later in the flow.</description>
        <name>Get_QA_Recordtype</name>
        <label>Get QA Recordtype</label>
        <locationX>446</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_QA_case_if_already_present</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>QA_QC</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Retrieves the QA Queue for assigning the newly created QA Case. This ensures the case is routed to the correct team or individual responsible for QA.</description>
        <name>qa_Analyst_Queue</name>
        <label>QA Analyst Queue</label>
        <locationX>842</locationX>
        <locationY>866</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_QA_record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>QA_queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <description>Success Message</description>
        <name>Confirmation_Screen</name>
        <label>Confirmation Screen</label>
        <locationX>842</locationX>
        <locationY>1082</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Confirmation_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Confirmation_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Confirmation_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Confirmation_Text</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;QA Case has been successfully created.&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Confirmation_Screen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Fault_Message_screen</name>
        <label>Fault Message screen</label>
        <locationX>1546</locationX>
        <locationY>1082</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FaultMessage</name>
            <fieldText>&lt;p&gt;There was an error when running this process. Please review the below error for more information.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>If a QA Case is found, the flow follows this path, indicating that a QA Case already exists and further creation is unnecessary.

Displays information about the existing QA Case to the user, informing them that a QA Case related to this parent case is already in place.</description>
        <name>Show_existing_QA_info</name>
        <label>Show existing QA info</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Show_existing_QA_info_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Show_existing_QA_info_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Show_existing_QA_info_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>QA_Info</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;This case already has one active QA case.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;QA Case Number : &lt;/strong&gt;&lt;a href=&quot;/{!Get_QA_case_if_already_present.Id}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot; style=&quot;font-size: 16px;&quot;&gt;&lt;strong&gt;{!Get_QA_case_if_already_present.CaseNumber}&lt;/strong&gt;&lt;/a&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Show_existing_QA_info_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>2</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_parent_record</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>CaseOrigin</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>NewQAcaseID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
