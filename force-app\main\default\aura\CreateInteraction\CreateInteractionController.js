({
    doInit: function(component, event, helper) {
        
        helper.getTaskRecord(component);
        helper.fetchChannelPicklist(component);
        helper.fetchDirectionPicklist(component);
        
        component.find("time").set("v.value", $A.localizationService.formatTime(new Date(), "HH:mm:ss.SSS"));
    },
    
    handleSubmit: function(component, event, helper) {    
        helper.createNewInteraction(component);
    },
    
    handleChannelChange : function(component, event, helper) {
        
    },
    handleDirectionChange : function(component, event, helper) {
        
    },
    
    handleCheck : function(component, event, helper) {
        var isChecked = component.find("closeTask").get("v.checked");
        component.set("v.closeTask", isChecked);
    }
})