/**
 * @description       : Guest List component with multi-selection support
 * <AUTHOR> <PERSON><PERSON> DXC
 * @group             : 
 * @last modified on  : 05-29-2025
 * @last modified by  : jam<PERSON><PERSON><PERSON>@gmail.com
**/
import { LightningElement, wire, track, api} from 'lwc';
import getGuestList from '@salesforce/apex/guestListController.getGuestList';
import initializInfo from '@salesforce/apex/GuestListDataTableController.initRecords';
import loadMore from '@salesforce/apex/GuestListDataTableController.loadMore';
import { deleteRecord } from "lightning/uiRecordApi";
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import {
    registerListener,
    unregisterListener,
    unregisterAllListeners,
    fireEvent
} from 'c/pubsub';
import { CurrentPageReference, NavigationMixin} from 'lightning/navigation';
export default class GuestList_clone extends NavigationMixin(LightningElement) {

    @track guests  // to store the data
    error // to store error
    @track selectedEventId;

   
    loadMoreStatus="";
    enableinfiniteLoading=true;
    spinner=false;
    isFilterApplied=false;
    totalInitialRow=0;
    rowLoad=50;

    /* Guest List Column names */
      columns = [
       
        { label: 'User Name', cellAttributes: { alignment: 'left' }, fieldName: 'Sports_Bet_User_Name__c',type: 'text'},
       
        {
            label: 'Account Number',
            cellAttributes: { alignment: 'center' },
            fieldName: 'URL',
            type: 'url',
            typeAttributes: {label: { fieldName: 'Sports_Bet_Account_Number__c'}, 
            target: '_top'},
           
        },

        { label: 'Tier',cellAttributes: { alignment: 'center' }, type: 'text', fieldName: 'Premium_Tier__c',sortable:true },
        { label: 'TPV Last 12 Months', cellAttributes: { alignment: 'center' }, type: 'text', fieldName: 'TPV_Last_12_Months__c',sortable:true },
        { label: 'Date of Last Event Attendance',
          type: 'date',
          cellAttributes: { alignment: 'center' },
           typeAttributes:{
            day: "2-digit",
            month: "2-digit",
            year: "numeric"
            },
          fieldName: 'Last_Event_Accepted_Date__c',
          sortable:true,
           
        },
        { label: '# of Events This Year', cellAttributes: { alignment: 'center' }, type: 'text', fieldName: 'Total_Events_Attended_This_Year__c', sortable:true },
        { label: 'Last Interaction Date (Without Bulk)',
          type: 'date',
          cellAttributes: { alignment: 'center' },
          typeAttributes:{
            day: "2-digit",
            month: "2-digit",
            year: "numeric"
            }, 
          fieldName: 'Last_Interaction_Date_Without_Bulk__c',
          sortable:true 
        },
        { label: 'Suburb',cellAttributes: { alignment: 'center' }, fieldName: 'Shipping_City__c',type: 'text' },
        { label: 'State',cellAttributes: { alignment: 'center' }, type: 'text', fieldName: 'Shipping_State__c', sortable:true },
        { label: 'Last Bet Date',
          type: 'date',
          cellAttributes: { alignment: 'center' },
          typeAttributes:{
            day: "2-digit",
            month: "2-digit",
            year: "numeric"
            },  
          fieldName: 'Last_Bet_Date__c',
           sortable:true },
        { label: 'Customer Owner',  cellAttributes: { alignment: 'center' }, type: 'text', fieldName: 'Owner__c',sortable:true },
        
    ];
     fields="Sports_Bet_User_Name__c,Sports_Bet_Account_Number__c,Premium_Tier__c,TPV_Last_12_Months__c,Last_Event_Accepted_Date__c,Total_Events_Attended_This_Year__c,Last_Interaction_Date_Without_Bulk__c,Shipping_City__c,Shipping_State__c,Last_Bet_Date__c,Owner__c";
    defaultSortDirection = 'asc';
    sortDirection = 'asc';
    sortedBy;
  
   prepareUrl(data){
    let dataRef=JSON.parse(JSON.stringify(data));
        dataRef.forEach(element=>{
            element.URL='/lightning/r/Account/'+element.Id+'/view'
        });
        this.guests =[...dataRef];
        this.spinner=false;
   }
    /*Wire page reference   */
    @wire(CurrentPageReference) pageRef;


    renderedCallback()
    {
        registerListener('filterComponent',this.filterFunction,this);
    }
    connectedCallback() {
        //code
        this.intialize();
    }

    intialize(){
        this.spinner=true;
        let input={
            ObjectName:'Account',
            fieldNamesStr:this.fields,
            Orderby:'Name',
            OrderDir:''
        }
        initializInfo(input).then(result=>{
            console.log('initial result=',result);
            this.totalInitialRow=result.totalCount;
             result.sobList.forEach(element=>{
            element.URL='/lightning/r/Account/'+element.Id+'/view'
        });
            this.guests=[...result.sobList];
            this.spinner=false;
        }).catch(error=>{
             this.spinner=false;
            console.log('Error while loading data',error);
        });
    }
    loadMoreData(event){
        console.log("laoding more");
         var dataSize = this.guests.length;
        
        console.log("Data Size:",dataSize);
        console.log("Total Initial rows:",this.totalInitialRow);
        if(this.rowLoad<this.totalInitialRow && dataSize<this.totalInitialRow && !this.isFilterApplied ){
            
           this.loadMoreWithoutFilter(event);
        }else{
            event.target.isLoading = false;
        }
        
        
    }
    loadMoreWithoutFilter(event){
        let target=event.target;
        try {
            this.isLoading= true;
        this.loadMoreStatus= 'Loading';
         var dataSize = this.guests.length;
         var lastId = this.guests[dataSize - 1].Id;
         console.log('--lastId----'+lastId);
         let input={
            ObjectName:'Account',
            recId:lastId
            
        }
        console.log("input load more== ",input);
        loadMore(input).then(result=>{
             console.log("loaded more== ",result);
             result.forEach(element=>{
                 element.URL='/lightning/r/Account/'+element.Id+'/view'
                });
                    var newData = this.guests.concat(result);
                   this.guests=[...newData];
                  this.loadMoreStatus="";
                 target.isLoading = false;
                 // this.onHandleSort(event);
        }).catch(error=>{
            console.log("Error while load more",error);
        });
        } catch (error) {
           console.log('canceled load more at client side',error); 
        }
         
    }

    @track preSelectedRows = [];

    

    filterFunction(detail){
        try {
                console.log('filterFunction=',detail);
                this.spinner = true;
            let dataValue=detail.data;
            let filterapplied=detail.filterapplied;
            this.isFilterApplied=filterapplied;
            if(filterapplied){
                console.log('dataValue ',dataValue);
            console.log('Campaign ID ',dataValue.campaignId);
            this.selectedEventId = dataValue.campaignId;
        
            getGuestList({afl:dataValue.aflTeamVal, nrl:dataValue.nrlTeamVal, hosp:dataValue.hospitalityPref , bet:dataValue.bettingPref, eventAttendedQtr:dataValue.eventAttendedQtr, postalRange:dataValue.postalRange, tie:dataValue.tier, sta:dataValue.sta,allavailableState:detail.allavailableState})
            .then(result =>{
                    console.log('Filter Result ',result);
                    //this.guests = JSON.parse(JSON.stringify(result));
                    this.prepareUrl(result);
                     this.spinner=false;
                    })
            .catch(error =>{
                        this.spinner=false;
                        console.log('Error ',error);
                    })
            }else{
                this.intialize();
            }
        } catch (error) {
             this.spinner=false;
            console.log('error while applying filter>>',error);
        }
       
        
    }

    getRecordId(event)
    {
        var selectedRows = event.detail.selectedRows;
        console.log('DEBUG: selectedRows from event:', selectedRows);
        console.log('DEBUG: selectedRows length:', selectedRows.length);

        // Always update preSelectedRows with the current selection from the datatable
        this.preSelectedRows = selectedRows;
        console.log('DEBUG: preSelectedRows updated to:', this.preSelectedRows);
        console.log('DEBUG: preSelectedRows length:', this.preSelectedRows.length);

        if (this.preSelectedRows.length > 0) {
            console.log('DEBUG: First row ID:', this.preSelectedRows[0]?.Id);
            if (this.preSelectedRows.length > 1) {
                console.log('DEBUG: Multiple rows selected!');
                this.preSelectedRows.forEach((row, index) => {
                    console.log(`DEBUG: Row ${index + 1} ID:`, row.Id);
                });
            }
        }

        event.preventDefault();
        return;
    }

    @api
    firedFromAura(){
        console.log('In LWC firedFromAura');
        console.log('DEBUG: preSelectedRows:', this.preSelectedRows);
        console.log('DEBUG: preSelectedRows length:', this.preSelectedRows.length);

        if (this.preSelectedRows.length > 0) {
            // Create an array of account IDs from all selected rows
            const accountIds = this.preSelectedRows.map(row => {
                console.log('DEBUG: Processing row:', row);
                console.log('DEBUG: Row ID:', row.Id);
                return row.Id;
            });
            console.log('DEBUG: Final accountIds array:', accountIds);

            const value = {
                accountIds: accountIds,
                campaignId: this.selectedEventId,
                // Maintain backward compatibility with single accountId
                accountId: this.preSelectedRows[0].Id
            };
            const selectedEvent = new CustomEvent('selectedRow', {
                detail:{value},
            });
            //dispatching the custom event
            this.dispatchEvent(selectedEvent);
        }else{
            const event = new ShowToastEvent({
                title: 'Warning!',
                message: 'Select at least one row!',
                variant: 'warning',
                mode: 'dismissable'
            });
            this.dispatchEvent(event);
        }
    }


    
    /* ** Sorting  */
    onHandleSort(event) {
        console.log('Sorting event triggered',event.detail);
        const { fieldName: sortedBy, sortDirection } = event.detail;
        const cloneData = JSON.parse(JSON.stringify([...this.guests]));

        cloneData.sort(this.sortBy(sortedBy, sortDirection === 'asc' ? 1 : -1));
        this.prepareUrl(cloneData);
        this.sortDirection = sortDirection;
        this.sortedBy = sortedBy;
    }
    sortBy(field, reverse, primer) {
        const key = primer
            ? function (x) {
                return primer(x[field]);
            }
            : function (x) {
                return x[field];
            };

        return function (a, b) {
            a = key(a);
            b = key(b);
            return reverse * ((a > b) - (b > a));
        };
    }

    @api
    handleExclude(){
        console.log('In lwc : ');
        if (this.preSelectedRows.length > 0) {
            // Get all selected IDs
            const selectedIds = this.preSelectedRows.map(row => row.Id);

            // Filter out all selected rows at once
            this.guests = this.guests.filter(element => {
                return !selectedIds.includes(element.Id);
            });
            this.preSelectedRows = [];
        }else{
            const event = new ShowToastEvent({
                title: 'Warning!',
                message: 'Select at least one row!',
                variant: 'warning',
                mode: 'dismissable'
            });
            this.dispatchEvent(event);
        }
    }
    
}