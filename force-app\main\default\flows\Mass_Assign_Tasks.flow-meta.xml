<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>56.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>User_has_Access</name>
        <label>User has Access</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>No_Access_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Access</defaultConnectorLabel>
        <rules>
            <name>User_has_Access2</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Permission.Mass_Assign_Tasks_on_Activity_List_View</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Profile.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>System Administrator</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Mass_Reassign_Tasks</targetReference>
            </connector>
            <label>User has Access</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Mass Assign Tasks {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Mass Assign Tasks</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordUpdates>
        <name>Update_Tasks</name>
        <label>Update Tasks</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>ids</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>LookupUser.recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <screens>
        <name>Mass_Reassign_Tasks</name>
        <label>Mass Reassign Tasks</label>
        <locationX>50</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Update_Tasks</targetReference>
        </connector>
        <fields>
            <name>LookupUser</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>CreatedById</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>User</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>Contact</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxValues</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>selectedUserId</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_Access_Screen</name>
        <label>No Access Screen</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>errorDisplayTxt</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 16, 52); font-size: 14px;&quot;&gt;You do not have access to use this feature.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>User_has_Access</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedUserId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>taskRecordCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
