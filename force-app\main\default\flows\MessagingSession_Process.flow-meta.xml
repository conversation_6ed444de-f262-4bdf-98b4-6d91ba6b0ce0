<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Completed_Status_to_Open_task</name>
        <label>Assign Completed Status to Open task</label>
        <locationX>1370</locationX>
        <locationY>384</locationY>
        <assignmentItems>
            <assignToReference>openTaskID.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Open_Task</targetReference>
        </connector>
    </assignments>
    <customErrors>
        <name>Display_Error2</name>
        <label>Display Error 2</label>
        <locationX>842</locationX>
        <locationY>1224</locationY>
        <customErrorMessages>
            <errorMessage>An error occurred when trying to automatically create a task against this record. Please review error message below for more detail.
{!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Fault_error_message_is_for_Locked_Row</name>
        <label>Fault error message is for Locked Row</label>
        <locationX>710</locationX>
        <locationY>1116</locationY>
        <defaultConnector>
            <targetReference>Display_Error2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Outcome_1_of_Fault_error_message_is_for_Locked_Row</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isLockedRowError</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_CreateTask</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>IS_there_an_Existing_Task</name>
        <label>IS there an Existing Task?</label>
        <locationX>182</locationX>
        <locationY>792</locationY>
        <defaultConnector>
            <targetReference>Get_TaskRT</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes_Existing</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Existing_Task</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateTask</targetReference>
            </connector>
            <label>Yes Existing</label>
        </rules>
    </decisions>
    <decisions>
        <name>IsrecordNew</name>
        <label>IsNew?</label>
        <locationX>644</locationX>
        <locationY>276</locationY>
        <defaultConnector>
            <targetReference>MeasageReasonIschanged</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_Is_New</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_If_Customer_has_opted_out_of_MS</targetReference>
            </connector>
            <label>Yes Is New</label>
        </rules>
    </decisions>
    <decisions>
        <name>MeasageReasonIschanged</name>
        <label>MeasageReasonIschanged</label>
        <locationX>644</locationX>
        <locationY>576</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>YesIschanged</name>
            <conditionLogic>(1 AND 2) OR 3</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Message_Reason__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Message_Reason__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Customer__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Existing_Task</targetReference>
            </connector>
            <label>YesIschanged</label>
        </rules>
    </decisions>
    <decisions>
        <name>Was_a_task_created_in_Open_status</name>
        <label>Was a task created in Open status?</label>
        <locationX>1502</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>openTaskID.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>openTaskID</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Completed_Status_to_Open_task</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>This flow is used to process all automation when a messaging session is created or updated.
SBET-1181 - Populate Current Customer Owner on Interaction Tasks</description>
    <environments>Default</environments>
    <formulas>
        <name>DirectionFormula</name>
        <dataType>String</dataType>
        <expression>IF( TEXT({!$Record.Origin}) = &quot;InboundInitiated&quot;, &quot;Inbound&quot;,&quot;Outbound&quot;)</expression>
    </formulas>
    <formulas>
        <description>Used to give Flow Current Date in user timezone. Referencing the field directly in the Flow gives it in UTC timezone, but referencing in this formula gives it in the user timezone</description>
        <name>formulaCurrentDateAEST</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate}</expression>
    </formulas>
    <formulas>
        <name>isLockedRowError</name>
        <dataType>Boolean</dataType>
        <expression>CONTAINS({!$Flow.FaultMessage}, &quot;UNABLE_TO_LOCK_ROW&quot;)</expression>
    </formulas>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <formulas>
        <name>SubjectFormula</name>
        <dataType>String</dataType>
        <expression>&quot;SMS  - &quot; + {!DirectionFormula}  +  &quot; - &quot; + TEXT({!$Record.Message_Reason__c})</expression>
    </formulas>
    <interviewLabel>MessagingSession Process {!$Flow.CurrentDateTime}</interviewLabel>
    <label>MessagingSession Process</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Copy_2_of_CreateTask</name>
        <label>CreateTask (Open Status)</label>
        <locationX>578</locationX>
        <locationY>1224</locationY>
        <assignRecordIdToReference>openTaskID.Id</assignRecordIdToReference>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formulaCurrentDateAEST</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>SMS</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Messaging_Session_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <elementReference>DirectionFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner_Role__c</field>
            <value>
                <elementReference>$Record.Customer__r.Owner.UserRole.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner__c</field>
            <value>
                <elementReference>$Record.Customer_Owner__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Messaging_Session__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Message_Reason__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_TaskRT.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>$Record.Customer__r.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>$Record.Within_Business_Hours__c</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordCreates>
    <recordCreates>
        <name>CreateTask</name>
        <label>CreateTask</label>
        <locationX>314</locationX>
        <locationY>1008</locationY>
        <faultConnector>
            <targetReference>Fault_error_message_is_for_Locked_Row</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formulaCurrentDateAEST</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bulk_Interactions__c</field>
            <value>
                <elementReference>$Record.Bulk_Interactions__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>SMS</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Customer__r.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Messaging_Session_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <elementReference>DirectionFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner_Role__c</field>
            <value>
                <elementReference>$Record.Customer__r.Owner.UserRole.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner__c</field>
            <value>
                <elementReference>$Record.Customer_Owner__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Messaging_Session__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Message_Reason__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_TaskRT.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>$Record.Customer__r.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>$Record.Within_Business_Hours__c</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Existing_Task</name>
        <label>Get Existing Task</label>
        <locationX>182</locationX>
        <locationY>684</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>IS_there_an_Existing_Task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Messaging_Session__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_TaskRT</name>
        <label>Get TaskRT</label>
        <locationX>314</locationX>
        <locationY>900</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CreateTask</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Interaction</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Open_Task</name>
        <label>Update Open Task</label>
        <locationX>1370</locationX>
        <locationY>492</locationY>
        <inputReference>openTaskID</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateTask</name>
        <label>UpdateTask</label>
        <locationX>50</locationX>
        <locationY>900</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Existing_Task.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Bulk_Interactions__c</field>
            <value>
                <elementReference>$Record.Bulk_Interactions__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Customer__r.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Messaging_Session_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Message_Reason__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>$Record.Customer__r.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>$Record.Within_Business_Hours__c</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <start>
        <locationX>947</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>IsrecordNew</targetReference>
        </connector>
        <filterFormula>AND(
  OR(
      NOT(TEXT({!$Record.Origin}) = &apos;TriggeredOutbound&apos;),
      {!$Record.Bulk_Interactions__c}
  ),
  OR(
    AND(
      ISNEW()
    ),
    AND(
      OR(
        NOT(ISBLANK(TEXT({!$Record.Message_Reason__c}))),
        ISCHANGED({!$Record.Message_Reason__c})
      ),
      OR(
        NOT(ISBLANK({!$Record.Customer__c})),
        ISCHANGED({!$Record.Customer__c})
      )
    )
  )
)</filterFormula>
        <object>MessagingSession</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <scheduledPaths>
            <name>Run_30_minutes_Later</name>
            <connector>
                <targetReference>Was_a_task_created_in_Open_status</targetReference>
            </connector>
            <label>Run 30 minutes Later</label>
            <offsetNumber>30</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <recordField>LastModifiedDate</recordField>
            <timeSource>RecordField</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Check_If_Customer_has_opted_out_of_MS</name>
        <label>Check If Customer has opted out of MS</label>
        <locationX>512</locationX>
        <locationY>384</locationY>
        <connector>
            <targetReference>MeasageReasonIschanged</targetReference>
        </connector>
        <flowName>Customer_Opted_Out_Channel_Check</flowName>
        <inputAssignments>
            <name>MessagingSession</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>CaseFailed</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>FaultMessage</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Flow.FaultMessage</elementReference>
        </value>
    </variables>
    <variables>
        <name>openTaskID</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
