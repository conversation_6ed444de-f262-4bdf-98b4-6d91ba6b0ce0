<aura:component implements="force:hasRecordId,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:appHostable,forceCommunity:availableForAllPageTypes" access="global">

    <aura:attribute name="accountRecord" type="Object"/>
    <aura:attribute name="recordLoadError" type="String"/>
    
    <force:recordData aura:id="recordLoader"
    recordId="{!v.recordId}"
    fields="Customer_Id__c"
    targetFields="{!v.accountRecord}"
    targetError="{!v.recordLoadError}"/>
   
    <lightning:card>
    <aura:set attribute="title">
      <div class="slds-media slds-media_center">
        <div class="slds-media_figure">
          <lightning:icon iconName="standard:link" size="small" />
        </div>
        <div class="slds-media_body slds-m-left_small">
          <span class="slds-text-heading_small " style="font-weight:bold;">
            System Links
          </span>
        </div>
      </div>
    </aura:set>
        
    <div>
      <div class="slds-p-around_small">
        <lightning:layout horizontalAlign="spread" class="">
            
            <lightning:layoutItem
            flexibility="auto"
            padding="around-x-small"
            size="3"
            class="slds-align_absolute-center"
          >
              <a href="{!'https://sam.pub.sp.prd.sbet.cloud/customers/'+v.accountRecord.Customer_Id__c+'/customer'}" target="_blank">        
              <div class="itemPad slds-text-align_center theBox" onclick="">
                <div class="slds-p-bottom_x-small">
                  <lightning:icon
                    iconName="utility:contact"
                    size="medium"
                  />
                </div>
                <div class="slds-text-body_small">
                  <span class="">SAM Cust Record</span>
                </div>
              </div>
            </a>
          </lightning:layoutItem>
            
          <lightning:layoutItem
            flexibility="auto"
            padding="around-x-small"
            size="3"
            class="slds-align_absolute-center">
              <a href="{!'https://backoffice.sportsbet.com.au/campaign_manager?action=FREEBETS::GoAdhocTokensCustDetails&amp;CustId='+v.accountRecord.Customer_Id__c}" target="_blank">            
              <div class="itemPad slds-text-align_center theBox" onclick="">
                <div class="slds-p-bottom_x-small">
                  <lightning:icon
                    iconName="standard:customer_360"
                    size="medium"
                    class="iconStyle"
                  />
                </div>
                <div class="slds-text-body_small">
                  <span class="">Apply Generosity</span>
                </div>
              </div>
            </a>
          </lightning:layoutItem>

            
            <lightning:layoutItem
            flexibility="auto"
            padding="around-x-small"
            size="3"
            class="slds-align_absolute-center"
          >
              <a href="{!'https://sam.pub.sp.prd.sbet.cloud/customers/'+v.accountRecord.Customer_Id__c+'/communications'}" target="_blank">        
              <div class="itemPad slds-text-align_center theBox" onclick="">
                <div class="slds-p-bottom_x-small">
                  <lightning:icon
                    iconName="standard:related_list"
                    size="medium"
                    class="iconStyle"
                  />
                </div>
                <div class="slds-text-body_small">
                  <span class="">SAM Comms</span>
                </div>
              </div>
            </a>
          </lightning:layoutItem>
          <lightning:layoutItem
            flexibility="auto"
            padding="around-x-small"
            size="3"
            class="slds-align_absolute-center"
          >
              <a href="{!'https://backoffice.sportsbet.com.au/admin?action=ADMIN::CUST::GoCust&amp;CustId='+v.accountRecord.Customer_Id__c}" target="_blank">            
              <div class="itemPad slds-text-align_center theBox" onclick="">
                <div class="slds-p-bottom_x-small">
                  <lightning:icon
                    iconName="standard:performance"
                    size="medium"
                  />
                </div>
                <div class="slds-text-body_small">
                  <span class="">View in OpenBet</span>
                </div>
              </div>
            </a>
          </lightning:layoutItem>
            
          

          
        </lightning:layout>
      </div>
    </div>
  </lightning:card>
</aura:component>