// Debug script to check Account access and permissions
// Run this in Developer Console > Debug > Open Execute Anonymous Window

// Check the specific Account ID that's failing
String failingAccountId = '7018s00000hy83g'; // The ID from your error

System.debug('=== DEBUGGING ACCOUNT ACCESS ===');
System.debug('Checking Account ID: ' + failingAccountId);

try {
    // Try to query the Account with minimal fields
    Account testAccount = [SELECT Id, Name FROM Account WHERE Id = :failingAccountId LIMIT 1];
    System.debug('SUCCESS: Account found - Name: ' + testAccount.Name);
    
    // Try to query with more fields that the flow might need
    Account fullAccount = [
        SELECT Id, Name, OwnerId, Owner.Name, RecordTypeId, RecordType.Name
        FROM Account 
        WHERE Id = :failingAccountId 
        LIMIT 1
    ];
    System.debug('Account Owner: ' + fullAccount.Owner.Name);
    System.debug('Record Type: ' + fullAccount.RecordType.Name);
    
} catch (Exception e) {
    System.debug('ERROR accessing Account: ' + e.getMessage());
    System.debug('Error Type: ' + e.getTypeName());
}

// Check current user permissions
System.debug('=== CURRENT USER INFO ===');
System.debug('Current User ID: ' + UserInfo.getUserId());
System.debug('Current User Name: ' + UserInfo.getName());
System.debug('Profile Name: ' + [SELECT Name FROM Profile WHERE Id = :UserInfo.getProfileId()].Name);

// Check if there are any accessible Accounts
System.debug('=== CHECKING ACCESSIBLE ACCOUNTS ===');
List<Account> accessibleAccounts = [
    SELECT Id, Name, Sports_Bet_Account_Number__c, Owner.Name
    FROM Account 
    LIMIT 5
];

System.debug('Found ' + accessibleAccounts.size() + ' accessible accounts:');
for (Account acc : accessibleAccounts) {
    System.debug('Account ID: ' + acc.Id + ', Name: ' + acc.Name + ', SB Number: ' + acc.Sports_Bet_Account_Number__c + ', Owner: ' + acc.Owner.Name);
}

// Test if we can create a Case with an accessible Account
if (!accessibleAccounts.isEmpty()) {
    System.debug('=== TESTING CASE CREATION ===');
    try {
        Case testCase = new Case(
            Subject = 'Test Case for Account Access',
            AccountId = accessibleAccounts[0].Id,
            Status = 'New',
            Origin = 'Web'
        );
        
        // Don't actually insert, just test the assignment
        System.debug('SUCCESS: Can assign Account ID to Case');
        System.debug('Test Case Account ID: ' + testCase.AccountId);
        
    } catch (Exception e) {
        System.debug('ERROR creating test case: ' + e.getMessage());
    }
}
