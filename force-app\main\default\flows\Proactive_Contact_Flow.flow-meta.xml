<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Contact_Poactive</name>
        <label>Contact Poactive</label>
        <locationX>182</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Proactive_Contact</name>
            <conditionLogic>(1 AND 2 AND 3 )OR (2 AND 3 AND 4)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Channel__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Call</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Outbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Log_Method__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Manual Bulk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Channel__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Face-to-Face</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Proactive_Contact</targetReference>
            </connector>
            <label>Is Proactive Contact</label>
        </rules>
    </decisions>
    <description>updated moved get record after decision</description>
    <environments>Default</environments>
    <formulas>
        <description>the field will be populated by activity date</description>
        <name>activitydate</name>
        <dataType>Date</dataType>
        <expression>{!Proactive_Contact.Last_Interaction_Date__c}</expression>
    </formulas>
    <formulas>
        <name>DateToday</name>
        <dataType>Date</dataType>
        <expression>Today()</expression>
    </formulas>
    <interviewLabel>Proactive Contact Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Proactive Contact Flow2</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Proactive_Contact</name>
        <label>Proactive Contact</label>
        <locationX>50</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Proactive</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Is_Proactive</name>
        <label>Is Proactive</label>
        <locationX>50</locationX>
        <locationY>575</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Proactive_Contact_Date__c</field>
            <value>
                <elementReference>activitydate</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Contact_Poactive</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
    <variables>
        <name>CustomerProactiveField</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>datetask</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
