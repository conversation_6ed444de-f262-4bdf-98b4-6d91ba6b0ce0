<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Updates the Email Encoding to UTF-8 for users with Team Member Profile so that the Email Footer displays just for these users</description>
    <environments>Default</environments>
    <interviewLabel>User: Before Save - Team Member Email Encoding {!$Flow.CurrentDateTime}</interviewLabel>
    <label>User: Before Save - Team Member Email Encoding</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Updates User record for Team Member profile to UTF-8 Email Encoding</description>
        <name>Update_Premium_Users_to_UTF_8_Encoding</name>
        <label>Update Premium Users to UTF-8 Encoding</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <inputAssignments>
            <field>EmailEncodingKey</field>
            <value>
                <stringValue>UTF-8</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Premium_Users_to_UTF_8_Encoding</targetReference>
        </connector>
        <filterFormula>{!$Record.Profile.Name} = &apos;Team Member&apos;</filterFormula>
        <object>User</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
