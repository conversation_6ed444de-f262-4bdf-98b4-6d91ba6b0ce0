<aura:component implements="lightning:availableForFlowScreens,force:hasRecordId" access="global">
    <aura:attribute name="accountRecord" type="Object" />
    <aura:attribute name="recordLoadError" type="String" />

    <force:recordData aura:id="recordLoader" recordId="{!v.recordId}" fields="PersonMobilePhone" targetFields="{!v.accountRecord}" targetError="{!v.recordLoadError}" />
    
    <lightning:workspaceAPI aura:id="workspace" />	
    <aura:handler event="lightning:tabCreated" action="{!c.onTabCreated}"/> 


    <lightning:card>
        <lightning:clickToDial value="{!v.accountRecord.PersonMobilePhone}" recordId="{!v.recordId}" />
    </lightning:card>

</aura:component>