<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Check if Account Id(WhatId) or Contact Id(WhoId) is not Null</description>
        <name>Account_or_Contact_Id_is_NOT_Null</name>
        <label>Account or Contact Id is NOT Null</label>
        <locationX>380</locationX>
        <locationY>311</locationY>
        <defaultConnector>
            <targetReference>Related_To_and_Name_fields_are_Blank</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>AccountId and ContactId are Null</defaultConnectorLabel>
        <rules>
            <name>Account_Id_is_not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Customer_Details</targetReference>
            </connector>
            <label>Account Id is not Null</label>
        </rules>
        <rules>
            <name>Account_Id_is_Null_and_Contact_Id_is_not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WhoId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Contact_Details</targetReference>
            </connector>
            <label>Account Id is Null and Contact Id is not Null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Related_To_and_Name_fields_are_Blank</name>
        <label>Related To and Name fields are Blank</label>
        <locationX>710</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Related_TO_and_Name_fields_are_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WhoId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_SB_Accout_No_to_Null</targetReference>
            </connector>
            <label>Related TO and Name fields are Null</label>
        </rules>
    </decisions>
    <description>Add/Update Customer Sportsbet Account Number on Task record</description>
    <interviewLabel>Add Sportsbet Account Number on Task record {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Add Sportsbet Account Number on Task record</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Get the Contact Details based on WhoId</description>
        <name>Get_Contact_Details</name>
        <label>Get Contact Details</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_SB_Acct_No_using_Contact_Id</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhoId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Customer Details associated to the Task</description>
        <name>Get_Customer_Details</name>
        <label>Get Customer Details</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Sportsbet_Acct_No_filed</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update Sportsbet Account Number to Null if Related To and Name fields are blank</description>
        <name>Update_SB_Accout_No_to_Null</name>
        <label>Update SB Accout No to Null</label>
        <locationX>578</locationX>
        <locationY>551</locationY>
        <inputAssignments>
            <field>Sportsbet_Acct_No__c</field>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update Sportsbet Account Number based on WhoId(Contact Id)</description>
        <name>Update_SB_Acct_No_using_Contact_Id</name>
        <label>Update SB Acct No using Contact Id</label>
        <locationX>314</locationX>
        <locationY>551</locationY>
        <inputAssignments>
            <field>Sportsbet_Acct_No__c</field>
            <value>
                <elementReference>Get_Contact_Details.Account.Sports_Bet_Account_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update Task record with Customer Sportsbet Account Number</description>
        <name>Update_Sportsbet_Acct_No_filed</name>
        <label>Update Sportsbet Acct No filed</label>
        <locationX>50</locationX>
        <locationY>551</locationY>
        <inputAssignments>
            <field>Sportsbet_Acct_No__c</field>
            <value>
                <elementReference>Get_Customer_Details.Sports_Bet_Account_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Account_or_Contact_Id_is_NOT_Null</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Obsolete</status>
    <variables>
        <description>Stores Account Id of the Task record</description>
        <name>AccountId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>SBAcctNo</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
