<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Email_with_BCC_Events</name>
        <label>Send Email (with BCC)</label>
        <locationX>666</locationX>
        <locationY>2330</locationY>
        <actionName>InvokeableSendEmailAction</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Get_Interaction_Record_Type</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>bccAddress</name>
            <value>
                <elementReference>$User.Email</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logActivity</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>orgWideEmailAddressId</name>
            <value>
                <elementReference>fromAddress2.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>recordId.Account__r.PersonContact.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedToId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>templateId</name>
            <value>
                <elementReference>Get_Template.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvokeableSendEmailAction</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>58.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_to_Collection</name>
        <label>Add to Collection</label>
        <locationX>886</locationX>
        <locationY>1838</locationY>
        <assignmentItems>
            <assignToReference>emailTemplateCdl_Collection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>newContentDocumentLink</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Is_file_a_ticket</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Set_From_Email</name>
        <label>Set From Email</label>
        <locationX>798</locationX>
        <locationY>1214</locationY>
        <assignmentItems>
            <assignToReference>fromAddress2.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Copy_1_of_Get_Org_Wide_Email_ID.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Warning_prior_to_send</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>setFromEmail</name>
        <label>Set From Email</label>
        <locationX>534</locationX>
        <locationY>1214</locationY>
        <assignmentItems>
            <assignToReference>fromAddress2.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Org_Wide_Email_ID.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Warning_prior_to_send</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>EventsTeamChoice</name>
        <choiceText><EMAIL></choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Events Team Member</stringValue>
        </value>
    </choices>
    <choices>
        <name>RMChoice</name>
        <choiceText><EMAIL></choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Customer Owner</stringValue>
        </value>
    </choices>
    <decisions>
        <name>Does_Template_Exist</name>
        <label>Does Template Exist?</label>
        <locationX>696</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>isCustomerActive</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Template Assigned</defaultConnectorLabel>
        <rules>
            <name>Template_Not_Assigned</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Template_Assignment.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Template.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Template_Not_Available</targetReference>
            </connector>
            <label>Template Not Assigned</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_file_a_ticket</name>
        <label>Is file a ticket?</label>
        <locationX>886</locationX>
        <locationY>1946</locationY>
        <defaultConnector>
            <targetReference>For_Each_Assignment</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not a Ticket</defaultConnectorLabel>
        <rules>
            <name>Is_Ticket</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Ticket_File.Title</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>Allocated: </stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_File_Name</targetReference>
            </connector>
            <label>Is Ticket</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_the_Owner_an_Events_Team_Member</name>
        <label>Is the Owner an Events Team Member?</label>
        <locationX>864</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Copy_1_of_Get_Org_Wide_Email_ID</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Standard User</defaultConnectorLabel>
        <rules>
            <name>decIsEventsTeam</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Head of E&amp;E</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Premium Events &amp; Experiences</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Select_Sender</targetReference>
            </connector>
            <label>Is Events Team Member</label>
        </rules>
    </decisions>
    <decisions>
        <name>isCustomerActive</name>
        <label>Is customer Active/SE?</label>
        <locationX>1342</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>CustomerNotActiveScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Active/SE</defaultConnectorLabel>
        <rules>
            <name>decActive</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Account__r.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Account__r.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Attendee_File_Assignments</targetReference>
            </connector>
            <label>Customer is Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Option_Selected</name>
        <label>Option Selected</label>
        <locationX>666</locationX>
        <locationY>998</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_Get_Org_Wide_Email_ID</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Send as SBDirect</defaultConnectorLabel>
        <rules>
            <name>decEventsTeam</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>EventsTeamChoice</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Org_Wide_Email_ID</targetReference>
            </connector>
            <label>Send As Events Team</label>
        </rules>
    </decisions>
    <decisions>
        <name>Tickets_exist_to_send</name>
        <label>Tickets exist to send</label>
        <locationX>1095</locationX>
        <locationY>674</locationY>
        <defaultConnector>
            <targetReference>Warning_Screen1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Attendee_File_Assignments</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_the_Owner_an_Events_Team_Member</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>SBET-1247 - Set default from address for Email Tickets action (Revert back to original functionality)</description>
    <environments>Default</environments>
    <formulas>
        <name>newTicketTitle</name>
        <dataType>String</dataType>
        <expression>{!recordId.Account__r.Last_Name__c} + &quot; - &quot; + TRIM(RIGHT({!Get_Ticket_File.Title}, (LEN({!Get_Ticket_File.Title})-14)))</expression>
    </formulas>
    <formulas>
        <description>Formula to calculate today&apos;s date for custom field insertion (adjusted to daylight savings time)</description>
        <name>todayDate</name>
        <dataType>Date</dataType>
        <expression>NOW()+(11/24)</expression>
    </formulas>
    <interviewLabel>Email Tickets {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Email Tickets</label>
    <loops>
        <name>For_Each_Assignment</name>
        <label>For Each Assignment</label>
        <locationX>666</locationX>
        <locationY>1514</locationY>
        <collectionReference>Get_Attendee_File_Assignments</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Get_Ticket_File</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Send_Email_with_BCC_Events</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Creates the interaction task for the associated customer</description>
        <name>Create_Interaction_Task</name>
        <label>Create Interaction Task</label>
        <locationX>666</locationX>
        <locationY>2654</locationY>
        <connector>
            <targetReference>Delete_Ticket_Links</targetReference>
        </connector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>todayDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>taskDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <stringValue>Outbound</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <stringValue>Hospitality</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Interaction_Record_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Email - Outbound - Hospitality</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId.Account__r.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>Get_Customer_Contact_Id.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Link_to_Email_Template</name>
        <label>Create Link to Email Template</label>
        <locationX>886</locationX>
        <locationY>1730</locationY>
        <assignRecordIdToReference>newContentDocumentLink.Id</assignRecordIdToReference>
        <connector>
            <targetReference>Add_to_Collection</targetReference>
        </connector>
        <inputAssignments>
            <field>ContentDocumentId</field>
            <value>
                <elementReference>Get_Ticket_File.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LinkedEntityId</field>
            <value>
                <elementReference>Get_Template.Id</elementReference>
            </value>
        </inputAssignments>
        <object>ContentDocumentLink</object>
    </recordCreates>
    <recordDeletes>
        <name>Delete_Ticket_Links</name>
        <label>Delete Ticket Links</label>
        <locationX>666</locationX>
        <locationY>2762</locationY>
        <connector>
            <targetReference>Tickets_Sent_is_True</targetReference>
        </connector>
        <inputReference>emailTemplateCdl_Collection</inputReference>
    </recordDeletes>
    <recordLookups>
        <name>Copy_1_of_Get_Org_Wide_Email_ID</name>
        <label>Get Premium Email ID</label>
        <locationX>798</locationX>
        <locationY>1106</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_1_of_Set_From_Email</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Address</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Attendee_File_Assignments</name>
        <label>Get Attendee File Assignments</label>
        <locationX>1095</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Tickets_exist_to_send</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>LinkedEntityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContentDocumentLink</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Contact ID of the related customer to assign to the interaction task created</description>
        <name>Get_Customer_Contact_Id</name>
        <label>Get Customer Contact Id</label>
        <locationX>666</locationX>
        <locationY>2546</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Interaction_Task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Account__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the interaction record type for assignment to the upcoming task creation</description>
        <name>Get_Interaction_Record_Type</name>
        <label>Get Interaction Record Type</label>
        <locationX>666</locationX>
        <locationY>2438</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Customer_Contact_Id</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Interaction</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Org_Wide_Email_ID</name>
        <label>Get Events Email ID</label>
        <locationX>534</locationX>
        <locationY>1106</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>setFromEmail</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Address</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Template</name>
        <label>Get Template</label>
        <locationX>696</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Does_Template_Exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Template_Assignment.Template_API_name__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Template_Assignment</name>
        <label>Get Template Assignment</label>
        <locationX>696</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Template</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Venue_Name__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Campaign_Venue__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Location_in_Venue__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Campaign__r.Venue_Location__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Event_type__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Campaign__r.Type</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Email_Template_Assignment__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Ticket_File</name>
        <label>Get Ticket File</label>
        <locationX>886</locationX>
        <locationY>1622</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Link_to_Email_Template</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>For_Each_Assignment.ContentDocumentId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContentDocument</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Tickets_Sent_is_True</name>
        <label>Tickets Sent is True</label>
        <locationX>666</locationX>
        <locationY>2870</locationY>
        <connector>
            <targetReference>Send_Tickets_to_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Tickets_Sent__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_File_Name</name>
        <label>Update File Name</label>
        <locationX>754</locationX>
        <locationY>2054</locationY>
        <connector>
            <targetReference>For_Each_Assignment</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Ticket_File.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Title</field>
            <value>
                <elementReference>newTicketTitle</elementReference>
            </value>
        </inputAssignments>
        <object>ContentDocument</object>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>CustomerNotActiveScreen</name>
        <label>Customer Not Active</label>
        <locationX>1590</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Copy_1_of_message1</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 22px;&quot;&gt;Cannot Contact Customer&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;The customer you are trying to email is either inactive or is Self Excluded.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Select_Sender</name>
        <label>Select Sender</label>
        <locationX>666</locationX>
        <locationY>890</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Option_Selected</targetReference>
        </connector>
        <fields>
            <name>radioButton</name>
            <choiceReferences>EventsTeamChoice</choiceReferences>
            <choiceReferences>RMChoice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>RMChoice</defaultSelectedChoiceReference>
            <fieldText>What email address would you like to send this email from?</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Send_Tickets_to_Customer</name>
        <label>Send Tickets to Customer</label>
        <locationX>666</locationX>
        <locationY>2978</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMessage</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 22px;&quot;&gt;Success&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Tickets were sent to {!recordId.Account__r.First_Name__c} {!recordId.Account__r.Last_Name__c}&apos;s email address&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Template_Not_Available</name>
        <label>Template Not Available</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>message1</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 22px;&quot;&gt;Error&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;No Email Template has been assigned to this venue, location in the venue or event type. Contact your administrator. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Warning_prior_to_send</name>
        <label>Warning prior to send</label>
        <locationX>666</locationX>
        <locationY>1406</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Cancel</backButtonLabel>
        <connector>
            <targetReference>For_Each_Assignment</targetReference>
        </connector>
        <fields>
            <name>WarningText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;You are about to send an email to {!recordId.Account__r.First_Name__c} {!recordId.Account__r.Last_Name__c}. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;Do you wish to proceed?&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Send Email to Customer</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Warning_Screen1</name>
        <label>Warning Screen</label>
        <locationX>1326</locationX>
        <locationY>782</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_1_of_errorMessage</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 22px;&quot;&gt;Error&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;No tickets are available to send to {!recordId.Account__r.First_Name__c} {!recordId.Account__r.Last_Name__c}. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Please allocate tickets from the Event page before selecting this action. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>570</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Template_Assignment</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>taskDescription</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Tickets for {!recordId.Campaign__r.Name} were sent to the customer with venue details.</text>
    </textTemplates>
    <variables>
        <name>emailTemplateCdl_Collection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>ContentDocumentLink</objectType>
    </variables>
    <variables>
        <name>fromAddress2</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OrgWideEmailAddress</objectType>
    </variables>
    <variables>
        <name>newContentDocumentLink</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContentDocumentLink</objectType>
    </variables>
    <variables>
        <name>newContentDocumentLink2</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContentDocumentLink</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
</Flow>
