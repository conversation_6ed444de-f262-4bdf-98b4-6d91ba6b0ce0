<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Is_Record_New</name>
        <label>Is Record New</label>
        <locationX>182</locationX>
        <locationY>395</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Record_is_New</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Person_Opt_In_RM_Phone</targetReference>
            </connector>
            <label>Record is New</label>
        </rules>
    </decisions>
    <description>SBET-544 - Auto opt-in customers to RM phone on create</description>
    <environments>Default</environments>
    <interviewLabel>Customer Workflow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer Workflows</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Set_First_Last_Name_update</name>
        <label>Set First Last Name update</label>
        <locationX>182</locationX>
        <locationY>287</locationY>
        <connector>
            <targetReference>Is_Record_New</targetReference>
        </connector>
        <inputAssignments>
            <field>First_Name__c</field>
            <value>
                <elementReference>$Record.FirstName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Last_Name__c</field>
            <value>
                <elementReference>$Record.LastName</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Set_Person_Opt_In_RM_Phone</name>
        <label>Set Opt In for RM Phone</label>
        <locationX>50</locationX>
        <locationY>503</locationY>
        <inputAssignments>
            <field>Communications_via_RM_Phone__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Set_First_Last_Name_update</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsPersonAccount</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
