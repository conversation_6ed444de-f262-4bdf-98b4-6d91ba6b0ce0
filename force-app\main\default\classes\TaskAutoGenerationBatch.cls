/**
 * @description       : Batch job to automatically generate tasks for eligible customers
 * <AUTHOR> <PERSON> (8Squad)
 * @group             : 
 * @last modified on  : 05-23-2025
 * @last modified by  : jam<PERSON><PERSON><PERSON>@gmail.com
**/
public class TaskAutoGenerationBatch implements Database.Batchable<sObject> {
    private Decimal generosityLimit;
    
    public TaskAutoGenerationBatch() {
        // Get the generosity limit from the custom setting
        Process_Automation_Control_Panel__c settings = Process_Automation_Control_Panel__c.getInstance();
        this.generosityLimit = settings.Task_Batch_Customer_Generosity_Limit__c;
        System.debug('Initialized batch with generosity limit: ' + this.generosityLimit);
    }
    
    public Database.QueryLocator start(Database.BatchableContext BC) {
        // Query for eligible customers
        String query = 'SELECT Id, OwnerId, Sports_Bet_Account_Number__c, ' +
                      'Generosity_TPV_Last_12_months__c, Main_Bookie__c, ' +
                      'Bets_With_Competitor__c ' +
                      'FROM Account ' +
                      'WHERE RecordType.DeveloperName = \'PersonAccount\' ' +
                      'AND Elite_Status__c = \'Elite\' ' +
                      'AND Portfolio__c IN (\'Protect\', \'Grow\') ' +
                      'AND (' +
                      'Generosity_TPV_Last_12_months__c < ' + generosityLimit +
                      ' OR Main_Bookie__c != \'Sportsbet\' ' +
                      'OR Bets_With_Competitor__c != NULL' +
                      ')';
        
        System.debug('Executing query: ' + query);
        return Database.getQueryLocator(query);
    }
    
    public void execute(Database.BatchableContext BC, List<Account> scope) {
        System.debug('Processing ' + scope.size() + ' accounts in batch');
        List<Task> tasksToInsert = new List<Task>();
        
        // Get the Task record type ID
        Id taskRecordTypeId = Schema.SObjectType.Task.getRecordTypeInfosByDeveloperName().get('Task').getRecordTypeId();
        
        for (Account acc : scope) {
            System.debug('Processing account: ' + acc.Sports_Bet_Account_Number__c);
            
            // Create task based on generosity
            if (acc.Generosity_TPV_Last_12_months__c != null && 
                acc.Generosity_TPV_Last_12_months__c < generosityLimit) {
                // Fix the description format to match exactly what's required
                String description = 'Customer\'s generosity percent of ' + 
                                    acc.Generosity_TPV_Last_12_months__c + 
                                    '% is below the 25% limit.';
                Task t = createTask(acc, description);
                t.RecordTypeId = taskRecordTypeId; // Explicitly set the record type
                tasksToInsert.add(t);
                System.debug('Added generosity task for account: ' + acc.Sports_Bet_Account_Number__c + ' with description: ' + description);
            }
            
            // Create task based on main bookie
            if (acc.Main_Bookie__c != null && acc.Main_Bookie__c != 'Sportsbet') {
                String description = 'Customer\'s main bookie is ' + acc.Main_Bookie__c;
                Task t = createTask(acc, description);
                t.RecordTypeId = taskRecordTypeId; // Explicitly set the record type
                tasksToInsert.add(t);
                System.debug('Added main bookie task for account: ' + acc.Sports_Bet_Account_Number__c + ' with description: ' + description);
            }
            
            // Create task based on competitor betting
            if (acc.Bets_With_Competitor__c != null && 
                acc.Bets_With_Competitor__c != 'Sportsbet') {
                String description = 'Customer bets with competitors: ' + acc.Bets_With_Competitor__c + '.';
                Task t = createTask(acc, description);
                t.RecordTypeId = taskRecordTypeId; // Explicitly set the record type
                tasksToInsert.add(t);
                System.debug('Added competitor betting task for account: ' + acc.Sports_Bet_Account_Number__c + ' with description: ' + description);
            }
        }
        
        System.debug('Preparing to insert ' + tasksToInsert.size() + ' tasks');
        
        if (!tasksToInsert.isEmpty()) {
            try {
                insert tasksToInsert;
                System.debug('Successfully inserted ' + tasksToInsert.size() + ' tasks');
                
                // Log the inserted tasks for verification
                for (Task t : tasksToInsert) {
                    System.debug('Inserted task: ' + t.Subject + ' - ' + t.Description);
                }
            } catch (Exception e) {
                System.debug('Error inserting tasks: ' + e.getMessage());
            }
        }
    }
    
    private Task createTask(Account acc, String description) {
        return new Task(
            OwnerId = acc.OwnerId,
            WhatId = acc.Id,
            Subject = 'Customer Eligible for Value: ' + acc.Sports_Bet_Account_Number__c,
            ActivityDate = Date.today(),
            Description = description,
            Priority = 'Normal',
            Status = 'Open',
            RecordTypeId = Schema.SObjectType.Task.getRecordTypeInfosByDeveloperName().get('Task').getRecordTypeId()
        );
    }
    
    public void finish(Database.BatchableContext BC) {
        System.debug('Batch job completed');
    }
}