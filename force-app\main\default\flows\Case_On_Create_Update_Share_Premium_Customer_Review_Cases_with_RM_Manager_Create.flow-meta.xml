<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Is_Current_Owner_the_Case_Creator</name>
        <label>Is Current Owner the Case Creator?</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>Was_case_created_by_a_Relationship_Manager</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Current_Owner_is_Case_Creator</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>$Record.CreatedById</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Create_Case_Share_Record</targetReference>
            </connector>
            <label>Current Owner is not Case Creator</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_this_a_Premium_Customer_Review_case</name>
        <label>Is this a Premium Customer Review case?</label>
        <locationX>380</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Get_Review_Case_Record_Type_ID.Id</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_Current_Owner_the_Case_Creator</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Was_case_created_by_a_Relationship_Manager</name>
        <label>Was case created by a Relationship Manager?</label>
        <locationX>182</locationX>
        <locationY>839</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Role_contains_Relationship_Manager</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CreatedBy.UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Relationship Manager</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>$Record.CreatedBy.ManagerId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Case_Share_Record</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>SBET-955 - CRI - Sharing cases to Team Managers to view subordinates Cases</description>
    <environments>Default</environments>
    <interviewLabel>Case On Create/Update - Share Premium Customer Review Cases with RM Manager/Created By User {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case On Create/Update - Share Premium Customer Review Cases with RM Manager/Created By User</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Copy_2_of_Create_Case_Share_Record</name>
        <label>Create Case Share for Created By User</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Was_case_created_by_a_Relationship_Manager</targetReference>
        </connector>
        <inputAssignments>
            <field>CaseAccessLevel</field>
            <value>
                <stringValue>Read</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CaseId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RowCause</field>
            <value>
                <stringValue>Manual</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>UserOrGroupId</field>
            <value>
                <elementReference>$Record.CreatedById</elementReference>
            </value>
        </inputAssignments>
        <object>CaseShare</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Case_Share_Record</name>
        <label>Create Case Share Record for Created By Manager</label>
        <locationX>50</locationX>
        <locationY>947</locationY>
        <inputAssignments>
            <field>CaseAccessLevel</field>
            <value>
                <stringValue>Edit</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CaseId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RowCause</field>
            <value>
                <stringValue>Manual</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>UserOrGroupId</field>
            <value>
                <elementReference>$Record.CreatedBy.ManagerId</elementReference>
            </value>
        </inputAssignments>
        <object>CaseShare</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Review_Case_Record_Type_ID</name>
        <label>Get Review Case Record Type ID</label>
        <locationX>380</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_this_a_Premium_Customer_Review_case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium Customer Review</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Review_Case_Record_Type_ID</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OwnerId</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
