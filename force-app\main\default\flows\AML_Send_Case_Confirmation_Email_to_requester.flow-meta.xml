<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Send LER Case Creation Acknowledgement Email to the Requester Email.</description>
        <name>Send_Email_to_Requester</name>
        <label>Send Email to Requester</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>$Record.Requester_Email__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>texttemplateLERAcknowledgementSubject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>texttemplateLERAcknowledgementBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <environments>Default</environments>
    <interviewLabel>AML - Send Case Confirmation Email to requester {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML - Send Case Confirmation Email to requester</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Send_Email_to_Requester</targetReference>
        </connector>
        <filterFormula>AND(
{!$Record.RecordType.DeveloperName} = &apos;Non_Transaction_Monitoring&apos;,
NOT(ISBLANK({!$Record.Requester_Name__c})),
NOT(ISBLANK({!$Record.Requester_Email__c}))
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>This email will sent to the requester email field on case when a Non Transaction Monitoring Record Type Case is created.</description>
        <name>texttemplateLERAcknowledgementBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Hi {!$Record.Requester_Name__c},
Thank you for your email. It has been received by our team and will be actioned within 10 business days.
If you have further information you wish to add please feel free to reply to this email.

Kind Regards,
Sportsbet</text>
    </textTemplates>
    <textTemplates>
        <name>texttemplateLERAcknowledgementSubject</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Case {!$Record.CaseNumber} has been created.</text>
    </textTemplates>
</Flow>
