<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <choices>
        <name>StatusChoice0</name>
        <choiceText>Offer Accepted</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Offer Accepted</stringValue>
        </value>
    </choices>
    <choices>
        <name>StatusChoice1</name>
        <choiceText>Attended</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Attended</stringValue>
        </value>
    </choices>
    <choices>
        <name>StatusChoice2</name>
        <choiceText>No Show</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>No Show</stringValue>
        </value>
    </choices>
    <decisions>
        <name>Campaign_Capacity</name>
        <label>Campaign Capacity</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>New_Attendee_Customer_Attendee_0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Capacity_Reached_Outcome</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>vipSeatsAvailable</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CapacityReachedScreen</targetReference>
            </connector>
            <label>Capacity Reached</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_VIP_meets</name>
        <label>Enough seats available for VIP?</label>
        <locationX>578</locationX>
        <locationY>674</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>New_Attendee_Customer_Attendee_0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>vipSeatsAvailable</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>Number_of_Seats_0</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Attendee_0</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Has_Access_to_Add_VIP_Attendee</name>
        <label>Has Access to Add VIP Attendee</label>
        <locationX>710</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>No_Access</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Has_Access</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Permission.Add_VIP_on_Campaign</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Profile.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>System Administrator</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Record_Type_Id</targetReference>
            </connector>
            <label>Has Access</label>
        </rules>
    </decisions>
    <description>SBET-1184 - VIP Flow Review - Improve Capacity Validations</description>
    <dynamicChoiceSets>
        <name>AttendeeStatus</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Status__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>ChannelPicklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Channel__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>DirectionPicklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Direction__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>ClickHere</name>
        <dataType>String</dataType>
        <expression>LEFT({!$Api.Partner_Server_URL_340}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_340})) 
 &amp; {!AttendeeRecordID}</expression>
    </formulas>
    <formulas>
        <name>CurrentUserProfileName</name>
        <dataType>String</dataType>
        <expression>{!$Profile.Name}</expression>
    </formulas>
    <formulas>
        <name>IsCampaignRecord</name>
        <dataType>Boolean</dataType>
        <expression>IF(LEFT({!recordId},3) =&quot;701&quot;, TRUE, FALSE)</expression>
    </formulas>
    <formulas>
        <name>Today</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <description>variable to calculate the number of seats that remain as allocated for VIPs and Guests</description>
        <name>vipSeatsAvailable</name>
        <dataType>Number</dataType>
        <expression>{!Get_Campaign_Details_2.Other_Sportsbet_Staff_Staff_guests__c} -{!Get_Campaign_Details_2.VIP_Guests_Confirmed_Seats__c}</expression>
        <scale>0</scale>
    </formulas>
    <interviewLabel>Add VIP Attendee {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Add VIP Attendee</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Attendee_0</name>
        <label>Create Attendee</label>
        <locationX>314</locationX>
        <locationY>782</locationY>
        <assignRecordIdToReference>AttendeeRecordID</assignRecordIdToReference>
        <connector>
            <targetReference>Get_Attendee_Record_Name</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Fault_Screen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Campaign__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_Invited__c</field>
            <value>
                <elementReference>Date_Invited_0</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_of_Birth__c</field>
            <value>
                <elementReference>Date_of_Birth</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email_Guest__c</field>
            <value>
                <elementReference>Guest_Email.value</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Guest_Name__c</field>
            <value>
                <elementReference>Guest_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Notes__c</field>
            <value>
                <elementReference>Notes_0</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Number_Of_Seats__c</field>
            <value>
                <elementReference>Number_of_Seats_0</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>CARecordTypeId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Seat_Table_Number__c</field>
            <value>
                <elementReference>Seat_Table_Number_0</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <elementReference>Status_0</elementReference>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordCreates>
    <recordLookups>
        <name>Get_Attendee_Record_Name</name>
        <label>Get Attendee Record Name</label>
        <locationX>314</locationX>
        <locationY>890</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>End</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AttendeeRecordID</elementReference>
            </value>
        </filters>
        <object>Attendee__c</object>
        <outputAssignments>
            <assignToReference>EventParticipationCapExceeded</assignToReference>
            <field>Event_Participation_Cap_Exceeded__c</field>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>AttendeeName</assignToReference>
            <field>Guest_Name__c</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>Get_Campaign_Details_2</name>
        <label>Get Campaign Details 2</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Campaign_Capacity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Campaign</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Record_Type_Id</name>
        <label>Get Record Type Id</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Campaign_Details_2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attendee__c</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>VIP_Attendee</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>CARecordTypeId</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <screens>
        <name>CapacityReachedScreen</name>
        <label>CapacityReachedScreen</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>CapacityReachedMessage</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;Error:&lt;/b&gt;&lt;span style=&quot;font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt; This event has reached its capacity.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; color: rgb(18, 106, 178);&quot;&gt;_________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>End</name>
        <label>End</label>
        <locationX>314</locationX>
        <locationY>998</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessPage</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Attendee {!AttendeeName} was created.&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; color: rgb(18, 106, 178);&quot;&gt;_________________________________________&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;Click &lt;/span&gt;&lt;a href=&quot;{!ClickHere}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot; style=&quot;font-size: 18px; color: rgb(0, 100, 199);&quot;&gt;&lt;b&gt;here&lt;/b&gt;&lt;/a&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt; to view your Attendee record.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Fault_Screen</name>
        <label>Fault Screen</label>
        <locationX>578</locationX>
        <locationY>890</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FaultMessage</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); font-size: 12px;&quot;&gt;Error:&lt;/b&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 12px;&quot;&gt; {!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; color: rgb(18, 106, 178);&quot;&gt;_________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>New_Attendee_Customer_Attendee_0</name>
        <label>New Attendee: VIP Attendee</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Check_VIP_meets</targetReference>
        </connector>
        <fields>
            <name>New_Attendee_Customer_Attendee_0_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>New_Attendee_Customer_Attendee_0_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Information_0</name>
                    <fieldText>&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Information&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 14px;&quot;&gt;_________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>CampaignReadOnly</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;*&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Campaign&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 16px; background-color: rgb(255, 255, 255);&quot;&gt;{!Get_Campaign_Details_2.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Guest_Name</name>
                    <dataType>String</dataType>
                    <fieldText>Guest Name</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>Date_of_Birth</name>
                    <dataType>Date</dataType>
                    <fieldText>Date of Birth</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Guest_Email</name>
                    <extensionName>flowruntime:email</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Guest Email</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>Status_0</name>
                    <choiceReferences>StatusChoice0</choiceReferences>
                    <choiceReferences>StatusChoice1</choiceReferences>
                    <choiceReferences>StatusChoice2</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Status_0</elementReference>
                    </defaultValue>
                    <fieldText>Status</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <helpText>&lt;p&gt;Populate to determine the Status in relation to this Event&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>Date_Invited_0</name>
                    <dataType>Date</dataType>
                    <defaultValue>
                        <elementReference>Today</elementReference>
                    </defaultValue>
                    <fieldText>Date Invited</fieldText>
                    <fieldType>InputField</fieldType>
                    <helpText>&lt;p&gt;This field is used to capture the Date that the Customer was invited to the Event.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Number_of_Seats_0</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Number_of_Seats_0</elementReference>
                    </defaultValue>
                    <fieldText>Number of Seats</fieldText>
                    <fieldType>InputField</fieldType>
                    <helpText>&lt;p&gt;Populate to capture the number of seats for this Customer. For example, if this Customer is attending with one other guest. They will be assigned two seats.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <scale>0</scale>
                    <validationRule>
                        <errorMessage>&lt;p&gt;&lt;span style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255);&quot;&gt;This field can only accept up to 6 characters.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                        <formulaExpression>LEN(TEXT({!Number_of_Seats_0})) &lt;= 6</formulaExpression>
                    </validationRule>
                </fields>
                <fields>
                    <name>notEnoughSeatsError</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 3, 3);&quot;&gt;You are trying to assign more seats to this VIP Attendee than are available. Number of remaining seats: &lt;/span&gt;&lt;strong style=&quot;color: rgb(255, 3, 3);&quot;&gt;{!vipSeatsAvailable}&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>vipSeatsAvailable</leftValueReference>
                            <operator>LessThan</operator>
                            <rightValue>
                                <elementReference>Number_of_Seats_0</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Seat_Table_Number_0</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Seat_Table_Number_0</elementReference>
                    </defaultValue>
                    <fieldText>Seat/Table Number</fieldText>
                    <fieldType>InputField</fieldType>
                    <helpText>&lt;p&gt;Populate the Seat Number or Table Number that the Customer has been assigned for this Event.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <scale>0</scale>
                    <validationRule>
                        <errorMessage>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(62, 62, 60);&quot;&gt;This field can only accept up to 16 characters.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                        <formulaExpression>LEN(TEXT({!Seat_Table_Number_0})) &lt;= 16</formulaExpression>
                    </validationRule>
                </fields>
                <fields>
                    <name>Notes_0</name>
                    <defaultValue>
                        <stringValue>{!Notes_0}</stringValue>
                    </defaultValue>
                    <fieldText>Notes</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <helpText>&lt;p&gt;Populate any relevant Notes for this Attendee/Attendee&apos;s Guest(s). Eg. - Dietary Requirements, Weight (for Chopper) etc.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>No_Access</name>
        <label>No Access</label>
        <locationX>1106</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NoAccessDisplayTxt</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0); font-size: 16px;&quot;&gt;You don&apos;t have access to add VIP Attendees&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>584</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Has_Access_to_Add_VIP_Attendee</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>test</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&amp;lt;h1&amp;gt;&amp;lt;mark&amp;gt;Test&amp;lt;/mark&amp;gt;&amp;lt;/h1&amp;gt;&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>AttendeeDupe</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AttendeeName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AttendeeRecordID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AttendeeRecordVariable</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
    <variables>
        <name>CARecordTypeId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>EventParticipationCapExceeded</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>RGScoreVar</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
</Flow>
