<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>57.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_task_owner_as_customer_owner</name>
        <label>Assign task owner as customer owner</label>
        <locationX>308</locationX>
        <locationY>755</locationY>
        <assignmentItems>
            <assignToReference>customerOwner.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Customer_Owner.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Fields_Changed_Decision</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_task_owner_as_SysAdmin</name>
        <label>Assign task owner as SysAdmin</label>
        <locationX>572</locationX>
        <locationY>863</locationY>
        <assignmentItems>
            <assignToReference>customerOwner.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_SysAdmin_User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Fields_Changed_Decision</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Task_Status_Closed_by_System</name>
        <label>Set Task Status - Closed by System</label>
        <locationX>572</locationX>
        <locationY>1463</locationY>
        <assignmentItems>
            <assignToReference>taskStatusVar</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Task_Record_Type_Id</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Task_Status_Open</name>
        <label>Set Task Status - Open</label>
        <locationX>308</locationX>
        <locationY>1463</locationY>
        <assignmentItems>
            <assignToReference>taskStatusVar</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Task_Record_Type_Id</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Task_Subject_Email</name>
        <label>Set Task Subject - Email</label>
        <locationX>440</locationX>
        <locationY>1163</locationY>
        <assignmentItems>
            <assignToReference>taskSubjectVar</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Alert - Customer has opted out of RM Email</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Customer_Active</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Task_Subject_Email_and_SMS</name>
        <label>Set Task Subject - Email and SMS</label>
        <locationX>176</locationX>
        <locationY>1163</locationY>
        <assignmentItems>
            <assignToReference>taskSubjectVar</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Alert - Customer has opted out of RM Email &amp; SMS</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Customer_Active</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Task_Subject_SMS</name>
        <label>Set Task Subject - SMS</label>
        <locationX>704</locationX>
        <locationY>1163</locationY>
        <assignmentItems>
            <assignToReference>taskSubjectVar</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Alert - Customer has opted out of RM SMS</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Customer_Active</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_Customer_Owner</name>
        <label>Check Customer Owner</label>
        <locationX>176</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>Check_Customer_Owner_is_Active</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not a System User</defaultConnectorLabel>
        <rules>
            <name>Integration_or_Kafka</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Lookup_Customer_Details.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>00528000004OfqPAAS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Lookup_Customer_Details.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0052y000002BsoVAAS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Lookup_Customer_Details.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0052y000000He8UAAS</stringValue>
                </rightValue>
            </conditions>
            <label>Integration or Kafka or Premium Service</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Customer_Owner_is_Active</name>
        <label>Check Customer Owner is Active</label>
        <locationX>440</locationX>
        <locationY>647</locationY>
        <defaultConnector>
            <targetReference>Get_SysAdmin_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Owner.IsActive</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_task_owner_as_customer_owner</targetReference>
            </connector>
            <label>Is active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Customer_Active</name>
        <label>Customer Active?</label>
        <locationX>440</locationX>
        <locationY>1355</locationY>
        <defaultConnector>
            <targetReference>Set_Task_Status_Closed_by_System</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Customer Not Active</defaultConnectorLabel>
        <rules>
            <name>Customer_is_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Lookup_Customer_Details.Account_Status_Bucket__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_Status_Open</targetReference>
            </connector>
            <label>Customer is Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Fields_Changed_Decision</name>
        <label>Fields Changed Decision</label>
        <locationX>440</locationX>
        <locationY>1055</locationY>
        <defaultConnector>
            <targetReference>Set_Task_Subject_SMS</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Opted Out of SMS</defaultConnectorLabel>
        <rules>
            <name>Opted_Out_of_SMS_and_Email</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_SMS__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_Subject_Email_and_SMS</targetReference>
            </connector>
            <label>Opted Out of SMS and Email</label>
        </rules>
        <rules>
            <name>Opted_Out_of_Email</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_Subject_Email</targetReference>
            </connector>
            <label>Opted Out of Email</label>
        </rules>
    </decisions>
    <description>SBET-572 CCC - Update to task record type for Cassie opt out task + scenario for inactive customer owner</description>
    <environments>Default</environments>
    <formulas>
        <name>taskActivityDateFormula</name>
        <dataType>DateTime</dataType>
        <expression>{!$Flow.CurrentDateTime}+3</expression>
    </formulas>
    <interviewLabel>Customer Consent - Task for RM When Opted Out by Cassie {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer Consent - Task for RM When Opted Out by Cassie</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Task_for_RM</name>
        <label>Create Task for RM</label>
        <locationX>440</locationX>
        <locationY>1763</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>taskActivityDateFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Contact Customer by opted in channel to confirm RM contact Preferences</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>customerOwner.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Task_Record_Type_Id.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <elementReference>taskStatusVar</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>taskSubjectVar</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>Lookup_Customer_Details.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>Lookup_Customer_Details.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Customer_Owner</name>
        <label>Get Customer Owner</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Customer_Owner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Lookup_Customer_Details.OwnerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_SysAdmin_User</name>
        <label>Get SysAdmin User</label>
        <locationX>572</locationX>
        <locationY>755</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_task_owner_as_SysAdmin</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Username</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Task_Record_Type_Id</name>
        <label>Get Task Record Type Id</label>
        <locationX>440</locationX>
        <locationY>1655</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Task_for_RM</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Lookup_Customer_Details</name>
        <label>Lookup Customer Details</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Customer_Owner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Lookup_Customer_Details</targetReference>
        </connector>
        <filterLogic>((1 AND 2) OR (3 AND 4)) AND 5</filterLogic>
        <filters>
            <field>Communications_via_RM_Email__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Communications_via_RM_Email__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Communications_via_RM_SMS__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Communications_via_RM_SMS__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Source__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>SALESFORCE</stringValue>
            </value>
        </filters>
        <object>Customer_Consent__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>customerOwner</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
    <variables>
        <name>taskStatusVar</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>taskSubjectVar</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
