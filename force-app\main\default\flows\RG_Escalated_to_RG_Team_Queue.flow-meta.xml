<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>RG_AML_Case_Assignment</name>
        <label>RG AML Case Assignment</label>
        <locationX>693</locationX>
        <locationY>292</locationY>
        <actionName>Case.RG_Alert_AML_Case_assignment</actionName>
        <actionType>emailAlert</actionType>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>VarCaseId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.RG_Alert_AML_Case_assignment</nameSegment>
    </actionCalls>
    <apiVersion>50.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>ECDD Case to RG Escalated</description>
    <interviewLabel>RG Escalated to RG Team Queue {!$Flow.CurrentDateTime}</interviewLabel>
    <label>RG Escalated to RG Team Queue</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Analyst_owner</name>
        <label>Get Analyst owner</label>
        <locationX>321</locationX>
        <locationY>381</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetUserName</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>VarCaseId</elementReference>
            </value>
        </filters>
        <object>Case</object>
        <outputAssignments>
            <assignToReference>VarUserId</assignToReference>
            <field>OwnerId</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>Get_RG_Queue</name>
        <label>Get RG Queue</label>
        <locationX>254</locationX>
        <locationY>263</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Analyst_owner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>RG_Team</stringValue>
            </value>
        </filters>
        <object>Group</object>
        <outputAssignments>
            <assignToReference>VarRGQueueId</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>GetUserName</name>
        <label>GetUserName</label>
        <locationX>419</locationX>
        <locationY>227</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Case_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>VarUserId</elementReference>
            </value>
        </filters>
        <object>User</object>
        <outputAssignments>
            <assignToReference>VarAnalystName</assignToReference>
            <field>Name</field>
        </outputAssignments>
    </recordLookups>
    <recordUpdates>
        <name>Update_Case_Queue</name>
        <label>Update Case Queue</label>
        <locationX>511</locationX>
        <locationY>379</locationY>
        <connector>
            <targetReference>RG_AML_Case_Assignment</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>VarCaseId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>ECDD_Customer_Security_Analyst__c</field>
            <value>
                <elementReference>VarAnalystName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>VarRGQueueId</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>Get_RG_Queue</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>VarAnalystName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>VarCaseId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>VarRGQueueId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>VarUserId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
</Flow>
