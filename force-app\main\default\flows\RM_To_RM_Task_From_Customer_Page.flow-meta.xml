<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_the_record_owner</name>
        <label>Add the record owner</label>
        <locationX>182</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>varUserListCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_the_record_owner</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>sort_assign_to_list</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_selected_user</name>
        <label>Assign selected user</label>
        <locationX>50</locationX>
        <locationY>1214</locationY>
        <assignmentItems>
            <assignToReference>var_assigned_to</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Assigned_To1</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Task_in_case</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Action_Required</name>
        <choiceText>Action Required</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Action_Required</stringValue>
        </value>
    </choices>
    <choices>
        <name>FYI</name>
        <choiceText>FYI</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>FYI</stringValue>
        </value>
    </choices>
    <choices>
        <name>Medium</name>
        <choiceText>Medium</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Medium</stringValue>
        </value>
    </choices>
    <collectionProcessors>
        <name>sort_assign_to_list</name>
        <elementSubtype>SortCollectionProcessor</elementSubtype>
        <label>sort assign to list</label>
        <locationX>182</locationX>
        <locationY>674</locationY>
        <collectionProcessorType>SortCollectionProcessor</collectionProcessorType>
        <collectionReference>varUserListCollection</collectionReference>
        <connector>
            <targetReference>Get_Records_for_Case</targetReference>
        </connector>
        <sortOptions>
            <doesPutEmptyStringAndNullFirst>true</doesPutEmptyStringAndNullFirst>
            <sortField>Name</sortField>
            <sortOrder>Asc</sortOrder>
        </sortOptions>
    </collectionProcessors>
    <constants>
        <name>varTeammember</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Team Member</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Has_a_user_been_choosen</name>
        <label>Has a user been choosen</label>
        <locationX>182</locationX>
        <locationY>1106</locationY>
        <defaultConnector>
            <targetReference>Create_Task_in_case</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Assigned_To1</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_selected_user</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>Removed the RB Review Comments.</description>
    <dynamicChoiceSets>
        <name>Task</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Type</picklistField>
        <picklistObject>Task</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>varUserChoiceSet</name>
        <collectionReference>varUserListCollection</collectionReference>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <object>User</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <description>check if the assigned to picklist has been used: return default customer owner, otherwise, return selected assigned to value</description>
        <name>ReturnAssignedOwnerID</name>
        <dataType>String</dataType>
        <expression>IF({!var_assigned_to}==null, {!get_the_customer_record.OwnerId}, {!var_assigned_to})</expression>
    </formulas>
    <formulas>
        <name>varPriority</name>
        <dataType>String</dataType>
        <expression>CASE({!Task_Type},
&quot;FYI&quot;, &quot;Medium&quot;,
&quot;Action Required&quot;, &quot;High Priority&quot;,
&quot;N/A&quot;)</expression>
    </formulas>
    <formulas>
        <name>varStatus</name>
        <dataType>String</dataType>
        <expression>CASE({!Task_Type},
&quot;FYI&quot;, &quot;Open&quot;,
&quot;Action Required&quot;, &quot;Not Started&quot;,
&quot;N/A&quot;)</expression>
    </formulas>
    <formulas>
        <name>varSubject</name>
        <dataType>String</dataType>
        <expression>{!Task_Type} +&apos; RM Allocated Task &apos;+{!Get_Records_for_Case.Sports_Bet_Account_Number__c}</expression>
    </formulas>
    <formulas>
        <name>varTodayDate</name>
        <dataType>Date</dataType>
        <expression>Today()+3</expression>
    </formulas>
    <interviewLabel>RM To RM Task From Customer Page {!$Flow.CurrentDateTime}</interviewLabel>
    <isAdditionalPermissionRequiredToRun>true</isAdditionalPermissionRequiredToRun>
    <label>RM To RM Task From Customer Page</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Task_in_case</name>
        <label>Create Task in case</label>
        <locationX>182</locationX>
        <locationY>1406</locationY>
        <assignRecordIdToReference>CreatedTaskID</assignRecordIdToReference>
        <connector>
            <targetReference>Task_Created</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Error_Screen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>ActivityDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Contact__c</field>
            <value>
                <elementReference>Get_Records_for_Case.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>Description</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Manual Singular</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>ReturnAssignedOwnerID</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <elementReference>varPriority</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Record_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <elementReference>varStatus</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>varSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <elementReference>Task_Type</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>Get_Records_for_Case.PersonContact.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordCreates>
    <recordLookups>
        <name>Get_Profile_Id</name>
        <label>Get Profile Id</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_User_List</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varTeammember</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Profile</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Name</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Record_Type</name>
        <label>Get Record Type</label>
        <locationX>182</locationX>
        <locationY>890</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>RM2RM_Task_From_Case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>TRM_to_RM_Task</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Records_for_Case</name>
        <label>Get Records for Case</label>
        <locationX>182</locationX>
        <locationY>782</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Record_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_the_customer_record</name>
        <label>get the customer record</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_the_record_owner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_the_record_owner</name>
        <label>Get the record owner</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Add_the_record_owner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_the_customer_record.OwnerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_User_List</name>
        <label>Get User List</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_the_customer_record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProfileId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Profile_Id.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>User</object>
        <outputReference>varUserListCollection</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Name</queriedFields>
    </recordLookups>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <name>Error_Screen</name>
        <label>Error Screen</label>
        <locationX>490</locationX>
        <locationY>1514</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Error_Message</name>
            <fieldText>&lt;p&gt;Task creation Failed&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>RM2RM_Task_From_Case</name>
        <label>RM2RM_Task_From_Case</label>
        <locationX>182</locationX>
        <locationY>998</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Has_a_user_been_choosen</targetReference>
        </connector>
        <fields>
            <name>Task_Type</name>
            <choiceReferences>FYI</choiceReferences>
            <choiceReferences>Action_Required</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Type</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Message</name>
            <fieldText>&lt;p&gt;Task will be assigned to Customer Owner: &lt;strong&gt;{!Get_the_record_owner.Name}&lt;/strong&gt;. To assign to a different user, update &quot;Assigned To&quot; field below:&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Assigned_To1</name>
            <choiceReferences>varUserChoiceSet</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Assigned to</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>ActivityDate</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>varTodayDate</elementReference>
            </defaultValue>
            <fieldText>Due Date</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Description</name>
            <fieldText>Comments</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Create Task</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Task_Created</name>
        <label>Task Created</label>
        <locationX>182</locationX>
        <locationY>1514</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Success_Message</name>
            <fieldText>&lt;p&gt;Task Created Successfully&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Profile_Id</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>CreatedTaskID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>var_assigned_to</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Date</description>
        <name>varDate</name>
        <dataType>Date</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varUserListCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>User</objectType>
    </variables>
</Flow>
