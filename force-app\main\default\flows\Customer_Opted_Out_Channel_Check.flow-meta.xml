<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>AssignInactiveAccount</name>
        <label>AssignInactiveAccount</label>
        <locationX>1062</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>voiceCall.Interaction_while_Account_is_Inactive__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_2_of_Update_Voice_Call</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignInactiveAccount2</name>
        <label>AssignInactiveAccount</label>
        <locationX>1326</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>EmailMessage.Interaction_while_Account_is_Inactive__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_2_of_Update_EmailMEssage</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignOptOUT</name>
        <label>AssignOptOUT</label>
        <locationX>50</locationX>
        <locationY>1766</locationY>
        <assignmentItems>
            <assignToReference>MessagingSession.Interaction_On_Opted_Out_Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Messaging_Session</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignOptOUT_Email</name>
        <label>AssignOptOUT</label>
        <locationX>1106</locationX>
        <locationY>1766</locationY>
        <assignmentItems>
            <assignToReference>EmailMessage.Interaction_On_Opted_Out_Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_EmailMEssage</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignOptOUTCall</name>
        <label>AssignOptOUT</label>
        <locationX>578</locationX>
        <locationY>1766</locationY>
        <assignmentItems>
            <assignToReference>voiceCall.Interaction_On_Opted_Out_Channel_Object__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Voice_Call</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignSelfExcluded</name>
        <label>AssignSelfExcluded</label>
        <locationX>1326</locationX>
        <locationY>1058</locationY>
        <assignmentItems>
            <assignToReference>EmailMessage.Interaction_while_Self_Excluded__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_1_of_Update_EmailMEssage</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignSelfExcluded2</name>
        <label>AssignSelfExcluded</label>
        <locationX>1062</locationX>
        <locationY>1058</locationY>
        <assignmentItems>
            <assignToReference>voiceCall.Interaction_while_Self_Excluded__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_1_of_Update_Voice_Call</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_AssignOptOUT</name>
        <label>AssignInactiveAccount</label>
        <locationX>798</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>MessagingSession.Interaction_while_Account_is_Inactive__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_1_of_Update_Messaging_Session</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_AssignOptOUT</name>
        <label>AssignSelfExcluded</label>
        <locationX>798</locationX>
        <locationY>1058</locationY>
        <assignmentItems>
            <assignToReference>MessagingSession.Interaction_while_Self_Excluded__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_1_of_Copy_1_of_Update_Messaging_Session</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Which interaction channel to update</description>
        <name>Copy_1_of_Which_interaction_channel_to_update</name>
        <label>Which interaction channel to update</label>
        <locationX>1194</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Is_Customer_Self_Excluded</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_Messaging_Session_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>MessagingSession</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_AssignOptOUT</targetReference>
            </connector>
            <label>Messaging Session Update</label>
        </rules>
        <rules>
            <name>Copy_1_of_Voice_Call_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>voiceCall</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignInactiveAccount</targetReference>
            </connector>
            <label>Voice Call Update</label>
        </rules>
        <rules>
            <name>Copy_1_of_Email_Message_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>EmailMessage</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignInactiveAccount2</targetReference>
            </connector>
            <label>Email Message Update</label>
        </rules>
    </decisions>
    <decisions>
        <description>Which interaction channel to update</description>
        <name>Copy_2_of_Which_interaction_channel_to_update</name>
        <label>Which interaction channel to update</label>
        <locationX>1194</locationX>
        <locationY>950</locationY>
        <defaultConnector>
            <targetReference>Get_Customer_Consent</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_2_of_Messaging_Session_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>MessagingSession</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_AssignOptOUT</targetReference>
            </connector>
            <label>Messaging Session Update</label>
        </rules>
        <rules>
            <name>Copy_2_of_Voice_Call_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>voiceCall</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignSelfExcluded2</targetReference>
            </connector>
            <label>Voice Call Update</label>
        </rules>
        <rules>
            <name>Copy_2_of_Email_Message_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>EmailMessage</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignSelfExcluded</targetReference>
            </connector>
            <label>Email Message Update</label>
        </rules>
    </decisions>
    <decisions>
        <name>Has_Opted_Out</name>
        <label>Has Opted Out?</label>
        <locationX>182</locationX>
        <locationY>1658</locationY>
        <defaultConnectorLabel>Opted In</defaultConnectorLabel>
        <rules>
            <name>Yes_OptedOUT</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Consent.Communications_via_RM_SMS__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignOptOUT</targetReference>
            </connector>
            <label>Yes OptedOUT</label>
        </rules>
    </decisions>
    <decisions>
        <name>Has_Opted_Out_Email</name>
        <label>Has Opted Out?</label>
        <locationX>1238</locationX>
        <locationY>1658</locationY>
        <defaultConnectorLabel>Opted In</defaultConnectorLabel>
        <rules>
            <name>Yes_OptedOUT_Email</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Consent.Communications_via_RM_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignOptOUT_Email</targetReference>
            </connector>
            <label>Yes OptedOUT</label>
        </rules>
    </decisions>
    <decisions>
        <name>Has_Opted_Out_PHone</name>
        <label>Has Opted Out?</label>
        <locationX>710</locationX>
        <locationY>1658</locationY>
        <defaultConnectorLabel>Opted In</defaultConnectorLabel>
        <rules>
            <name>Yes_OptedOUT_Phone</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Consent.Communications_via_RM_Phone__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignOptOUTCall</targetReference>
            </connector>
            <label>Yes OptedOUT</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_customer_account_Active</name>
        <label>Is Account Active?</label>
        <locationX>908</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_Which_interaction_channel_to_update</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_Customer_Self_Excluded</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Customer_Self_Excluded</name>
        <label>Is Customer Self Excluded</label>
        <locationX>908</locationX>
        <locationY>842</locationY>
        <defaultConnector>
            <targetReference>Copy_2_of_Which_interaction_channel_to_update</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Customer_Consent</targetReference>
            </connector>
            <label>No</label>
        </rules>
    </decisions>
    <decisions>
        <description>Which interaction channel to update</description>
        <name>Which_interaction_channel_to_update</name>
        <label>Which interaction channel to update</label>
        <locationX>908</locationX>
        <locationY>1550</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Messaging_Session_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>MessagingSession</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Has_Opted_Out</targetReference>
            </connector>
            <label>Messaging Session Update</label>
        </rules>
        <rules>
            <name>Voice_Call_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>voiceCall</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Has_Opted_Out_PHone</targetReference>
            </connector>
            <label>Voice Call Update</label>
        </rules>
        <rules>
            <name>Email_Message_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>EmailMessage</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Has_Opted_Out_Email</targetReference>
            </connector>
            <label>Email Message Update</label>
        </rules>
    </decisions>
    <description>Used to determine if Customer has been contacted on opted-out channel
Updated for SBET-761 - Interaction on Self Excluded or Inactive Account fields</description>
    <environments>Default</environments>
    <interviewLabel>Customer: Opted Out Channel Check {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer: Opted Out Channel, Account Status and Self Excluded Check</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the customer&apos;s consent preferences</description>
        <name>Get_Customer_Consent</name>
        <label>Get Customer Consent</label>
        <locationX>908</locationX>
        <locationY>1442</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Which_interaction_channel_to_update</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Customer__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Customer_Consent__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Communications_via_RM_Email__c</queriedFields>
        <queriedFields>Communications_via_RM_Phone__c</queriedFields>
        <queriedFields>Communications_via_RM_Opt_Out__c</queriedFields>
        <queriedFields>Communications_via_RM_SMS__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Customer_Record</name>
        <label>Get Customer Record</label>
        <locationX>908</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_customer_account_Active</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Account_Status__c</queriedFields>
        <queriedFields>Self_Excluded__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates the current Messaging Session with Warning Message Acknowledged = TRUE</description>
        <name>Copy_1_of_Copy_1_of_Update_Messaging_Session</name>
        <label>Update Messaging Session</label>
        <locationX>798</locationX>
        <locationY>1166</locationY>
        <connector>
            <targetReference>Get_Customer_Consent</targetReference>
        </connector>
        <inputReference>MessagingSession</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_1_of_Update_EmailMEssage</name>
        <label>Update Email Message</label>
        <locationX>1326</locationX>
        <locationY>1166</locationY>
        <connector>
            <targetReference>Get_Customer_Consent</targetReference>
        </connector>
        <inputReference>EmailMessage</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Messaging Session with Warning Message Acknowledged = TRUE</description>
        <name>Copy_1_of_Update_Messaging_Session</name>
        <label>Update Messaging Session</label>
        <locationX>798</locationX>
        <locationY>566</locationY>
        <connector>
            <targetReference>Is_Customer_Self_Excluded</targetReference>
        </connector>
        <inputReference>MessagingSession</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Voice Call with Warning Message Ackowledged = TRUE</description>
        <name>Copy_1_of_Update_Voice_Call</name>
        <label>Update Voice Call</label>
        <locationX>1062</locationX>
        <locationY>1166</locationY>
        <connector>
            <targetReference>Get_Customer_Consent</targetReference>
        </connector>
        <inputReference>voiceCall</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_2_of_Update_EmailMEssage</name>
        <label>Update Email Message</label>
        <locationX>1326</locationX>
        <locationY>566</locationY>
        <connector>
            <targetReference>Is_Customer_Self_Excluded</targetReference>
        </connector>
        <inputReference>EmailMessage</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Voice Call with Warning Message Ackowledged = TRUE</description>
        <name>Copy_2_of_Update_Voice_Call</name>
        <label>Update Voice Call</label>
        <locationX>1062</locationX>
        <locationY>566</locationY>
        <connector>
            <targetReference>Is_Customer_Self_Excluded</targetReference>
        </connector>
        <inputReference>voiceCall</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_EmailMEssage</name>
        <label>Update Email Message</label>
        <locationX>1106</locationX>
        <locationY>1874</locationY>
        <inputReference>EmailMessage</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Messaging Session with Warning Message Acknowledged = TRUE</description>
        <name>Update_Messaging_Session</name>
        <label>Update Messaging Session</label>
        <locationX>50</locationX>
        <locationY>1874</locationY>
        <inputReference>MessagingSession</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Voice Call with Warning Message Ackowledged = TRUE</description>
        <name>Update_Voice_Call</name>
        <label>Update Voice Call</label>
        <locationX>578</locationX>
        <locationY>1874</locationY>
        <inputReference>voiceCall</inputReference>
    </recordUpdates>
    <start>
        <locationX>782</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Customer_Record</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>Stores the SLDS div class to render warning notifications in display text</description>
        <name>texttemplateWarningSLDS</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-scoped-notification slds-theme_warning&quot; role=&quot;status&quot;&gt;</text>
    </textTemplates>
    <variables>
        <name>EmailMessage</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>EmailMessage</objectType>
    </variables>
    <variables>
        <name>MessagingSession</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>MessagingSession</objectType>
    </variables>
    <variables>
        <description>Stores the Id of the record the Screen flow is launched from as a text to be used for different purposes throughout the Flow</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the output of the formulaChannelOptOut variable. 
TRUE = Opted Out
FALSE = Opted In</description>
        <name>varChannelOptOut</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>voiceCall</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>VoiceCall</objectType>
    </variables>
</Flow>
