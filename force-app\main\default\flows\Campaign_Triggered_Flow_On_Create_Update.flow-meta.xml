<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>58.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Parent_Settings</name>
        <label>Assign Parent Settings</label>
        <locationX>138</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>forEachChild.Details_Required_for_Ticket_Allocation__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Details_Required_for_Ticket_Allocation__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>childCampaignsForUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>forEachChild</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>forEachChild</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Do_Child_Campaigns_Exist</name>
        <label>Do Child Campaigns Exist</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Event_Start_Date</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decYes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Child_Campaigns</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>forEachChild</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Event_Start_Date</name>
        <label>Event Start Date</label>
        <locationX>314</locationX>
        <locationY>1163</locationY>
        <defaultConnector>
            <targetReference>Is_Event_Changed_to_Completed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decIsNow</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>EventDateIsToday</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Cancelled</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>subUpdateAttendeeStatus</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Is Now</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if the event has been moved to the Completed Status</description>
        <name>Is_Event_Changed_to_Completed</name>
        <label>Is Event Changed to Completed?</label>
        <locationX>314</locationX>
        <locationY>1463</locationY>
        <defaultConnectorLabel>Status is not changed</defaultConnectorLabel>
        <rules>
            <name>Status_Changed_to_Completed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Wastage_Calculation_Fields</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Status Changed to Completed</label>
        </rules>
    </decisions>
    <description>SBET-1114 - New Wastage Calculations for Campaigns Tickets/Seats</description>
    <environments>Default</environments>
    <formulas>
        <name>EventDateIsToday</name>
        <dataType>Boolean</dataType>
        <expression>DATEVALUE({!$Record.Start_Date__c})=TODAY()</expression>
    </formulas>
    <formulas>
        <name>EventIn4Days</name>
        <dataType>Boolean</dataType>
        <expression>DATEVALUE({!$Record.Start_Date__c})=(TODAY()+4)</expression>
    </formulas>
    <formulas>
        <description>Calculates the total number of customer attendees that were no-shows at an event, plus the tickets allocated for customers but not used.</description>
        <name>totalCustomerWastageAmount</name>
        <dataType>Number</dataType>
        <expression>IF( 
({!$Record.Customer_Tickets_Remaining__c} + {!$Record.Customer_Guest_No_Shows_Hosted__c})&gt;= 0,
({!$Record.Customer_Tickets_Remaining__c} + {!$Record.Customer_Guest_No_Shows_Hosted__c}),
0)</expression>
        <scale>0</scale>
    </formulas>
    <formulas>
        <name>totalCustomerWastagePercent</name>
        <dataType>Number</dataType>
        <expression>({!totalCustomerWastageAmount}/{!$Record.Customer_Ticket_Allocation__c})*100</expression>
        <scale>2</scale>
    </formulas>
    <interviewLabel>Campaign Triggered Flow On Create/Update {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Campaign Triggered Flow - On Create/Update</label>
    <loops>
        <name>forEachChild</name>
        <label>For Each Child Campaign</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <collectionReference>Get_Child_Campaigns</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Parent_Settings</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Campaign_Settings</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Child_Campaigns</name>
        <label>Get Child Campaigns</label>
        <locationX>314</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Do_Child_Campaigns_Exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ParentId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Campaign</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Campaign_Settings</name>
        <label>Update Campaign Settings</label>
        <locationX>50</locationX>
        <locationY>839</locationY>
        <connector>
            <targetReference>Event_Start_Date</targetReference>
        </connector>
        <inputReference>childCampaignsForUpdate</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update fields that calculate total customer wastage on the Event</description>
        <name>Update_Wastage_Calculation_Fields</name>
        <label>Update Wastage Calculation Fields</label>
        <locationX>50</locationX>
        <locationY>1571</locationY>
        <inputAssignments>
            <field>Total_Customer_Wastage_Amount__c</field>
            <value>
                <elementReference>totalCustomerWastageAmount</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Total_Customer_Wastage_Percent__c</field>
            <value>
                <elementReference>totalCustomerWastagePercent</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Child_Campaigns</targetReference>
        </connector>
        <filterFormula>{!$Record.RecordType.Name}=&apos;Event&apos;</filterFormula>
        <object>Campaign</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>subUpdateAttendeeStatus</name>
        <label>Update Attendee Status</label>
        <locationX>182</locationX>
        <locationY>1271</locationY>
        <connector>
            <targetReference>Is_Event_Changed_to_Completed</targetReference>
        </connector>
        <flowName>Attendees_Update_Customer_Status_at_the_Event_Field</flowName>
        <inputAssignments>
            <name>CampaignId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>childCampaignsForUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Campaign</objectType>
    </variables>
</Flow>
