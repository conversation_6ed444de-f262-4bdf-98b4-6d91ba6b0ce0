<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>


    <assignments>
        <name>Convert_String_To_Collection</name>
        <label>Convert String To Collection</label>
        <locationX>1750</locationX>
        <locationY>200</locationY>
        <assignmentItems>
            <assignToReference>accountIdsList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>accountIdsString</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Multi_Selection_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Process_Multi_Selection</name>
        <label>Process Multi Selection</label>
        <locationX>1750</locationX>
        <locationY>400</locationY>
        <assignmentItems>
            <assignToReference>recordId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>accountIdsString</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Multi_Selection_Success</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ID_to_Lookup</name>
        <label>Assign ID to Lookup</label>
        <locationX>3042</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>Campaign.recordId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>CampaignIdOutput</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Account_Details</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Campaign_Capacity</name>
        <label>Campaign Capacity</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>New_Attendee_Customer_Attendee_0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Capacity_Reached_Outcome</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Campaign_Details_2.Total_Number_of_Seats_Not_Allocated__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>waitlistCapacityReached2</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CapacityReachedScreen</targetReference>
            </connector>
            <label>Capacity Reached</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Customer_Eligibility</name>
        <label>Check Customer Eligibility</label>
        <locationX>3174</locationX>
        <locationY>866</locationY>
        <defaultConnector>
            <targetReference>New_Attendee_Customer_Attendee</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Customer_is_Blacklisted</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Account_Details.Blacklist_from_Events__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Copy_1_of_AlertMessage</targetReference>
            </connector>
            <label>Customer is Blacklisted</label>
        </rules>
        <rules>
            <name>Customer_is_not_Eligible</name>
            <conditionLogic>(1 OR 2 OR 3 OR 4 OR 7 OR (5 AND 6))</conditionLogic>
            <conditions>
                <leftValueReference>Get_Account_Details.Account_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details.Premium_Tier__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>-1</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details.Premium_Tier__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details.Self_Excluded__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details.RG_Score__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>RGScoreVar</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details.RG_Score__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details_0.API_AML_Flag__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>AML1</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AlertMessage</targetReference>
            </connector>
            <label>Customer is not Eligible</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Launched_from_Guest_List_Generator</name>
        <label>Check if Launched from Guest List Generator</label>
        <locationX>3174</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Get_Account_Details</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>From_Guest_List</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CampaignIdOutput</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ID_to_Lookup</targetReference>
            </connector>
            <label>From Guest List</label>
        </rules>
    </decisions>

    <decisions>
        <name>Check_where_the_flow_was_launched</name>
        <label>Check where the flow was launched</label>
        <locationX>1876</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Check_if_Launched_from_Guest_List_Generator</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>From Account</defaultConnectorLabel>
        <rules>
            <name>From_Campaign</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsCampaignRecord</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Campaign_Details_2</targetReference>
            </connector>
            <label>From Campaign</label>
        </rules>
    </decisions>
    <decisions>
        <name>Let_System_Admin_Bypass_the_Validation_Rue</name>
        <label>Let System Admin Bypass the Validation Rule</label>
        <locationX>3174</locationX>
        <locationY>1082</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Current_User_is_System_Admin</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CurrentUserProfileName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>System Administrator</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>New_Attendee_Customer_Attendee</targetReference>
            </connector>
            <label>Current User is System Admin</label>
        </rules>
    </decisions>
    <decisions>
        <name>Let_System_Admin_Bypass_the_Validation_Rue_0</name>
        <label>Let System Admin Bypass the Validation Rule</label>
        <locationX>1722</locationX>
        <locationY>1322</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Current_User_is_System_Admin_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CurrentUserProfileName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>System Administrator</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Attendee_0</targetReference>
            </connector>
            <label>Current User is System Admin</label>
        </rules>
    </decisions>
    <decisions>
        <name>Process_Checker</name>
        <label>Process Checker</label>
        <locationX>3174</locationX>
        <locationY>1790</locationY>
        <defaultConnector>
            <targetReference>Create_Attendee</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_Enough_Seats</name>
            <conditionLogic>(1 AND 2 AND 3) OR (3 AND 4 AND 5)</conditionLogic>
            <conditions>
                <leftValueReference>Get_Campaign_Details.Customer_Tickets_Remaining__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>Number_of_Seats</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Offer Accepted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>waitlistCapacityReached</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Waitlisted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>waitlistSeatsRemaining</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>Number_of_Seats</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Attendee_Customer_Attendee</targetReference>
            </connector>
            <label>Not Enough Seats</label>
        </rules>
        <rules>
            <name>Capacity_Reached</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Campaign_Details.Customer_Tickets_Remaining__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>waitlistCapacityReached</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Attendee_Customer_Attendee</targetReference>
            </connector>
            <label>Capacity Reached</label>
        </rules>
        <rules>
            <name>No_Show_Value</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No Show</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>CurrentUserProfileName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>System Administrator</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Attendee_Customer_Attendee</targetReference>
            </connector>
            <label>No Show Value</label>
        </rules>
        <rules>
            <name>Duplicate_Attendee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>AttendeeDupe</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Attendee_Customer_Attendee</targetReference>
            </connector>
            <label>Duplicate Attendee</label>
        </rules>
        <rules>
            <name>Parent_Campaign</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Campaign_Details.Is_Parent_Campaign__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Attendee_Customer_Attendee</targetReference>
            </connector>
            <label>Parent Campaign</label>
        </rules>
    </decisions>
    <decisions>
        <name>Process_Checker_0</name>
        <label>Process Checker</label>
        <locationX>1106</locationX>
        <locationY>998</locationY>
        <defaultConnector>
            <targetReference>Process_Checker_Level_2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Blacklisted</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Account_Details_0.Blacklist_from_Events__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_AlertMessage</targetReference>
            </connector>
            <label>Blacklisted</label>
        </rules>
        <rules>
            <name>Not_Enough_Seats_0</name>
            <conditionLogic>(1 AND 2 AND 3) OR (3 AND 4 AND 5)</conditionLogic>
            <conditions>
                <leftValueReference>Get_Campaign_Details_2.Customer_Tickets_Remaining__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>Number_of_Seats_0</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Status_0</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Offer Accepted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>waitlistCapacityReached2</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>waitlistSeatsRemaining2</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>Number_of_Seats_0</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Status_0</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Waitlisted</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Attendee_Customer_Attendee_0</targetReference>
            </connector>
            <label>Not Enough Seats</label>
        </rules>
        <rules>
            <name>Capacity_Reached_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Campaign_Details_2.Customer_Tickets_Remaining__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>waitlistCapacityReached2</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Attendee_Customer_Attendee_0</targetReference>
            </connector>
            <label>Capacity Reached</label>
        </rules>
        <rules>
            <name>No_Show_Value_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Status_0</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No Show</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>CurrentUserProfileName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>System Administrator</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Attendee_Customer_Attendee_0</targetReference>
            </connector>
            <label>No Show Value</label>
        </rules>
        <rules>
            <name>Duplicate_Attendee2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>AttendeeDupe</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Attendee_Customer_Attendee_0</targetReference>
            </connector>
            <label>Duplicate Attendee</label>
        </rules>
    </decisions>
    <decisions>
        <name>Process_Checker_Level_2</name>
        <label>Process Checker Level 2</label>
        <locationX>1898</locationX>
        <locationY>1106</locationY>
        <defaultConnector>
            <targetReference>Create_Attendee_0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Customer_is_not_Eligible_Outcome_0</name>
            <conditionLogic>((1 OR 2 OR 3 OR 4 OR 7) OR (5 AND 6))</conditionLogic>
            <conditions>
                <leftValueReference>Get_Account_Details_0.Account_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details_0.Premium_Tier__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>-1</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details_0.Premium_Tier__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details_0.Self_Excluded__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details_0.RG_Score__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>RGScoreVar</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details_0.RG_Score__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Account_Details_0.API_AML_Flag__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>AML1</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AlertMessage_0</targetReference>
            </connector>
            <label>Customer is not Eligible</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Multi_Selection</name>
        <label>Check Multi Selection</label>
        <locationX>1750</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>Get_RG_Score_Limit</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Single Selection</defaultConnectorLabel>
        <rules>
            <name>Multi_Selection</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isMultiSelection</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Convert_String_To_Collection</targetReference>
            </connector>
            <label>Multi Selection</label>
        </rules>
    </decisions>
    <description>SBET-1113 New Wastage Calculations for Campaigns Tickets/Seats - fix waitlist</description>
    <dynamicChoiceSets>
        <name>AttendeeStatus</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Status__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>ChannelPicklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Channel__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>DirectionPicklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Direction__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>ClickHere</name>
        <dataType>String</dataType>
        <expression>LEFT({!$Api.Partner_Server_URL_340}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_340})) 
 &amp; {!AttendeeRecordID}</expression>
    </formulas>
    <formulas>
        <name>CurrentUserProfileName</name>
        <dataType>String</dataType>
        <expression>{!$Profile.Name}</expression>
    </formulas>
    <formulas>
        <name>IsCampaignRecord</name>
        <dataType>Boolean</dataType>
        <expression>IF(LEFT({!recordId},3) =&quot;701&quot;, TRUE, FALSE)</expression>
    </formulas>
    <formulas>
        <name>Today</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <name>waitlistCapacityReached</name>
        <dataType>Boolean</dataType>
        <expression>{!Get_Campaign_Details.Waitlist_Capacity__c}-{!Get_Campaign_Details.Total_Number_of_Seats_Waitlisted__c}&lt;=0</expression>
    </formulas>
    <formulas>
        <name>waitlistCapacityReached2</name>
        <dataType>Boolean</dataType>
        <expression>{!Get_Campaign_Details_2.Waitlist_Capacity__c}-{!Get_Campaign_Details_2.Total_Number_of_Seats_Waitlisted__c}&lt;=0</expression>
    </formulas>
    <formulas>
        <name>waitlistSeatsRemaining</name>
        <dataType>Number</dataType>
        <expression>{!Get_Campaign_Details.Waitlist_Capacity__c}-{!Get_Campaign_Details.Total_Number_of_Seats_Waitlisted__c}</expression>
        <scale>0</scale>
    </formulas>
    <formulas>
        <name>waitlistSeatsRemaining2</name>
        <dataType>Number</dataType>
        <expression>{!Get_Campaign_Details_2.Waitlist_Capacity__c}-{!Get_Campaign_Details_2.Total_Number_of_Seats_Waitlisted__c}</expression>
        <scale>0</scale>
    </formulas>
    <interviewLabel>Add Attendee Flow Desktop {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Add Attendee Flow Desktop</label>

    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Attendee</name>
        <label>Create Attendee</label>
        <locationX>3834</locationX>
        <locationY>1898</locationY>
        <assignRecordIdToReference>AttendeeRecordID</assignRecordIdToReference>
        <connector>
            <targetReference>Get_Attendee_Record_Name</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>FaultScreen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Account__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Campaign__c</field>
            <value>
                <elementReference>Campaign.recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_Invited__c</field>
            <value>
                <elementReference>Date_Invited</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Notes__c</field>
            <value>
                <elementReference>Notes</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Number_Of_Seats__c</field>
            <value>
                <elementReference>Number_of_Seats</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>CARecordTypeId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Seat_Table_Number__c</field>
            <value>
                <elementReference>Seat_Table_Number</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <elementReference>Status</elementReference>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordCreates>
    <recordCreates>
        <name>Create_Attendee_0</name>
        <label>Create Attendee</label>
        <locationX>1898</locationX>
        <locationY>1706</locationY>
        <assignRecordIdToReference>AttendeeRecordID</assignRecordIdToReference>
        <connector>
            <targetReference>Get_Attendee_Record_Name</targetReference>
        </connector>
        <faultConnector>
            <targetReference>FaultScreen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Account__c</field>
            <value>
                <elementReference>CustomerLookup.recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Campaign__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_Invited__c</field>
            <value>
                <elementReference>Date_Invited_0</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Notes__c</field>
            <value>
                <elementReference>Notes_0</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Number_Of_Seats__c</field>
            <value>
                <elementReference>Number_of_Seats_0</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>CARecordTypeId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Seat_Table_Number__c</field>
            <value>
                <elementReference>Seat_Table_Number_0</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <elementReference>Status_0</elementReference>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordCreates>

    <recordLookups>
        <name>Check_Attendee_Duplicates</name>
        <label>Check Attendee Duplicates</label>
        <locationX>3174</locationX>
        <locationY>1682</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Process_Checker</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Campaign.recordId</elementReference>
            </value>
        </filters>
        <object>Attendee__c</object>
        <outputAssignments>
            <assignToReference>AttendeeDupe</assignToReference>
            <field>Name</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>Check_Attendee_Duplicates_0</name>
        <label>Check Attendee Duplicates</label>
        <locationX>1106</locationX>
        <locationY>890</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Process_Checker_0</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CustomerLookup.recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <object>Attendee__c</object>
        <outputAssignments>
            <assignToReference>AttendeeDupe</assignToReference>
            <field>Name</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>Get_Account_Details</name>
        <label>Get Account Details</label>
        <locationX>3174</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Customer_Eligibility</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Account_Details_0</name>
        <label>Get Account Details</label>
        <locationX>1106</locationX>
        <locationY>782</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Attendee_Duplicates_0</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CustomerLookup.recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>

    <recordLookups>
        <name>Get_Attendee_Record_Name</name>
        <label>Get Attendee Record Name</label>
        <locationX>1876</locationX>
        <locationY>2306</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>End</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AttendeeRecordID</elementReference>
            </value>
        </filters>
        <object>Attendee__c</object>
        <outputAssignments>
            <assignToReference>EventParticipationCapExceeded</assignToReference>
            <field>Event_Participation_Cap_Exceeded__c</field>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>AttendeeName</assignToReference>
            <field>Name</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>Get_Campaign_Details</name>
        <label>Get Campaign Details</label>
        <locationX>3174</locationX>
        <locationY>1574</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Attendee_Duplicates</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Campaign.recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Campaign</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Campaign_Details_2</name>
        <label>Get Campaign Details 2</label>
        <locationX>578</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Campaign_Capacity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Campaign</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Record_Type_Id</name>
        <label>Get Record Type Id</label>
        <locationX>1876</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_where_the_flow_was_launched</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attendee__c</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Customer_Attendee</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>CARecordTypeId</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>Get_RG_Score_Limit</name>
        <label>Get RG Score Limit</label>
        <locationX>1876</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Record_Type_Id</targetReference>
        </connector>
        <object>System_Limits__c</object>
        <outputAssignments>
            <assignToReference>RGScoreVar</assignToReference>
            <field>RG_Score__c</field>
        </outputAssignments>
    </recordLookups>
    <screens>
        <name>AlertMessage</name>
        <label>AlertMessage</label>
        <locationX>3174</locationX>
        <locationY>974</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Let_System_Admin_Bypass_the_Validation_Rue</targetReference>
        </connector>
        <fields>
            <name>AlertMessage01</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(62, 62, 60);&quot;&gt;Note:&lt;/b&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(62, 62, 60);&quot;&gt; You cannot add this Customer as an Attendee. Please see the checklist below of an eligible Attendee:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(18, 106, 178);&quot;&gt;______________________________________________________________________________________________________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>CurrentUserProfileName</leftValueReference>
                    <operator>NotEqualTo</operator>
                    <rightValue>
                        <stringValue>System Administrator</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>AlertMessageforAdmin</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Warning:&lt;/b&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt; This customer is &lt;/span&gt;&lt;u style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;ineligible&lt;/u&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt; to be added as an Attendee as per the checklist below. Click on the &lt;/span&gt;&lt;b style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Next &lt;/b&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;button to continue.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); color: rgb(18, 106, 178); font-size: 14px;&quot;&gt;______________________________________________________________________________________________________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>CurrentUserProfileName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>System Administrator</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Blank04</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>AlertMessage_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>AccountStatusGreenTick</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Account_Status__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Active</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>AccountStatusError</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Account_Status__c</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>Active</stringValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Account_Status__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>AccountStatusValue</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Account Status must be &lt;/span&gt;&lt;b style=&quot;font-size: 14px;&quot;&gt;Active&lt;/b&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section2_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section2_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>NoteMessage</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_Section3</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_Section3_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>RGScoreStatusGreenTick</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.RG_Score__c</leftValueReference>
                            <operator>LessThan</operator>
                            <rightValue>
                                <elementReference>RGScoreVar</elementReference>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details.RG_Score__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>RGScoreError</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.RG_Score__c</leftValueReference>
                            <operator>GreaterThan</operator>
                            <rightValue>
                                <elementReference>RGScoreVar</elementReference>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details.RG_Score__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section3_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>RGScore</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;RG Score must be &lt;/span&gt;&lt;b style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;less than {!RGScoreVar}&lt;/b&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section3_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section3_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Blank02</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_Section4</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_Section4_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>SelfExStatusGreenTick</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Self_Excluded__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>SelfExError</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Self_Excluded__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Self_Excluded__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section4_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>SelfExcluded</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;Self-excluded value must be set to &lt;/span&gt;&lt;b style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;No&lt;/b&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section4_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section4_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Blank03</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_Section5</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_Section5_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>PremiumGreenTick</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Premium_Tier__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Premium_Tier__c</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>-1</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>PremiumError</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Premium_Tier__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>-1</stringValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details.Premium_Tier__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section5_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Premium</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Customer Tier must be &lt;/span&gt;&lt;b style=&quot;font-size: 14px;&quot;&gt;Premium&lt;/b&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section5_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section5_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>blank</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_Section6</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_Section6_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>AMLGreenTick</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.API_AML_Flag__c</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>AML1</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>AMLError</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details.API_AML_Flag__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>AML1</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section6_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>AMLMessage1</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;Customer must not have an AML Flag of&lt;/span&gt;&lt;strong style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt; AML1&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section6_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_Section6_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>AlertMessage_0</name>
        <label>AlertMessage</label>
        <locationX>1722</locationX>
        <locationY>1214</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Let_System_Admin_Bypass_the_Validation_Rue_0</targetReference>
        </connector>
        <fields>
            <name>AlertMessage01_0</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(62, 62, 60);&quot;&gt;Note:&lt;/b&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(62, 62, 60);&quot;&gt; You cannot add this Customer as an Attendee. Please see the checklist below of an eligible Attendee:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(18, 106, 178);&quot;&gt;______________________________________________________________________________________________________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>CurrentUserProfileName</leftValueReference>
                    <operator>NotEqualTo</operator>
                    <rightValue>
                        <stringValue>System Administrator</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>AlertMessageforAdmin_0</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Warning:&lt;/b&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt; This customer is &lt;/span&gt;&lt;u style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;ineligible&lt;/u&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt; to be added as an Attendee as per the checklist below. Click on the &lt;/span&gt;&lt;b style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Next &lt;/b&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;button to continue.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); color: rgb(18, 106, 178); font-size: 14px;&quot;&gt;______________________________________________________________________________________________________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>CurrentUserProfileName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>System Administrator</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Blank04_0</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_0_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_0_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>AlertMessage_0_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_0_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>AccountStatusGreenTick_0</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Account_Status__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Active</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>AccountStatusError_0</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Account_Status__c</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>Active</stringValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Account_Status__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>AccountStatusValue_0</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Account Status must be &lt;/span&gt;&lt;b style=&quot;font-size: 14px;&quot;&gt;Active&lt;/b&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section2_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section2_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>NoteMessage_0</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_0_Section3</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_0_Section3_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>RGScoreStatusGreenTick_0</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.RG_Score__c</leftValueReference>
                            <operator>LessThan</operator>
                            <rightValue>
                                <elementReference>RGScoreVar</elementReference>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.RG_Score__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>RGScoreError_0</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.RG_Score__c</leftValueReference>
                            <operator>GreaterThan</operator>
                            <rightValue>
                                <elementReference>RGScoreVar</elementReference>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.RG_Score__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section3_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>RGScore_0</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;RG Score must be &lt;/span&gt;&lt;b style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;less than {!RGScoreVar}&lt;/b&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section3_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section3_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Blank02_0</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_0_Section4</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_0_Section4_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>SelfExStatusGreenTick_0</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Self_Excluded__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>SelfExError_0</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Self_Excluded__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Yes</stringValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Self_Excluded__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section4_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>SelfExcluded_0</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;Self-excluded value must be set to &lt;/span&gt;&lt;b style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;No&lt;/b&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section4_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section4_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Blank03_0</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_0_Section5</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_0_Section5_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>PremiumGreenTick_0</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Premium_Tier__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Premium_Tier__c</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>-1</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>PremiumError_0</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Premium_Tier__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>-1</stringValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.Premium_Tier__c</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section5_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Premium_0</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Customer Tier must be &lt;/span&gt;&lt;b style=&quot;font-size: 14px;&quot;&gt;Premium&lt;/b&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section5_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section5_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>blank2</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>AlertMessage_0_Section6</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>AlertMessage_0_Section6_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>AMLGreenTick1</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>GreenTick</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.API_AML_Flag__c</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>AML1</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>AMLError1</name>
                    <extensionName>flowruntime:image</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>imageName</name>
                        <value>
                            <stringValue>Error</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Account_Details_0.API_AML_Flag__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>AML1</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section6_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>AMLText2</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(0, 0, 0); font-size: 14px;&quot;&gt;Customer must not have an AML Flag of&lt;/span&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(0, 0, 0); font-size: 14px;&quot;&gt; AML1&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>5</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section6_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>AlertMessage_0_Section6_Column4</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>CapacityReachedScreen</name>
        <label>CapacityReachedScreen</label>
        <locationX>50</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>CapacityReachedMessage</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Note:&lt;/b&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt; This event has reached its capacity.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;______________________________________________________________________________________________________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Copy_1_of_AlertMessage</name>
        <label>BlacklistAlert</label>
        <locationX>314</locationX>
        <locationY>1106</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <fields>
            <name>Copy_1_of_AlertMessage01_0</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Alert Message&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(62, 62, 60); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;Note:&lt;/strong&gt;&lt;span style=&quot;color: rgb(62, 62, 60); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt; You cannot add this Customer as an Attendee as they have been Blacklisted from Events. Contact the E&amp;amp;E team for further details. &lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(18, 106, 178); font-size: 14px;&quot;&gt;_________________________________________&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Copy_1_of_Copy_1_of_AlertMessage</name>
        <label>BlacklistMessage</label>
        <locationX>2822</locationX>
        <locationY>974</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <fields>
            <name>Copy_1_of_Copy_1_of_AlertMessage01_0</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Alert Message&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(62, 62, 60); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;Note:&lt;/strong&gt;&lt;span style=&quot;color: rgb(62, 62, 60); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt; You cannot add this Customer as an Attendee as they have been Blacklisted from Events. Contact the E&amp;amp;E team for further details. &lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(18, 106, 178); font-size: 14px;&quot;&gt;_________________________________________&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>End</name>
        <label>End</label>
        <locationX>1876</locationX>
        <locationY>2414</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Approval1Alert</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 16px; font-family: &amp;quot;apple color emoji&amp;quot;, &amp;quot;segoe ui emoji&amp;quot;, &amp;quot;noto color emoji&amp;quot;, &amp;quot;android emoji&amp;quot;, emojisymbols, &amp;quot;emojione mozilla&amp;quot;, &amp;quot;twemoji mozilla&amp;quot;, &amp;quot;segoe ui symbol&amp;quot;; background-color: rgb(255, 255, 255);&quot;&gt;⚠️&lt;/span&gt;&lt;span style=&quot;color: rgb(205, 98, 0); background-color: rgb(255, 255, 255);&quot;&gt;This record has been &lt;/span&gt;&lt;u style=&quot;color: rgb(205, 98, 0); background-color: rgb(255, 255, 255);&quot;&gt;sent for approval&lt;/u&gt;&lt;span style=&quot;color: rgb(205, 98, 0); background-color: rgb(255, 255, 255);&quot;&gt; because the Customer has exceeded the number of events based on allocation in the Premium Generosity Policy.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>(1 AND (2 OR 3))</conditionLogic>
                <conditions>
                    <leftValueReference>EventParticipationCapExceeded</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Status</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Offer Accepted</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Status_0</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Offer Accepted</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>End_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>End_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Approval2Alert</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;font-family: &amp;quot;apple color emoji&amp;quot;, &amp;quot;segoe ui emoji&amp;quot;, &amp;quot;noto color emoji&amp;quot;, &amp;quot;android emoji&amp;quot;, emojisymbols, &amp;quot;emojione mozilla&amp;quot;, &amp;quot;twemoji mozilla&amp;quot;, &amp;quot;segoe ui symbol&amp;quot;; font-size: 16px; color: rgb(0, 0, 0);&quot;&gt;⚠️&lt;/span&gt;&lt;span style=&quot;color: rgb(205, 98, 0);&quot;&gt;This record has been &lt;/span&gt;&lt;u style=&quot;color: rgb(205, 98, 0);&quot;&gt;sent for approval&lt;/u&gt;&lt;span style=&quot;color: rgb(205, 98, 0);&quot;&gt; because the Customer has more than one (1) guest allocated to an event and requires approval to proceed as per the Premium Events &amp;amp; Experiences Policy.&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>BlankSpace</name>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>((1 OR 2) AND (3 OR 4))</conditionLogic>
                <conditions>
                    <leftValueReference>Status</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Offer Accepted</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Status_0</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Offer Accepted</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Number_of_Seats</leftValueReference>
                    <operator>GreaterThan</operator>
                    <rightValue>
                        <numberValue>2.0</numberValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Number_of_Seats_0</leftValueReference>
                    <operator>GreaterThan</operator>
                    <rightValue>
                        <numberValue>2.0</numberValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>SuccessPage</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Attendee {!AttendeeName} was created.&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; color: rgb(18, 106, 178); background-color: rgb(255, 255, 255);&quot;&gt;______________________________________________________________________________________________________________________________________&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;Click &lt;/span&gt;&lt;a href=&quot;{!ClickHere}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot; style=&quot;font-size: 18px; color: rgb(0, 100, 199);&quot;&gt;&lt;b&gt;here&lt;/b&gt;&lt;/a&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt; to view your Attendee record.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>FaultScreen</name>
        <label>Fault Screen</label>
        <locationX>2250</locationX>
        <locationY>1814</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FaultMessage</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Error:&lt;/b&gt;&lt;span style=&quot;font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt; {!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;______________________________________________________________________________________________________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>New_Attendee_Customer_Attendee</name>
        <label>New Attendee: Customer Attendee</label>
        <locationX>3174</locationX>
        <locationY>1466</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Get_Campaign_Details</targetReference>
        </connector>
        <fields>
            <name>Information</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Information&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(18, 106, 178);&quot;&gt;____________________________________________________________________________________________________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>New_Attendee_Customer_Attendee_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>New_Attendee_Customer_Attendee_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>CustomerReadOnly</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 12px; color: rgb(255, 0, 0);&quot;&gt;*&lt;/span&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 12px; color: rgb(62, 62, 60);&quot;&gt;Customer&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(0, 0, 0);&quot;&gt;{!Get_Account_Details.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>DupeError</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;This customer has already been added to {!Campaign.recordName} event&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>AttendeeDupe</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Campaign</name>
                    <extensionName>flowruntime:lookup</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>fieldApiName</name>
                        <value>
                            <stringValue>Campaign__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Campaign</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>objectApiName</name>
                        <value>
                            <stringValue>Attendee__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>required</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>recordId</name>
                        <value>
                            <elementReference>Campaign.recordId</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>EventCapacityError</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 12px; color: rgb(234, 0, 30);&quot;&gt;This event has reached its capacity&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Campaign_Details.Customer_Tickets_Remaining__c</leftValueReference>
                            <operator>LessThanOrEqualTo</operator>
                            <rightValue>
                                <numberValue>0.0</numberValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>waitlistCapacityReached</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>ErrorMessageWaitlist</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;This event has reached its capacity. You can only add customers in the Waitlisted status.&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Campaign_Details.Customer_Tickets_Remaining__c</leftValueReference>
                            <operator>LessThanOrEqualTo</operator>
                            <rightValue>
                                <numberValue>0.0</numberValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>waitlistCapacityReached</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>parentCampaignError</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;You cannot add attendees to Parent campaigns&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Campaign_Details.Is_Parent_Campaign__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Status</name>
                    <choiceReferences>AttendeeStatus</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Status</elementReference>
                    </defaultValue>
                    <fieldText>Status</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <helpText>&lt;p&gt;Populate to determine the Status in relation to this Event&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>NoShowStatus</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;Add Customer with &quot;No Show&quot; is not allowed&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Status</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No Show</stringValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>CurrentUserProfileName</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>System Administrator</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Date_Invited</name>
                    <dataType>Date</dataType>
                    <defaultValue>
                        <elementReference>Today</elementReference>
                    </defaultValue>
                    <fieldText>Date Invited</fieldText>
                    <fieldType>InputField</fieldType>
                    <helpText>&lt;p&gt;This field is used to capture the Date that the Customer was invited to the Event.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>New_Attendee_Customer_Attendee_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Number_of_Seats</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Number_of_Seats</elementReference>
                    </defaultValue>
                    <fieldText>Number of Seats</fieldText>
                    <fieldType>InputField</fieldType>
                    <helpText>&lt;p&gt;Populate to capture the number of seats for this Customer. For example, if this Customer is attending with one other guest. They will be assigned two seats.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <scale>0</scale>
                    <validationRule>
                        <errorMessage>&lt;p&gt;&lt;span style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255);&quot;&gt;This field can only accept up to 6 characters.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                        <formulaExpression>LEN(TEXT({!Number_of_Seats})) &lt;= 6</formulaExpression>
                    </validationRule>
                </fields>
                <fields>
                    <name>SeatLimitError</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;You are trying to assign more seats to this Attendee than are available. No. of available seats remaining: &lt;/span&gt;&lt;strong style=&quot;color: rgb(234, 0, 30); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;{!Get_Campaign_Details.Customer_Tickets_Remaining__c}&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Campaign_Details.Customer_Tickets_Remaining__c</leftValueReference>
                            <operator>LessThan</operator>
                            <rightValue>
                                <elementReference>Number_of_Seats</elementReference>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Status</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Offer Accepted</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>waitlistSeatError</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;You are trying to assign more seats to this Attendee than are available on the waitlist. No. of waitlist seats remaining: &lt;/span&gt;&lt;strong style=&quot;color: rgb(255, 0, 0);&quot;&gt;{!waitlistSeatsRemaining2}&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>waitlistSeatsRemaining</leftValueReference>
                            <operator>LessThan</operator>
                            <rightValue>
                                <elementReference>Number_of_Seats</elementReference>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Status</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Waitlisted</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Seat_Table_Number</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Seat_Table_Number</elementReference>
                    </defaultValue>
                    <fieldText>Seat/Table Number</fieldText>
                    <fieldType>InputField</fieldType>
                    <helpText>&lt;p&gt;Populate the Seat Number or Table Number that the Customer has been assigned for this Event.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <scale>0</scale>
                    <validationRule>
                        <errorMessage>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(62, 62, 60);&quot;&gt;This field can only accept up to 16 characters.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                        <formulaExpression>LEN(TEXT({!Seat_Table_Number})) &lt;= 16</formulaExpression>
                    </validationRule>
                </fields>
                <fields>
                    <name>Notes</name>
                    <defaultValue>
                        <stringValue>{!Notes}</stringValue>
                    </defaultValue>
                    <fieldText>Notes</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <helpText>&lt;p&gt;Populate any relevant Notes for this Attendee/Attendee&apos;s Guest(s). Eg. - Dietary Requirements, Weight (for Chopper) etc.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>New_Attendee_Customer_Attendee_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>New_Attendee_Customer_Attendee_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>BlankField</name>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>New_Attendee_Customer_Attendee_0</name>
        <label>New Attendee: Customer Attendee</label>
        <locationX>1106</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Get_Account_Details_0</targetReference>
        </connector>
        <fields>
            <name>Information_0</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Information&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(18, 106, 178);&quot;&gt;____________________________________________________________________________________________________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>New_Attendee_Customer_Attendee_0_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>New_Attendee_Customer_Attendee_0_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>CustomerLookup</name>
                    <extensionName>flowruntime:lookup</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>fieldApiName</name>
                        <value>
                            <stringValue>Account__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Customer</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>objectApiName</name>
                        <value>
                            <stringValue>Attendee__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>required</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>recordId</name>
                        <value>
                            <elementReference>CustomerLookup.recordId</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>DupeError2</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;This customer has already been added to {!Get_Campaign_Details_2.Name} event&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>AttendeeDupe</leftValueReference>
                            <operator>IsNull</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>CampaignReadOnly</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;*&lt;/span&gt;&lt;span style=&quot;color: rgb(62, 62, 60); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;Campaign&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;{!Get_Campaign_Details_2.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>EventCapacityError_0</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;This event has reached its capacity&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Campaign_Details_2.Total_Number_of_Seats_Not_Allocated__c</leftValueReference>
                            <operator>LessThanOrEqualTo</operator>
                            <rightValue>
                                <numberValue>0.0</numberValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>waitlistCapacityReached2</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>CapacityWaitlistError</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30); font-size: 12px;&quot;&gt;This event has reached its capacity. You can only add customers in the Waitlisted status&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Campaign_Details_2.Customer_Tickets_Remaining__c</leftValueReference>
                            <operator>LessThanOrEqualTo</operator>
                            <rightValue>
                                <numberValue>0.0</numberValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>waitlistCapacityReached2</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Status_0</name>
                    <choiceReferences>AttendeeStatus</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Status_0</elementReference>
                    </defaultValue>
                    <fieldText>Status</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <helpText>&lt;p&gt;Populate to determine the Status in relation to this Event&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>NoShowStatus_0</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30); font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;Add Customer with &quot;No Show&quot; is not allowed&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Status_0</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>No Show</stringValue>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>CurrentUserProfileName</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>System Administrator</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Date_Invited_0</name>
                    <dataType>Date</dataType>
                    <defaultValue>
                        <elementReference>Today</elementReference>
                    </defaultValue>
                    <fieldText>Date Invited</fieldText>
                    <fieldType>InputField</fieldType>
                    <helpText>&lt;p&gt;This field is used to capture the Date that the Customer was invited to the Event.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>New_Attendee_Customer_Attendee_0_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Number_of_Seats_0</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Number_of_Seats_0</elementReference>
                    </defaultValue>
                    <fieldText>Number of Seats</fieldText>
                    <fieldType>InputField</fieldType>
                    <helpText>&lt;p&gt;Populate to capture the number of seats for this Customer. For example, if this Customer is attending with one other guest. They will be assigned two seats.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <scale>0</scale>
                    <validationRule>
                        <errorMessage>&lt;p&gt;&lt;span style=&quot;color: rgb(62, 62, 60); background-color: rgb(255, 255, 255);&quot;&gt;This field can only accept up to 6 characters.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                        <formulaExpression>LEN(TEXT({!Number_of_Seats_0})) &lt;= 6</formulaExpression>
                    </validationRule>
                </fields>
                <fields>
                    <name>SeatLimitError_0</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 12px; color: rgb(234, 0, 30);&quot;&gt;You are trying to assign more seats to this Attendee than are available. No. of available seats remaining: &lt;/span&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); font-size: 12px; color: rgb(234, 0, 30);&quot;&gt;{!Get_Campaign_Details_2.Total_Seats_Remaining__c}&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Campaign_Details_2.Customer_Tickets_Remaining__c</leftValueReference>
                            <operator>LessThan</operator>
                            <rightValue>
                                <elementReference>Number_of_Seats_0</elementReference>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Status_0</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Offer Accepted</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>waitlistSeatError0</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0); font-size: 12px;&quot;&gt;You are trying to assign more seats to this Attendee than are availabe on the Waitlist. No. of available waitlist seats remaining: &lt;/span&gt;&lt;strong style=&quot;color: rgb(255, 0, 0); font-size: 12px;&quot;&gt;{!waitlistSeatsRemaining2}&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>waitlistSeatsRemaining2</leftValueReference>
                            <operator>LessThan</operator>
                            <rightValue>
                                <elementReference>Number_of_Seats_0</elementReference>
                            </rightValue>
                        </conditions>
                        <conditions>
                            <leftValueReference>Status_0</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <stringValue>Waitlisted</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Seat_Table_Number_0</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Seat_Table_Number_0</elementReference>
                    </defaultValue>
                    <fieldText>Seat/Table Number</fieldText>
                    <fieldType>InputField</fieldType>
                    <helpText>&lt;p&gt;Populate the Seat Number or Table Number that the Customer has been assigned for this Event.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <scale>0</scale>
                    <validationRule>
                        <errorMessage>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(62, 62, 60);&quot;&gt;This field can only accept up to 16 characters.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                        <formulaExpression>LEN(TEXT({!Seat_Table_Number_0})) &lt;= 16</formulaExpression>
                    </validationRule>
                </fields>
                <fields>
                    <name>Notes_0</name>
                    <defaultValue>
                        <stringValue>{!Notes_0}</stringValue>
                    </defaultValue>
                    <fieldText>Notes</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <helpText>&lt;p&gt;Populate any relevant Notes for this Attendee/Attendee&apos;s Guest(s). Eg. - Dietary Requirements, Weight (for Chopper) etc.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>New_Attendee_Customer_Attendee_0_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>New_Attendee_Customer_Attendee_0_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>BlankField_0</name>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Multi_Selection_Screen</name>
        <label>Multi Selection Screen</label>
        <locationX>1750</locationX>
        <locationY>300</locationY>
        <allowBack>true</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Process_Multi_Selection</targetReference>
        </connector>
        <fields>
            <name>MultiSelectionInfo</name>
            <fieldText>&lt;p&gt;&lt;b style="font-size: 18px; color: rgb(18, 106, 178);"&gt;Multiple Customers Selected&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style="font-size: 14px;"&gt;You have selected multiple customers. Please fill out the details below and all selected customers will be added as attendees with the same information.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b&gt;Selected Customer IDs:&lt;/b&gt; {!accountIdsString}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Campaign_Multi</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>Id</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Campaign</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>Campaign</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Status_Multi</name>
            <choiceReferences>AttendeeStatus</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>DateInvited_Multi</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>$Flow.CurrentDate</elementReference>
            </defaultValue>
            <fieldText>Date Invited</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>NumberOfSeats_Multi</name>
            <dataType>Number</dataType>
            <fieldText>Number of Seats</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <scale>0</scale>
        </fields>
        <fields>
            <name>SeatTableNumber_Multi</name>
            <dataType>String</dataType>
            <fieldText>Seat/Table Number</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Notes_Multi</name>
            <dataType>String</dataType>
            <fieldText>Notes</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Multi_Selection_Success</name>
        <label>Multi Selection Success</label>
        <locationX>1750</locationX>
        <locationY>500</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMessage</name>
            <fieldText>&lt;p&gt;&lt;b style="font-size: 18px; color: rgb(0, 153, 0);"&gt;✅ Multi-Selection Processed Successfully!&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style="font-size: 14px;"&gt;The selected customers have been processed for attendee creation.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b&gt;Customer IDs Processed:&lt;/b&gt; {!accountIdsString}&lt;/p&gt;&lt;p&gt;&lt;b&gt;Campaign:&lt;/b&gt; {!Campaign_Multi.recordId}&lt;/p&gt;&lt;p&gt;&lt;b&gt;Status:&lt;/b&gt; {!Status_Multi}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>



    <start>
        <locationX>1750</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Multi_Selection</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>test</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&amp;lt;h1&amp;gt;&amp;lt;mark&amp;gt;Test&amp;lt;/mark&amp;gt;&amp;lt;/h1&amp;gt;&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>AttendeeDupe</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AttendeeName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AttendeeRecordID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AttendeeRecordVariable</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
    <variables>
        <name>CampaignIdOutput</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CARecordTypeId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>EventParticipationCapExceeded</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>accountIdsString</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>isMultiSelection</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>accountIdsList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>currentAccountId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>


    <variables>
        <name>RGScoreVar</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
</Flow>
