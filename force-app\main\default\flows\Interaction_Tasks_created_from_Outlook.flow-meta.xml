<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Field_Criteria</name>
        <label>Field Criteria</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Fields</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Interaction</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Reason_for_Contact__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Subject</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Email:</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Interaction_Task</targetReference>
            </connector>
            <label>Fields</label>
        </rules>
    </decisions>
    <description>Auto Populated fields for Tasks Interaction created from Outlook</description>
    <environments>Default</environments>
    <interviewLabel>Hide all versionsInteraction Task created from Outlook {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Interaction Tasks created from Outlook</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetCustomer</name>
        <label>GetCustomer</label>
        <locationX>182</locationX>
        <locationY>311</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Field_Criteria</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhoId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Interaction_Task</name>
        <label>Interaction Task</label>
        <locationX>50</locationX>
        <locationY>551</locationY>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>GetCustomer.Account.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCustomer</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
