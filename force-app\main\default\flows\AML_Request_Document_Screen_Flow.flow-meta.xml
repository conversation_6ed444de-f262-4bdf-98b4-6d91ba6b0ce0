<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Request_Email</name>
        <label>Send Request Email</label>
        <locationX>50</locationX>
        <locationY>2270</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>Get_Customer_Integrity_email.Address</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>recordId.Account.PersonContactId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailTemplateId</name>
            <value>
                <elementReference>Get_email_template.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_Document_to_List</name>
        <label>Add Document to List</label>
        <locationX>468</locationX>
        <locationY>1106</locationY>
        <assignmentItems>
            <assignToReference>CaseIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>DocumentationRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_Selection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_Documentation_variables</name>
        <label>assign Documentation variables</label>
        <locationX>468</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>DocumentationRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_through_Selection.Label</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>DocumentationRecord.Case__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>DocumentationRecord.Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Pending</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>DocumentationRecord.Instructions__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_through_Selection.Upload_Instructions__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Confirm_Documentation_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Case_Fields</name>
        <label>Update Case Fields</label>
        <locationX>380</locationX>
        <locationY>1406</locationY>
        <assignmentItems>
            <assignToReference>recordId.Sub_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>pending_for_customer_response</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>recordId.Document_Requested_Date_Time__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Case</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_Email</name>
        <label>Check Email</label>
        <locationX>1056</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>No_Email_Exists</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Email</defaultConnectorLabel>
        <rules>
            <name>Email_Exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Account.PersonEmail</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Owner_role</targetReference>
            </connector>
            <label>Email Exists</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_owner</name>
        <label>check owner</label>
        <locationX>1345</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>screen_to_display_the_error_of_case_ownership</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>record owner is not a user</defaultConnectorLabel>
        <rules>
            <name>record_owner_is_user</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>005</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Email</targetReference>
            </connector>
            <label>record owner is user</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Owner_role</name>
        <label>Check Owner role</label>
        <locationX>743</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>No_Owner_Role</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Owner role</defaultConnectorLabel>
        <rules>
            <name>Owner_role_Exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Account.Owner.UserRoleId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Documentation_Metadata</targetReference>
            </connector>
            <label>Owner role Exists</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_User_Active</name>
        <label>Is User Active?</label>
        <locationX>182</locationX>
        <locationY>1946</locationY>
        <defaultConnector>
            <targetReference>Activate_and_Set_Deactivate_date_for_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Active</defaultConnectorLabel>
        <rules>
            <name>Yes_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Portal_User.IsActive</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Customer_Integrity_email</targetReference>
            </connector>
            <label>Yes Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>User_Exists</name>
        <label>User Exists?</label>
        <locationX>380</locationX>
        <locationY>1838</locationY>
        <defaultConnector>
            <targetReference>Get_AML_Profile</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No User</defaultConnectorLabel>
        <rules>
            <name>Yes_User_Exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Portal_User</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_User_Active</targetReference>
            </connector>
            <label>Yes User Exists</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>SetExpirationDate</name>
        <dataType>Date</dataType>
        <expression>TODAY()+180</expression>
    </formulas>
    <formulas>
        <name>UserAlias</name>
        <dataType>String</dataType>
        <expression>LEFT(LEFT({!recordId.Account.FirstName}, 1) + {!recordId.Account.LastName},8)</expression>
    </formulas>
    <interviewLabel>AML Request Document Screen Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Request Document Screen Flow</label>
    <loops>
        <name>Loop_through_Selection</name>
        <label>Loop through Selection</label>
        <locationX>380</locationX>
        <locationY>674</locationY>
        <collectionReference>DocumentationSelection.selectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>assign_Documentation_variables</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_created_documentation_records</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Documentation_Record</name>
        <label>Create Documentation Record</label>
        <locationX>468</locationX>
        <locationY>998</locationY>
        <connector>
            <targetReference>Add_Document_to_List</targetReference>
        </connector>
        <inputReference>DocumentationRecord</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Create_Portal_User</name>
        <label>Create Portal User</label>
        <locationX>578</locationX>
        <locationY>2054</locationY>
        <connector>
            <targetReference>Create_Welcome_email_sent_task</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Error_Message</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>AML_User_Deactivation_Date__c</field>
            <value>
                <elementReference>SetExpirationDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Alias</field>
            <value>
                <elementReference>UserAlias</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>recordId.Account.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email</field>
            <value>
                <elementReference>recordId.Account.PersonEmail</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>EmailEncodingKey</field>
            <value>
                <stringValue>ISO-8859-1</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FirstName</field>
            <value>
                <elementReference>recordId.Account.FirstName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LanguageLocaleKey</field>
            <value>
                <stringValue>en_US</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LastName</field>
            <value>
                <elementReference>recordId.Account.LastName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LocaleSidKey</field>
            <value>
                <stringValue>en_AU</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ProfileId</field>
            <value>
                <elementReference>Get_AML_Profile.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TimeZoneSidKey</field>
            <value>
                <stringValue>Australia/Sydney</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Username</field>
            <value>
                <elementReference>recordId.Account.PersonEmail</elementReference>
            </value>
        </inputAssignments>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Welcome_email_sent_task</name>
        <label>Create Welcome email sent task</label>
        <locationX>578</locationX>
        <locationY>2162</locationY>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Hi XXXX,   Welcome to the Sportsbet Portal.  This Portal is used to securely upload supporting documentation requested by Sportsbet.    To get started, click here.   This link will expire in 7 days.  Your username is XXXX. Please keep this in case you need to reset your password.  If you require assistance, please contact 1800 990 907.    Regards,  Sportsbet</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>recordId.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Welcome Email Sent</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>recordId.ContactId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_AML_Profile</name>
        <label>Get AML Profile</label>
        <locationX>578</locationX>
        <locationY>1946</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Portal_User</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Customer AML Portal</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Profile</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_created_documentation_records</name>
        <label>Get created documentation records</label>
        <locationX>380</locationX>
        <locationY>1298</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Case_Fields</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>CaseIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Documentation__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get it from Org wide addresses</description>
        <name>Get_Customer_Integrity_email</name>
        <label>Get Customer Integrity email</label>
        <locationX>50</locationX>
        <locationY>2054</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_email_template</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DisplayName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Customer Integrity</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Documentation_Metadata</name>
        <label>Get Documentation Metadata</label>
        <locationX>380</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Select_Documents</targetReference>
        </connector>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>AML_Document_Types__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_email_template</name>
        <label>Get email template</label>
        <locationX>50</locationX>
        <locationY>2162</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Send_Request_Email</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>AML Request Documents Email</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Portal_User</name>
        <label>Get Portal User</label>
        <locationX>380</locationX>
        <locationY>1730</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>User_Exists</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ContactId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Account.PersonContact.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Activate_and_Set_Deactivate_date_for_User</name>
        <label>Activate and Set Deactivate date for User</label>
        <locationX>314</locationX>
        <locationY>2054</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Portal_User.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>AML_User_Deactivation_Date__c</field>
            <value>
                <elementReference>SetExpirationDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>IsActive</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>User</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Case</name>
        <label>Update Case</label>
        <locationX>380</locationX>
        <locationY>1514</locationY>
        <connector>
            <targetReference>Document_Select</targetReference>
        </connector>
        <inputReference>recordId</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>Confirm_Documentation_Record</name>
        <label>Confirm Documentation Record</label>
        <locationX>468</locationX>
        <locationY>890</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Create_Documentation_Record</targetReference>
        </connector>
        <fields>
            <name>EditText</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Please review and make any additional last changes.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <fieldType>ObjectProvided</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <objectFieldReference>DocumentationRecord.Name</objectFieldReference>
        </fields>
        <fields>
            <fieldType>ObjectProvided</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <objectFieldReference>DocumentationRecord.Instructions__c</objectFieldReference>
        </fields>
        <nextOrFinishButtonLabel>Create Documentation Record</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Document_Select</name>
        <label>Document Select</label>
        <locationX>380</locationX>
        <locationY>1622</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Get_Portal_User</targetReference>
        </connector>
        <fields>
            <name>Documentlist</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Documentation__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>The below documentation records have been successfully created</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_created_documentation_records</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-c5c0&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Document Type&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Document Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Instructions__c&quot;,&quot;guid&quot;:&quot;column-3d18&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Instructions&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Error_Message</name>
        <label>Error Message</label>
        <locationX>842</locationX>
        <locationY>2162</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>ErrorMessage</name>
            <fieldText>&lt;p&gt;There was an error when creating the customers portal user. Please review below error for more detail.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>No_Email_Exists</name>
        <label>No Email Exists</label>
        <locationX>1370</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NoEmailDisplayText</name>
            <fieldText>&lt;p&gt;This Customer does not have an Email associated and therefore we cannot proceed.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>No_Owner_Role</name>
        <label>No Owner Role</label>
        <locationX>1106</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NoownerRoleText</name>
            <fieldText>&lt;p&gt;This Customer record owner does not have a role assigned and therefore we cannot proceed. Reach out to the system admin to add the role to the owner user record, before proceeding.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>screen_to_display_the_error_of_case_ownership</name>
        <label>screen to display the error of case ownership</label>
        <locationX>1634</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ownership_error_message</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px; color: rgb(211, 0, 0);&quot;&gt;Change the Case Owner to yourself before requesting documents.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Okay</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Select_Documents</name>
        <label>Select Documents</label>
        <locationX>380</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Loop_through_Selection</targetReference>
        </connector>
        <fields>
            <name>DocumentationSelection</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>AML_Document_Types__mdt</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Please select the documents that you would like to request from the customer.</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Documentation_Metadata</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Label&quot;,&quot;guid&quot;:&quot;column-0145&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Document Type&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Label&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Upload_Instructions__c&quot;,&quot;guid&quot;:&quot;column-18e3&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Upload Instructions&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>1219</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>check_owner</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>DocumentRequestSubject</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&lt;strong&gt;Documents have been requested from you.&lt;/strong&gt;&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>DocumentsRequestEmail</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&lt;img src=&quot;https://sb-premium--sbetuat2.sandbox.file.force.com/sfc/servlet.shepherd/version/download/0689j000004F2wr?asPdf=false&amp;amp;operationContext=CHATTER&quot; alt=&quot;SLIM for Email 2.png&quot;&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Documents have been requested from you.&lt;/span&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Please login to the Sportsbet Customer Integrity Portal to upload the requested documents.&lt;/span&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;a href=&quot;https://sb-premium--sbetuat2.sandbox.my.site.com/customerintegrityportal&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot; style=&quot;font-size: 16px;&quot;&gt;Click here to Login&lt;/a&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;If you require assistance, please contact&amp;nbsp;1800 990 907.&lt;/span&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Regards,&lt;/span&gt;&lt;/p&gt;&lt;p class=&quot;ql-indent-1&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Sportsbet&lt;/span&gt;&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>CaseIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>DocumentationList</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Documentation__c</objectType>
    </variables>
    <variables>
        <name>DocumentationRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Documentation__c</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
</Flow>
