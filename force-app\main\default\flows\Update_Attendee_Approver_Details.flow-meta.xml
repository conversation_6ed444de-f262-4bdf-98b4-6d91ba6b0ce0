<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>56.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>AttendeeRecordType</name>
        <dataType>String</dataType>
        <value>
            <stringValue>0122y000000GqbLAAS</stringValue>
        </value>
    </constants>
    <constants>
        <name>EventOverThresholdId</name>
        <dataType>String</dataType>
        <value>
            <stringValue>04a2y000000PAuZAAW</stringValue>
        </value>
    </constants>
    <constants>
        <name>NoOfSeatsGreaterId</name>
        <dataType>String</dataType>
        <value>
            <stringValue>04a2y0000004JDtAAM</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Checks which approval process type is triggered</description>
        <name>Check_Approval_Process_Type</name>
        <label>Check Approval Process Type</label>
        <locationX>1436</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Event_Over_Threshold_Triggered</name>
            <conditionLogic>1 AND ((2 AND 3) OR (3 AND 4))</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>AttendeeRecordType</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Details_ProcessInstance_Object_1</targetReference>
            </connector>
            <label>Event Over Threshold Triggered</label>
        </rules>
        <rules>
            <name>No_of_Guests_Triggered</name>
            <conditionLogic>1 AND ((2 AND 3) OR (3 AND 4 AND 5 AND 6))</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>AttendeeRecordType</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.No_of_Guest_Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.No_of_Guest_Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ProcessInstance_Object_2</targetReference>
            </connector>
            <label>No of Guests Triggered</label>
        </rules>
        <rules>
            <name>Event_Over_Threshold_Actioned</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>AttendeeRecordType</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.No_of_Guest_Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.No_of_Guest_Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ProcessInstance_Object_3</targetReference>
            </connector>
            <label>Event Over Threshold Actioned</label>
        </rules>
        <rules>
            <name>No_of_Guests_Actioned</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>AttendeeRecordType</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.No_of_Guest_Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.No_of_Guest_Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ProcessInstance_Object_4</targetReference>
            </connector>
            <label>No of Guests Actioned</label>
        </rules>
        <rules>
            <name>Event_Over_Threshold_Actioned_No_of_Guests_Triggered</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>AttendeeRecordType</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.No_of_Guest_Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ProcessInstance_Object_5a_EventOverThreshold</targetReference>
            </connector>
            <label>Event Over Threshold Actioned No of Guests Triggered</label>
        </rules>
    </decisions>
    <decisions>
        <name>ProcessInstance_not_null_1</name>
        <label>ProcessInstance not null 1</label>
        <locationX>182</locationX>
        <locationY>815</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ProcessInstance_record_is_not_NULL_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Details_ProcessInstance_Object_1.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Approval_Type_1</targetReference>
            </connector>
            <label>ProcessInstance record is not NULL 1</label>
        </rules>
    </decisions>
    <decisions>
        <name>ProcessInstance_Object_3_has_record</name>
        <label>ProcessInstance Object 3 has record</label>
        <locationX>1238</locationX>
        <locationY>695</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ProcessInstance_Object_3_not_NULL</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ProcessInstance_Object_3.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Approval_Details_3</targetReference>
            </connector>
            <label>ProcessInstance Object 3 not NULL</label>
        </rules>
    </decisions>
    <decisions>
        <name>ProcessInstance_Object_4_has_records</name>
        <label>ProcessInstance Object 4 has records</label>
        <locationX>1766</locationX>
        <locationY>695</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ProcessInstance_Object_4_NOT_NULL</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ProcessInstance_Object_4.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Approval_Details_4</targetReference>
            </connector>
            <label>ProcessInstance Object 4 NOT NULL</label>
        </rules>
    </decisions>
    <decisions>
        <name>ProcessInstance_object_5a_and_5b_not_null</name>
        <label>ProcessInstance object 5a and 5b not null</label>
        <locationX>2294</locationX>
        <locationY>1055</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ProcessInstance_5a_5b_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ProcessInstance_Object_5a_EventOverThreshold.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>ProcessInstance_Object_5b_1Guest.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_EventOverThreshold_approver_details</targetReference>
            </connector>
            <label>ProcessInstance 5a &amp; 5b not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>ProcessInstance_Object_has_record_2</name>
        <label>ProcessInstance Object has record 2</label>
        <locationX>710</locationX>
        <locationY>815</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ProcessInstance_record_2_is_not_NULL</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ProcessInstance_Object_2.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Approval_Type_2</targetReference>
            </connector>
            <label>ProcessInstance record 2 is not NULL</label>
        </rules>
    </decisions>
    <description>Flow updates Attendee Actual approver and comments</description>
    <environments>Default</environments>
    <formulas>
        <name>isNew</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <interviewLabel>Update Attendee Approver Details {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Attendee Approver Details</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Approval Type from ProcessInstance</description>
        <name>Get_Details_ProcessInstance_Object_1</name>
        <label>Get Details ProcessInstance Object 1</label>
        <locationX>182</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstanceStep_Object_1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>TargetObjectId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstance</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>ProcessDefinition_details_1</name>
        <label>ProcessDefinition details 1</label>
        <locationX>182</locationX>
        <locationY>695</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstance_not_null_1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Details_ProcessInstance_Object_1.ProcessDefinitionId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessDefinition</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>ProcessDefinition_Details_2</name>
        <label>ProcessDefinition Details 2</label>
        <locationX>710</locationX>
        <locationY>695</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstance_Object_has_record_2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object_2.ProcessDefinitionId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessDefinition</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>ProcessDefinition_Details_5</name>
        <label>ProcessDefinition Details 5</label>
        <locationX>2294</locationX>
        <locationY>935</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstance_object_5a_and_5b_not_null</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object_5b_1Guest.ProcessDefinitionId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessDefinition</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Approval details from the ProcessInstance Object</description>
        <name>ProcessInstance_Object_2</name>
        <label>ProcessInstance Object 2</label>
        <locationX>710</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstanceStep_Object_2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>TargetObjectId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstance</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get ProcessInstance Object details after approval process actioned</description>
        <name>ProcessInstance_Object_3</name>
        <label>ProcessInstance Object 3</label>
        <locationX>1238</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstanceStep_Object_3</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>TargetObjectId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstance</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get ProcessInstance Object details after approved/Rejected</description>
        <name>ProcessInstance_Object_4</name>
        <label>ProcessInstance Object 4</label>
        <locationX>1766</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstanceStep_Object_4</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>TargetObjectId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstance</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the ProcessInstance Object details after the event over over threshold is actioned and before &gt;1 guests is triggered</description>
        <name>ProcessInstance_Object_5a_EventOverThreshold</name>
        <label>ProcessInstance Object 5a EventOverThreshold</label>
        <locationX>2294</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstance_Object_5b_1Guest</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>TargetObjectId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>ProcessDefinitionId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>EventOverThresholdId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstance</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get ProcessInstance record for More than 1 Guest approval trigger</description>
        <name>ProcessInstance_Object_5b_1Guest</name>
        <label>ProcessInstance Object 5b 1Guest</label>
        <locationX>2294</locationX>
        <locationY>575</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstanceStep_5a_EventOverThreshold</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>TargetObjectId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>ProcessDefinitionId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>NoOfSeatsGreaterId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstance</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>ProcessInstanceStep_5a_EventOverThreshold</name>
        <label>ProcessInstanceStep 5a EventOverThreshold</label>
        <locationX>2294</locationX>
        <locationY>695</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstanceStep_5b_NoOfGuests</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProcessInstanceId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object_5a_EventOverThreshold.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstanceStep</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>ProcessInstanceStep_5b_NoOfGuests</name>
        <label>ProcessInstanceStep 5b NoOfGuests</label>
        <locationX>2294</locationX>
        <locationY>815</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessDefinition_Details_5</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProcessInstanceId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object_5b_1Guest.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstanceStep</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Approval Type from ProcessInstanceStep Object</description>
        <name>ProcessInstanceStep_Object_1</name>
        <label>ProcessInstanceStep Object 1</label>
        <locationX>182</locationX>
        <locationY>575</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessDefinition_details_1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProcessInstanceId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Details_ProcessInstance_Object_1.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstanceStep</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Approval details from ProcessInstanceStep Object</description>
        <name>ProcessInstanceStep_Object_2</name>
        <label>ProcessInstanceStep Object 2</label>
        <locationX>710</locationX>
        <locationY>575</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessDefinition_Details_2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProcessInstanceId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object_2.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstanceStep</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>ProcessInstanceStep_Object_3</name>
        <label>ProcessInstanceStep Object 3</label>
        <locationX>1238</locationX>
        <locationY>575</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstance_Object_3_has_record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProcessInstanceId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object_3.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstanceStep</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get ProcessInstanceStep Object details after Approved/Rejected 4</description>
        <name>ProcessInstanceStep_Object_4</name>
        <label>ProcessInstanceStep Object 4</label>
        <locationX>1766</locationX>
        <locationY>575</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstance_Object_4_has_records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProcessInstanceId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object_4.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstanceStep</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update Event over threshold approver details</description>
        <name>Update_Approval_Details_3</name>
        <label>Update Approval Details 3</label>
        <locationX>1106</locationX>
        <locationY>815</locationY>
        <inputAssignments>
            <field>Approval_Comments_Event_over_threshold__c</field>
            <value>
                <elementReference>ProcessInstanceStep_Object_3.Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Approval_Date_Event_over_threshold__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Approver_Event_over_threshold__c</field>
            <value>
                <elementReference>ProcessInstance_Object_3.LastActorId</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update No of Guests &gt; 1 approver details</description>
        <name>Update_Approval_Details_4</name>
        <label>Update Approval Details 4</label>
        <locationX>1634</locationX>
        <locationY>815</locationY>
        <inputAssignments>
            <field>Approval_Comments_Greater_than_1_guest__c</field>
            <value>
                <elementReference>ProcessInstanceStep_Object_4.Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Approval_Date_Greater_than_1_guest__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Approver_Greater_than_1_guest__c</field>
            <value>
                <elementReference>ProcessInstance_Object_4.LastActorId</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Approval_Type_1</name>
        <label>Update Approval Type 1</label>
        <locationX>50</locationX>
        <locationY>935</locationY>
        <inputAssignments>
            <field>Approval_Type_Event_over_threshold__c</field>
            <value>
                <elementReference>ProcessDefinition_details_1.Name</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update No of Guests &gt; 1 approval type</description>
        <name>Update_Approval_Type_2</name>
        <label>Update Approval Type 2</label>
        <locationX>578</locationX>
        <locationY>935</locationY>
        <inputAssignments>
            <field>Approval_Type_Greater_than_1_guest__c</field>
            <value>
                <elementReference>ProcessDefinition_Details_2.Name</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_EventOverThreshold_approver_details</name>
        <label>Update EventOverThreshold approver details</label>
        <locationX>2162</locationX>
        <locationY>1175</locationY>
        <inputAssignments>
            <field>Approval_Comments_Event_over_threshold__c</field>
            <value>
                <elementReference>ProcessInstanceStep_5a_EventOverThreshold.Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Approval_Date_Event_over_threshold__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Approval_Type_Greater_than_1_guest__c</field>
            <value>
                <elementReference>ProcessDefinition_Details_5.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Approver_Event_over_threshold__c</field>
            <value>
                <elementReference>ProcessInstance_Object_5a_EventOverThreshold.LastActorId</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>1310</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Approval_Process_Type</targetReference>
        </connector>
        <object>Attendee__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
