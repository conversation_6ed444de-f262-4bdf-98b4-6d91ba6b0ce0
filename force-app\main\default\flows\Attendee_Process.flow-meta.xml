<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Event Over Threshold Approval - Event_Over_Threshold_Approval</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>reference</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_1_A1</name>
        <label>Submit for Approval</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision2</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Customer has exceeded the allowed Number of Events Attended per Year.</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Event_Over_Threshold_Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>myVariable_current.Account__r.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Number of Seats (Guests) Over Two Approv - Number_of_Seats_Guests_Over_Two_Approv</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>reference</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_3_A1</name>
        <label>Submit for Approval: More than 2 Guests</label>
        <locationX>300</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision4</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>This customer has more than 1 guest allocated to an event and requires approval to proceed as per the Premium Events &amp; Experiences Policy.</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Number_of_Seats_Guests_Over_Two_Approv</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>myVariable_current.Account__r.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Customer: Event Last Event Accepted Date</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_9_A1</name>
        <label>Call Flow</label>
        <locationX>900</locationX>
        <locationY>200</locationY>
        <actionName>Customer_Event_Last_Event_Accepted_Date</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision10</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>CustomerId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>CustomerId</name>
            <value>
                <elementReference>myVariable_current.Account__c</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Customer_Event_Last_Event_Accepted_Date</nameSegment>
    </actionCalls>
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_A1</targetReference>
            </connector>
            <label>Interaction is created and campaign capacity is full</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>4.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision10</name>
        <label>myDecision10</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision13</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_11</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Log_an_Interaction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_11_pmetdec</targetReference>
            </connector>
            <label>Interaction checkbox is set to TRUE</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>5.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision13</name>
        <label>myDecision13</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision15</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_14</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Attended__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_14_pmetdec</targetReference>
            </connector>
            <label>Attended Checkbox is set to TRUE</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>6.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision15</name>
        <label>myDecision15</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_16</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Guest_Name__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Date</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Date</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Date_of_Birth__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Is_SportsBet_Customer__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_16_pmetdec</targetReference>
            </connector>
            <label>Populate Guest Check Confirmed</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision2</name>
        <label>myDecision2</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision4</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_3</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_3_A1</targetReference>
            </connector>
            <label>Number of Seats is More than 2</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>2.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision4</name>
        <label>myDecision4</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision8</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_5</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_5</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_5_pmetdec</targetReference>
            </connector>
            <label>Status is Offer Accepted</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>3.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision8</name>
        <label>myDecision8</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision10</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_9</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_9</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_9_pmetdec</targetReference>
            </connector>
            <label>Status Is Changed to Other Value from Offer Accepted</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_11_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>1100</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_11_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_11_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_11_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_11_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_old.Log_an_Interaction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_14_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>1400</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_14_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_14_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_14_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_14_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_old.Attended__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_16_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>1600</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_16_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_16_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_16_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_16_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_old.Guest_Name__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Date</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Date</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_old.Date_of_Birth__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_old.Is_SportsBet_Customer__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_5_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>500</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_5_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_5_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_5_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_5_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_5_pmetrule</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision8</targetReference>
            </connector>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_9_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>900</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_9_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_9_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_9_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_9_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_9_pmetrule</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision10</targetReference>
            </connector>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <description>This Process is used for automation triggered from the Attendee Object</description>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>TODAY()</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_12_myRule_11_A1_4074279868</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>TODAY()</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_17_myRule_16_A1_5959942439</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>TODAY()</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_6_myRule_5_A2_0206164846</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>TODAY()</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_7_myRule_5_A1_5412044639</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>/*Runs on New Record*/

(!$Setup.Process_Automation_Control_Panel__c.Attendee_PB_Entry_1__c 
&amp;&amp;
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
&amp;&amp;
!ISBLANK([Attendee__c].Account__c )
&amp;&amp;
[Attendee__c].Event_Participation_Cap_Exceeded__c = TRUE
&amp;&amp;
ISPICKVAL([Attendee__c].Status__c, &apos;Offer Accepted&apos;)
&amp;&amp;
NOT(ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;))
&amp;&amp;
![Attendee__c].Approval_Process_Triggered__c
&amp;&amp;
ISNEW()) ||



/*Runs on Update of Record*/
(!$Setup.Process_Automation_Control_Panel__c.Attendee_PB_Entry_1__c 
&amp;&amp;
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
&amp;&amp;
!ISBLANK([Attendee__c].Account__c )
&amp;&amp;
[Attendee__c].Event_Participation_Cap_Exceeded__c = TRUE
&amp;&amp;
ISPICKVAL([Attendee__c].Status__c, &apos;Offer Accepted&apos;)
&amp;&amp;
NOT(ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;))
&amp;&amp;
ISCHANGED([Attendee__c].Status__c)
&amp;&amp;
![Attendee__c].Approval_Process_Triggered__c)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1</name>
        <dataType>Boolean</dataType>
        <expression>/*Runs on New Record*/

(!{!$Setup.Process_Automation_Control_Panel__c.Attendee_PB_Entry_1__c} 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
&amp;&amp;
!ISBLANK({!myVariable_current.Account__c} )
&amp;&amp;
{!myVariable_current.Event_Participation_Cap_Exceeded__c} = TRUE
&amp;&amp;
ISPICKVAL({!myVariable_current.Status__c}, &apos;Offer Accepted&apos;)
&amp;&amp;
NOT(ISPICKVAL(PRIORVALUE({!myVariable_current.Status__c}), &quot;Pending Approval&quot;))
&amp;&amp;
!{!myVariable_current.Approval_Process_Triggered__c}
&amp;&amp;
ISNEW()) ||



/*Runs on Update of Record*/
(!{!$Setup.Process_Automation_Control_Panel__c.Attendee_PB_Entry_1__c} 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
&amp;&amp;
!ISBLANK({!myVariable_current.Account__c} )
&amp;&amp;
{!myVariable_current.Event_Participation_Cap_Exceeded__c} = TRUE
&amp;&amp;
ISPICKVAL({!myVariable_current.Status__c}, &apos;Offer Accepted&apos;)
&amp;&amp;
NOT(ISPICKVAL(PRIORVALUE({!myVariable_current.Status__c}), &quot;Pending Approval&quot;))
&amp;&amp;
ISCHANGED({!myVariable_current.Status__c})
&amp;&amp;
!{!myVariable_current.Approval_Process_Triggered__c})</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>OR(

/* Runs on New Record and does not hit the prev approval process */

AND(
ISNEW(),
[Attendee__c].Number_Of_Seats__c &gt; 2,
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].RecordType.DeveloperName = &quot;Customer_Attendee&quot;,
[Attendee__c].Event_Participation_Cap_Exceeded__c = FALSE
),


/* Runs on Update of record and does not hit the prev approval process */

AND(
[Attendee__c].Number_Of_Seats__c &gt; 2,
ISCHANGED([Attendee__c].Status__c),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
NOT(ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;)),
[Attendee__c].RecordType.DeveloperName = &quot;Customer_Attendee&quot;,
[Attendee__c].Event_Participation_Cap_Exceeded__c = FALSE
),


/* Runs on Update of record and is executed by the prev approval process */

AND(
[Attendee__c].Number_Of_Seats__c &gt; 2,
ISCHANGED([Attendee__c].Status__c),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;),
[Attendee__c].RecordType.DeveloperName = &quot;Customer_Attendee&quot;,
ISCHANGED([Attendee__c].Approval_Process_Triggered__c), 
PRIORVALUE([Attendee__c].Approval_Process_Triggered__c) = TRUE
),


/* Runs on Update of record when the Number of Seats was changed and status was Offer Accepted*/

AND(
ISCHANGED([Attendee__c].Number_Of_Seats__c),
[Attendee__c].Number_Of_Seats__c &gt; 2,
PRIORVALUE([Attendee__c].Number_Of_Seats__c) &lt; [Attendee__c].Number_Of_Seats__c,
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].RecordType.DeveloperName = &quot;Customer_Attendee&quot;
)

)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_3</name>
        <dataType>Boolean</dataType>
        <expression>OR(

/* Runs on New Record and does not hit the prev approval process */

AND(
ISNEW(),
{!myVariable_current.Number_Of_Seats__c} &gt; 2,
TEXT({!myVariable_current.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_current.RecordType.DeveloperName} = &quot;Customer_Attendee&quot;,
{!myVariable_current.Event_Participation_Cap_Exceeded__c} = FALSE
),


/* Runs on Update of record and does not hit the prev approval process */

AND(
{!myVariable_current.Number_Of_Seats__c} &gt; 2,
ISCHANGED({!myVariable_current.Status__c}),
TEXT({!myVariable_current.Status__c}) = &quot;Offer Accepted&quot;,
NOT(ISPICKVAL(PRIORVALUE({!myVariable_current.Status__c}), &quot;Pending Approval&quot;)),
{!myVariable_current.RecordType.DeveloperName} = &quot;Customer_Attendee&quot;,
{!myVariable_current.Event_Participation_Cap_Exceeded__c} = FALSE
),


/* Runs on Update of record and is executed by the prev approval process */

AND(
{!myVariable_current.Number_Of_Seats__c} &gt; 2,
ISCHANGED({!myVariable_current.Status__c}),
TEXT({!myVariable_current.Status__c}) = &quot;Offer Accepted&quot;,
ISPICKVAL(PRIORVALUE({!myVariable_current.Status__c}), &quot;Pending Approval&quot;),
{!myVariable_current.RecordType.DeveloperName} = &quot;Customer_Attendee&quot;,
ISCHANGED({!myVariable_current.Approval_Process_Triggered__c}), 
PRIORVALUE({!myVariable_current.Approval_Process_Triggered__c}) = TRUE
),


/* Runs on Update of record when the Number of Seats was changed and status was Offer Accepted*/

AND(
ISCHANGED({!myVariable_current.Number_Of_Seats__c}),
{!myVariable_current.Number_Of_Seats__c} &gt; 2,
PRIORVALUE({!myVariable_current.Number_Of_Seats__c}) &lt; {!myVariable_current.Number_Of_Seats__c},
TEXT({!myVariable_current.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_current.RecordType.DeveloperName} = &quot;Customer_Attendee&quot;
)

)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>OR(

/* For new records that did not hit any approval processes*/
AND(
ISNEW(),
TEXT([Attendee__c].Status__c ) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c &lt;= 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = FALSE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
),

/* For update records that did not hit any approval process*/

AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c&lt;= 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = FALSE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
),


/* Runs after the Event Participation cap approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c &lt;= 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = TRUE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
),


/* Runs after the Guests with more than 1 guest approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c &gt; 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = FALSE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;,
ISCHANGED([Attendee__c].No_of_Guest_Approval_Process_Triggered__c),
[Attendee__c].No_of_Guest_Approval_Process_Triggered__c = FALSE
),


/* Runs after the 2 Approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c &gt; 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = TRUE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;,
ISCHANGED([Attendee__c].No_of_Guest_Approval_Process_Triggered__c),
[Attendee__c].No_of_Guest_Approval_Process_Triggered__c = FALSE
)


)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_5</name>
        <dataType>Boolean</dataType>
        <expression>OR(

/* For new records that did not hit any approval processes*/
AND(
ISNEW(),
TEXT({!myVariable_current.Status__c} ) = &quot;Offer Accepted&quot;,
{!myVariable_current.Number_Of_Seats__c} &lt;= 2,
{!myVariable_current.Event_Participation_Cap_Exceeded__c} = FALSE,
{!myVariable_current.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
),

/* For update records that did not hit any approval process*/

AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_current.Status__c}),
TEXT({!myVariable_current.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_current.Number_Of_Seats__c}&lt;= 2,
{!myVariable_current.Event_Participation_Cap_Exceeded__c} = FALSE,
{!myVariable_current.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
),


/* Runs after the Event Participation cap approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_current.Status__c}),
ISPICKVAL(PRIORVALUE({!myVariable_current.Status__c}), &quot;Pending Approval&quot;),
TEXT({!myVariable_current.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_current.Number_Of_Seats__c} &lt;= 2,
{!myVariable_current.Event_Participation_Cap_Exceeded__c} = TRUE,
{!myVariable_current.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
),


/* Runs after the Guests with more than 1 guest approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_current.Status__c}),
ISPICKVAL(PRIORVALUE({!myVariable_current.Status__c}), &quot;Pending Approval&quot;),
TEXT({!myVariable_current.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_current.Number_Of_Seats__c} &gt; 2,
{!myVariable_current.Event_Participation_Cap_Exceeded__c} = FALSE,
{!myVariable_current.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;,
ISCHANGED({!myVariable_current.No_of_Guest_Approval_Process_Triggered__c}),
{!myVariable_current.No_of_Guest_Approval_Process_Triggered__c} = FALSE
),


/* Runs after the 2 Approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_current.Status__c}),
ISPICKVAL(PRIORVALUE({!myVariable_current.Status__c}), &quot;Pending Approval&quot;),
TEXT({!myVariable_current.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_current.Number_Of_Seats__c} &gt; 2,
{!myVariable_current.Event_Participation_Cap_Exceeded__c} = TRUE,
{!myVariable_current.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;,
ISCHANGED({!myVariable_current.No_of_Guest_Approval_Process_Triggered__c}),
{!myVariable_current.No_of_Guest_Approval_Process_Triggered__c} = FALSE
)


)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>OR(

/* For new records that did not hit any approval processes*/
AND(
ISNEW(),
TEXT([Attendee__c].Status__c ) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c &lt;= 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = FALSE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
),

/* For update records that did not hit any approval process*/

AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c&lt;= 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = FALSE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
),


/* Runs after the Event Participation cap approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c &lt;= 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = TRUE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
),


/* Runs after the Guests with more than 1 guest approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c &gt; 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = FALSE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;,
ISCHANGED([Attendee__c].No_of_Guest_Approval_Process_Triggered__c),
[Attendee__c].No_of_Guest_Approval_Process_Triggered__c = FALSE
),


/* Runs after the 2 Approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Pending Approval&quot;),
TEXT([Attendee__c].Status__c) = &quot;Offer Accepted&quot;,
[Attendee__c].Number_Of_Seats__c &gt; 2,
[Attendee__c].Event_Participation_Cap_Exceeded__c = TRUE,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;,
ISCHANGED([Attendee__c].No_of_Guest_Approval_Process_Triggered__c),
[Attendee__c].No_of_Guest_Approval_Process_Triggered__c = FALSE
)


)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_5_pmetrule</name>
        <dataType>Boolean</dataType>
        <expression>OR(

/* For new records that did not hit any approval processes*/
AND(
ISNEW(),
TEXT({!myVariable_old.Status__c} ) = &quot;Offer Accepted&quot;,
{!myVariable_old.Number_Of_Seats__c} &lt;= 2,
{!myVariable_old.Event_Participation_Cap_Exceeded__c} = FALSE,
{!myVariable_old.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
),

/* For update records that did not hit any approval process*/

AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_old.Status__c}),
TEXT({!myVariable_old.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_old.Number_Of_Seats__c}&lt;= 2,
{!myVariable_old.Event_Participation_Cap_Exceeded__c} = FALSE,
{!myVariable_old.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
),


/* Runs after the Event Participation cap approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_old.Status__c}),
ISPICKVAL(PRIORVALUE({!myVariable_old.Status__c}), &quot;Pending Approval&quot;),
TEXT({!myVariable_old.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_old.Number_Of_Seats__c} &lt;= 2,
{!myVariable_old.Event_Participation_Cap_Exceeded__c} = TRUE,
{!myVariable_old.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
),


/* Runs after the Guests with more than 1 guest approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_old.Status__c}),
ISPICKVAL(PRIORVALUE({!myVariable_old.Status__c}), &quot;Pending Approval&quot;),
TEXT({!myVariable_old.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_old.Number_Of_Seats__c} &gt; 2,
{!myVariable_old.Event_Participation_Cap_Exceeded__c} = FALSE,
{!myVariable_old.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;,
ISCHANGED({!myVariable_old.No_of_Guest_Approval_Process_Triggered__c}),
{!myVariable_old.No_of_Guest_Approval_Process_Triggered__c} = FALSE
),


/* Runs after the 2 Approval process was executed and it was approved*/

AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_old.Status__c}),
ISPICKVAL(PRIORVALUE({!myVariable_old.Status__c}), &quot;Pending Approval&quot;),
TEXT({!myVariable_old.Status__c}) = &quot;Offer Accepted&quot;,
{!myVariable_old.Number_Of_Seats__c} &gt; 2,
{!myVariable_old.Event_Participation_Cap_Exceeded__c} = TRUE,
{!myVariable_old.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;,
ISCHANGED({!myVariable_old.No_of_Guest_Approval_Process_Triggered__c}),
{!myVariable_old.No_of_Guest_Approval_Process_Triggered__c} = FALSE
)


)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Offer Accepted&quot;),
TEXT([Attendee__c].Status__c) &lt;&gt; &quot;Pending Approval&quot;,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_9</name>
        <dataType>Boolean</dataType>
        <expression>AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_current.Status__c}),
ISPICKVAL(PRIORVALUE({!myVariable_current.Status__c}), &quot;Offer Accepted&quot;),
TEXT({!myVariable_current.Status__c}) &lt;&gt; &quot;Pending Approval&quot;,
{!myVariable_current.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>AND(
NOT(ISNEW()),
ISCHANGED([Attendee__c].Status__c),
ISPICKVAL(PRIORVALUE([Attendee__c].Status__c), &quot;Offer Accepted&quot;),
TEXT([Attendee__c].Status__c) &lt;&gt; &quot;Pending Approval&quot;,
[Attendee__c].RecordType.DeveloperName != &apos;Guest_Attendee&apos;
)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_9_pmetrule</name>
        <dataType>Boolean</dataType>
        <expression>AND(
NOT(ISNEW()),
ISCHANGED({!myVariable_old.Status__c}),
ISPICKVAL(PRIORVALUE({!myVariable_old.Status__c}), &quot;Offer Accepted&quot;),
TEXT({!myVariable_old.Status__c}) &lt;&gt; &quot;Pending Approval&quot;,
{!myVariable_old.RecordType.DeveloperName} != &apos;Guest_Attendee&apos;
)</expression>
    </formulas>
    <interviewLabel>Attendee_Process-29_InterviewLabel</interviewLabel>
    <label>Attendee Process</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Attendee__c</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>ObjectVariable</name>
        <value>
            <elementReference>myVariable_current</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OldObjectVariable</name>
        <value>
            <elementReference>myVariable_old</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>TriggerType</name>
        <value>
            <stringValue>onAllChanges</stringValue>
        </value>
    </processMetadataValues>
    <processType>Workflow</processType>
    <recordCreates>
        <name>myRule_11_A1</name>
        <label>Interaction is created</label>
        <locationX>1100</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myRule_11_A2</targetReference>
        </connector>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>ActivityDate</field>
            <value>
                <elementReference>formula_12_myRule_11_A1_4074279868</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Channel</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>Channel__c</field>
            <value>
                <elementReference>myVariable_current.Channel__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Description</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>Description</field>
            <value>
                <elementReference>myVariable_current.Comments__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Direction</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>Direction__c</field>
            <value>
                <elementReference>myVariable_current.Direction__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Assigned To ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>OwnerId</field>
            <value>
                <elementReference>myVariable_current.LastModifiedById</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Reason for Contact</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Reason_for_Contact__c</field>
            <value>
                <stringValue>Hospitality</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Subject</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <field>Subject</field>
            <value>
                <stringValue>Interaction - Event Invite Conversation</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Related To ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>Account;Account_Plan__c;Asset;AssetRelationship;Attendee__c;Available_Seats__c;Betting_Rule__c;Campaign;Case;ContactRequest;Contract;Event__c;Global_Timeouts__c;Interaction__c;ListEmail;Opportunity;Order;Product2;Quote;Reserved_Seats__c;Reserved_Spot__c;Solution;Spend__c;Strategy__c;Take_a_Break__c</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>WhatId</field>
            <value>
                <elementReference>myVariable_current.Campaign__r.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Name ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>Contact;Lead</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>WhoId</field>
            <value>
                <elementReference>myVariable_current.Account__r.PersonContact.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordCreates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Attendee__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_11_A2</name>
        <label>Clear Interaction Fields</label>
        <locationX>1100</locationX>
        <locationY>300</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Channel</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>GlobalConstant</stringValue>
                </value>
            </processMetadataValues>
            <field>Channel__c</field>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Comments</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>GlobalConstant</stringValue>
                </value>
            </processMetadataValues>
            <field>Comments__c</field>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Boolean</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Log an Interaction</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Boolean</stringValue>
                </value>
            </processMetadataValues>
            <field>Log_an_Interaction__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Attendee__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_14_A1</name>
        <label>Update Status to Attended</label>
        <locationX>1400</locationX>
        <locationY>200</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Attended</stringValue>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Attendee__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_16_A1</name>
        <label>Update Guest Completed Date</label>
        <locationX>1600</locationX>
        <locationY>200</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Guest Check Confirmed?</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Guest_Check_Confirmed__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Guest Completed Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>Guest_Completed_Date__c</field>
            <value>
                <elementReference>formula_17_myRule_16_A1_5959942439</elementReference>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Attendee__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_5_A1</name>
        <label>Attendee: Last Event Accepted Date</label>
        <locationX>500</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myRule_5_A2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Event Offer Accepted Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>Event_Offer_Accepted_Date__c</field>
            <value>
                <elementReference>formula_7_myRule_5_A1_5412044639</elementReference>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Attendee__c].Customer</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_5_A2</name>
        <label>Customer: Last Event Accepted Date</label>
        <locationX>500</locationX>
        <locationY>300</locationY>
        <connector>
            <targetReference>myDecision8</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Account__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Last Event Accepted Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>Last_Event_Accepted_Date__c</field>
            <value>
                <elementReference>formula_6_myRule_5_A2_0206164846</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <startElementReference>myDecision</startElementReference>
    <status>Obsolete</status>
    <variables>
        <name>myVariable_current</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
    <variables>
        <name>myVariable_old</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
</Flow>
