<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Is_Account_Id_Populated</name>
        <label>Is Account Id Populated?</label>
        <locationX>374</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Account_Id_Is_Populated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Person_Contact_Id</targetReference>
            </connector>
            <label>Account Id Is Populated</label>
        </rules>
    </decisions>
    <description>The purpose of this flow is to update the Contact Id field for Tasks where the record type equals Task</description>
    <environments>Default</environments>
    <interviewLabel>Attach Contact Id to Task Based on Account Id for Task Record Type {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Attach Contact Id to Task Based on Account Id for Task Record Type</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Person_Contact_Id</name>
        <label>Get Person Contact Id</label>
        <locationX>242</locationX>
        <locationY>384</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account.PersonContactId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update Task with Contact id from Account</description>
        <name>Update_Task</name>
        <label>Update Task</label>
        <locationX>242</locationX>
        <locationY>492</locationY>
        <inputAssignments>
            <field>Contact__c</field>
            <value>
                <elementReference>Get_Person_Contact_Id.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterFormula>AND(
  {!$Record.RecordType.DeveloperName} = &apos;Task&apos;,
  NOT(ISBLANK({!$Record.WhatId}))
)</filterFormula>
        <object>Task</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Is_Account_Id_Populated</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
