<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 2.3- Approval Process - Adjustment_Level_23_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_11_A1</name>
        <label>Level 2.3 Approval</label>
        <locationX>1100</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision12</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 2.3 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_23_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 3- Approval Process - Adjustment_Level_3_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_13_A1</name>
        <label>Level 3 Approval</label>
        <locationX>1300</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision14</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 3 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_3_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 4- Approval Process - Adjustment_Level_4_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_15_A1</name>
        <label>Level 4 Approval</label>
        <locationX>1500</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision16</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 4 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_4_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Rebate - Approval Process - Adjustment_Rebate_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_17_A1</name>
        <label>Cash Rebate Approval</label>
        <locationX>1700</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision18</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Cash Rebate Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Rebate_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 1- Approval Process - Adjustment_Level_1_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_29_A1</name>
        <label>Level 1 Approval</label>
        <locationX>2900</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision30</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 1 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_1_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 2.1- Approval Process - Adjustment_Level_21_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_31_A1</name>
        <label>Level 2.1 Approval</label>
        <locationX>3100</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision32</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 2.1 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_21_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 2.2- Approval Process - Adjustment_Level_22_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_33_A1</name>
        <label>Level 2.2 Approval</label>
        <locationX>3300</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision34</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 2.2 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_22_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 2.3- Approval Process - Adjustment_Level_23_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_35_A1</name>
        <label>Level 2.3 Approval</label>
        <locationX>3500</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision36</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 2.3 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_23_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 3- Approval Process - Adjustment_Level_3_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_37_A1</name>
        <label>Level 3 Approval</label>
        <locationX>3700</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision38</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 3 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_3_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 4- Approval Process - Adjustment_Level_4_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_39_A1</name>
        <label>Level 4 Approval</label>
        <locationX>3900</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision40</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 4 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_4_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Expense Approval Process - Expense_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_49_A1</name>
        <label>Submit Expense Approval</label>
        <locationX>4900</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision50</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Please approve expense</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Expense_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 1- Approval Process - Adjustment_Level_1_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_5_A1</name>
        <label>Level 1 Approval</label>
        <locationX>500</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision6</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 1 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_1_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 2.1- Approval Process - Adjustment_Level_21_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_7_A1</name>
        <label>Level 2.1 Approval</label>
        <locationX>700</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision8</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 2.1 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_21_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>processSelection</name>
            <value>
                <stringValue>Adjustment Level 2.2- Approval Process - Adjustment_Level_22_Approval_Process</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>selectionType</name>
            <value>
                <stringValue>selectedProcess</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>submitterType</name>
            <value>
                <stringValue>currentUser</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_9_A1</name>
        <label>Level 2.2 Approval</label>
        <locationX>900</locationX>
        <locationY>200</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>myDecision10</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Level 2.2 Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Adjustment_Level_22_Approval_Process</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skipEntryCriteria</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
    </actionCalls>
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rejected</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Expense</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_A1</targetReference>
            </connector>
            <label>Remove Submitted</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>5.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision10</name>
        <label>myDecision10</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision12</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_11</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_11</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_11_A1</targetReference>
            </connector>
            <label>Bonus - Included Level 2.3</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>6.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision12</name>
        <label>myDecision12</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision14</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_13</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_13</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_13_A1</targetReference>
            </connector>
            <label>Bonus - Included Level 3</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>7.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision14</name>
        <label>myDecision14</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision16</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_15</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_15</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_15_A1</targetReference>
            </connector>
            <label>Bonus Included Level 4</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>8.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision16</name>
        <label>myDecision16</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision18</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_17</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_17</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_17_A1</targetReference>
            </connector>
            <label>Cash Rebate</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>9.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision18</name>
        <label>myDecision18</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision20</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_19</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_19</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_19_A1</targetReference>
            </connector>
            <label>Bonus - Exclude Level 1</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision2</name>
        <label>myDecision2</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision4</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_3</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_3_A1</targetReference>
            </connector>
            <label>Bonus - Exclude Level 0</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>10.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision20</name>
        <label>myDecision20</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision22</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_21</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_21</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_21_A1</targetReference>
            </connector>
            <label>Bonus - Exclude Level 2.1, 2.2, 2.3</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>11.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision22</name>
        <label>myDecision22</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision24</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_23</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_23</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_23_A1</targetReference>
            </connector>
            <label>Bonus - Excluded Level 3</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>12.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision24</name>
        <label>myDecision24</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision26</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_25</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_25</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_25_A1</targetReference>
            </connector>
            <label>Bonus - Excluded Level 4</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>13.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision26</name>
        <label>myDecision26</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision28</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_27</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_27</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_27_A1</targetReference>
            </connector>
            <label>MA - Exclude Level 0</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>14.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision28</name>
        <label>myDecision28</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision30</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_29</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_29</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_29_A1</targetReference>
            </connector>
            <label>MA - Include Level 1</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>15.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision30</name>
        <label>myDecision30</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision32</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_31</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_31</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_31_A1</targetReference>
            </connector>
            <label>MA - Include Level 2.1</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>16.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision32</name>
        <label>myDecision32</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision34</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_33</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_33</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_33_A1</targetReference>
            </connector>
            <label>MA - Include 2.2</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>17.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision34</name>
        <label>myDecision34</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision36</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_35</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_35</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_35_A1</targetReference>
            </connector>
            <label>MA - Include Level 2.3</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>18.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision36</name>
        <label>myDecision36</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision38</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_37</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_37</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_37_A1</targetReference>
            </connector>
            <label>MA - Include Level 3</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>19.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision38</name>
        <label>myDecision38</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision40</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_39</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_39</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_39_A1</targetReference>
            </connector>
            <label>MA - Include Level 4</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>2.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision4</name>
        <label>myDecision4</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision6</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_5</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_5</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_5_A1</targetReference>
            </connector>
            <label>Bonus - Include Level 1</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>20.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision40</name>
        <label>myDecision40</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision42</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_41</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_41</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_41_A1</targetReference>
            </connector>
            <label>MA - Exclude Level 1</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>21.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision42</name>
        <label>myDecision42</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision44</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_43</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_43</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_43_A1</targetReference>
            </connector>
            <label>MA - Exclude 2.1,2.2,2.3</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>22.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision44</name>
        <label>myDecision44</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision46</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_45</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_45</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_45_A1</targetReference>
            </connector>
            <label>MA - Exclude Level 3</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>23.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision46</name>
        <label>myDecision46</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision48</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_47</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_47</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_47_A1</targetReference>
            </connector>
            <label>MA - Exclude level 4</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>24.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision48</name>
        <label>myDecision48</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision50</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_49</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Currency</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Currency</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Currency</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Currency</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>400.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Expense</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Submitted</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_49_A1</targetReference>
            </connector>
            <label>Expense Approval</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>25.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision50</name>
        <label>myDecision50</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision52</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_51</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Submitted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Currency</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Currency</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Currency</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Currency</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>400.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Expense</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_51_A1</targetReference>
            </connector>
            <label>Expense Auto Approve</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>26.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision52</name>
        <label>myDecision52</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_53</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Submitted</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_53_A1</targetReference>
            </connector>
            <label>Submitted</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>3.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision6</name>
        <label>myDecision6</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision8</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_7</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_7</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_7_A1</targetReference>
            </connector>
            <label>Bonus - Include Level 2.1</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>4.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision8</name>
        <label>myDecision8</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision10</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_9</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_9</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_9_A1</targetReference>
            </connector>
            <label>Bonus - Include Level 2.2</label>
        </rules>
    </decisions>
    <description>This Process is used for automation triggered from the Adjustment Object</description>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Level_23_Greater_than__c   
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_23_Less_than_or_equal_to__c 
&amp;&amp; AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_1__c  ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_2__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_3__c ) )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_11</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_23_Greater_than__c}   
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_23_Less_than_or_equal_to__c} 
&amp;&amp; AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_1__c}  ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_2__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_3__c} ) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Level_3_Greater_than__c  
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_3_Less_than_or_equal_to__c  
&amp;&amp; AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_1__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_2__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_3__c) ||
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_4__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_5__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_6__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_7__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_8__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_9__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_10__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_11__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_12__c) )
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_13</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_3_Greater_than__c}  
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_3_Less_than_or_equal_to__c}  
&amp;&amp; AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_1__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_2__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_3__c}) ||
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_4__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_5__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_6__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_7__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_8__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_9__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_10__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_11__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_12__c}) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Level_4_Greater_than__c  
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_4_Less_than_or_equal_to__c  
&amp;&amp;
 NOT(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_4_Role_1__c) )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_15</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_4_Greater_than__c}  
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_4_Less_than_or_equal_to__c}  
&amp;&amp;
 NOT(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_4_Role_1__c}) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Cash Rebate&apos;)
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_17</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Cash Rebate&apos;)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Level_1_Greater_than__c  
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_1_Less_than_or_equal_to__c  
&amp;&amp;
NOT(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_1__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_2__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_3__c ) )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_19</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_1_Greater_than__c}  
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_1_Less_than_or_equal_to__c}  
&amp;&amp;
NOT(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_1__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_2__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_3__c} ) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt;$Setup.AdjustmentsApprovalProcess__c.Level_21_Greater_than__c   
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_21_Less_than_or_equal_to__c 
&amp;&amp;
[Adjustment__c].CreatedBy.UserRole.Name &lt;&gt; $Setup.AdjustmentsApprovalProcess__c.Level_21_Roles__c
&amp;&amp;
NOT(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_1__c  ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_2__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_3__c  ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_1__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_2__c  ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_3__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_1__c  ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_2__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_3__c  ))
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_21</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt;{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Greater_than__c}   
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_21_Less_than_or_equal_to__c} 
&amp;&amp;
{!myVariable_current.CreatedBy.UserRole.Name} &lt;&gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_21_Roles__c}
&amp;&amp;
NOT(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_1__c}  ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_2__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_3__c}  ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_1__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_2__c}  ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_3__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_1__c}  ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_2__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_3__c}  ))</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Level_3_Greater_than__c  
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_3_Less_than_or_equal_to__c  
&amp;&amp;
NOT(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_1__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_2__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_3__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_4__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_5__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_6__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_7__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_8__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_9__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_10__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_11__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_12__c ))



</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_23</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_3_Greater_than__c}  
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_3_Less_than_or_equal_to__c}  
&amp;&amp;
NOT(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_1__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_2__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_3__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_4__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_5__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_6__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_7__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_8__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_9__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_10__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_11__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_12__c} ))</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Level_4_Greater_than__c  
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_4_Less_than_or_equal_to__c  
&amp;&amp;
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_4_Role_1__c) </stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_25</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_4_Greater_than__c}  
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_4_Less_than_or_equal_to__c}  
&amp;&amp;
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_4_Role_1__c})</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &lt;= 1000  </stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_27</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= 1000</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl1_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl1_Less_Equal_to__c 
&amp;&amp; AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_1__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_2__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_3__c ) )
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_29</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl1_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl1_Less_Equal_to__c} 
&amp;&amp; AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_1__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_2__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_3__c} ) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_0_Less_than_or_equal_to__c   
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_3</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_0_Less_than_or_equal_to__c}</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Less_Equal_to__c
&amp;&amp; AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_1__c  ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_2__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_3__c ) )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_31</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Less_Equal_to__c}
&amp;&amp; AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_1__c}  ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_2__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_3__c} ) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Less_Equal_to__c
&amp;&amp; AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_1__c  ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_2__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_3__c ) )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_33</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Less_Equal_to__c}
&amp;&amp; AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_1__c}  ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_2__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_3__c} ) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Less_Equal_to__c
&amp;&amp; AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_1__c  ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_2__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_3__c ) )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_35</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Less_Equal_to__c}
&amp;&amp; AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_1__c}  ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_2__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_3__c} ) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl3_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl3_Less_Equal_to__c
&amp;&amp; AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_1__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_2__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_3__c) ||
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_4__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_5__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_6__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_7__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_8__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_9__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_10__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_11__c) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_12__c) )
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_37</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl3_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl3_Less_Equal_to__c}
&amp;&amp; AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_1__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_2__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_3__c}) ||
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_4__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_5__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_6__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_7__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_8__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_9__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_10__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_11__c}) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_12__c}) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl4_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl4_Less_Equal_to__c
&amp;&amp;
NOT(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_4_Role_1__c) )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_39</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl4_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl4_Less_Equal_to__c}
&amp;&amp;
NOT(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_4_Role_1__c}) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl1_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl1_Less_Equal_to__c
&amp;&amp; 
NOT(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_1__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_2__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_3__c ) )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_41</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl1_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl1_Less_Equal_to__c}
&amp;&amp; 
NOT(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_1__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_2__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_3__c} ) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Less_Equal_to__c
&amp;&amp; 
NOT(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_1__c  ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_2__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_3__c  ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_1__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_2__c  ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_3__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_1__c  ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_2__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_3__c  ))
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_43</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl2_Less_Equal_to__c}
&amp;&amp; 
NOT(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_1__c}  ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_2__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_3__c}  ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_1__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_2__c}  ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_3__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_1__c}  ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_2__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_23_Role_3__c}  ))</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl3_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl3_Less_Equal_to__c
&amp;&amp; 
NOT(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_1__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_2__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_3__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_4__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_5__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_6__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_7__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_8__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_9__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_10__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_11__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_12__c ))



</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_45</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl3_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl3_Less_Equal_to__c}
&amp;&amp; 
NOT(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_1__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_2__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_3__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_4__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_5__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_6__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_7__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_8__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_9__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_10__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_11__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_3_Role_12__c} ))</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Manual Adjustment&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl4_Greater_Than__c 
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl4_Less_Equal_to__c
&amp;&amp; 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_4_Role_1__c) </stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_47</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Manual Adjustment&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl4_Greater_Than__c} 
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Manual_Adjustment_Lvl4_Less_Equal_to__c}
&amp;&amp; 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_4_Role_1__c})</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Level_1_Greater_than__c  
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_1_Less_than_or_equal_to__c 
&amp;&amp; AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_1__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_2__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_3__c ) )
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_5</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_1_Greater_than__c}  
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_1_Less_than_or_equal_to__c} 
&amp;&amp; AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_1__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_2__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_1_Role_3__c} ) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Level_21_Greater_than__c   
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_21_Less_than_or_equal_to__c 
&amp;&amp; AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_1__c  ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_2__c ) || 
CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_3__c ) )


</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_7</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_21_Greater_than__c}   
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_21_Less_than_or_equal_to__c} 
&amp;&amp; AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_1__c}  ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_2__c} ) || 
CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_21_Role_3__c} ) )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Adjustment__c].RecordType.DeveloperName  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL([Adjustment__c].Type__c , &apos;Bonus Bet&apos;)
&amp;&amp;
[Adjustment__c].Amount__c &gt; $Setup.AdjustmentsApprovalProcess__c.Level_22_Greater_than__c   
&amp;&amp;
[Adjustment__c].Amount__c &lt;= $Setup.AdjustmentsApprovalProcess__c.Level_22_Less_than_or_equal_to__c 
&amp;&amp;
AND(CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_1__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_2__c ) || CONTAINS([Adjustment__c].CreatedBy.UserRole.Name,$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_3__c ) )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_9</name>
        <dataType>Boolean</dataType>
        <expression>{!myVariable_current.RecordType.DeveloperName}  == &apos;Customer_Adjustment&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Type__c} , &apos;Bonus Bet&apos;)
&amp;&amp;
{!myVariable_current.Amount__c} &gt; {!$Setup.AdjustmentsApprovalProcess__c.Level_22_Greater_than__c}   
&amp;&amp;
{!myVariable_current.Amount__c} &lt;= {!$Setup.AdjustmentsApprovalProcess__c.Level_22_Less_than_or_equal_to__c} 
&amp;&amp;
AND(CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_1__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_2__c} ) || CONTAINS({!myVariable_current.CreatedBy.UserRole.Name},{!$Setup.AdjustmentsApprovalProcess__c.Level_22_Role_3__c} ) )</expression>
    </formulas>
    <interviewLabel>Adjustment_Approval-19_InterviewLabel</interviewLabel>
    <label>Adjustment Approval</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Adjustment__c</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>ObjectVariable</name>
        <value>
            <elementReference>myVariable_current</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OldObjectVariable</name>
        <value>
            <elementReference>myVariable_old</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>TriggerType</name>
        <value>
            <stringValue>onCreateOnly</stringValue>
        </value>
    </processMetadataValues>
    <processType>Workflow</processType>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_19_A1</name>
        <label>Auto Approved</label>
        <locationX>1900</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision20</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_1_A1</name>
        <label>Remove Customer Submitted</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Customer Pending Approval</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>Account</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>GlobalConstant</stringValue>
                </value>
            </processMetadataValues>
            <field>Customer_Pending_Approval__c</field>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Notes</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <field>Notes__c</field>
            <value>
                <stringValue>dgdgdgd</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_21_A1</name>
        <label>Auto Approve Level 2.1</label>
        <locationX>2100</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision22</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_23_A1</name>
        <label>Auto Approve Level 3</label>
        <locationX>2300</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision24</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_25_A1</name>
        <label>Auto Approve Level 4</label>
        <locationX>2500</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision26</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_27_A1</name>
        <label>Auto Approve</label>
        <locationX>2700</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision28</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_3_A1</name>
        <label>Auto Approve</label>
        <locationX>300</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision4</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_41_A1</name>
        <label>Auto Approve</label>
        <locationX>4100</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision42</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_43_A1</name>
        <label>Auto Approve</label>
        <locationX>4300</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision44</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_45_A1</name>
        <label>Auto Approve</label>
        <locationX>4500</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision46</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_47_A1</name>
        <label>Auto Approve</label>
        <locationX>4700</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision48</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_51_A1</name>
        <label>Approved</label>
        <locationX>5100</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision52</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Approval Notes</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <field>Approval_Notes__c</field>
            <value>
                <stringValue>Auto Approved</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Customer Pending Approval</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>Account</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>GlobalConstant</stringValue>
                </value>
            </processMetadataValues>
            <field>Customer_Pending_Approval__c</field>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_53_A1</name>
        <label>Update Customer Submitted</label>
        <locationX>5300</locationX>
        <locationY>200</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Customer Pending Approval</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>Account</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>Customer_Pending_Approval__c</field>
            <value>
                <elementReference>myVariable_current.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <startElementReference>myDecision</startElementReference>
    <status>Obsolete</status>
    <variables>
        <name>myVariable_current</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Adjustment__c</objectType>
    </variables>
    <variables>
        <name>myVariable_old</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Adjustment__c</objectType>
    </variables>
</Flow>
