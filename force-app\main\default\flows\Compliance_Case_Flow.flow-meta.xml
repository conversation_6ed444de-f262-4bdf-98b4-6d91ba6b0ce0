<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Compliance_Case_Escalation_Send_to_Jen</name>
        <label>Compliance Case Escalation - Send to Jen</label>
        <locationX>314</locationX>
        <locationY>900</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Date_Check</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>EmailSubject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>EmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>ComplianceCase.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Compliance_Case_Escalation_Send_to_Manager</name>
        <label>Compliance Case Escalation - Send to Manager</label>
        <locationX>50</locationX>
        <locationY>900</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Date_Check</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>EmailSubject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>EmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>ComplianceCase.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>ManagerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>58.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Manager</name>
        <label>Assign Manager</label>
        <locationX>182</locationX>
        <locationY>684</locationY>
        <assignmentItems>
            <assignToReference>Users.ManagerId</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>ManagerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Manager_Null_Check</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Case_Collection</name>
        <label>Case Collection</label>
        <locationX>138</locationX>
        <locationY>1716</locationY>
        <assignmentItems>
            <assignToReference>CaseCollector</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>ComplianceCase</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Users</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Case_Instance</name>
        <label>Case Instance</label>
        <locationX>138</locationX>
        <locationY>1608</locationY>
        <assignmentItems>
            <assignToReference>ComplianceCase.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Case_Record_Type_ID.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ComplianceCase.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>CaseDesc</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ComplianceCase.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Betstop</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ComplianceCase.Priority</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Medium</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ComplianceCase.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>BetStop Compliance Adherence</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ComplianceCase.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Users.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ComplianceCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>New</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Case_Collection</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Case_Closed_within_5_days</name>
        <label>Case Closed within 5 days</label>
        <locationX>380</locationX>
        <locationY>468</locationY>
        <defaultConnector>
            <targetReference>Date_Check</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CreatedDate5DaysAgo</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Compliance_Cases.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Compliance_Cases.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Compliance_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Case_Priority</targetReference>
            </connector>
            <label>No</label>
        </rules>
    </decisions>
    <decisions>
        <name>Date_Check</name>
        <label>Date Check</label>
        <locationX>380</locationX>
        <locationY>1176</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>First_of_the_Month</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FirstofMonthTrue</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetActivePremiumUsers</targetReference>
            </connector>
            <label>First of the Month</label>
        </rules>
    </decisions>
    <decisions>
        <name>Manager_Null_Check</name>
        <label>Manager Null Check</label>
        <locationX>182</locationX>
        <locationY>792</locationY>
        <defaultConnector>
            <targetReference>Compliance_Case_Escalation_Send_to_Jen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_Null1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ManagerId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Compliance_Case_Escalation_Send_to_Manager</targetReference>
            </connector>
            <label>Not Null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Null_Check</name>
        <label>Null Check</label>
        <locationX>182</locationX>
        <locationY>1392</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetActivePremiumUsers</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Users</targetReference>
            </connector>
            <label>Not Null</label>
        </rules>
    </decisions>
    <description>SBET-627 - Compliance Cases not being created from flow</description>
    <environments>Default</environments>
    <formulas>
        <name>CreatedDate5DaysAgo</name>
        <dataType>Boolean</dataType>
        <expression>DATEVALUE({!Get_Compliance_Cases.CreatedDate}) = (TODAY() - 6)</expression>
    </formulas>
    <formulas>
        <name>FirstofMonthTrue</name>
        <dataType>Boolean</dataType>
        <expression>DAY(TODAY()) = 1</expression>
    </formulas>
    <interviewLabel>Compliance Case Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Compliance Case Flow</label>
    <loops>
        <name>Users</name>
        <label>Users</label>
        <locationX>50</locationX>
        <locationY>1500</locationY>
        <collectionReference>GetActivePremiumUsers</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Case_Instance</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Create_Compliance_Case</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Compliance_Case</name>
        <label>Create Compliance Case</label>
        <locationX>50</locationX>
        <locationY>1908</locationY>
        <inputReference>CaseCollector</inputReference>
    </recordCreates>
    <recordLookups>
        <name>Get_Case_Record_Type_ID</name>
        <label>Get Case Record Type ID</label>
        <locationX>380</locationX>
        <locationY>252</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Compliance_Cases</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Compliance_Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Compliance_Cases</name>
        <label>Get Compliance Cases</label>
        <locationX>380</locationX>
        <locationY>360</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Case_Closed_within_5_days</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case_Record_Type_ID.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Origin</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Betstop</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetActivePremiumUsers</name>
        <label>GetActivePremiumUsers</label>
        <locationX>182</locationX>
        <locationY>1284</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check</targetReference>
        </connector>
        <filterLogic>1 AND (2 OR 3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13 OR 14 OR 15 OR 16 OR 17 OR 18 OR 19 OR 20 OR 21 OR 22)</filterLogic>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>smNSWRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ptmNSWARole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>rmNSWARole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ptmNSWBRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>rmNSWBRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>smQLDRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ptmQLDARole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>rmQLDARole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ptmWARole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>rmWARole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>smVICRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ptmVICARole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>rmVICARole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ptmVICBRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>rmVICBRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ptmNSWRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>rmNSWRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ptmQLDRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>rmQLDRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ptmVICRole</elementReference>
            </value>
        </filters>
        <filters>
            <field>UserRoleId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>rmVICRole</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Case_Priority</name>
        <label>Update Case Priority</label>
        <locationX>182</locationX>
        <locationY>576</locationY>
        <connector>
            <targetReference>Assign_Manager</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ComplianceCase.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case_Record_Type_ID</targetReference>
        </connector>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2023-09-28</startDate>
            <startTime>10:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>CaseDesc</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>As part of the changes with BetStop, all Premium team members are required to complete a monthly attestation to confirm the following: 
1. You have set up the &apos;text replacement&apos; on your Sportsbet mobile device: Phrase: BetStop? Visit www.sportsbet.com.au Shortcut: ZZZ
2. You have promoted BetStop (using the above shortcut) at the end of each customer SMS interaction.
3. Provide monthly screenshot (for previous month) to show promotion of BetStop on an outbound SMS</text>
    </textTemplates>
    <textTemplates>
        <name>EmailBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&lt;span style=&quot;font-size: medium; color: rgb(0, 0, 0);&quot;&gt;***COMPLIANCE CASE ESCALATION ALERT ***&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: medium; color: rgb(0, 0, 0);&quot;&gt;The following Compliance case has been escalated.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: medium; color: rgb(0, 0, 0);&quot;&gt;Case Number: {!ComplianceCase.CaseNumber}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: medium; color: rgb(0, 0, 0);&quot;&gt;Priority: {!ComplianceCase.Priority}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: medium; color: rgb(0, 0, 0);&quot;&gt;Subject: {!ComplianceCase.Subject}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: medium; color: rgb(0, 0, 0);&quot;&gt;Login to Salesforce to action&lt;/span&gt;&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>EmailSubject</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px;&quot;&gt;Compliance Case Escalation - {!ComplianceCase.CaseNumber}&lt;/span&gt;&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>CaseCollector</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>ComplianceCase</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>ManagerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ptmNSWARole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5luEAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>ptmNSWBRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5lvEAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>ptmNSWRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000HShGEAW</stringValue>
        </value>
    </variables>
    <variables>
        <name>ptmQLDARole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5m5EAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>ptmQLDRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000HShIEAW</stringValue>
        </value>
    </variables>
    <variables>
        <name>ptmVICARole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5lwEAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>ptmVICBRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5lxEAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>ptmVICRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000HShHEAW</stringValue>
        </value>
    </variables>
    <variables>
        <name>ptmWARole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5lyEAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>rmNSWARole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5lzEAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>rmNSWBRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5m0EAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>rmNSWRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000HShJEAW</stringValue>
        </value>
    </variables>
    <variables>
        <name>rmQLDARole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5mAEAS</stringValue>
        </value>
    </variables>
    <variables>
        <name>rmQLDRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000HShLEAW</stringValue>
        </value>
    </variables>
    <variables>
        <name>rmVICARole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5m1EAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>rmVICBRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5m2EAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>rmVICRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000t81YEAQ</stringValue>
        </value>
    </variables>
    <variables>
        <name>rmWARole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5m3EAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>smNSWRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000HShDEAW</stringValue>
        </value>
    </variables>
    <variables>
        <name>smQLDRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5lsEAC</stringValue>
        </value>
    </variables>
    <variables>
        <name>smVICRole</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>00E2y000000I5ltEAC</stringValue>
        </value>
    </variables>
</Flow>
