<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Add Updated Tasks to Collection</description>
        <name>Add_Updated_Tasks_to_Collection</name>
        <label>Add Updated Tasks to Collection</label>
        <locationX>598</locationX>
        <locationY>411</locationY>
        <assignmentItems>
            <assignToReference>varTasksToBeUpdated</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>LoopAllTasks</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>LoopAllTasks</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>AssignTaskUpdates</description>
        <name>AssignTaskUpdates</name>
        <label>AssignTaskUpdates</label>
        <locationX>773</locationX>
        <locationY>411</locationY>
        <assignmentItems>
            <assignToReference>LoopAllTasks.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Updated_Tasks_to_Collection</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Router used to send flow to a path with Specific Get Records</description>
        <name>Router</name>
        <label>Router</label>
        <locationX>168</locationX>
        <locationY>221</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decAccount_Locked_Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>AccountLocked</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Account_Locked_Tasks</targetReference>
            </connector>
            <label>Account Locked Task</label>
        </rules>
        <rules>
            <name>decNegative_Balance_Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>NegativeBalance</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Negative_Balance_Tasks</targetReference>
            </connector>
            <label>Negative Balance Task</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check if Tasks are there to be Completed</description>
        <name>Tasks_Available</name>
        <label>Tasks Available</label>
        <locationX>549</locationX>
        <locationY>222</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decYes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varOpenTasks</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>LoopAllTasks</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>This flow can be used to Invoke Automated Task Completion for various scenarios.</description>
    <formulas>
        <description>formula for Today  + 1</description>
        <name>formulaTodayPlus1</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate}+1</expression>
    </formulas>
    <interviewLabel>Invoke Automated Task Completion {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Invoke Automated Task Completion</label>
    <loops>
        <description>LoopAllTasks</description>
        <name>LoopAllTasks</name>
        <label>LoopAllTasks</label>
        <locationX>774</locationX>
        <locationY>221</locationY>
        <collectionReference>varOpenTasks</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>AssignTaskUpdates</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Tasks</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Get Account Locked Tasks related to recordId (Customer)</description>
        <name>Get_Account_Locked_Tasks</name>
        <label>Get Account Locked Tasks</label>
        <locationX>383</locationX>
        <locationY>128</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Tasks_Available</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Alert: User Locked Out</stringValue>
            </value>
        </filters>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <object>Task</object>
        <outputReference>varOpenTasks</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>BE_Completed_Date__c</queriedFields>
        <queriedFields>Status</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Get Negative Balance Tasks</description>
        <name>Get_Negative_Balance_Tasks</name>
        <label>Get Negative Balance Tasks</label>
        <locationX>373</locationX>
        <locationY>364</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Tasks_Available</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Alert: Negative Account Balance</stringValue>
            </value>
        </filters>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <object>Task</object>
        <outputReference>varOpenTasks</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Status</queriedFields>
        <queriedFields>BE_Completed_Date__c</queriedFields>
    </recordLookups>
    <recordUpdates>
        <description>Update Tasks</description>
        <name>Update_Tasks</name>
        <label>Update Tasks</label>
        <locationX>961</locationX>
        <locationY>220</locationY>
        <inputReference>varTasksToBeUpdated</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>Router</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>recordId Input variable</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to capture Automated Task Type which will be used in the Router to create Specific task</description>
        <name>varAutomatedTaskType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to Store all the Open Tasks using Get Records</description>
        <name>varOpenTasks</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>varTaskOwner</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to store Task Record Type ID</description>
        <name>varTaskRecordTypeID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to store all the Tasks to be Updated</description>
        <name>varTasksToBeUpdated</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
