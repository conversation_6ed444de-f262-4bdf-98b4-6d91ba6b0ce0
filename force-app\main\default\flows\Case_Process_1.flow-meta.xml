<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>sendEmailAlertClosedAccount</name>
        <label>Send Closed Account Self Exclusion Alert</label>
        <locationX>842</locationX>
        <locationY>2699</locationY>
        <actionName>Case.Closed_Account_Customer_Self_Exclusion_Alert_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Closed_Account_Customer_Self_Exclusion_Alert_Case_Email_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>sendEmailSelfExc</name>
        <label>Send Email to Case Owner</label>
        <locationX>578</locationX>
        <locationY>2699</locationY>
        <actionName>Case.Self_Exclusion_Alert_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Self_Exclusion_Alert_Case_Email_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>sendTimeOutEmail</name>
        <label>Send Email Notification</label>
        <locationX>446</locationX>
        <locationY>2399</locationY>
        <actionName>Case.Time_Out_Self_Exclusion_Alert_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>limitClosedMobile</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Time_Out_Self_Exclusion_Alert_Case_Email_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>sendUnassignedCaseNotification</name>
        <label>Send Unassigned Case Notification</label>
        <locationX>1122</locationX>
        <locationY>755</locationY>
        <actionName>Case.RG_Alert_Unassigned_Case_Notification</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>newRGCase</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.RG_Alert_Unassigned_Case_Notification</nameSegment>
    </actionCalls>
    <apiVersion>58.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>assignRGAlertCaseToOwner</name>
        <label>Assign RG Alert Case to Customer Owner</label>
        <locationX>578</locationX>
        <locationY>323</locationY>
        <defaultConnector>
            <targetReference>assignRGCaseToDelegate</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue1</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND ((5 OR 6) OR (7 OR 8))</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_1__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0122y000000GqZuAAK</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isOwnedByTempQueue</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Owner.Delegate_Assignment_Start_Date__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>todayDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Owner.Delegate_Assignment_Start_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Owner.Delegate_Assignment_End_Date__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>todayDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Owner.Delegate_Assignment_End_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assignOwnerSentNotification</targetReference>
            </connector>
            <label>Assign RG Alert Case to Customer Owner</label>
        </rules>
    </decisions>
    <decisions>
        <name>assignRGCaseToDelegate</name>
        <label>Assign RG Alert Case to Customer Owner Delegate User</label>
        <locationX>825</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>rgCaseInUnmanagedQueue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_2__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RG_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isOwnedByTempQueue</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Owner.Delegate_Assignment_User_ID__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Owner.Delegate_Assignment_Start_Date__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>todayDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Owner.Delegate_Assignment_End_Date__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>todayDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assignDelegateOwnerSendNotification</targetReference>
            </connector>
            <label>Assign RG Alert Case to Customer Owner Delegate User</label>
        </rules>
    </decisions>
    <decisions>
        <name>emailForSelfExclTimeOut</name>
        <label>Email Notification for Self Excl Time Out</label>
        <locationX>578</locationX>
        <locationY>2291</locationY>
        <defaultConnector>
            <targetReference>limitClosedMobile</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue10</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Self_Exclusion_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Closed_Reason__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Time Out</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isOwnedByTempQueue</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>sendTimeOutEmail</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Email Notification for Self Excl Time Out</label>
        </rules>
    </decisions>
    <decisions>
        <name>limitClosedMobile</name>
        <label>Limit Type = Closed Mobile</label>
        <locationX>578</locationX>
        <locationY>2591</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue11</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND (5 OR 6 OR 7 OR 8 OR 9) AND 10</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Self_Exclusion_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Limit_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed Mobile</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Closed_Reason__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>DMA - No longer interested</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Closed_Reason__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>DMA - Employment Restrictions</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Closed_Reason__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>DMA - Prefer not to specify</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Closed_Reason__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>DMA - Time management</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Closed_Reason__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>DMA - Personal reasons</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>00G</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>closeDeactivationCase</targetReference>
            </connector>
            <label>Closed reason contains DMA</label>
        </rules>
        <rules>
            <name>decTrue12</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND (5 OR 6) AND 7</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Self_Exclusion_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Limit_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed Mobile</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Closed_Reason__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Permanent Self Exclusion</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Closed_Reason__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Self Exclusion</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>00G</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>closeSelfExclCase</targetReference>
            </connector>
            <label>Closed reason contains Self Exclusion</label>
        </rules>
        <rules>
            <name>decTrue13</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isOtherSelfExclValue</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>sendEmailSelfExc</targetReference>
            </connector>
            <label>Other Closed Reason</label>
        </rules>
        <rules>
            <name>decTrue14</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsDMAValue</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>sendEmailAlertClosedAccount</targetReference>
            </connector>
            <label>Other Closed Reason 2</label>
        </rules>
    </decisions>
    <decisions>
        <name>newRGCase</name>
        <label>New RG Case</label>
        <locationX>1254</locationX>
        <locationY>947</locationY>
        <defaultConnector>
            <targetReference>selfExclCaseOwner</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue20</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_5__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RG_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Case_Name__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer Deposit over Avg Threshold</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ClosedDate</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>invokeCaseEscalationNotification</targetReference>
            </connector>
            <label>Case Status is New</label>
        </rules>
    </decisions>
    <decisions>
        <name>newSelfExclCaseClosedQueueOwner</name>
        <label>If New Self Excl case with Closed Operator and Queue Owner</label>
        <locationX>776</locationX>
        <locationY>1607</locationY>
        <defaultConnector>
            <targetReference>timeOutSelfExclLimitType</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue6</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND (5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Self_Exclusion_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Limit_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed Operator</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Owner:Group.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Premium_Team_Managed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Owner:Group.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Premium_Service</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>closeSelfExclCaseInQueue</targetReference>
            </connector>
            <label>If New Self Excl case with Closed Operator and Queue Owner</label>
        </rules>
    </decisions>
    <decisions>
        <name>rgCaseInUnmanagedQueue</name>
        <label>RG Alert Case Assigned to Unmanaged Queue</label>
        <locationX>1056</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>rgCaseNotNew</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_3__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RG_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isOwnedByUnmanagedQueue</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>closeUnassignedRgCase</targetReference>
            </connector>
            <label>RG Alert Case Assigned to Unmanaged Queue</label>
        </rules>
    </decisions>
    <decisions>
        <name>rgCaseNotNew</name>
        <label>RG Case is Not New</label>
        <locationX>1254</locationX>
        <locationY>647</locationY>
        <defaultConnector>
            <targetReference>newRGCase</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue4</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_4__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RG_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>WasSet</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>lastModifiedMinusCreatedDate</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <numberValue>0.01</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isOwnedByUnassignedCases</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>sendUnassignedCaseNotification</targetReference>
            </connector>
            <label>Case is not new</label>
        </rules>
    </decisions>
    <decisions>
        <name>selfExclCaseOwner</name>
        <label>Assign Self Excl Case to Account Owner</label>
        <locationX>578</locationX>
        <locationY>1499</locationY>
        <defaultConnector>
            <targetReference>newSelfExclCaseClosedQueueOwner</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue5</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Self_Exclusion_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isOwnedByTempQueue</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assignCaseToAccountOwner</targetReference>
            </connector>
            <label>Assign Self Excl Case to Account Owner</label>
        </rules>
    </decisions>
    <decisions>
        <name>timeOutSelfExclLimitType</name>
        <label>Time Out or Self Excluded Limit</label>
        <locationX>578</locationX>
        <locationY>1991</locationY>
        <defaultConnector>
            <targetReference>emailForSelfExclTimeOut</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue8</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Self_Exclusion_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Limit_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>time_out</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>00G</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>closeTimeOut</targetReference>
            </connector>
            <label>Time Out</label>
        </rules>
        <rules>
            <name>decTrue9</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Self_Exclusion_Alert_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Limit_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>self_excl</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>00G</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>closeSelfExcl</targetReference>
            </connector>
            <label>Self Exclusion</label>
        </rules>
    </decisions>
    <description>SBET-879 - Case flows requiring update prior to AML Go-Live</description>
    <environments>Default</environments>
    <formulas>
        <name>caseClosureComment</name>
        <dataType>String</dataType>
        <expression>&quot;System Auto Closed&quot;</expression>
    </formulas>
    <formulas>
        <name>caseOwnerName</name>
        <dataType>String</dataType>
        <expression>{!$Record.Owner:User.FirstName}+&quot; &quot;+{!$Record.Owner:User.LastName}</expression>
    </formulas>
    <formulas>
        <name>ecddSubjectUpdate</name>
        <dataType>String</dataType>
        <expression>{!$Record.ECDD_SportsBet_Account_Number__c+&quot;} - &quot;+{!$Record.ECDD_First_Name__c+&quot;} &quot;+{!$Record.ECDD_Last_Name__c+&quot;} - &quot;+ Text(Datevalue({!$Record.CreatedDate} ))</expression>
    </formulas>
    <formulas>
        <name>IsDMAValue</name>
        <dataType>Boolean</dataType>
        <expression>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c 
&amp;&amp;
{!$Record.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
(
(ISPICKVAL({!$Record.Limit_Type__c} ,&apos;Closed Mobile&apos;)
&amp;&amp;
(
ISPICKVAL({!$Record.Closed_Reason__c} , &apos;DMA - No longer interested&apos;) ||
ISPICKVAL({!$Record.Closed_Reason__c}  , &apos;DMA - Employment Restrictions&apos;) ||
ISPICKVAL({!$Record.Closed_Reason__c}  ,&apos;DMA - Prefer not to specify&apos;) ||
ISPICKVAL({!$Record.Closed_Reason__c} ,&apos;DMA - Time management&apos;) ||
ISPICKVAL({!$Record.Closed_Reason__c}  ,&apos;DMA - Personal reasons&apos;) ||
ISPICKVAL({!$Record.Closed_Reason__c} ,&apos;Permanent Self Exclusion&apos;) ||
ISPICKVAL({!$Record.Closed_Reason__c} ,&apos;Self Exclusion&apos;)
))
||
(ISPICKVAL({!$Record.Limit_Type__c}  ,&apos;self_excl&apos;) &amp;&amp; ISPICKVAL({!$Record.Closed_Reason__c} ,&apos;Self Exclusion&apos;))
)
&amp;&amp;
!ISBLANK({!$Record.AccountId})
&amp;&amp;
IF(LEFT({!$Record.OwnerId}, 3) =&quot;00G&quot;, 
{!$Record.Owner:Group.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</expression>
    </formulas>
    <formulas>
        <name>isOtherSelfExclValue</name>
        <dataType>Boolean</dataType>
        <expression>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c 
&amp;&amp;
{!$Record.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
(
  (
    ISPICKVAL({!$Record.Limit_Type__c} ,&apos;Closed Mobile&apos;)
	&amp;&amp;
	(
		ISPICKVAL({!$Record.Closed_Reason__c}, &apos;DMA - No reason specified&apos;) ||
		ISPICKVAL({!$Record.Closed_Reason__c} , &apos;DMA - Unhappy with service/product&apos;) ||
		ISPICKVAL({!$Record.Closed_Reason__c}, &apos;DMA - Unhappy with your offers&apos;) ||
		ISPICKVAL({!$Record.Closed_Reason__c} , &apos;DMA - Using another bookmaker&apos;) ||	
		ISPICKVAL({!$Record.Closed_Reason__c}, &apos;Closed Operator - Other&apos;)
	)
  ) 
	||
	ISPICKVAL({!$Record.Limit_Type__c}, &apos;Closed Operator&apos;) 
	&amp;&amp; 
	{!$Record.Owner:Group.DeveloperName} &lt;&gt; &apos;Premium_Service&apos; 
)
&amp;&amp;
!ISBLANK({!$Record.AccountId})
&amp;&amp;
ISPICKVAL({!$Record.Status},&apos;New&apos;)
&amp;&amp;
IF(LEFT({!$Record.OwnerId}, 3) =&quot;00G&quot;, 
{!$Record.Owner:Group.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</expression>
    </formulas>
    <formulas>
        <name>isOwnedByTempQueue</name>
        <dataType>Boolean</dataType>
        <expression>IF(LEFT({!$Record.OwnerId},3)=&quot;00G&quot;, 
{!$Record.Owner:Group.DeveloperName}==&apos;Temp_Queue_to_Assign_Case_to_RM&apos;, 
False)</expression>
    </formulas>
    <formulas>
        <name>isOwnedByUnassignedCases</name>
        <dataType>Boolean</dataType>
        <expression>IF(LEFT({!$Record.OwnerId},3)=&quot;00G&quot;, {!$Record.Owner:Group.DeveloperName}==&apos;Unassigned_Cases&apos;, False)</expression>
    </formulas>
    <formulas>
        <name>isOwnedByUnmanagedQueue</name>
        <dataType>Boolean</dataType>
        <expression>IF(LEFT({!$Record.OwnerId},3)=&quot;00G&quot;, {!$Record.Owner:Group.DeveloperName}==&apos;Unmanaged_Queue&apos;, False)</expression>
    </formulas>
    <formulas>
        <name>lastModifiedMinusCreatedDate</name>
        <dataType>Number</dataType>
        <expression>{!$Record.LastModifiedDate}-{!$Record.CreatedDate}</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>todayDate</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <interviewLabel>Case Process_1 {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case On Create/Update - RG Alert and Self Exclusion Alert Cases</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>assignCaseToAccountOwner</name>
        <label>Assign Case Owner as Account Owner</label>
        <locationX>380</locationX>
        <locationY>1607</locationY>
        <connector>
            <targetReference>timeOutSelfExclLimitType</targetReference>
        </connector>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.Account.Owner.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>closeDeactivationCase</name>
        <label>Close Deactivation</label>
        <locationX>50</locationX>
        <locationY>2699</locationY>
        <inputAssignments>
            <field>Closure_Comments__c</field>
            <value>
                <elementReference>caseClosureComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <stringValue>Deactivation</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>closeSelfExcl</name>
        <label>Close Self Exclusion</label>
        <locationX>578</locationX>
        <locationY>2099</locationY>
        <connector>
            <targetReference>emailForSelfExclTimeOut</targetReference>
        </connector>
        <inputAssignments>
            <field>Closed_Reason__c</field>
            <value>
                <stringValue>Self Exclusion</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Closure_Comments__c</field>
            <value>
                <elementReference>caseClosureComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <stringValue>Closure</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>closeSelfExclCase</name>
        <label>Close Permanent and Self Exclusion</label>
        <locationX>314</locationX>
        <locationY>2699</locationY>
        <inputAssignments>
            <field>Closure_Comments__c</field>
            <value>
                <elementReference>caseClosureComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <stringValue>Closure</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>closeSelfExclCaseInQueue</name>
        <label>Close the Case</label>
        <locationX>644</locationX>
        <locationY>1715</locationY>
        <connector>
            <targetReference>timeOutSelfExclLimitType</targetReference>
        </connector>
        <inputAssignments>
            <field>Closure_Comments__c</field>
            <value>
                <elementReference>caseClosureComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>closeTimeOut</name>
        <label>Close Time Out</label>
        <locationX>314</locationX>
        <locationY>2099</locationY>
        <connector>
            <targetReference>emailForSelfExclTimeOut</targetReference>
        </connector>
        <inputAssignments>
            <field>Closed_Reason__c</field>
            <value>
                <stringValue>Time Out</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Closure_Comments__c</field>
            <value>
                <elementReference>caseClosureComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <stringValue>Closure</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>closeUnassignedRgCase</name>
        <label>Close Case</label>
        <locationX>858</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>selfExclCaseOwner</targetReference>
        </connector>
        <inputAssignments>
            <field>Closure_Comments__c</field>
            <value>
                <elementReference>caseClosureComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Further Action</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RG_Tools_Accepted__c</field>
            <value>
                <stringValue>Not Offered</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RG_Tools_Offered__c</field>
            <value>
                <stringValue>Not Offered</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>452</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>assignRGAlertCaseToOwner</targetReference>
        </connector>
        <filterFormula>OR(
({!$Record.RecordType.DeveloperName} = &quot;RG_Alert_Case&quot;),
({!$Record.RecordType.DeveloperName} = &quot;Self_Exclusion_Alert_Case&quot;)
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>assignDelegateOwnerSendNotification</name>
        <label>Assign Case Owner to Delegated User and Send Notification</label>
        <locationX>594</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>selfExclCaseOwner</targetReference>
        </connector>
        <flowName>invoke_RG_Case_Send_Notification_to_Owner</flowName>
        <inputAssignments>
            <name>inputCase</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>inputUserId</name>
            <value>
                <elementReference>$Record.Account.Owner.Delegate_Assignment_User_ID__c</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>assignOwnerSentNotification</name>
        <label>Assign Case Owner And Send Notification</label>
        <locationX>330</locationX>
        <locationY>431</locationY>
        <connector>
            <targetReference>selfExclCaseOwner</targetReference>
        </connector>
        <flowName>invoke_RG_Case_Send_Notification_to_Owner</flowName>
        <inputAssignments>
            <name>inputCase</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>inputUserId</name>
            <value>
                <elementReference>$Record.Account.OwnerId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>invokeCaseEscalationNotification</name>
        <label>Invoke Case Escalation Notification</label>
        <locationX>1122</locationX>
        <locationY>1055</locationY>
        <connector>
            <targetReference>selfExclCaseOwner</targetReference>
        </connector>
        <flowName>Invoke_Case_Escalation_Notification</flowName>
        <inputAssignments>
            <name>inputCase</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>inputUserId</name>
            <value>
                <elementReference>$Record.Account.OwnerId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>amlEmailAddress</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue><EMAIL></stringValue>
        </value>
    </variables>
</Flow>
