<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>taskDescription</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Contact customer with a service message to confirm their RM Contact Channel preferences. Update the relevant RM Opt In Email and/or SMS fields with the outcome of your interaction.   Ensure you log an interaction - Reason for Contact = Contact Preferences and ensure you update comments within the interaction.  FYI - Service Message: “So I can continue communicating with you, we’ll just need to make sure your marketing preferences are updated – are you happy for me to send you offers and other information over SMS or email to the details listed on your account?”</stringValue>
        </value>
    </constants>
    <constants>
        <name>taskSubject</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Resub Task - Contact customer to confirm their RM Contact Channels</stringValue>
        </value>
    </constants>
    <constants>
        <name>varEmail</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Email</stringValue>
        </value>
    </constants>
    <constants>
        <name>varEmailSMS</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Email &amp; SMS</stringValue>
        </value>
    </constants>
    <constants>
        <name>varSMS</name>
        <dataType>String</dataType>
        <value>
            <stringValue>SMS</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Checks if RM Opt in SMS/Email field is updated to No and has any open Task</description>
        <name>Check_RM_Opt_in_and_Open_Tasks</name>
        <label>Check RM Opt in and Open Tasks</label>
        <locationX>974</locationX>
        <locationY>911</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RM_Opt_in_Email_or_SMS_is_No</name>
            <conditionLogic>1 AND (2 OR 3)</conditionLogic>
            <conditions>
                <leftValueReference>Get_RM_Contact_Channel_Tasks</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_RM_Contact_Channel_Task</targetReference>
            </connector>
            <label>RM Opt in Email or SMS is No</label>
        </rules>
    </decisions>
    <decisions>
        <description>checking if Unsub Status Mass Comms field is changed</description>
        <name>Is_Unsub_Status_Mass_Comms_Changed</name>
        <label>Is Unsub Status Mass Comms Changed?</label>
        <locationX>974</locationX>
        <locationY>575</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Email_in_Unsub_Status_Mass_Comms</name>
            <conditionLogic>1 AND NOT(2) AND 3</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_Date</targetReference>
            </connector>
            <label>Email in Unsub Status Mass Comms</label>
        </rules>
        <rules>
            <name>SMS_in_Unsub_Status_Mass_Comms</name>
            <conditionLogic>1 AND NOT(2) AND 3</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_SMS_Date</targetReference>
            </connector>
            <label>SMS in Unsub Status Mass Comms</label>
        </rules>
        <rules>
            <name>Email_SMS_in_Unsub_Status_Mass_Comms</name>
            <conditionLogic>1 AND NOT(2) AND NOT(3)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_SMS_and_Date</targetReference>
            </connector>
            <label>Email&amp;SMS in Unsub Status Mass Comms</label>
        </rules>
        <rules>
            <name>Null_in_Unsub_Status_Mass_Comms</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_SMS_to_Yes</targetReference>
            </connector>
            <label>Null in Unsub Status Mass Comms</label>
        </rules>
        <rules>
            <name>RM_Opt_In_Email_Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email</targetReference>
            </connector>
            <label>RM Opt In Email Updated</label>
        </rules>
        <rules>
            <name>RM_Opt_In_SMS_Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_SMS_Date_field</targetReference>
            </connector>
            <label>RM Opt In SMS Updated</label>
        </rules>
        <rules>
            <name>RM_Opt_In_Email_SMS_Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_SMS_Date_filed</targetReference>
            </connector>
            <label>RM Opt In Email &amp; SMS Updated</label>
        </rules>
    </decisions>
    <description>Flow updates RM Opt in Email, SMS and Date fields based on Unsub Status Mass Comms field</description>
    <formulas>
        <name>taskActivityDate</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate} + 7</expression>
    </formulas>
    <interviewLabel>Update RM Opt in Email SMS and Date Fields {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update RM Opt in Email SMS and Date Fields</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>Create Resub Task - RM Contact Channel Task and assign to Customer Owner</description>
        <name>Create_RM_Contact_Channel_Task</name>
        <label>Create RM Contact Channel Task</label>
        <locationX>842</locationX>
        <locationY>1031</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>taskActivityDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>taskDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>taskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>Current_Customer_Details.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <description>Get the current Customer details</description>
        <name>Current_Customer_Details</name>
        <label>Current Customer Details</label>
        <locationX>974</locationX>
        <locationY>335</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_RM_Contact_Channel_Tasks</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get all the Open Tasks for the RM Contact Channel</description>
        <name>Get_RM_Contact_Channel_Tasks</name>
        <label>Get RM Contact Channel Tasks</label>
        <locationX>974</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Unsub_Status_Mass_Comms_Changed</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>Contains</operator>
            <value>
                <elementReference>taskSubject</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Current_Customer_Details.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update RM Opt in EMail field when RM Opt in Email field is updated</description>
        <name>Update_RM_Opt_in_Email</name>
        <label>Update RM Opt in Email</label>
        <locationX>1106</locationX>
        <locationY>695</locationY>
        <connector>
            <targetReference>Check_RM_Opt_in_and_Open_Tasks</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates RM Opt in Email to No and Date to Today</description>
        <name>Update_RM_Opt_in_Email_Date</name>
        <label>Update RM Opt in Email &amp; Date</label>
        <locationX>50</locationX>
        <locationY>695</locationY>
        <connector>
            <targetReference>Check_RM_Opt_in_and_Open_Tasks</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in EMail and SMS to No Date to today</description>
        <name>Update_RM_Opt_in_Email_SMS_and_Date</name>
        <label>Update RM Opt in Email &amp; SMS and Date</label>
        <locationX>578</locationX>
        <locationY>695</locationY>
        <connector>
            <targetReference>Check_RM_Opt_in_and_Open_Tasks</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update both RM Opt in Email Date and RM Opt in SMS Date fields when both are changed</description>
        <name>Update_RM_Opt_in_Email_SMS_Date_filed</name>
        <label>Update RM Opt in Email SMS Date filed</label>
        <locationX>1634</locationX>
        <locationY>695</locationY>
        <connector>
            <targetReference>Check_RM_Opt_in_and_Open_Tasks</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in Email and SMS to Yes and RM opt in Email and SMS date to today</description>
        <name>Update_RM_Opt_in_Email_SMS_to_Yes</name>
        <label>Update RM Opt in Email &amp; SMS to Yes</label>
        <locationX>842</locationX>
        <locationY>695</locationY>
        <connector>
            <targetReference>Check_RM_Opt_in_and_Open_Tasks</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in SMS to No and Date to today</description>
        <name>Update_RM_Opt_in_SMS_Date</name>
        <label>Update RM Opt in SMS &amp; Date</label>
        <locationX>314</locationX>
        <locationY>695</locationY>
        <connector>
            <targetReference>Check_RM_Opt_in_and_Open_Tasks</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>RM Opt in SMS date field is set to today when RM Opt in SMS field is changed</description>
        <name>Update_RM_Opt_in_SMS_Date_field</name>
        <label>Update RM Opt in SMS Date field</label>
        <locationX>1370</locationX>
        <locationY>695</locationY>
        <connector>
            <targetReference>Check_RM_Opt_in_and_Open_Tasks</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>848</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Current_Customer_Details</targetReference>
        </connector>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
    <variables>
        <name>TodaysDate</name>
        <dataType>Date</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Flow.CurrentDate</elementReference>
        </value>
    </variables>
</Flow>
