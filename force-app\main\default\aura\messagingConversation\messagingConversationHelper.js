({
    getChatLog : function(cmp, recordId) {
        if (!cmp.isValid()) {
            console.error('[AURA]messagingConversationHelper.getChatLog() cmp is invalid');
            return;
        }
        
        var newconversationAPI = cmp.find('newconversationAPI');
        
        if (newconversationAPI && recordId && cmp.isValid()) {
            newconversationAPI.getChatLog({
                recordId: recordId
            })
            .then((result) => {
                // console.log(
                //     'AURA getChatLog() result:', JSON.parse(JSON.stringify(result))
                // );

                var error;
                if (cmp.isValid())
                  cmp.find('lwc').conversationLog(recordId, result, error);
            })
            .catch((error) => {
                console.error(
                    'AURA getChatLog() error',
                    'error:', JSON.parse(JSON.stringify(error))
                );
               
                var result;
                if (cmp.isValid())
                    cmp.find('lwc').conversationLog(recordId, result, error);
            });
        } else {
            console.error(
                'AURA getChatLog() could not find newconversationAPI, or no recordId',
                'newconversationAPI:', newconversationAPI,
                'recordId:', recordId
            );

            var result;

            var error = 'could not find newconversationAPI, or no recordId' + 
            'newconversationAPI:' + newconversationAPI +
            'recordId:' + recordId;
            if (cmp.isValid())
                cmp.find('lwc').conversationLog(recordId, result, error);
        }
    },

    sendChatMessage : function(cmp, recordId, msgText) {
        if (!cmp.isValid()) {
            console.error('[AURA]messagingConversationHelper.sendChatMessage() cmp is invalid');
            return;
        }

        var newconversationAPI = cmp.find('newconversationAPI');
        
        if (newconversationAPI && recordId && msgText && cmp.isValid()) {
            newconversationAPI.sendMessage({
                recordId: recordId,
                message: {
                    text: msgText
                }
            })
            .then((result) => {
                               
                // console.log(
                //     'AURA sendChatMessage() result:', JSON.parse(JSON.stringify(result))
                // );

                var error;
                     
                if (cmp.isValid())
                    cmp.find('lwc').sendMessageResult(recordId, msgText, result, error);
            })
            .catch((error) => {
                console.error(
                    'AURA sendChatMessage() error sending message',
                    'error:', JSON.parse(JSON.stringify(error))
                );

                var result;

                cmp.find('lwc').sendMessageResult(recordId, msgText, result, error);
            });
        } else {
            console.error(
                'AURA sendChatMessage() could not find newconversationAPI, no recordId, or no msgText',
                'newconversationAPI:', newconversationAPI,
                'recordId:', recordId,
                'msgText:', msgText
            );

            var result;

            var error = 'could not find newconversationAPI, no recordId, or no msgText' +
            'newconversationAPI:' + newconversationAPI +
            'recordId:' + recordId;
            
            if (cmp.isValid())
                cmp.find('lwc').sendMessageResult(recordId, msgText, result, error);
        }
    },

    endChat : function(cmp, recordId) {
        if (cmp.isValid()) {
            var newconversationAPI = cmp.find('newconversationAPI');
        
            if (newconversationAPI && recordId && cmp.isValid()) {
                newconversationAPI.endChat({
                    recordId: recordId
                })
                .then((result) => {
                    console.error(
                        'AURA endChat() result:', JSON.parse(JSON.stringify(result))
                    );

                    var error;
                    if (cmp.isValid())
                        cmp.find('lwc').endChatResult(recordId, result, error);
                })
                .catch((error) => {
                    console.error(
                        'AURA endChat() error ending chat',
                        'error:', error, 'stringify error', JSON.stringify(error), 'json parse(stringify):', JSON.parse(JSON.stringify(error))
                    );

                    var result;

                    if (cmp.isValid())
                        cmp.find('lwc').endChatResult(recordId, result, error);
                });
                var result;
            } else {
                var error = 'could not find newconversationAPI, or no recordId' +
                'newconversationAPI:' + newconversationAPI +
                'recordId:' + recordId;
            }
        } else {
            console.error('[AURA]messagingConversationHelper.endChat() cmp is invalid');
        }
    },

    // attempt end chat via closing agent work item
    // endChat : function(cmp, recordId) {
    //     var omniAPI = cmp.find('omniAPI');

    //     if (omniAPI && recordId) {

    //         omniAPI.getAgentWorks()
    //             .then((result) => {
    //                 const works = JSON.parse(result.works);
    //                 let foundWorkItem = false;
    //                 let closeWorkItemId = recordId;

    //                 works.forEach((work) => {
    //                     console.log('agentwork:', work);
    //                     console.log('workItemId:', work.workItemId, 'recordId', recordId);

    //                     if (work.workItemId === recordId || work.workItemId === recordId.substring(0, 15)) {
    //                         foundWorkItem = true;
    //                         closeWorkItemId = work.workId;
    //                         console.log('found matching workItemId; calling closeAgentWork...');
    //                     }
    //                 })

    //                 if (!foundWorkItem) {
    //                     console.log('found no matching current work item for agent... calling closeAgentWork with recordId', recordId);
    //                 }

    //                 omniAPI.closeAgentWork({ workId: closeWorkItemId })
    //                     .then((closeResult) => {
    //                         console.log('closeAgentWork for workItemId:', closeWorkItemId, 'closeResult', closeResult);
    //                         let error;
    //                         cmp.find('lwc').endChatResult(recordId, closeResult, error);
    //                     })
    //             })
    //             .catch((error) => {
    //                 console.error('error calling getAgentWorks/closeAgentWork:', error);
    //                 let closeResult;
    //                 cmp.find('lwc').endChatResult(recordId, closeResult, error);
    //             })
    //     } else {
    //         let closeResult;
    //         let error = 'could not find omniAPI and/or missing recordId; omniAPI:' + omniAPI + ' recordId: ' + recordId;
    //         cmp.find('lwc').endChatResult(recordId, closeResult, error);
    //     }
    // },

    doTabHighlight : function(cmp, hlp) {
        if (cmp.isValid()) {
            var tabId = cmp.get('v.tabId');

            if (tabId) {
                this.highlightIfNotActive(cmp);
            } else {
                let forHighlight = true, componentInit = false;
                hlp.getTabInfo(cmp, hlp, forHighlight, componentInit);
            }    
        } else {
            console.error('[AURA]messagingConversationHelper.doTabHighlight() cmp is invalid');
        }
    },

    getTabInfoDelayed : function(cmp, hlp, forHighlight, componentInitializing) {
        // let timerRef = window.setTimeout($A.getCallback(function() {
        //     let existingTimerRef = cmp.get('v.timerRef');
        //     if (existingTimerRef) {
        //         window.clearTimeout(existingTimerRef);
        //         cmp.set('v.timerRef', null);
        //     }

        //     if (cmp.isValid()) {
        //         hlp.getTabInfo(cmp, hlp, forHighlight, componentInitializing);
        //     }
        // }), cmp.get('v.tabInfoDelay'));
        let theTabId = cmp.get('v.tabId');

        if (!theTabId) {
            cmp.set('v.timerRef', setInterval(() => {
                let existingTimerRef = cmp.get('v.timerRef');
                if (existingTimerRef) {
                    clearInterval(existingTimerRef);
                }
                cmp.set('v.timerRef', null);
                
                if (cmp.isValid())
                    hlp.getTabInfo(cmp, hlp, forHighlight, componentInitializing);
            }, cmp.get('v.tabInfoDelay')));
        }
    },

    getTabInfo : function(cmp, hlp, forHighlight, componentIsInitializing) {
        let recallWithDelay = componentIsInitializing;

        if (cmp.isValid()) {
            let curTabId = cmp.get('v.tabId');
            var workspace = cmp.find('workspaceAPI');
            if (!curTabId && workspace && cmp.isValid()) {
                workspace.getEnclosingTabId()
                    .then((respTabId) => {
                        if (respTabId && cmp.isValid()) {
                            workspace.getTabInfo({
                                tabId: respTabId
                            })
                            .then((respTabInfo) => {
                                if (respTabInfo) {
                                    recallWithDelay = false;
                                    cmp.set('v.tabId', respTabInfo.tabId);
                                    
                                    if (respTabInfo.isSubtab)
                                        cmp.set('v.parentTabId', respTabInfo.parentTabId);

                                    if (forHighlight && cmp.isValid())
                                        this.highlightIfNotActive(cmp);

                                    // if (cmp.get('v.showSpinner') === true) {
                                    //     console.log('AURA STOPPING SPINNER AS WE HAVE THE TABID NOW');
                                    //     cmp.set('v.showSpinner', false);
                                    // }
                                    console.error('AURA - HAVE TABIDS - NOT STOPPING SPINNER');
                                } else if (recallWithDelay) {
                                    hlp.getTabInfoDelayed(cmp, hlp, forHighlight, componentIsInitializing);
                                }
                            })
                            .catch((error) => {
                                console.error('error calling getTabInfo', error);
                                if (recallWithDelay)
                                    hlp.getTabInfoDelayed(cmp, hlp, forHighlight, componentIsInitializing);
                            });
                        } else if (recallWithDelay) {
                            hlp.getTabInfoDelayed(cmp, hlp, forHighlight, componentIsInitializing);
                        }
                    })
                    .catch((error) => {
                        console.error('error calling getEnclosingTabId ', error);
                        if (recallWithDelay)
                            hlp.getTabInfoDelayed(cmp, hlp, forHighlight, componentIsInitializing);
                    });
            }
        } else {
            console.error('[AURA]messagingConversationHelper.getTabInfo() cmp is invalid');
        }

        if (recallWithDelay)
            hlp.getTabInfoDelayed(cmp, hlp, forHighlight, componentIsInitializing);
    },

    highlightIfNotActive : function(cmp) {
        if (cmp.isValid()) {
            var tabId = cmp.get('v.tabId');
            var parentTabId = cmp.get('v.parentTabId');
            var workspace = cmp.find('workspaceAPI');

            if (tabId && workspace && cmp.isValid()) {
                workspace.getFocusedTabInfo()
                    .then((response) => {
                        if (response.tabId != tabId) {
                            // use parent tab id if there is one...
                            let highlightTabId = (parentTabId ? parentTabId : tabId);
                            // if parent tab id, and focused tab is the parent tab id, highlight tabId (the sub-tab)
                            if (parentTabId && response.tabId == parentTabId)
                                highlightTabId = tabId;

                            workspace.setTabHighlighted({
                                tabId: highlightTabId,
                                highlighted: true,
                                options: {
                                    pulse: true,
                                    state: "warning"
                                }
                            });
                        }
                    })
                    .catch((error) => {
                        console.error('error calling getFocusedTabInfo()' ,error);
                    })
            }
        } else {
            console.error('[AURA]messagingConversationHelper.getTabInfo() cmp is invalid');
        }
    },

    unhighlightTab : function(cmp, targetTabId) {
        if (cmp.isValid()) {
        
            var workspace = cmp.find('workspaceAPI');

            if (targetTabId && workspace && cmp.isValid()) {
                workspace.setTabHighlighted({
                    tabId: targetTabId,
                    highlighted: false
                });
            }
        } else {
            console.error('[AURA]messagingConversationHelper.unhighlightTab() cmp is invalid');
        }
    }

    // only works for livechattranscript; even tho standard cmp lets you attach files 
    // transferFile : function(cmp, recordId, attachId) {
    //     var newconversationAPI = cmp.find('newconversationAPI');

    //     if (newconversationAPI && recordId && attachId) {
    //         newconversationAPI.initFileTransfer({
    //             recordId: recordId,
    //             attachmentRecordId: attachId
    //         })
    //         .then((result) => {
    //             console.log(
    //                 'AURA initFileTransfer() result:', JSON.parse(JSON.stringify(result))
    //             );

    //             var error;

    //             cmp.find('lwc').fileTransferResult(recordId, attachId, result, error);
    //         })
    //         .catch((error) => {
    //             console.error(
    //                 'AURA initFileTransfer() error:', JSON.parse(JSON.stringify(error))
    //             );

    //             var result;

    //             cmp.find('lwc').fileTransferResult(recordId, attachId, result, error);
    //         });
    //     } else {
    //         console.error(
    //             'AURA initFileTransfer() could not find newconversationAPI, no attachId, or no recordId',
    //             'newconversationAPI:', newconversationAPI,
    //             'recordId:', recordId,
    //             'attachId', attachId
    //         );

    //         var result;

    //         var error = 'could not find newconversationAPI, no attachId, or no recordId' +
    //         'newconversationAPI:' + newconversationAPI +
    //         'recordId:' + recordId +
    //         'attachId:' + attachId;

    //         cmp.find('lwc').fileTransferResult(recordId, attachId, result, error);
    //     }
    // }
})