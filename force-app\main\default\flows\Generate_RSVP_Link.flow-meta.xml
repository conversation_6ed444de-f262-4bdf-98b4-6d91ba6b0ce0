<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>58.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Parent_to_Campaign</name>
        <label>Assign Parent to Campaign</label>
        <locationX>138</locationX>
        <locationY>1214</locationY>
        <assignmentItems>
            <assignToReference>Get_Child_Campaign.ParentId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Child</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Clear_Selected_Child_Variable</name>
        <label>Clear Selected Child Variable</label>
        <locationX>50</locationX>
        <locationY>1514</locationY>
        <assignmentItems>
            <assignToReference>newChildRecordIds</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>blackCollection</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Children_records</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>childChoice</name>
        <choiceText>This campaign is an stand-alone event that customers can RSVP to (Child)</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>child</stringValue>
        </value>
    </choices>
    <choices>
        <name>parentChoice</name>
        <choiceText>This campaign will be used as a Parent where there are multiple events to RSVP to</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>parent</stringValue>
        </value>
    </choices>
    <decisions>
        <name>Parent_or_Child_Selected</name>
        <label>Parent or Child Selected</label>
        <locationX>336</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Update_to_Child</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decParent_selected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Select_how_you_want_to_use_this_campaign</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>parentChoice</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Parent_field</targetReference>
            </connector>
            <label>Parent selected</label>
        </rules>
    </decisions>
    <decisions>
        <name>Were_New_children_added</name>
        <label>Were New children added</label>
        <locationX>182</locationX>
        <locationY>890</locationY>
        <defaultConnector>
            <targetReference>URL_Link</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>New_Children_were_added</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>newChildRecordIds</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>For_Each_New_Child</targetReference>
            </connector>
            <label>New Children were added</label>
        </rules>
    </decisions>
    <description>SBET-531 - Generate Parent Campaign Link - end date</description>
    <environments>Default</environments>
    <formulas>
        <name>parentEndDate</name>
        <dataType>DateTime</dataType>
        <expression>DATETIMEVALUE({!Copy_1_of_Event_Date}+1)</expression>
    </formulas>
    <interviewLabel>Generate RSVP Link {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Generate RSVP Link</label>
    <loops>
        <name>For_Each_New_Child</name>
        <label>For Each New Child</label>
        <locationX>50</locationX>
        <locationY>998</locationY>
        <collectionReference>newChildRecordIds</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Get_Child_Campaign</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Clear_Selected_Child_Variable</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Child_Campaign</name>
        <label>Get Child Campaign</label>
        <locationX>138</locationX>
        <locationY>1106</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Parent_to_Campaign</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>For_Each_New_Child</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Campaign</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Children_records</name>
        <label>Get Children records</label>
        <locationX>182</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Show_existing_children_records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ParentId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <object>Campaign</object>
        <outputReference>childrenRecords</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Match_Name__c</queriedFields>
        <queriedFields>Start_Date__c</queriedFields>
        <queriedFields>Venue__c</queriedFields>
        <queriedFields>Accessibility_Requirements__c</queriedFields>
        <queriedFields>Dietary_Requirements__c</queriedFields>
        <queriedFields>Transfer_Required__c</queriedFields>
        <queriedFields>Transfer_Required_From__c</queriedFields>
        <sortField>Match_Name__c</sortField>
        <sortOrder>Asc</sortOrder>
    </recordLookups>
    <recordUpdates>
        <name>Copy_1_of_Update_Campaign_Settings</name>
        <label>Update Parent Campaign Settings</label>
        <locationX>182</locationX>
        <locationY>566</locationY>
        <connector>
            <targetReference>Get_Children_records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>End_Date__c</field>
            <value>
                <elementReference>parentEndDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Match_Name__c</field>
            <value>
                <elementReference>Copy_1_of_Event_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>One_Child_Event_Per_Campaign__c</field>
            <value>
                <elementReference>Allow_customers_to_only_attend_one_event_in_this_group</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Start_Date__c</field>
            <value>
                <elementReference>Copy_1_of_Event_Date</elementReference>
            </value>
        </inputAssignments>
        <object>Campaign</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Campaign_Settings</name>
        <label>Update Campaign Settings</label>
        <locationX>490</locationX>
        <locationY>566</locationY>
        <connector>
            <targetReference>Copy_1_of_Select_RSVP_Form_Configurations</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Accessibility_Requirements__c</field>
            <value>
                <elementReference>Accessibility_Requirements</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Assign_Customer_2_Tickets__c</field>
            <value>
                <elementReference>Assume_the_customer_is_bringing_a_guest_and_assign_2_tickets_by_default</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Dietary_Requirements__c</field>
            <value>
                <elementReference>Dietary_Requirements</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Limit_Customer_to_One_Guest__c</field>
            <value>
                <elementReference>Limit_Customer_to_1_Guest</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Match_Name__c</field>
            <value>
                <elementReference>Event_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Start_Date__c</field>
            <value>
                <elementReference>Event_Date</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transfer_Required_From__c</field>
            <value>
                <elementReference>Transfer_From_Venue</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transfer_Required__c</field>
            <value>
                <elementReference>Transfer_To_Venue</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Venue__c</field>
            <value>
                <elementReference>venueField</elementReference>
            </value>
        </inputAssignments>
        <object>Campaign</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Child</name>
        <label>Update Child</label>
        <locationX>138</locationX>
        <locationY>1322</locationY>
        <connector>
            <targetReference>For_Each_New_Child</targetReference>
        </connector>
        <inputReference>Get_Child_Campaign</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Parent_field</name>
        <label>Update Parent field</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>Select_Parent_Form_Configurations</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Is_Parent_Campaign__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Campaign</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_to_Child</name>
        <label>Update to meet Child Criteria</label>
        <locationX>490</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>Select_RSVP_Form_Configurations</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Is_Parent_Campaign__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>selectParentForChild.recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Campaign</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Waitlist_Settings</name>
        <label>Update Waitlist Settings</label>
        <locationX>490</locationX>
        <locationY>782</locationY>
        <connector>
            <targetReference>URL_Link</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>RSVP_Due_By__c</field>
            <value>
                <elementReference>RSVP_before_this_date</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Set_Waitlist_Limit__c</field>
            <value>
                <elementReference>Set_a_limit_for_the_waitlist_of_this_event</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Waitlist_Capacity__c</field>
            <value>
                <elementReference>Waitlist_Size_Total_Seats</elementReference>
            </value>
        </inputAssignments>
        <object>Campaign</object>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>Copy_1_of_Select_RSVP_Form_Configurations</name>
        <label>Waitlist Settings</label>
        <locationX>490</locationX>
        <locationY>674</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Update_Waitlist_Settings</targetReference>
        </connector>
        <fields>
            <name>Set_a_limit_for_the_waitlist_of_this_event</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <elementReference>recordId.Set_Waitlist_Limit__c</elementReference>
            </defaultValue>
            <fieldText>Limit the waitlist for this event</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Copy_1_of_Description1</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Select how you wish to restrict the Waitlist&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Set_a_limit_for_the_waitlist_of_this_event</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>RSVP_before_this_date</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>recordId.RSVP_Due_By__c</elementReference>
            </defaultValue>
            <fieldText>RSVP before this date:</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Set_a_limit_for_the_waitlist_of_this_event</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Waitlist_Size_Total_Seats</name>
            <dataType>Number</dataType>
            <defaultValue>
                <elementReference>recordId.Waitlist_Capacity__c</elementReference>
            </defaultValue>
            <fieldText>Waitlist Size (Total Seats)</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <scale>0</scale>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Set_a_limit_for_the_waitlist_of_this_event</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ParentOrChildScreen</name>
        <label>ParentOrChildScreen</label>
        <locationX>336</locationX>
        <locationY>134</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Parent_or_Child_Selected</targetReference>
        </connector>
        <fields>
            <name>Select_how_you_want_to_use_this_campaign</name>
            <choiceReferences>childChoice</choiceReferences>
            <choiceReferences>parentChoice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>childChoice</defaultSelectedChoiceReference>
            <fieldText>Select how you want to use this campaign</fieldText>
            <fieldType>RadioButtons</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>ParentOrChildScreen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ParentOrChildScreen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>selectParentForChild</name>
                    <extensionName>flowruntime:lookup</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>fieldApiName</name>
                        <value>
                            <stringValue>ParentId</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Optionally, you can select another campaign to be the landing page for this event</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>objectApiName</name>
                        <value>
                            <stringValue>Campaign</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>recordId</name>
                        <value>
                            <elementReference>recordId.ParentId</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Select_how_you_want_to_use_this_campaign</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>childChoice</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Select_Parent_Form_Configurations</name>
        <label>Select Parent Form Configurations</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Copy_1_of_Update_Campaign_Settings</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_helptext</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Check that the following details are correct:&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Copy_1_of_Event_Name</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>recordId.Match_Name__c</elementReference>
            </defaultValue>
            <fieldText>Parent Event Name</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Copy_1_of_Event_Date</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>recordId.Start_Date__c</elementReference>
            </defaultValue>
            <fieldText>Landing Page End Date</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Allow_customers_to_only_attend_one_event_in_this_group</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <booleanValue>true</booleanValue>
            </defaultValue>
            <fieldText>Allow customers to only attend one event in this group</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Select_RSVP_Form_Configurations</name>
        <label>Select RSVP Form Configurations</label>
        <locationX>490</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Update_Campaign_Settings</targetReference>
        </connector>
        <fields>
            <name>helptext</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Check that the following details are correct:&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Event_Name</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>recordId.Match_Name__c</elementReference>
            </defaultValue>
            <fieldText>Event Name</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>venueField</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>recordId.Venue__c</elementReference>
            </defaultValue>
            <fieldText>Venue Name</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Event_Date</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>recordId.Start_Date__c</elementReference>
            </defaultValue>
            <fieldText>Event Date</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Description1</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Select the additional fields you wish to add to your RSVP form for this event:&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Dietary_Requirements</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <elementReference>recordId.Dietary_Requirements__c</elementReference>
            </defaultValue>
            <fieldText>Dietary Requirements</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Accessibility_Requirements</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <elementReference>recordId.Accessibility_Requirements__c</elementReference>
            </defaultValue>
            <fieldText>Accessibility Requirements</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Transfer_To_Venue</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <elementReference>recordId.Transfer_Required__c</elementReference>
            </defaultValue>
            <fieldText>Transfer To Venue</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Transfer_From_Venue</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <elementReference>recordId.Transfer_Required_From__c</elementReference>
            </defaultValue>
            <fieldText>Transfer From Venue</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>helptext2</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Select &lt;strong&gt;one&lt;/strong&gt; of the following to choose how many tickets will be assigned to a customer:&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Limit_Customer_to_1_Guest</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <elementReference>recordId.Limit_Customer_to_One_Guest__c</elementReference>
            </defaultValue>
            <fieldText>Ask the customer if they will be bringing a guest</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Assume_the_customer_is_bringing_a_guest_and_assign_2_tickets_by_default</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <elementReference>recordId.Assign_Customer_2_Tickets__c</elementReference>
            </defaultValue>
            <fieldText>Assume the customer is bringing a guest and assign 2 tickets by default</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Show_existing_children_records</name>
        <label>Show existing children records</label>
        <locationX>182</locationX>
        <locationY>782</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Were_New_children_added</targetReference>
        </connector>
        <fields>
            <name>Show_existing_children_records_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Show_existing_children_records_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>NoRecordsText</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;There are currently no child campaigns linked to this event. &lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Get_Children_records</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Show_existing_children_records_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Show_existing_children_records_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>message3</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;The following events will appear as links on this event&apos;s page:&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>existingChildrenTable</name>
                    <dataTypeMappings>
                        <typeName>T</typeName>
                        <typeValue>Campaign</typeValue>
                    </dataTypeMappings>
                    <extensionName>flowruntime:datatable</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Child Events</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>selectionMode</name>
                        <value>
                            <stringValue>NO_SELECTION</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>minRowSelection</name>
                        <value>
                            <numberValue>0.0</numberValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>tableData</name>
                        <value>
                            <elementReference>childrenRecords</elementReference>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>columns</name>
                        <value>
                            <stringValue>[{&quot;apiName&quot;:&quot;Match_Name__c&quot;,&quot;guid&quot;:&quot;column-691e&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Event Name&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Match Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Start_Date__c&quot;,&quot;guid&quot;:&quot;column-e618&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Event Start Date&quot;,&quot;type&quot;:&quot;customDateTime&quot;},{&quot;apiName&quot;:&quot;Venue__c&quot;,&quot;guid&quot;:&quot;column-3716&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Venue&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Accessibility_Requirements__c&quot;,&quot;guid&quot;:&quot;column-0664&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Access Req.&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Request Accessibility Requirements&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;apiName&quot;:&quot;Dietary_Requirements__c&quot;,&quot;guid&quot;:&quot;column-9e5b&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Dietary Req.&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Request Dietary Requirements&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;apiName&quot;:&quot;Transfer_Required__c&quot;,&quot;guid&quot;:&quot;column-d524&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Transfer To Venue&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;Request Transfer to Venue&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;apiName&quot;:&quot;Transfer_Required_From__c&quot;,&quot;guid&quot;:&quot;column-dab8&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Transfer From Venue&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:6,&quot;label&quot;:&quot;Request Transfer From Venue&quot;,&quot;type&quot;:&quot;boolean&quot;}]</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>maxRowSelection</name>
                        <value>
                            <numberValue>0.0</numberValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>shouldDisplayLabel</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>message</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;To update the details of the child campaigns above, use the Generate RSVP Link action on that event&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>or</conditionLogic>
                <conditions>
                    <leftValueReference>Get_Children_records</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>selectAdditionalChildren</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>ParentId</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>Campaign</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Select an additional event to add to the list above:</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxValues</name>
                <value>
                    <numberValue>5.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <outputParameters>
                <assignToReference>newChildRecordIds</assignToReference>
                <name>recordIds</name>
            </outputParameters>
        </fields>
        <fields>
            <name>message2</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;If the event does not exist yet, you can create a new campaign and select this event as the parent when you click Generate RSVP Link on the new event&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>URL_Link</name>
        <label>URL Link</label>
        <locationX>336</locationX>
        <locationY>1856</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Description2</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;The following link can be shared with customers to RSVP to this event:&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>URLComponent</name>
            <extensionName>flowruntime:url</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>recordId.RSVP_Site_URL__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Copy and paste the following link:</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>210</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>ParentOrChildScreen</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>blackCollection</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>childrenRecords</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Campaign</objectType>
    </variables>
    <variables>
        <name>newChildRecordIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Campaign</objectType>
    </variables>
    <variables>
        <name>selectedParent</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Campaign</objectType>
    </variables>
</Flow>
