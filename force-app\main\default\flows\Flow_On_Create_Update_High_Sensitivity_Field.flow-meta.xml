<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>New_or_Returning_to_Premium</name>
        <label>New or Returning to Premium</label>
        <locationX>314</locationX>
        <locationY>287</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>New_or_Returning</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>newOrReturningFormula</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Default_Sensitivity_to_Medium</targetReference>
            </connector>
            <label>New or Returning</label>
        </rules>
        <rules>
            <name>Removed_from_Premium</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Elite_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Elite</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Elite_Status__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Elite_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Elite</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Clear_Sensitivity</targetReference>
            </connector>
            <label>Removed from Premium</label>
        </rules>
    </decisions>
    <description>Flow to either clear or populate the High Sensitivity field on the Customer record when a customer is created, added to or removed from Premium.</description>
    <environments>Default</environments>
    <formulas>
        <name>newOrReturningFormula</name>
        <dataType>Boolean</dataType>
        <expression>OR(
ISNEW(),
AND(
ISCHANGED({!$Record.Elite_Status__c}),
{!$Record.Elite_Status__c} = &quot;Elite&quot;
)
)</expression>
    </formulas>
    <interviewLabel>Flow On Create/Update - High Sensitivity Field {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Flow On Create/Update - High Sensitivity Field</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Clear_Sensitivity</name>
        <label>Clear Sensitivity</label>
        <locationX>314</locationX>
        <locationY>395</locationY>
        <inputAssignments>
            <field>High_Sensitivity__c</field>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Default_Sensitivity_to_Medium</name>
        <label>Default Sensitivity to Medium</label>
        <locationX>50</locationX>
        <locationY>395</locationY>
        <inputAssignments>
            <field>High_Sensitivity__c</field>
            <value>
                <stringValue>Medium</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>New_or_Returning_to_Premium</targetReference>
        </connector>
        <filterFormula>OR(
    ISNEW(),
    ISCHANGED({!$Record.Elite_Status__c})
)</filterFormula>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
