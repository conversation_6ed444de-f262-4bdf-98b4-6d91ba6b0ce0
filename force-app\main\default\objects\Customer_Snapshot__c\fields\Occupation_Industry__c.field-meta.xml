<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Occupation_Industry__c</fullName>
    <label>Occupation Industry</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Agriculture</fullName>
                <default>false</default>
                <label>Agriculture</label>
            </value>
            <value>
                <fullName>Construction</fullName>
                <default>false</default>
                <label>Construction</label>
            </value>
            <value>
                <fullName>Customer Services</fullName>
                <default>false</default>
                <label>Customer Services</label>
            </value>
            <value>
                <fullName>Education</fullName>
                <default>false</default>
                <label>Education</label>
            </value>
            <value>
                <fullName>Entertainment &amp; Arts</fullName>
                <default>false</default>
                <label>Entertainment &amp; Arts</label>
            </value>
            <value>
                <fullName>Financial</fullName>
                <default>false</default>
                <label>Financial</label>
            </value>
            <value>
                <fullName>Government &amp; Public Services</fullName>
                <default>false</default>
                <label>Government &amp; Public Services</label>
            </value>
            <value>
                <fullName>Hospitality</fullName>
                <default>false</default>
                <label>Hospitality</label>
            </value>
            <value>
                <fullName>Legal</fullName>
                <default>false</default>
                <label>Legal</label>
            </value>
            <value>
                <fullName>Leisure</fullName>
                <default>false</default>
                <label>Leisure</label>
            </value>
            <value>
                <fullName>Manufacturing</fullName>
                <default>false</default>
                <label>Manufacturing</label>
            </value>
            <value>
                <fullName>Medical</fullName>
                <default>false</default>
                <label>Medical</label>
            </value>
            <value>
                <fullName>Military</fullName>
                <default>false</default>
                <label>Military</label>
            </value>
            <value>
                <fullName>Other</fullName>
                <default>false</default>
                <label>Other</label>
            </value>
            <value>
                <fullName>Property &amp; Real Estate</fullName>
                <default>false</default>
                <label>Property &amp; Real Estate</label>
            </value>
            <value>
                <fullName>Sports</fullName>
                <default>false</default>
                <label>Sports</label>
            </value>
            <value>
                <fullName>Technology</fullName>
                <default>false</default>
                <label>Technology</label>
            </value>
            <value>
                <fullName>Telecommunications</fullName>
                <default>false</default>
                <label>Telecommunications</label>
            </value>
            <value>
                <fullName>Transport</fullName>
                <default>false</default>
                <label>Transport</label>
            </value>
            <value>
                <fullName>Travel</fullName>
                <default>false</default>
                <label>Travel</label>
            </value>
            <value>
                <fullName>Unknown</fullName>
                <default>false</default>
                <label>Unknown</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
