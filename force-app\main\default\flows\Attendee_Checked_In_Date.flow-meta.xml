<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <formulas>
        <name>DateDiffCheckedIn</name>
        <dataType>Number</dataType>
        <expression>{!$Record.Checked_In_Date__c}-{!$Record.Event_Date__c}</expression>
        <scale>0</scale>
    </formulas>
    <formulas>
        <name>DaysSinceChecked</name>
        <dataType>Number</dataType>
        <expression>{!$Record.Checked_In_Date__c}-{!$Record.Event_End_Date__c}</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>Daystakentocheckin</name>
        <dataType>Number</dataType>
        <expression>{!$Record.Checked_In_Date__c}-{!$Record.Event_End_Date__c}</expression>
        <scale>0</scale>
    </formulas>
    <interviewLabel>Attendee Checked In Date {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Attendee Checked In Date</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>getAttendeeRecord</name>
        <label>getAttendeeRecord</label>
        <locationX>541</locationX>
        <locationY>112</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Checked_In_Date</targetReference>
        </connector>
        <filterLogic>1 AND (2 OR 3)</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attended</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>No Show</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Checked_In_Date</name>
        <label>Update Checked In Date</label>
        <locationX>869</locationX>
        <locationY>164</locationY>
        <connector>
            <targetReference>Update_Days_Taken_to_Check_In</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Checked_In_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Days_Taken_to_Check_In</name>
        <label>Update Days Taken to Check In</label>
        <locationX>848</locationX>
        <locationY>435</locationY>
        <inputAssignments>
            <field>Check_in_timing__c</field>
            <value>
                <elementReference>Daystakentocheckin</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>49</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>getAttendeeRecord</targetReference>
        </connector>
        <filterLogic>(1 or 2) and 3</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attended</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>No Show</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Attendee__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Draft</status>
</Flow>
