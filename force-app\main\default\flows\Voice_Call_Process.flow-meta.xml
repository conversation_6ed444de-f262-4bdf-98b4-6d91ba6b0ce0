<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Completed_Status_to_Task</name>
        <label>Assign Completed Status to Task</label>
        <locationX>2338</locationX>
        <locationY>384</locationY>
        <assignmentItems>
            <assignToReference>openTaskID.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Open_Task</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Transform_call_type</name>
        <label>Transform call type</label>
        <locationX>578</locationX>
        <locationY>1008</locationY>
        <assignmentItems>
            <assignToReference>$Record.CallType</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Outbound</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Task</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Transform_call_type2</name>
        <label>Transform call type</label>
        <locationX>842</locationX>
        <locationY>1008</locationY>
        <assignmentItems>
            <assignToReference>$Record.CallType</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Inbound</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Task</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Transform_call_type3</name>
        <label>Transform call type</label>
        <locationX>1106</locationX>
        <locationY>1008</locationY>
        <assignmentItems>
            <assignToReference>$Record.CallType</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Outbound</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Task</targetReference>
        </connector>
    </assignments>
    <customErrors>
        <name>ErrorMessage</name>
        <label>ErrorMessge</label>
        <locationX>1810</locationX>
        <locationY>1524</locationY>
        <customErrorMessages>
            <errorMessage>An error occurred when trying to create a task related to this record. Please review error message below for more detail.
{!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Error_is_Locked_Row</name>
        <label>Error is Locked Row</label>
        <locationX>1678</locationX>
        <locationY>1416</locationY>
        <defaultConnector>
            <targetReference>ErrorMessage</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>errorIsLockedRow</leftValueReference>
                <operator>EqualTo</operator>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_CreateTask</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Has_Voice_Call_reason_Changed</name>
        <label>Has Voice Call reason Changed</label>
        <locationX>1293</locationX>
        <locationY>576</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_Is_Changed</name>
            <conditionLogic>(1 AND 2) OR (3 AND 4)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Call_Reason__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Call_Reason__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Customer__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RelatedRecordId</leftValueReference>
                <operator>EqualTo</operator>
            </conditions>
            <connector>
                <targetReference>Get_Existing_Task</targetReference>
            </connector>
            <label>Yes Is Changed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_there_an_Existing_Task</name>
        <label>Is there an Existing Task?</label>
        <locationX>512</locationX>
        <locationY>792</locationY>
        <defaultConnector>
            <targetReference>What_is_call_type</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes_Existing</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Existing_Task</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Records_1</targetReference>
            </connector>
            <label>Yes Existing</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Voice_Call_New</name>
        <label>Is Voice Call New</label>
        <locationX>1293</locationX>
        <locationY>276</locationY>
        <defaultConnector>
            <targetReference>Has_Voice_Call_reason_Changed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_Is_New</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_if_Customer_has_opted_out_of_calls</targetReference>
            </connector>
            <label>Yes Is New</label>
        </rules>
    </decisions>
    <decisions>
        <name>Was_Task_created_in_Open_Status</name>
        <label>Was Task created in Open Status?</label>
        <locationX>2470</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>openTaskID.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>openTaskID</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Completed_Status_to_Task</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>What_is_call_type</name>
        <label>What is call type?</label>
        <locationX>974</locationX>
        <locationY>900</locationY>
        <defaultConnector>
            <targetReference>Get_Task</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Inbound Or Outbound</defaultConnectorLabel>
        <rules>
            <name>IsInternal</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CallType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Internal</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Transform_call_type</targetReference>
            </connector>
            <label>IsInternal</label>
        </rules>
        <rules>
            <name>IsTransfer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CallType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Transfer</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Transform_call_type2</targetReference>
            </connector>
            <label>IsTransfer</label>
        </rules>
        <rules>
            <name>IsCallBack</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CallType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Callback</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Transform_call_type3</targetReference>
            </connector>
            <label>IsCallBack</label>
        </rules>
    </decisions>
    <description>This flow is used to handle all automation when a voice call record is created
SBET-1181 - Populate Current Customer Owner on Interaction Tasks</description>
    <environments>Default</environments>
    <formulas>
        <name>dateTimeWithLocale</name>
        <dataType>DateTime</dataType>
        <expression>((IF(ISPICKVAL({!$Record.CallOrigin},&quot;Voicemail&quot;), {!$Record.CallEndDateTime}, {!$Record.CallStartDateTime}))+(11/24))</expression>
    </formulas>
    <formulas>
        <name>errorIsLockedRow</name>
        <dataType>Boolean</dataType>
        <expression>CONTAINS({!$Flow.FaultMessage}, &quot;UNABLE_TO_LOCK_ROW&quot;)</expression>
    </formulas>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <formulas>
        <name>SubjectFormula</name>
        <dataType>String</dataType>
        <expression>&quot;Call - &quot; + TEXT({!$Record.CallType}) + &quot; - &quot; + TEXT({!$Record.Call_Reason__c})</expression>
    </formulas>
    <interviewLabel>Voice Call Process {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Voice Call Process</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Copy_1_of_CreateTask</name>
        <label>CreateTask (Open Status)</label>
        <locationX>1546</locationX>
        <locationY>1524</locationY>
        <assignRecordIdToReference>openTaskID.Id</assignRecordIdToReference>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>dateTimeWithLocale</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CallDurationInSeconds</field>
            <value>
                <elementReference>$Record.Call_Duration_Seconds__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>Call</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Call_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <elementReference>$Record.CallType</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DubberCallDuration__c</field>
            <value>
                <elementReference>$Record.Call_Duration_Seconds__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Call_Reason__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Task.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Time__c</field>
            <value>
                <elementReference>dateTimeWithLocale</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Voice_Call__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>$Record.Customer__r.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>$Record.Within_Business_Hours__c</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordCreates>
    <recordCreates>
        <name>CreateTask</name>
        <label>CreateTask</label>
        <locationX>974</locationX>
        <locationY>1308</locationY>
        <faultConnector>
            <targetReference>Error_is_Locked_Row</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>dateTimeWithLocale</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CallDurationInSeconds</field>
            <value>
                <elementReference>$Record.Call_Duration_Seconds__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>Call</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Call_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <elementReference>$Record.CallType</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DubberCallDuration__c</field>
            <value>
                <elementReference>$Record.Call_Duration_Seconds__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner_Role__c</field>
            <value>
                <elementReference>$Record.Customer__r.Owner.UserRole.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner__c</field>
            <value>
                <elementReference>$Record.Customer_Owner__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Call_Reason__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Task.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Time__c</field>
            <value>
                <elementReference>dateTimeWithLocale</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Voice_Call__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>$Record.Customer__r.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>$Record.Within_Business_Hours__c</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Existing_Task</name>
        <label>Get Existing Task</label>
        <locationX>512</locationX>
        <locationY>684</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_there_an_Existing_Task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Voice_Call__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Task</name>
        <label>Get Interaction RT</label>
        <locationX>974</locationX>
        <locationY>1200</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CreateTask</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Interaction</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Open_Task</name>
        <label>Update Open Task</label>
        <locationX>2338</locationX>
        <locationY>492</locationY>
        <inputReference>openTaskID</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Records_1</name>
        <label>Update Records 1</label>
        <locationX>50</locationX>
        <locationY>900</locationY>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorMessage</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Existing_Task.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>dateTimeWithLocale</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CallDurationInSeconds</field>
            <value>
                <elementReference>$Record.Call_Duration_Seconds__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Customer__r.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Call_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DubberCallDuration__c</field>
            <value>
                <elementReference>$Record.Call_Duration_Seconds__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Call_Reason__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Time__c</field>
            <value>
                <elementReference>dateTimeWithLocale</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>$Record.Within_Business_Hours__c</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <start>
        <locationX>1755</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_Voice_Call_New</targetReference>
        </connector>
        <filterFormula>OR(
  ISNEW(),
  AND(
    OR(
      NOT(ISBLANK(TEXT({!$Record.Call_Reason__c}))),
      ISCHANGED({!$Record.Call_Reason__c})
    ),
    OR(
      NOT(ISBLANK({!$Record.Customer__c})),
      ISCHANGED({!$Record.Customer__c})
    )
  )
)</filterFormula>
        <object>VoiceCall</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <scheduledPaths>
            <name>Run_30_Minutes_Later</name>
            <connector>
                <targetReference>Was_Task_created_in_Open_Status</targetReference>
            </connector>
            <label>Run 30 Minutes Later</label>
            <maxBatchSize>1</maxBatchSize>
            <offsetNumber>30</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <recordField>LastModifiedDate</recordField>
            <timeSource>RecordField</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Check_if_Customer_has_opted_out_of_calls</name>
        <label>Check if Customer has opted out of calls</label>
        <locationX>1161</locationX>
        <locationY>384</locationY>
        <connector>
            <targetReference>Has_Voice_Call_reason_Changed</targetReference>
        </connector>
        <flowName>Customer_Opted_Out_Channel_Check</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>voiceCall</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>openTaskID</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
