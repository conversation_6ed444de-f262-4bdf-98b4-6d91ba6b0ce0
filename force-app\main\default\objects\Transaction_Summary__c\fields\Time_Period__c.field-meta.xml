<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Time_Period__c</fullName>
    <label>Time Period</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>1 month</fullName>
                <default>false</default>
                <label>1 month</label>
            </value>
            <value>
                <fullName>3 months</fullName>
                <default>false</default>
                <label>3 months</label>
            </value>
            <value>
                <fullName>6 months</fullName>
                <default>false</default>
                <label>6 months</label>
            </value>
            <value>
                <fullName>12 months</fullName>
                <default>false</default>
                <label>12 months</label>
            </value>
            <value>
                <fullName>18 months</fullName>
                <default>false</default>
                <label>18 months</label>
            </value>
            <value>
                <fullName>2 years</fullName>
                <default>false</default>
                <label>2 years</label>
            </value>
            <value>
                <fullName>3 years</fullName>
                <default>false</default>
                <label>3 years</label>
            </value>
            <value>
                <fullName>5 years</fullName>
                <default>false</default>
                <label>5 years</label>
            </value>
            <value>
                <fullName>Lifetime</fullName>
                <default>false</default>
                <label>Lifetime</label>
            </value>
            <value>
                <fullName>Remediation</fullName>
                <default>false</default>
                <label>Remediation</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
