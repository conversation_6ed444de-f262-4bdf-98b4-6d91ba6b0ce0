<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_to_Final_Collection</name>
        <label>Assign to Final Collection</label>
        <locationX>792</locationX>
        <locationY>497</locationY>
        <assignmentItems>
            <assignToReference>AttendeeFinalCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>SingleAttendee</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Attendees</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Values</name>
        <label>Assign Values</label>
        <locationX>800</locationX>
        <locationY>197</locationY>
        <assignmentItems>
            <assignToReference>SingleAttendee.Customer_Status_at_Event__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Attendees.Account__r.Account_Status__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>SingleAttendee.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Attendees.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_to_Final_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Check_Attendee_Count</name>
        <label>Check Attendee Count</label>
        <locationX>297</locationX>
        <locationY>343</locationY>
        <assignmentItems>
            <assignToReference>AttendeeCounter</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_All_Customer_Attendees</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Attendee_Counter_Checker</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Attendee_Counter_Checker</name>
        <label>Attendee Counter Checker</label>
        <locationX>441</locationX>
        <locationY>198</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Attendee_Counter_is_Greater_than_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>AttendeeCounter</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Attendees</targetReference>
            </connector>
            <label>Attendee Counter is Greater than 0</label>
        </rules>
    </decisions>
    <description>- Updates the Customer Status at the Event field on the day of the campaign</description>
    <interviewLabel>Attendees: Update Customer Status at the Event Field {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Attendees: Update Customer Status at the Event Field</label>
    <loops>
        <name>Loop_Attendees</name>
        <label>Loop Attendees</label>
        <locationX>603</locationX>
        <locationY>191</locationY>
        <collectionReference>Get_All_Customer_Attendees</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Values</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Attendees</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Customer_Attendee_Record_Type</name>
        <label>Customer Attendee Record Type</label>
        <locationX>176</locationX>
        <locationY>192</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_All_Customer_Attendees</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attendee__c</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Customer_Attendee</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_All_Customer_Attendees</name>
        <label>Get All Customer Attendees</label>
        <locationX>301</locationX>
        <locationY>192</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Attendee_Count</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CampaignId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Customer_Attendee_Record_Type.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <sortField>Account__c</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Attendees</name>
        <label>Update Attendees</label>
        <locationX>599</locationX>
        <locationY>502</locationY>
        <inputReference>AttendeeFinalCollection</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>Customer_Attendee_Record_Type</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>AttendeeCounter</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>AttendeeFinalCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
    <variables>
        <name>CampaignId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>SingleAttendee</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
</Flow>
