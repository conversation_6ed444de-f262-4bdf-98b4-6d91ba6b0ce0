<!--  --><!--
  @description       : 
  <AUTHOR> <PERSON><PERSON>X<PERSON>
  @group             : 
  @last modified on  : 05-29-2025
  @last modified by  : jam<PERSON><PERSON><PERSON>@gmail.com
-->

<template>
  <template if:true={spinner}>
    <lightning-spinner></lightning-spinner>
  </template>

  <lightning-card title="Guest List" icon-name="standard:account">
    <div class="slds-p-around_medium tableheight">
      <lightning-datatable
        key-field="Id"
        columns={columns}
        data={guests}
        enable-infinite-loading={enableinfiniteLoading}
        onloadmore={loadMoreData}
        load-more-offset={rowLoad}
        selected-rows={preSelectedRows}
        default-sort-direction={defaultSortDirection}
        sorted-direction={sortDirection}
        sorted-by={sortedBy}
        onrowselection={getRecordId}
        onsort={onHandleSort}
        max-row-selection="999"
        show-row-number-column="true"
        hide-checkbox-column="false"
        suppress-bottom-bar="false"
      >
      </lightning-datatable>
    </div>

    {loadMoreStatus}
  </lightning-card>
</template>