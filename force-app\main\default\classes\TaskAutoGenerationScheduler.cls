/**
 * @description       : Scheduler for TaskAutoGenerationBatch
 * <AUTHOR> <PERSON> (8Squad)
 * @group             : 
 * @last modified on  : 05-23-2025
 * @last modified by  : jam<PERSON><PERSON><PERSON>@gmail.com
**/
public class TaskAutoGenerationScheduler implements Schedulable {
    public void execute(SchedulableContext sc) {
        TaskAutoGenerationBatch batchJob = new TaskAutoGenerationBatch();
        Database.executeBatch(batchJob);
    }
    
    // Method to schedule the job
    public static String scheduleJob() {
        // Schedule to run at midnight every day
        String cronExp = '0 0 0 * * ?';
        String jobName = 'Task Auto Generation - Daily';
        TaskAutoGenerationScheduler scheduler = new TaskAutoGenerationScheduler();
        return System.schedule(jobName, cronExp, scheduler);
    }
}