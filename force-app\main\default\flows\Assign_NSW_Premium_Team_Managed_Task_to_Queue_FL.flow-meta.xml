<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>56.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>checking if customer details of the task are valid and present</description>
        <name>Are_customer_details_present</name>
        <label>Are customer details present?</label>
        <locationX>407</locationX>
        <locationY>551</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_Customer_available_in_Get_Task_Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Task_Customer.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_Customer_Owner_NSW_Premium_Team_Managed</targetReference>
            </connector>
            <label>is Customer available in Get Task Customer</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Customer_Owner_NSW_Premium_Team_Managed</name>
        <label>Is Customer Owner NSW Premium Team Managed?</label>
        <locationX>253</locationX>
        <locationY>671</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>NSW_Premium_Team_and_Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0122y0000004HSpAAM</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Task_Customer.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0052y000003CNOPAA4</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>NSW_Premium_Queue_Details</targetReference>
            </connector>
            <label>NSW Premium Team and Task</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_task_an_Offboarding_Task</name>
        <label>Is task an Offboarding Task</label>
        <locationX>176</locationX>
        <locationY>311</locationY>
        <defaultConnector>
            <targetReference>Get_Task_Customer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decIsOffboardingTask</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Subject</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>Alert: Offboarding</stringValue>
                </rightValue>
            </conditions>
            <label>Is Offboarding Task</label>
        </rules>
    </decisions>
    <description>Flow Assigns NSW Premium Team Managed Tasks to Queue - excludes Offboarding Tasks</description>
    <environments>Default</environments>
    <interviewLabel>Assign NSW Premium Team Managed Task to Queue FL {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Assign NSW Premium Team Managed Task to Queue FL</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Get customer details of the Task</description>
        <name>Get_Task_Customer</name>
        <label>Get Task Customer</label>
        <locationX>407</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Are_customer_details_present</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>NSW_Premium_Queue_Details</name>
        <label>NSW Premium Queue Details</label>
        <locationX>121</locationX>
        <locationY>791</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Task_Owner_to_NSW_Premium_Team</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>NSW_Premium_Team_Managed</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Task_Owner_to_NSW_Premium_Team</name>
        <label>Update Task Owner to NSW Premium Team</label>
        <locationX>121</locationX>
        <locationY>911</locationY>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>NSW_Premium_Queue_Details.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_task_an_Offboarding_Task</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
