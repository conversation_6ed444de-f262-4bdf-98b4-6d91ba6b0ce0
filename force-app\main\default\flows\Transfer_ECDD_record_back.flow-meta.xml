<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Notify_ECDD_analyst_when_re_transfered</name>
        <label>Notify ECDD analyst when re-transfered</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <actionName>Case.Notify_ECDD_Analsyt_queue_on_re_transfer_alert</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Notify_ECDD_Analsyt_queue_on_re_transfer_alert</nameSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Conditions_to_reassign_back_to_ECDD_analyst</name>
        <label>Conditions to reassign back to ECDD analyst</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Update_Case_owner_to_ECDD_Analyst</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Success</defaultConnectorLabel>
        <rules>
            <name>Validation_error</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Escalated</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Owner:Group.DeveloperName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Unassigned_Queue</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Case_Manager_Comments__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Error_Screen</targetReference>
            </connector>
            <label>Validation error</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Transfer ECDD record back {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Transfer ECDD record back</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_ECDD_Queue</name>
        <label>Get ECDD Queue</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Conditions_to_reassign_back_to_ECDD_analyst</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Unassigned_Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Case_owner_to_ECDD_Analyst</name>
        <label>Update Case owner to ECDD Analyst</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>success_screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_ECDD_Queue.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>ECDD Data Received</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Sub_Status__c</field>
            <value>
                <stringValue>Transferred back to ECDD Analyst Queue</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <screens>
        <name>Error_Screen</name>
        <label>Error Screen</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ErrorMessage</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(224, 6, 6); font-size: 20px;&quot;&gt;Please provide case manager comments before reassigning back to the ECDD Analyst.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Done</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>success_screen</name>
        <label>success screen</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Notify_ECDD_analyst_when_re_transfered</targetReference>
        </connector>
        <fields>
            <name>Success_message</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(36, 99, 18); font-size: 20px;&quot;&gt;﻿&lt;/span&gt;&lt;strong style=&quot;color: rgb(33, 115, 17); font-size: 20px;&quot;&gt;The ECDD record has been successfully transferred back to the ECDD Analyst&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(14, 15, 14); font-size: 18px;&quot;&gt;Here is the case manager&apos;s comment: &lt;/strong&gt;&lt;span style=&quot;color: rgb(14, 15, 14); font-size: 14px;&quot;&gt;{!recordId.Case_Manager_Comments__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Done</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_ECDD_Queue</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>QueueDeveloperName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Case</objectType>
    </variables>
</Flow>
