<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>email alert for big win tasks</description>
        <name>big_win_task_alert</name>
        <label>big win task alert</label>
        <locationX>50</locationX>
        <locationY>863</locationY>
        <actionName>Task.Big_Win_Task_Alert</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>Create_Bigwin_alert_Task</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Task.Big_Win_Task_Alert</nameSegment>
    </actionCalls>
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>InactiveDescription</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Customer account is not active. Task closed by System.</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Check the payout value and tier value</description>
        <name>Check_Payout_value</name>
        <label>Check Payout value</label>
        <locationX>858</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Tier_And_Payout_Value</name>
            <conditionLogic>1 AND 2 AND 3 AND (14 OR 15) AND ((4 AND 5) OR (6 AND 7) OR (8 AND 9) OR (10 AND 11) OR (12 AND 13))</conditionLogic>
            <conditions>
                <leftValueReference>$Record.API_Payout__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>00528000004OfqPAAS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>0052y000002BsoVAAS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Premium_Tier__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.API_Payout__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.BigWinAlerts__c.Tier0Amount__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Premium_Tier__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>1</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.API_Payout__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.BigWinAlerts__c.Tier1Amount__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Premium_Tier__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>2</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.API_Payout__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.BigWinAlerts__c.Tier2Amount__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Premium_Tier__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>3</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.API_Payout__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.BigWinAlerts__c.Tier3Amount__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Premium_Tier__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>4</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.API_Payout__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Setup.BigWinAlerts__c.TIer4Amount__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>SettledDate</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>SettledDate</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Yesterday</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>IsCustomerActive</targetReference>
            </connector>
            <label>Tier And Payout Value</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Customer_Owner_User_Active</name>
        <label>Is Customer Owner User Active?</label>
        <locationX>380</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>Get_Sys_Admin_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Owner.IsActive</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Bigwin_alert_Task</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Premium_service_owner</name>
        <label>is Premium service owner?</label>
        <locationX>182</locationX>
        <locationY>755</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_Premium_Service</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>0052y000000He8UAAS</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>big_win_task_alert</targetReference>
            </connector>
            <label>Not Premium Service</label>
        </rules>
    </decisions>
    <decisions>
        <name>IsCustomerActive</name>
        <label>Is Customer Active</label>
        <locationX>611</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Create_Bigwin_alert_Task_Inactive</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decIsActive</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_Customer_Owner_User_Active</targetReference>
            </connector>
            <label>Is Active</label>
        </rules>
    </decisions>
    <description>Flow creates Big Win Alert Tasks and sends email notification
Updated for SBET-137 - Update to Task - Biggest Win Last 12 Months (create for Inactive Customers)
Updated for SBET-591 - If Customer Owner is Inactive a Task is created as Closed and assigned to System Admin user
Updated for SBET-603 - Create Big Win Alert Tasks as Closed tasks (including active customers)</description>
    <environments>Default</environments>
    <formulas>
        <name>Description</name>
        <dataType>String</dataType>
        <expression>&quot;Sportsbet Account Number: &quot; &amp; {!$Record.Sports_Bet_Account_Number__c} &amp; BR() &amp; &quot; &quot; &amp; &quot;Tier: &quot; &amp; {!$Record.Premium_Tier__c} &amp; BR() &amp; &quot; &quot; &amp; &quot;Payout: &quot; &amp; &quot;$&quot; &amp; TEXT({!$Record.API_Payout__c}) &amp; BR() &amp; &quot; &quot; &amp; &quot;Settled Date:&quot; &amp; TEXT({!$Record.API_Settled_Date__c}) &amp; BR() &amp; &quot; &quot; &amp; &quot;Bet Receipt :&quot; &amp; {!$Record.API_Bet_Receipt__c}
&amp; &quot; FYI only, no outbound for this task.&quot;</expression>
    </formulas>
    <formulas>
        <name>ExpiryDate</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate} + 3</expression>
    </formulas>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>IsNew()</expression>
    </formulas>
    <formulas>
        <description>Returns Setteled Date in Date format</description>
        <name>SettledDate</name>
        <dataType>Date</dataType>
        <expression>DATEVALUE({!$Record.API_Settled_Date__c})</expression>
    </formulas>
    <formulas>
        <name>Yesterday</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 1</expression>
    </formulas>
    <interviewLabel>Create Big Win Alert Tasks {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Create Big Win Alert Tasks</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Bigwin_alert_Task</name>
        <label>Create Bigwin alert Task</label>
        <locationX>182</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>is_Premium_service_owner</targetReference>
        </connector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>ExpiryDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>Description</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI - Biggest Win Last 12 Months</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Bigwin_alert_Task_for_System_Admin</name>
        <label>Create Bigwin alert Task for System Admin</label>
        <locationX>578</locationX>
        <locationY>755</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>ExpiryDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>Description</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Sys_Admin_User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI - Biggest Win Last 12 Months</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Bigwin_alert_Task_Inactive</name>
        <label>Create Bigwin alert Task - Inactive</label>
        <locationX>842</locationX>
        <locationY>539</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>ExpiryDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI - Biggest Win Last 12 Months</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Sys_Admin_User</name>
        <label>Get Sys Admin User</label>
        <locationX>578</locationX>
        <locationY>647</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Bigwin_alert_Task_for_System_Admin</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>System Admin</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>732</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Payout_value</targetReference>
        </connector>
        <object>Account</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
