<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Routes the selected Case to the selected Queue</description>
        <name>Route_Case_to_Queue</name>
        <label>Route Case to Queue</label>
        <locationX>314</locationX>
        <locationY>1622</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <connector>
            <targetReference>Messaging_Session_Count</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Screen_Route_Work_Error</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Select_Queue_to_Reassign_to</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordsToRouteType</name>
            <value>
                <stringValue>multiple</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>Filter_Cases</elementReference>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
    </actionCalls>
    <actionCalls>
        <description>Routes the selected Case to the selected User</description>
        <name>Route_Case_to_User</name>
        <label>Route Case to User</label>
        <locationX>842</locationX>
        <locationY>1622</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <connector>
            <targetReference>Messaging_Session_Count</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_Route_Work_Error</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
            <value>
                <stringValue>0K92y000000XZHrCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
            <value>
                <stringValue>Premium Managed Routing Configuration</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Select_Queue_to_Reassign_to</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Select_Agent_to_Reassign_to</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentRequired</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordsToRouteType</name>
            <value>
                <stringValue>multiple</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>Filter_Cases</elementReference>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
    </actionCalls>
    <actionCalls>
        <description>Routes the selected Messaging Session to the selected Queue</description>
        <name>Route_Messaging_Session_to_Queue</name>
        <label>Route Messaging Session to Queue</label>
        <locationX>314</locationX>
        <locationY>2354</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <connector>
            <targetReference>Voice_Call_Count</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_Route_Work_Error</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Select_Queue_to_Reassign_to</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordsToRouteType</name>
            <value>
                <stringValue>multiple</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>Filter_Messaging_Sessions</elementReference>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
    </actionCalls>
    <actionCalls>
        <description>Routes the selected Messaging Session to the selected User</description>
        <name>Route_Messaging_Session_to_User</name>
        <label>Route Messaging Session to User</label>
        <locationX>842</locationX>
        <locationY>2354</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <connector>
            <targetReference>Voice_Call_Count</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_Route_Work_Error</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
            <value>
                <stringValue>0K92y000000XZHrCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
            <value>
                <stringValue>Premium Managed Routing Configuration</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Select_Queue_to_Reassign_to</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Select_Agent_to_Reassign_to</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentRequired</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordsToRouteType</name>
            <value>
                <stringValue>multiple</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>Filter_Messaging_Sessions</elementReference>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
    </actionCalls>
    <actionCalls>
        <description>Routes the selected Voice Call to the selected Queue</description>
        <name>Route_Voice_Call_to_Queue</name>
        <label>Route Voice Call to Queue</label>
        <locationX>314</locationX>
        <locationY>2894</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_Route_Work_Error</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Select_Queue_to_Reassign_to</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordsToRouteType</name>
            <value>
                <stringValue>multiple</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>Filter_Voice_Calls</elementReference>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
    </actionCalls>
    <actionCalls>
        <description>Routes the selected Voice Call to the selected User</description>
        <name>Route_Voice_Call_to_User</name>
        <label>Route Voice Call to User</label>
        <locationX>842</locationX>
        <locationY>2894</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_Route_Work_Error</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
            <value>
                <stringValue>0K92y000000XZHsCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
            <value>
                <stringValue>Premium Managed Voicemail Routing</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Select_Queue_to_Reassign_to</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Select_Agent_to_Reassign_to</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentRequired</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordsToRouteType</name>
            <value>
                <stringValue>multiple</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>Filter_Voice_Calls</elementReference>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assign Role Id for each record to collection</description>
        <name>Assign_Role_Id</name>
        <label>Assign Role Id</label>
        <locationX>1062</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>varRoleIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Role_Records.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Role_Records</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Count of number of records passed into the flow</description>
        <name>Count_of_Records</name>
        <label>Count of Records</label>
        <locationX>512</locationX>
        <locationY>134</locationY>
        <assignmentItems>
            <assignToReference>numberRecords</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>ids</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Record_Count</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Counts number of records in each filtered collection</description>
        <name>Count_Records</name>
        <label>Count Records</label>
        <locationX>974</locationX>
        <locationY>1298</locationY>
        <assignmentItems>
            <assignToReference>numberCases</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Filter_Cases</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>numberMessagingSessions</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Filter_Messaging_Sessions</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>numberVoiceCalls</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Filter_Voice_Calls</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Case_Count</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>choiceQueue</name>
        <choiceText>Queue</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Choice</stringValue>
        </value>
    </choices>
    <choices>
        <name>choiceUsers</name>
        <choiceText>User</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>User</stringValue>
        </value>
    </choices>
    <collectionProcessors>
        <description>Filters Case records from recordids passed into the Flow</description>
        <name>Filter_Cases</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Cases</label>
        <locationX>974</locationX>
        <locationY>974</locationY>
        <assignNextValueToReference>currentItem_Filter_Voice_Calls</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>ids</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Voice_Calls</leftValueReference>
            <operator>StartsWith</operator>
            <rightValue>
                <stringValue>500</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Filter_Messaging_Sessions</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <description>Filters Messaging Sessions records from recordids passed into the Flow</description>
        <name>Filter_Messaging_Sessions</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Messaging Sessions</label>
        <locationX>974</locationX>
        <locationY>1082</locationY>
        <assignNextValueToReference>currentItem_Filter_Voice_Calls</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>ids</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Voice_Calls</leftValueReference>
            <operator>StartsWith</operator>
            <rightValue>
                <stringValue>0Mw</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Filter_Voice_Calls</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <description>Filters Voice Call records from recordids passed into the Flow</description>
        <name>Filter_Voice_Calls</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Voice Calls</label>
        <locationX>974</locationX>
        <locationY>1190</locationY>
        <assignNextValueToReference>currentItem_Filter_Voice_Calls</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>ids</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Voice_Calls</leftValueReference>
            <operator>StartsWith</operator>
            <rightValue>
                <stringValue>0LQ</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Count_Records</targetReference>
        </connector>
    </collectionProcessors>
    <decisions>
        <description>Checks number of Case records</description>
        <name>Case_Count</name>
        <label>Case Count</label>
        <locationX>974</locationX>
        <locationY>1406</locationY>
        <defaultConnector>
            <targetReference>Messaging_Session_Count</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Cases</defaultConnectorLabel>
        <rules>
            <name>Cases</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>numberCases</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Case_Routing_Target</targetReference>
            </connector>
            <label>Cases</label>
        </rules>
    </decisions>
    <decisions>
        <description>Should this Case be routed to Queue or User</description>
        <name>Case_Routing_Target</name>
        <label>Case Routing Target</label>
        <locationX>578</locationX>
        <locationY>1514</locationY>
        <defaultConnector>
            <targetReference>Route_Case_to_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>User</defaultConnectorLabel>
        <rules>
            <name>Queue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Select_Assignment_Target</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>choiceQueue</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_Case_to_Queue</targetReference>
            </connector>
            <label>Queue</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check number of records passed into flow</description>
        <name>Check_Record_Count</name>
        <label>Check Record Count</label>
        <locationX>512</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Get_Relationship_Manager_Role</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Records</defaultConnectorLabel>
        <rules>
            <name>No_Records</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>numberRecords</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Screen_Incorrect_Record</targetReference>
            </connector>
            <label>No Records</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks number of Messaging Session records</description>
        <name>Messaging_Session_Count</name>
        <label>Messaging Session Count</label>
        <locationX>974</locationX>
        <locationY>2138</locationY>
        <defaultConnector>
            <targetReference>Voice_Call_Count</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Messaging Session</defaultConnectorLabel>
        <rules>
            <name>Messaging_Sessions</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>numberMessagingSessions</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Messaging_Session_Routing_Target</targetReference>
            </connector>
            <label>Messaging Sessions</label>
        </rules>
    </decisions>
    <decisions>
        <description>Should this Messaging Session be routed to Queue or User</description>
        <name>Messaging_Session_Routing_Target</name>
        <label>Messaging Session Routing Target</label>
        <locationX>578</locationX>
        <locationY>2246</locationY>
        <defaultConnector>
            <targetReference>Route_Messaging_Session_to_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>User</defaultConnectorLabel>
        <rules>
            <name>Queue_MS</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Select_Assignment_Target</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>choiceQueue</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_Messaging_Session_to_Queue</targetReference>
            </connector>
            <label>Queue</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks number of Voice Call records</description>
        <name>Voice_Call_Count</name>
        <label>Voice Call Count</label>
        <locationX>974</locationX>
        <locationY>2678</locationY>
        <defaultConnectorLabel>No Voice Calls</defaultConnectorLabel>
        <rules>
            <name>Voice_Calls</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>numberVoiceCalls</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Voice_Call_Routing_Target</targetReference>
            </connector>
            <label>Voice Calls</label>
        </rules>
    </decisions>
    <decisions>
        <description>Should this Voice Call be routed to Queue or User</description>
        <name>Voice_Call_Routing_Target</name>
        <label>Voice Call Routing Target</label>
        <locationX>578</locationX>
        <locationY>2786</locationY>
        <defaultConnector>
            <targetReference>Route_Voice_Call_to_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>User</defaultConnectorLabel>
        <rules>
            <name>Queue_VC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Select_Assignment_Target</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>choiceQueue</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_Voice_Call_to_Queue</targetReference>
            </connector>
            <label>Queue</label>
        </rules>
    </decisions>
    <description>Screen Flow action used on the Omni Supervisor page for managers to reassign a single selected work item to another queue or user</description>
    <dynamicChoiceSets>
        <name>QueueList</name>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <filterLogic>1 AND 2 AND NOT(3)</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>Name</field>
            <operator>Contains</operator>
            <value>
                <stringValue>Premium Managed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Name</field>
            <operator>Contains</operator>
            <value>
                <stringValue>Voice</stringValue>
            </value>
        </filters>
        <object>Group</object>
        <sortField>Name</sortField>
        <sortOrder>Asc</sortOrder>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <description>Collection of Records</description>
        <name>varUsers</name>
        <collectionReference>Get_Users</collectionReference>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <object>User</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>Omni Supervisor: Screen Flow - Reassign Work Item {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Omni Supervisor: Screen Flow - Reassign Work Item</label>
    <loops>
        <description>Loop through Role records to get Id to assign to text variable</description>
        <name>Loop_Role_Records</name>
        <label>Loop Role Records</label>
        <locationX>974</locationX>
        <locationY>458</locationY>
        <collectionReference>Get_Relationship_Manager_Role</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Role_Id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Users</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <description>Get Id for Relationship Manager Role</description>
        <name>Get_Relationship_Manager_Role</name>
        <label>Get Relationship Manager Role</label>
        <locationX>974</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Role_Records</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Name</field>
            <operator>Contains</operator>
            <value>
                <stringValue>Relationship Manager</stringValue>
            </value>
        </filters>
        <filters>
            <field>Name</field>
            <operator>Contains</operator>
            <value>
                <stringValue>Premium Team Manager</stringValue>
            </value>
        </filters>
        <filters>
            <field>Name</field>
            <operator>Contains</operator>
            <value>
                <stringValue>Premium State Manager</stringValue>
            </value>
        </filters>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium Events &amp; Experiences</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>UserRole</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets Users that have a Role Id in the collection</description>
        <name>Get_Users</name>
        <label>Get Users</label>
        <locationX>974</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Screen_Reassign_Work</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>UserRoleId</field>
            <operator>In</operator>
            <value>
                <elementReference>varRoleIds</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <sortField>FirstName</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <description>Displays message when no records or more than 1 record is passed into flow</description>
        <name>Screen_Incorrect_Record</name>
        <label>Screen Incorrect Record</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displaytextError</name>
            <fieldText>&lt;p&gt;{!texttemplateErrorSLDS}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 12px;&quot;&gt;Error&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>displaytextNoRecords</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;﻿&lt;/strong&gt;No records were selected. Please select at least &lt;strong&gt;1 record&lt;/strong&gt; and try again&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen to select Queue or User to reassign work item to</description>
        <name>Screen_Reassign_Work</name>
        <label>Screen Reassign Work</label>
        <locationX>974</locationX>
        <locationY>866</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Filter_Cases</targetReference>
        </connector>
        <fields>
            <name>Select_Assignment_Target</name>
            <choiceReferences>choiceQueue</choiceReferences>
            <choiceReferences>choiceUsers</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Select Assignment Target</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Select_Queue_to_Reassign_to</name>
            <choiceReferences>QueueList</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Select Queue to Reassign to</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Select_Assignment_Target</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>choiceQueue</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Select_Agent_to_Reassign_to</name>
            <choiceReferences>varUsers</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Select Agent to Reassign to</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Select_Assignment_Target</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>choiceUsers</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen to handle errors from Route Work element</description>
        <name>Screen_Route_Work_Error</name>
        <label>Screen Route Work Error</label>
        <locationX>578</locationX>
        <locationY>1730</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displaytextRouteWorkError</name>
            <fieldText>&lt;p&gt;{!texttemplateErrorSLDS}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;ERROR&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>displaytextRouteWorkErrorMessage</name>
            <fieldText>&lt;p&gt;An error occur when routing this work item, please try again.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;The selected work item type is correct for the queue e.g. Voice Call record must go into a Voice queue&lt;/li&gt;&lt;li&gt;The selected queue has user online and available to accept the work item&lt;/li&gt;&lt;li&gt;The selected user is online and available to accept work item&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;If the problem persists please contact your System Administrator with the message below&lt;/p&gt;&lt;p&gt;Flow Date: {!$Flow.CurrentDateTime}&lt;/p&gt;&lt;p&gt;Flow Error: {!$Flow.FaultMessage}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Retry</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>386</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Count_of_Records</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>Stores the SLDS div class to render error notifications in display text</description>
        <name>texttemplateErrorSLDS</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-scoped-notification slds-theme_warning&quot; role=&quot;error&quot;&gt;</text>
    </textTemplates>
    <variables>
        <name>currentItem_Filter_Voice_Calls</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Collection of recordids passed from Omni Supervisor page</description>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>numberCases</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>numberMessagingSessions</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <description>Stores the number of records</description>
        <name>numberRecords</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>numberVoiceCalls</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>recordid</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores Ids of Roles</description>
        <name>varRoleIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
