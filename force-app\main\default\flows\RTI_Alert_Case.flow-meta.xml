<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Check_Owner_Name</name>
        <label>Check Owner Name</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Integration_Premium</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account.Owner.Id</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>00528000004OfqPAAS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0052y000000He8UAAS</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Closed</targetReference>
            </connector>
            <label>Integration &amp; Premium Service</label>
        </rules>
    </decisions>
    <description>Email-to-Case auto populates Case Owner Assignment 
Check ownership change notification removed</description>
    <environments>Default</environments>
    <interviewLabel>RTI Case Alert {!$Flow.CurrentDateTime}</interviewLabel>
    <label>RTI Alert Case</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Closed</name>
        <label>Closed</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>Closed_Reason__c</field>
            <value>
                <stringValue>Non Managed Customer</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Closure_Comments__c</field>
            <value>
                <stringValue>Closed By System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.Account.Owner.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Owner_Name</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>0122y000000GqZuAAK</stringValue>
            </value>
        </filters>
        <filters>
            <field>Origin</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>RTI Alert</stringValue>
            </value>
        </filters>
        <filters>
            <field>Case_Name__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>RTI Premium</stringValue>
            </value>
        </filters>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>QueueDevName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Premium_Service</stringValue>
        </value>
    </variables>
</Flow>
