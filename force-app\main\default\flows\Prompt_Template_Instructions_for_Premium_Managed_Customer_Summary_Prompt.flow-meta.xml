<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Sends a summary Attendee Last 2 Weeks to Prompt Template</description>
        <name>Attendee_Last_2_Weeks_to_Prompt_Template</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Attendee Last 2 Weeks to Prompt Template</label>
        <locationX>162</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Provide the name of any Events the customer attended in the last 2 weeks using {!Loop_Attendee_Last_2_Weeks.Campaign__r.Name} as well as the customers Attendee status for that particular event using {!Loop_Attendee_Last_2_Weeks.Status__c}
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Attendee_Last_2_Weeks</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sends a summary Attendee Next 2 Weeks to Prompt Template</description>
        <name>Attendee_Next_2_Weeks_to_Prompt_Template</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Attendee Next 2 Weeks to Prompt Template</label>
        <locationX>162</locationX>
        <locationY>1058</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Provide the name of any Events the customer attended in the last 2 weeks using {!Loop_Attendee_Next_2_Weeks.Campaign__r.Name} as well as the customers Attendee status for that particular event using{!Loop_Attendee_Next_2_Weeks.Status__c}
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Attendee_Next_2_Weeks</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sends message that there was a fault in the flow and it couldn&apos;t find the records</description>
        <name>Flow_Fault_Prompt_Instructions</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Flow Fault Prompt Instructions</label>
        <locationX>578</locationX>
        <locationY>1442</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>There was a fault in the flow when retrieving records
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>No_Open_Tasks</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sends message that no Attendee Last 2 Weeks were found to Prompt Template</description>
        <name>No_Attendee_Last_2_Weeks</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>No Attendee Last 2 Weeks</label>
        <locationX>290</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Customers attended no events in the last 2 weeks
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Attendee_Records_Next_2_Weeks</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sends message that no Attendee Next 2 Weeks were found to Prompt Template</description>
        <name>No_Attendee_Next_2_Weeks</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>No Attendee Next 2 Weeks</label>
        <locationX>290</locationX>
        <locationY>950</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Customers is not planning on attending any events in the next 2 weeks
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Customer_Open_Tasks</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sends message that no Tasks were found to Prompt Template</description>
        <name>No_Open_Tasks</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>No Open Tasks</label>
        <locationX>314</locationX>
        <locationY>1550</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Customers had no interactions in the last 90 days
</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sends a summary of the customers open Tasks to Prompt Template</description>
        <name>Send_Open_Tasks_to_Prompt_Template</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Send Open Tasks to Prompt Template</label>
        <locationX>138</locationX>
        <locationY>1658</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Provide the total number of the customers Open Tasks using {!Get_Customer_Open_Tasks} and a short summary of the subject of those tasks using{!Loop_Open_Tasks.Subject}
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Open_Tasks</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Are there any Attendee records of the customer for the last 2 Weeks</description>
        <name>Attendee_Records_Last_2_Weeks_Found</name>
        <label>Attendee Records Last 2 Weeks Found?</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>No_Attendee_Last_2_Weeks</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Attendance</defaultConnectorLabel>
        <rules>
            <name>Attended_Last_2_Weeks</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Attendee_Records_Last_2_Weeks</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Attendee_Records_Last_2_Weeks</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Attendee_Last_2_Weeks</targetReference>
            </connector>
            <label>Attended Last 2 Weeks</label>
        </rules>
    </decisions>
    <decisions>
        <description>Are there any Attendee records of the customer for the next 2 Weeks</description>
        <name>Copy_1_of_Attendee_Records_Next_2_Weeks_Found</name>
        <label>Attendee Records Next 2 Weeks Found?</label>
        <locationX>182</locationX>
        <locationY>842</locationY>
        <defaultConnector>
            <targetReference>No_Attendee_Next_2_Weeks</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Attendance</defaultConnectorLabel>
        <rules>
            <name>Attended_Next_2_Weeks</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Attendee_Records_Next_2_Weeks</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Attendee_Records_Next_2_Weeks</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Attendee_Next_2_Weeks</targetReference>
            </connector>
            <label>Attended Next 2 Weeks</label>
        </rules>
    </decisions>
    <decisions>
        <description>Are there any open tasks?</description>
        <name>Open_Tasks_Found</name>
        <label>Open Tasks Found?</label>
        <locationX>182</locationX>
        <locationY>1442</locationY>
        <defaultConnector>
            <targetReference>No_Open_Tasks</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Tasks</defaultConnectorLabel>
        <rules>
            <name>Open_Tasks</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Open_Tasks</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Customer_Open_Tasks</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Open_Tasks</targetReference>
            </connector>
            <label>Open Tasks</label>
        </rules>
    </decisions>
    <description>Used by Customer Summary Prompt Template to generate additional prompt instructions from the customers Task and Attendee records</description>
    <environments>Default</environments>
    <formulas>
        <description>Stores date 14 days ago</description>
        <name>formulaDateLast14Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 14</expression>
    </formulas>
    <formulas>
        <description>Stores date 365 days ago</description>
        <name>formulaDateLast365Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 365</expression>
    </formulas>
    <formulas>
        <description>Stores date 14 days in future</description>
        <name>formulaDateNext14Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() + 14</expression>
    </formulas>
    <formulas>
        <description>Stores value for today&apos;s date</description>
        <name>formulaDateToday</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <interviewLabel>Prompt Template: Instructions for Premium Managed Customer Summary Prompt {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Prompt Template: Instructions for Premium Managed Customer Summary Prompt</label>
    <loops>
        <description>Loops through Attendee records for last 2 weeks</description>
        <name>Loop_Attendee_Last_2_Weeks</name>
        <label>Loop Attendee Last 2 Weeks</label>
        <locationX>74</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_Attendee_Records_Last_2_Weeks</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Attendee_Last_2_Weeks_to_Prompt_Template</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Attendee_Records_Next_2_Weeks</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops through Attendee records for next 2 weeks</description>
        <name>Loop_Attendee_Next_2_Weeks</name>
        <label>Loop Attendee Next 2 Weeks</label>
        <locationX>74</locationX>
        <locationY>950</locationY>
        <collectionReference>Get_Attendee_Records_Next_2_Weeks</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Attendee_Next_2_Weeks_to_Prompt_Template</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Customer_Open_Tasks</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loop through all Open customers Task records</description>
        <name>Loop_Open_Tasks</name>
        <label>Loop Open Tasks</label>
        <locationX>50</locationX>
        <locationY>1550</locationY>
        <collectionReference>Get_Customer_Open_Tasks</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Send_Open_Tasks_to_Prompt_Template</targetReference>
        </nextValueConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>PromptFlow</processType>
    <recordLookups>
        <description>Gets the Attendee records of the customer for the last 2 Weeks with specific statuses</description>
        <name>Get_Attendee_Records_Last_2_Weeks</name>
        <label>Get Attendee Records Last 2 Weeks</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Attendee_Records_Last_2_Weeks_Found</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Fault_Prompt_Instructions</targetReference>
        </faultConnector>
        <filterLogic>1 AND 2 AND (3 OR 4)</filterLogic>
        <filters>
            <field>Campaign_Date__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>formulaDateLast14Days</elementReference>
            </value>
        </filters>
        <filters>
            <field>Campaign_Date__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>formulaDateToday</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attended</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Attendee records of the customer for the next 2 Weeks with specific statuses</description>
        <name>Get_Attendee_Records_Next_2_Weeks</name>
        <label>Get Attendee Records Next 2 Weeks</label>
        <locationX>182</locationX>
        <locationY>734</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_1_of_Attendee_Records_Next_2_Weeks_Found</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Fault_Prompt_Instructions</targetReference>
        </faultConnector>
        <filterLogic>1 AND 2 AND (3 OR 4 OR 5)</filterLogic>
        <filters>
            <field>Campaign_Date__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>formulaDateToday</elementReference>
            </value>
        </filters>
        <filters>
            <field>Campaign_Date__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>formulaDateNext14Days</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Pending</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Waitlisted</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets any task for the Customer that has status of Open</description>
        <name>Get_Customer_Open_Tasks</name>
        <label>Get Customer Open Tasks</label>
        <locationX>182</locationX>
        <locationY>1334</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Open_Tasks_Found</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Flow_Fault_Prompt_Instructions</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhoId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Input.Customer.PersonContactId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <capabilityTypes>
            <name>FlexTemplate://Premium_Managed_Customer_Summary</name>
            <capabilityName>FlexTemplate://Premium_Managed_Customer_Summary</capabilityName>
            <inputs>
                <name>Customer</name>
                <capabilityInputName>Customer</capabilityInputName>
                <dataType>SOBJECT://Account</dataType>
                <isCollection>false</isCollection>
            </inputs>
        </capabilityTypes>
        <connector>
            <targetReference>Get_Attendee_Records_Last_2_Weeks</targetReference>
        </connector>
        <triggerType>Capability</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>currentItem_Filter_Attendee_Last_2_Weeks</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
</Flow>
