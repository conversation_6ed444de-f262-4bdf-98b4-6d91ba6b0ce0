<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Screen Flow used to surface Click-to-Dial functionality with a custom LWC launched from Customer page; LWC replaced with Aura CMP for workspaces api functionality to hopefully focus new tab.</description>
    <environments>Default</environments>
    <interviewLabel>Customer: Screen Flow Start Outbound Call {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer: Screen Flow Start Outbound Call</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <screens>
        <description>The screen has Click-to-Dial component to make outbound call</description>
        <name>Click_to_Dial_Screen</name>
        <label>Click-to-Dial Screen</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displaytextOnline</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;You must be logged in to Omnichannel and &lt;strong&gt;Available for Voice&lt;/strong&gt; to make an outbound call&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Click_to_Dial_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Click_to_Dial_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Click_to_Dial_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>clickToDial</name>
                    <extensionName>c:clickToDialTabFocus</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>recordId</name>
                        <value>
                            <elementReference>recordId</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Click_to_Dial_Screen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <nextOrFinishButtonLabel>Go To Record</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Click_to_Dial_Screen</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
