<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Add the looped cases to a variable.</description>
        <name>Add_Case_to_List</name>
        <label>Add Case to List</label>
        <locationX>1854</locationX>
        <locationY>974</locationY>
        <assignmentItems>
            <assignToReference>QaCasesLIst</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_through_QA_Cases.ParentId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_QA_Cases</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Add QA to Case Origin.</description>
        <name>Add_QA</name>
        <label>Add QA</label>
        <locationX>1634</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>CaseOrigin</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>QA</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_QA_Recordtype</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Add QC to Case Origin.</description>
        <name>AddQC</name>
        <label>Add QC</label>
        <locationX>1898</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>CaseOrigin</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>QC</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_QA_Recordtype</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign Cases to variable.</description>
        <name>assign_Case_to_list</name>
        <label>assign Case to list</label>
        <locationX>1656</locationX>
        <locationY>3014</locationY>
        <assignmentItems>
            <assignToReference>QACases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>QAcaseVAR</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_Cases</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Cases_to_Case_List</name>
        <label>Assign Cases to Case List</label>
        <locationX>314</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>Cases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>GetCases</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_No_Cases_to_true</name>
        <label>Assign No Cases to true</label>
        <locationX>3218</locationX>
        <locationY>1898</locationY>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign Parent Case ID, Owner ID, RecordType ID, Case Origin and Customer Name.</description>
        <name>Assign_QA_variables</name>
        <label>Assign QA variables</label>
        <locationX>1656</locationX>
        <locationY>2906</locationY>
        <assignmentItems>
            <assignToReference>QAcaseVAR.ParentId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_through_Cases.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>QAcaseVAR.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Please_select_which_Queue.selectedChoiceValues</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>QAcaseVAR.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_QA_Recordtype.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>QAcaseVAR.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>CaseOrigin</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>QAcaseVAR.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_through_Cases.AccountId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_Case_to_list</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign RecordTypes to a variable.</description>
        <name>Assign_RT_to_list</name>
        <label>Assign RT to list</label>
        <locationX>3438</locationX>
        <locationY>1490</locationY>
        <assignmentItems>
            <assignToReference>SMRrecordtypes</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Through_recordtypes.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_recordtypes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign the Cases selected in the Select Cases Screen.</description>
        <name>Assign_Selected_Cases</name>
        <label>Assign Selected Cases</label>
        <locationX>1700</locationX>
        <locationY>2606</locationY>
        <assignmentItems>
            <assignToReference>varSelectedCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Copy_1_of_CaseSelector.selectedRows</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_Cases</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign the Cases selected in the Select Cases Screen.</description>
        <name>Assign_Selected_Cases_ECDD_ORR</name>
        <label>Assign Selected Cases ECDD ORR</label>
        <locationX>1436</locationX>
        <locationY>2606</locationY>
        <assignmentItems>
            <assignToReference>varSelectedCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>CaseSelector.selectedRows</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_Cases</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Assign_Cases_to_Case_List</name>
        <label>Assign Cases to Case List</label>
        <locationX>1898</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>Cases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_LER_response</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Assign_No_Cases_to_true</name>
        <label>Assign No Cases to true</label>
        <locationX>2162</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_Assign_Cases_to_Case_List</name>
        <label>Assign Cases to Case List</label>
        <locationX>1370</locationX>
        <locationY>1706</locationY>
        <assignmentItems>
            <assignToReference>Cases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_LER_investigation_Case</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_Assign_No_Cases_to_true</name>
        <label>Assign No Cases to true</label>
        <locationX>2690</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_3_of_Assign_Cases_to_Case_List</name>
        <label>Assign Cases to Case List</label>
        <locationX>2426</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>Cases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_ORR_Case</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_3_of_Assign_No_Cases_to_true</name>
        <label>Assign No Cases to true</label>
        <locationX>1634</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_4_of_Assign_No_Cases_to_true</name>
        <label>Assign No Cases to true</label>
        <locationX>1106</locationX>
        <locationY>1706</locationY>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_5_of_Assign_Cases_to_Case_List</name>
        <label>Assign Cases to Case List</label>
        <locationX>2954</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>Cases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_Case_Management_Case</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_5_of_Assign_No_Cases_to_true</name>
        <label>Assign No Cases to true</label>
        <locationX>50</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_6_of_Assign_Cases_to_Case_List</name>
        <label>Assign Cases to Case List</label>
        <locationX>3482</locationX>
        <locationY>1898</locationY>
        <assignmentItems>
            <assignToReference>Cases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>GetSMRCases</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>CRA_Assign_Cases_to_Case_List</name>
        <label>Assign Cases to Case List</label>
        <locationX>842</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>Cases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_CRA_Cases</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>CRA_Assign_No_Cases_to_true</name>
        <label>Assign No Cases to true</label>
        <locationX>578</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>NoCases</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_cases_found</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign Case IDs of new QA Cases.</description>
        <name>Get_Create_Case_id</name>
        <label>Get Create Case id</label>
        <locationX>1656</locationX>
        <locationY>3422</locationY>
        <assignmentItems>
            <assignToReference>QACreatedCases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_through_created_cases.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_created_cases</targetReference>
        </connector>
    </assignments>
    <choices>
        <description>Choice for CRA Case.</description>
        <name>CRAInitialDeposits</name>
        <choiceText>CRA Initial Deposits</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>CRA Initial Deposits </stringValue>
        </value>
    </choices>
    <choices>
        <name>ECDDCaseManagement</name>
        <choiceText>ECDD Case Management</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Case Management </stringValue>
        </value>
    </choices>
    <choices>
        <name>ECDDOverallRR</name>
        <choiceText>ECDD Overall Risk Rating</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>ECDD Analyst </stringValue>
        </value>
    </choices>
    <choices>
        <name>LERInvestigation</name>
        <choiceText>LER Investigation</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>LER Investigation </stringValue>
        </value>
    </choices>
    <choices>
        <name>LERResponse</name>
        <choiceText>LER Response</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>LER Response </stringValue>
        </value>
    </choices>
    <choices>
        <name>SMRCases</name>
        <choiceText>SMR Cases</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>SMR Case </stringValue>
        </value>
    </choices>
    <choices>
        <name>TMAlerts</name>
        <choiceText>TM Alerts</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>TM Alert </stringValue>
        </value>
    </choices>
    <collectionProcessors>
        <description>Sort the cases on Closed Date in ascending order.</description>
        <name>Sort_Case_list_by_Closed_Date_ASC</name>
        <elementSubtype>SortCollectionProcessor</elementSubtype>
        <label>Sort Case list by Closed Date ASC</label>
        <locationX>1700</locationX>
        <locationY>2390</locationY>
        <collectionProcessorType>SortCollectionProcessor</collectionProcessorType>
        <collectionReference>Cases</collectionReference>
        <connector>
            <targetReference>Copy_1_of_Select_Cases_Screen</targetReference>
        </connector>
        <sortOptions>
            <doesPutEmptyStringAndNullFirst>false</doesPutEmptyStringAndNullFirst>
            <sortField>ClosedDate</sortField>
            <sortOrder>Asc</sortOrder>
        </sortOptions>
    </collectionProcessors>
    <collectionProcessors>
        <description>Sort the cases on Date_Analyst_Risk_Rating_Provided__c in ascending order.</description>
        <name>Sort_Case_list_by_Date_Analyst_Risk_Rating_Provided_ASC</name>
        <elementSubtype>SortCollectionProcessor</elementSubtype>
        <label>Sort Case list by Date Analyst Risk Rating Provided ASC</label>
        <locationX>1436</locationX>
        <locationY>2390</locationY>
        <collectionProcessorType>SortCollectionProcessor</collectionProcessorType>
        <collectionReference>Cases</collectionReference>
        <connector>
            <targetReference>Select_Cases_Screen</targetReference>
        </connector>
        <sortOptions>
            <doesPutEmptyStringAndNullFirst>false</doesPutEmptyStringAndNullFirst>
            <sortField>Date_Analyst_Risk_Rating_Provided__c</sortField>
            <sortOrder>Asc</sortOrder>
        </sortOptions>
    </collectionProcessors>
    <decisions>
        <name>Are_cases_found</name>
        <label>Are cases found?</label>
        <locationX>1766</locationX>
        <locationY>2174</locationY>
        <defaultConnector>
            <targetReference>No_Cases_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>NoCases</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_the_Case_Type_ECDD_ORR</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_Is_cases_null</name>
        <label>Is cases null?</label>
        <locationX>2294</locationX>
        <locationY>1490</locationY>
        <defaultConnector>
            <targetReference>Copy_3_of_Assign_Cases_to_Case_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Null</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_YesNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_ORR_Case</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Assign_No_Cases_to_true</targetReference>
            </connector>
            <label>YesNull</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_s_cases_null</name>
        <label>Is cases null?</label>
        <locationX>2822</locationX>
        <locationY>1490</locationY>
        <defaultConnector>
            <targetReference>Copy_5_of_Assign_Cases_to_Case_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Null</defaultConnectorLabel>
        <rules>
            <name>Copy_2_of_YesNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Case_Management_Case</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Assign_No_Cases_to_true</targetReference>
            </connector>
            <label>YesNull</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_2_of_s_cases_null</name>
        <label>Is cases null?</label>
        <locationX>1766</locationX>
        <locationY>1490</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_Assign_Cases_to_Case_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Null</defaultConnectorLabel>
        <rules>
            <name>Copy_3_of_YesNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_LER_response</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_3_of_Assign_No_Cases_to_true</targetReference>
            </connector>
            <label>Copy 3 of YesNull</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_3_of_s_cases_null</name>
        <label>Is cases null?</label>
        <locationX>1238</locationX>
        <locationY>1598</locationY>
        <defaultConnector>
            <targetReference>Copy_2_of_Assign_Cases_to_Case_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Null</defaultConnectorLabel>
        <rules>
            <name>Copy_4_of_YesNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_LER_investigation_Case</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_4_of_Assign_No_Cases_to_true</targetReference>
            </connector>
            <label>YesNull</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_4_of_s_cases_null</name>
        <label>Is cases null?</label>
        <locationX>182</locationX>
        <locationY>1490</locationY>
        <defaultConnector>
            <targetReference>Assign_Cases_to_Case_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Null</defaultConnectorLabel>
        <rules>
            <name>Copy_5_of_YesNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCases</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_5_of_Assign_No_Cases_to_true</targetReference>
            </connector>
            <label>Copy 5 of YesNull</label>
        </rules>
    </decisions>
    <decisions>
        <name>CRA_are_cases_null</name>
        <label>Are cases null?</label>
        <locationX>710</locationX>
        <locationY>1490</locationY>
        <defaultConnector>
            <targetReference>CRA_Assign_Cases_to_Case_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Null</defaultConnectorLabel>
        <rules>
            <name>CRAYesNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_CRA_Cases</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CRA_Assign_No_Cases_to_true</targetReference>
            </connector>
            <label>YesNull</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_cases_null</name>
        <label>Is cases null?</label>
        <locationX>3350</locationX>
        <locationY>1790</locationY>
        <defaultConnector>
            <targetReference>Copy_6_of_Assign_Cases_to_Case_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Null</defaultConnectorLabel>
        <rules>
            <name>YesNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetSMRCases</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_No_Cases_to_true</targetReference>
            </connector>
            <label>YesNull</label>
        </rules>
    </decisions>
    <decisions>
        <description>Did the user select QA or QC?</description>
        <name>Is_QA_or_QC</name>
        <label>Is QA or QC</label>
        <locationX>1766</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>AddQC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is QC</defaultConnectorLabel>
        <rules>
            <name>Outcome_1_of_Decision_9</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Please_select_which_Queue.selectedChoiceLabels</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>QA queue</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_QA</targetReference>
            </connector>
            <label>IS For QA</label>
        </rules>
    </decisions>
    <decisions>
        <description>For ECDD ORR there are different columns to be shown in the Select Cases Screen.</description>
        <name>Is_the_Case_Type_ECDD_ORR</name>
        <label>Is the Case Type ECDD ORR?</label>
        <locationX>1568</locationX>
        <locationY>2282</locationY>
        <defaultConnector>
            <targetReference>Sort_Case_list_by_Closed_Date_ASC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_ECDD_ORR</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Please_select_what_type_of_QA_Cases_you_would_like_to_create</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>ECDDOverallRR</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Sort_Case_list_by_Date_Analyst_Risk_Rating_Provided_ASC</targetReference>
            </connector>
            <label>Yes ECDD ORR</label>
        </rules>
    </decisions>
    <decisions>
        <description>Filter on the choice labels selected by the user.</description>
        <name>What_type_of_case</name>
        <label>What type of case</label>
        <locationX>1766</locationX>
        <locationY>1166</locationY>
        <defaultConnector>
            <targetReference>Get_Recordtypes</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>IS SMR</defaultConnectorLabel>
        <rules>
            <name>ISTM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>TMAlerts</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Recordtype</targetReference>
            </connector>
            <label>ISTM</label>
        </rules>
        <rules>
            <name>Is_CRA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CRAInitialDeposits</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_CRA_Recordtype</targetReference>
            </connector>
            <label>Is CRA</label>
        </rules>
        <rules>
            <name>IS_LER_Investigation</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>LERInvestigation</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Get_QA_Cases</targetReference>
            </connector>
            <label>IS LER Investigation</label>
        </rules>
        <rules>
            <name>IS_LER_Response</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>LERResponse</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Get_Recordtype</targetReference>
            </connector>
            <label>IS LER Response</label>
        </rules>
        <rules>
            <name>Is_ECDD_ORR</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ECDDOverallRR</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_3_of_Get_Recordtype</targetReference>
            </connector>
            <label>Is ECDD ORR</label>
        </rules>
        <rules>
            <name>Is_ECDD_Case_Management</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ECDDCaseManagement</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_4_of_Get_Recordtype</targetReference>
            </connector>
            <label>Is ECDD Case Management</label>
        </rules>
    </decisions>
    <description>Flow for &quot;Create QA Cases&quot; utility bar button:
- Allows QA Analysts to select Case Origin, Queue Type, and define a custom date range for bulk creation of QA Cases.</description>
    <dynamicChoiceSets>
        <name>QueueChoice</name>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <filterLogic>1 AND (2 OR 3)</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>QC_queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>QA_queue</stringValue>
            </value>
        </filters>
        <limit>2</limit>
        <object>Group</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>Bulk Create QA cases {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Bulk Create QA cases</label>
    <loops>
        <description>Loop through the selected cases in the Select Cases Screen.</description>
        <name>Loop_through_Cases</name>
        <label>Loop through Cases</label>
        <locationX>1568</locationX>
        <locationY>2798</locationY>
        <collectionReference>varSelectedCases</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_QA_variables</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Create_Cases</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loop through newly created QA Cases.</description>
        <name>Loop_through_created_cases</name>
        <label>Loop through created cases</label>
        <locationX>1568</locationX>
        <locationY>3314</locationY>
        <collectionReference>QACases</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Get_Create_Case_id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>GetCompletedCase</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loop through the Get QA Cases records.</description>
        <name>Loop_through_QA_Cases</name>
        <label>Loop through QA Cases</label>
        <locationX>1766</locationX>
        <locationY>866</locationY>
        <collectionReference>Get_QA_Cases</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_Case_to_List</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>What_type_of_case</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loop through SMR RecordTypes i.e Standard SMR &amp; Fast Track SMR.</description>
        <name>Loop_Through_recordtypes</name>
        <label>Loop Through recordtypes</label>
        <locationX>3350</locationX>
        <locationY>1382</locationY>
        <collectionReference>Get_Recordtypes</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_RT_to_list</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>GetSMRCases</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Create the QA Cases for the selected cases.</description>
        <name>Create_Cases</name>
        <label>Create Cases</label>
        <locationX>1568</locationX>
        <locationY>3206</locationY>
        <connector>
            <targetReference>Loop_through_created_cases</targetReference>
        </connector>
        <inputReference>QACases</inputReference>
    </recordCreates>
    <recordLookups>
        <description>Get Non TM Cases with LER Case Origin.</description>
        <name>Copy_1_of_Get_QA_Cases</name>
        <label>Get QA Cases</label>
        <locationX>1238</locationX>
        <locationY>1274</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_1_of_Get_Recordtype</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_QA_Recordtype.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Origin</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CaseOrigin</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Non Transaction Monitoring RecordType.</description>
        <name>Copy_1_of_Get_Recordtype</name>
        <label>Get Recordtype</label>
        <locationX>1238</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_LER_investigation_Case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Non_Transaction_Monitoring</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>recordtypeIdVAR</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get Non Transaction Monitoring RecordType.</description>
        <name>Copy_2_of_Get_Recordtype</name>
        <label>Get Recordtype</label>
        <locationX>1766</locationX>
        <locationY>1274</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_LER_response</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Non_Transaction_Monitoring</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>recordtypeIdVAR</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get ECDD RecordType Cases.</description>
        <name>Copy_3_of_Get_Recordtype</name>
        <label>Get Recordtype</label>
        <locationX>2294</locationX>
        <locationY>1274</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_ORR_Case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Case</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>recordtypeIdVAR</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get QA/QC RecordType Cases.</description>
        <name>Copy_4_of_Get_Recordtype</name>
        <label>Get Recordtype</label>
        <locationX>2822</locationX>
        <locationY>1274</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Case_Management_Case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>QA_QC</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>recordtypeIdVAR</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get QA/QC RecordType Cases with ECDD Analyst QA Case Origin and Closed date should be between the date range the user selects in the first screen.</description>
        <name>Get_Case_Management_Case</name>
        <label>Get Case Management Case</label>
        <locationX>2822</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_1_of_s_cases_null</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordtypeIdVAR</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotIn</operator>
            <value>
                <elementReference>QaCasesLIst</elementReference>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>Range_Start_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>Range_End_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Origin</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD Analyst QA</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Cases with CRA RecordType where the case is closed and the closed date is between the date range the user selects in the first screen.</description>
        <name>Get_CRA_Cases</name>
        <label>Get CRA Cases</label>
        <locationX>710</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CRA_are_cases_null</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordtypeIdVAR</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsClosed</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>Range_Start_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotIn</operator>
            <value>
                <elementReference>QaCasesLIst</elementReference>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>Range_End_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Origin</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CRA Initial Deposits</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Customer Risk Assessment Record Type.</description>
        <name>Get_CRA_Recordtype</name>
        <label>Get CRA Recordtype</label>
        <locationX>710</locationX>
        <locationY>1274</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_CRA_Cases</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Customer_Risk_Assessment</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>recordtypeIdVAR</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get Non-TM RecordType Cases with LER Case Origin and LER Investigation date should be between the date range the user selects in the first screen.</description>
        <name>Get_LER_investigation_Case</name>
        <label>Get LER investigation Case</label>
        <locationX>1238</locationX>
        <locationY>1490</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_3_of_s_cases_null</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordtypeIdVAR</elementReference>
            </value>
        </filters>
        <filters>
            <field>LER_Investigation_Completed__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>Range_Start_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotIn</operator>
            <value>
                <elementReference>QaCasesLIst</elementReference>
            </value>
        </filters>
        <filters>
            <field>LER_Investigation_Completed__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>Range_End_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Origin</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>LER</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Non-TM RecordType Cases with LER Case Origin and Case Closed date should be between the date range the user selects in the first screen.</description>
        <name>Get_LER_response</name>
        <label>Get LER response</label>
        <locationX>1766</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_2_of_s_cases_null</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordtypeIdVAR</elementReference>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>Range_Start_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotIn</operator>
            <value>
                <elementReference>QaCasesLIst</elementReference>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>Range_End_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Origin</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>LER</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get ECDD RecordType Cases and the Date Analyst Risk Rating Provided should be between the date range the user selects on the first screen.</description>
        <name>Get_ORR_Case</name>
        <label>Get ORR Case</label>
        <locationX>2294</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_1_of_Is_cases_null</targetReference>
        </connector>
        <filterLogic>1 AND 2 AND 3 AND 4</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordtypeIdVAR</elementReference>
            </value>
        </filters>
        <filters>
            <field>Date_Analyst_Risk_Rating_Provided__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>Range_Start_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotIn</operator>
            <value>
                <elementReference>QaCasesLIst</elementReference>
            </value>
        </filters>
        <filters>
            <field>Date_Analyst_Risk_Rating_Provided__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>Range_End_Date</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get QA Cases where RecordType = QA/QC and the case origin selected by the user.</description>
        <name>Get_QA_Cases</name>
        <label>Get QA Cases</label>
        <locationX>1766</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_through_QA_Cases</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_QA_Recordtype.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Origin</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CaseOrigin</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get QA/QC RecordType.</description>
        <name>Get_QA_Recordtype</name>
        <label>Get QA Recordtype</label>
        <locationX>1766</locationX>
        <locationY>650</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_QA_Cases</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>QA_QC</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Transaction Monitoring Record Type.</description>
        <name>Get_Recordtype</name>
        <label>Get Recordtype</label>
        <locationX>182</locationX>
        <locationY>1274</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetCases</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Transaction_Monitoring</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>recordtypeIdVAR</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get SMR RecordType Cases.</description>
        <name>Get_Recordtypes</name>
        <label>Get Recordtypes</label>
        <locationX>3350</locationX>
        <locationY>1274</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Through_recordtypes</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>Contains</operator>
            <value>
                <stringValue>SMR</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Cases with TM RecordType where the case is closed and the closed date is between the date range the user selects in the first screen.</description>
        <name>GetCases</name>
        <label>GetCases</label>
        <locationX>182</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_4_of_s_cases_null</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordtypeIdVAR</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsClosed</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>Range_Start_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotIn</operator>
            <value>
                <elementReference>QaCasesLIst</elementReference>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>Range_End_Date</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get newly created QA cases and display them to the user.</description>
        <name>GetCompletedCase</name>
        <label>Get Completed Cases</label>
        <locationX>1568</locationX>
        <locationY>3614</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Confirmation_Screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>QACreatedCases</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get SMR RecordType Cases where the Closed date is between the date range the user selects in the first screen.</description>
        <name>GetSMRCases</name>
        <label>Get SMR Cases</label>
        <locationX>3350</locationX>
        <locationY>1682</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_cases_null</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>In</operator>
            <value>
                <elementReference>SMRrecordtypes</elementReference>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>Range_Start_Date</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotIn</operator>
            <value>
                <elementReference>QaCasesLIst</elementReference>
            </value>
        </filters>
        <filters>
            <field>ClosedDate</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>Range_End_Date</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get QA and QC Queues from Group Object.</description>
        <name>qa_Analyst_Queue</name>
        <label>QA Analyst Queue</label>
        <locationX>1766</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>QASelection</targetReference>
        </connector>
        <filterLogic>1 AND (2 OR 3)</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>QA_queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>QC_queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <description>Display newly created QA Cases to the user.</description>
        <name>Confirmation_Screen</name>
        <label>Confirmation Screen</label>
        <locationX>1568</locationX>
        <locationY>3722</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>CompletedCasesTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>The below cases have successfully been created!</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>GetCompletedCase</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Case_Number_Hyperlink__c&quot;,&quot;guid&quot;:&quot;column-82b1&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Case Number Hyperlink&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Origin&quot;,&quot;guid&quot;:&quot;column-7727&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Case Origin&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Select the cases you want to create QA cases for.</description>
        <name>Copy_1_of_Select_Cases_Screen</name>
        <label>Select Cases Screen</label>
        <locationX>1700</locationX>
        <locationY>2498</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_Selected_Cases</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_CaseSelector</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Please select Cases that you would like to QA</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Cases</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Case_Number_Hyperlink__c&quot;,&quot;guid&quot;:&quot;column-054d&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Case Number Hyperlink&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Status&quot;,&quot;guid&quot;:&quot;column-efcd&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Record_Type_text__c&quot;,&quot;guid&quot;:&quot;column-84bf&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Record Type&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Record Type text&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;ClosedDate&quot;,&quot;guid&quot;:&quot;column-c224&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Closed Date&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Closed Date&quot;,&quot;type&quot;:&quot;customDateTime&quot;},{&quot;apiName&quot;:&quot;Origin&quot;,&quot;guid&quot;:&quot;column-8c37&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Case Origin&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Case_Owner_1__c&quot;,&quot;guid&quot;:&quot;column-86a0&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Owner&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;Case Owner 1&quot;,&quot;type&quot;:&quot;customRichText&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Create QA Cases</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>No case found.</description>
        <name>No_Cases_Screen</name>
        <label>No Cases Screen</label>
        <locationX>1964</locationX>
        <locationY>2282</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NoCasesScreenDisplayText</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;There were no cases that matched  this criteria. Please change criteria and try again.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Change Criteria</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>First Screen to select the type of cases to do QA on.</description>
        <name>QASelection</name>
        <label>QASelection</label>
        <locationX>1766</locationX>
        <locationY>242</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Is_QA_or_QC</targetReference>
        </connector>
        <fields>
            <name>Select_what_type_of_QA_cases_you_would_like_to_create</name>
            <fieldText>Select what type of QA cases you would like to create:</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Select_what_type_of_QA_cases_you_would_like_to_create_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Please_select_what_type_of_QA_Cases_you_would_like_to_create</name>
                    <choiceReferences>TMAlerts</choiceReferences>
                    <choiceReferences>LERInvestigation</choiceReferences>
                    <choiceReferences>LERResponse</choiceReferences>
                    <choiceReferences>ECDDOverallRR</choiceReferences>
                    <choiceReferences>ECDDCaseManagement</choiceReferences>
                    <choiceReferences>SMRCases</choiceReferences>
                    <choiceReferences>CRAInitialDeposits</choiceReferences>
                    <dataType>String</dataType>
                    <defaultSelectedChoiceReference>TMAlerts</defaultSelectedChoiceReference>
                    <fieldText>Types</fieldText>
                    <fieldType>RadioButtons</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Select_what_type_of_QA_cases_you_would_like_to_create_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Please_select_which_Queue</name>
                    <choiceReferences>QueueChoice</choiceReferences>
                    <extensionName>flowruntime:choiceLookup</extensionName>
                    <fieldText>Please select which Queue (QA or QC queue)</fieldText>
                    <fieldType>ComponentChoice</fieldType>
                    <inputParameters>
                        <name>placeholder</name>
                        <value>
                            <stringValue>QA or QC queue</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Date_Range</name>
            <fieldText>Select the Date range below.</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Date_Range_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Range_Start_Date</name>
                    <dataType>Date</dataType>
                    <fieldText>Range: Start Date</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <validationRule>
                        <errorMessage>&lt;p&gt;Start Date cannot be later than End Date.&lt;/p&gt;</errorMessage>
                        <formulaExpression>{!Range_End_Date} &gt; {!Range_Start_Date}</formulaExpression>
                    </validationRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Date_Range_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Range_End_Date</name>
                    <dataType>Date</dataType>
                    <fieldText>Range: End Date</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <validationRule>
                        <errorMessage>&lt;p&gt;End Date cannot be before the Start Date.&lt;/p&gt;</errorMessage>
                        <formulaExpression>{!Range_End_Date} &gt; {!Range_Start_Date}</formulaExpression>
                    </validationRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Guide_info_text</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Guidance for QA:&lt;/strong&gt;&lt;/p&gt;&lt;ol&gt;&lt;li&gt;QA analyst must search for QA cases from the day before the required date.&lt;/li&gt;&lt;li&gt;In the search results, filter the required date.&lt;/li&gt;&lt;li&gt;Click the check box to the left of the column name ”Case number” to select all cases for the required date.&lt;/li&gt;&lt;li&gt;Click “Create QA Cases”&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;Note: Search results are displayed according to GMT date/time.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Select the cases you want to create QA cases for.</description>
        <name>Select_Cases_Screen</name>
        <label>Select Cases Screen</label>
        <locationX>1436</locationX>
        <locationY>2498</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_Selected_Cases_ECDD_ORR</targetReference>
        </connector>
        <fields>
            <name>CaseSelector</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Please select Cases that you would like to QA</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Cases</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Case_Number_Hyperlink__c&quot;,&quot;guid&quot;:&quot;column-ff73&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Case Number Hyperlink&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Status&quot;,&quot;guid&quot;:&quot;column-efcd&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Record_Type_text__c&quot;,&quot;guid&quot;:&quot;column-84bf&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Record Type&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Record Type text&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Date_Analyst_Risk_Rating_Provided__c&quot;,&quot;guid&quot;:&quot;column-c224&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Initial Completion Date&quot;,&quot;wrapText&quot;:false,&quot;order&quot;:3,&quot;label&quot;:&quot;Date Analyst Risk Rating Provided&quot;,&quot;type&quot;:&quot;date-local&quot;},{&quot;apiName&quot;:&quot;Origin&quot;,&quot;guid&quot;:&quot;column-8c37&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Case Origin&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Case_Owner_1__c&quot;,&quot;guid&quot;:&quot;column-86a0&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Owner&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;Case Owner 1&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Analyst_Risk_Rating__c&quot;,&quot;guid&quot;:&quot;column-8bb4&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:6,&quot;label&quot;:&quot;Analyst Risk Rating&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Create QA Cases</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>1640</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>qa_Analyst_Queue</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>CaseOrigin</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>Please_select_what_type_of_QA_Cases_you_would_like_to_create</elementReference>
        </value>
    </variables>
    <variables>
        <name>Cases</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>NoCases</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>QACases</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>QaCasesLIst</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>QAcaseVAR</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>QACreatedCases</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordtypeIdVAR</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>SMRrecordtypes</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>TaskVAR</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>varSelectedCases</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
</Flow>
