<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns the Case to CM Analyst Queue.</description>
        <name>Assign_CM_Analyst_queue</name>
        <label>Assign CM Analyst queue</label>
        <locationX>578</locationX>
        <locationY>935</locationY>
        <assignmentItems>
            <assignToReference>$Record.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_CM_Analyst_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Conditions0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the Case to CM Queue and updates Sub-Status to “Escalated to Case Manager”.</description>
        <name>Assign_CM_queue</name>
        <label>Assign CM queue</label>
        <locationX>314</locationX>
        <locationY>935</locationY>
        <assignmentItems>
            <assignToReference>$Record.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Cm_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.Sub_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Escalated to Case Manager</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Conditions0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the Case to CRC Queue.</description>
        <name>Assign_CRC_queue</name>
        <label>Assign CRC queue</label>
        <locationX>842</locationX>
        <locationY>935</locationY>
        <assignmentItems>
            <assignToReference>$Record.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_CRC_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Conditions0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sets Case Origin to “CRA QA” for Customer Risk Assessment QA Cases.</description>
        <name>Assign_Origin_to_CRA_QA</name>
        <label>Assign Origin to CRA QA</label>
        <locationX>2426</locationX>
        <locationY>611</locationY>
        <assignmentItems>
            <assignToReference>$Record.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>CRA QA</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sets Case Origin to “ECDD QA” for ECDD Cases.</description>
        <name>Assign_Origin_to_ECDD</name>
        <label>Assign Origin to ECDD</label>
        <locationX>1898</locationX>
        <locationY>611</locationY>
        <assignmentItems>
            <assignToReference>$Record.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>ECDD QA</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sets Case Origin to “LER QA” for Non-Transaction Monitoring cases.</description>
        <name>Assign_Origin_to_LER</name>
        <label>Assign Origin to LER</label>
        <locationX>1634</locationX>
        <locationY>611</locationY>
        <assignmentItems>
            <assignToReference>$Record.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>LER QA</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sets Case Origin to “SMR QA” for Standard SMR or Fast-Track SMR Cases.</description>
        <name>Assign_Origin_to_SMR</name>
        <label>Assign Origin to SMR</label>
        <locationX>2162</locationX>
        <locationY>611</locationY>
        <assignmentItems>
            <assignToReference>$Record.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>SMR QA</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sets Case Origin to “TM QA” for Transaction Monitoring cases.</description>
        <name>Assign_Origin_to_TM</name>
        <label>Assign Origin to TM</label>
        <locationX>1370</locationX>
        <locationY>611</locationY>
        <assignmentItems>
            <assignToReference>$Record.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>TM QA</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Assigns the correct Entitlement ID to the Case.</description>
        <name>Set_Case_Fields</name>
        <label>Set Case Fields</label>
        <locationX>50</locationX>
        <locationY>503</locationY>
        <assignmentItems>
            <assignToReference>$Record.EntitlementId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Entitlement.Id</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <customErrors>
        <description>Displays an error if the Case Manager does not provide comments before reassigning to ECDD Analyst.</description>
        <name>Error_message_to_display</name>
        <label>Error message to display</label>
        <locationX>578</locationX>
        <locationY>1235</locationY>
        <customErrorMessages>
            <errorMessage>Please provide case manager comments before reassigning back to the ECDD Analyst.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <description>Routes Cases based on Record Type (ECDD or QA/QC).</description>
        <name>Check_Case_Type</name>
        <label>Check Case Type</label>
        <locationX>1832</locationX>
        <locationY>395</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ECDD</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Cm_Queue</targetReference>
            </connector>
            <label>ECDD</label>
        </rules>
        <rules>
            <name>QA_QC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>QA_QC</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_recordtype_of_parent_record</targetReference>
            </connector>
            <label>QA/QC</label>
        </rules>
    </decisions>
    <decisions>
        <description>Assigns Case Origin based on parent record’s Record Type.</description>
        <name>Check_recordtype_of_parent_record</name>
        <label>Check recordtype of parent record</label>
        <locationX>2030</locationX>
        <locationY>503</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>TM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Origin_to_TM</targetReference>
            </connector>
            <label>TM</label>
        </rules>
        <rules>
            <name>NTM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non_Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Origin_to_LER</targetReference>
            </connector>
            <label>NTM</label>
        </rules>
        <rules>
            <name>ECDD0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Origin_to_ECDD</targetReference>
            </connector>
            <label>ECDD</label>
        </rules>
        <rules>
            <name>SMR</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Standard_SMR</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fast_Track_SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Origin_to_SMR</targetReference>
            </connector>
            <label>SMR</label>
        </rules>
        <rules>
            <name>CRA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer_Risk_Assessment</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Origin_to_CRA_QA</targetReference>
            </connector>
            <label>CRA</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determines if the Case is new (ISNEW()).</description>
        <name>Check_Trigger_Context</name>
        <label>Check Trigger Context</label>
        <locationX>941</locationX>
        <locationY>287</locationY>
        <defaultConnector>
            <targetReference>Check_Case_Type</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Insert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Entitlement</targetReference>
            </connector>
            <label>Insert</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determines which queue (CM, CM Analyst, or CRC) the Case should be assigned to.</description>
        <name>Conditions</name>
        <label>Conditions</label>
        <locationX>710</locationX>
        <locationY>827</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Case_manager_queue_assignment_Check</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Escalated</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Profile.Name</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Customer AML Portal</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_CM_queue</targetReference>
            </connector>
            <label>Case manager queue assignment Check</label>
        </rules>
        <rules>
            <name>Case_Management_Analyst_Queue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Escalated</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Sub_Status__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Sub_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>CM Analyst to action</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_CM_Analyst_queue</targetReference>
            </connector>
            <label>Case Management Analyst Queue</label>
        </rules>
        <rules>
            <name>CRC_Queue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Escalated</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Sub_Status__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Sub_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Escalated to Customer Risk Committee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_CRC_queue</targetReference>
            </connector>
            <label>CRC Queue</label>
        </rules>
    </decisions>
    <decisions>
        <description>Validates Case Manager comments before reassigning to ECDD Analyst.</description>
        <name>Conditions0</name>
        <label>Conditions</label>
        <locationX>710</locationX>
        <locationY>1127</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Conditions_to_reassign_back_to_ECDD_analyst</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Escalated</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Owner:Group.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Analyst</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Case_Manager_Comments__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Error_message_to_display</targetReference>
            </connector>
            <label>Conditions to reassign back to ECDD analyst</label>
        </rules>
    </decisions>
    <description>Used for AML Only

This Auto-Launched Record-Triggered Flow runs before a Case record is created or updated. It is triggered when a Case record meets specific conditions and is responsible for:
	•	Assigning Entitlements based on Record Type.
	•	Routing Cases to different Queues (CM Queue, CRC Queue, CM Analyst Queue) based on Status and Sub-status.
	•	Setting the Case Origin based on Record Type.
	•	Validating required fields before reassigning Cases.

Edit 28/04/2025
- Updated &quot;Case manager queue assignment Check&quot; condition to exclude &quot;Customer AML Portal&quot; profile.</description>
    <environments>Default</environments>
    <formulas>
        <description>Determines the correct Entitlement Process based on Record Type.</description>
        <name>EntitlementName</name>
        <dataType>String</dataType>
        <expression>IF($Record.RecordType.DeveloperName = &quot;Transaction_Monitoring&quot;, &quot;TM Entitlement Process&quot;,
IF($Record.RecordType.DeveloperName = &quot;Non_Transaction_Monitoring&quot;, &quot;NTM Entitlement Process&quot;,
IF($Record.RecordType.DeveloperName = &quot;ECDD_Case&quot;, &quot;ECDD Entitlement Process&quot;,
IF($Record.RecordType.DeveloperName = &quot;Fast_Track_SMR&quot;, &quot;FastTrack SMR Entitlement Process&quot;,
IF($Record.RecordType.DeveloperName = &quot;Standard_SMR&quot;, &quot;Standard SMR Entitlement Process&quot;,
IF($Record.RecordType.DeveloperName = &quot;Customer_Risk_Assessment&quot;, &quot;CRA Entitlement Process&quot;, &quot;&quot;))))))</expression>
    </formulas>
    <formulas>
        <description>Checks if the record is newly created using ISNEW().</description>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <interviewLabel>Case Before Insert Update {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case Before Insert Update</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Retrieves the CM Analyst Queue Group where DeveloperName = &quot;CM_Analyst_Queue&quot;.</description>
        <name>Get_CM_Analyst_Queue</name>
        <label>Get CM Analyst Queue</label>
        <locationX>710</locationX>
        <locationY>611</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_CRC_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CM_Analyst_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Retrieves the CM Queue Group where DeveloperName = &quot;CM_Queue&quot;.</description>
        <name>Get_Cm_Queue</name>
        <label>Get CM Queue</label>
        <locationX>710</locationX>
        <locationY>503</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_CM_Analyst_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CM_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Retrieves the CRC Queue Group where DeveloperName = &quot;CRC_Queue&quot;.</description>
        <name>Get_CRC_Queue</name>
        <label>Get CRC Queue</label>
        <locationX>710</locationX>
        <locationY>719</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Conditions</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CRC_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Retrieves the Entitlement ID based on the assigned Entitlement Name.</description>
        <name>Get_Entitlement</name>
        <label>Get Entitlement</label>
        <locationX>50</locationX>
        <locationY>395</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Set_Case_Fields</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>EntitlementName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Entitlement</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>815</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Trigger_Context</targetReference>
        </connector>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
