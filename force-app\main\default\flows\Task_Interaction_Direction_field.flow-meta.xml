<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Check_From_Address</name>
        <label>Check From Address</label>
        <locationX>611</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>From_Address_not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.FromAddress</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Task_record</targetReference>
            </connector>
            <label>From Address not Null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_From_Address_Domain</name>
        <label>Check From Address Domain</label>
        <locationX>182</locationX>
        <locationY>695</locationY>
        <defaultConnector>
            <targetReference>Inbound</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Sportsbet_Domain</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.FromAddress</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>@sportsbet.com.au</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Outbound</targetReference>
            </connector>
            <label>Sportsbet Domain</label>
        </rules>
    </decisions>
    <decisions>
        <name>Task_record_found</name>
        <label>Task record found</label>
        <locationX>380</locationX>
        <locationY>575</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Task_record.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_From_Address_Domain</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>When Tasks are created from Outlook, Direction field will be populated based on the Sender or Recipient.</description>
    <environments>Default</environments>
    <interviewLabel>Task Interaction - Direction field {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Task Interaction - Direction field</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Task_record</name>
        <label>Get Task record</label>
        <locationX>380</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Task_record_found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ActivityId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>0122y0000004HSoAAM</stringValue>
            </value>
        </filters>
        <filters>
            <field>Log_Method__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Inbound</name>
        <label>Inbound</label>
        <locationX>314</locationX>
        <locationY>815</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ActivityId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <stringValue>Inbound</stringValue>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <recordUpdates>
        <name>Outbound</name>
        <label>Outbound</label>
        <locationX>50</locationX>
        <locationY>815</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ActivityId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <stringValue>Outbound</stringValue>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <start>
        <locationX>485</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_From_Address</targetReference>
        </connector>
        <object>EmailMessage</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
