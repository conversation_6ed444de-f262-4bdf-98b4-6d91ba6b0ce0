<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Refresh_Case_Page</name>
        <label>Refresh Case Page</label>
        <locationX>1370</locationX>
        <locationY>1298</locationY>
        <actionName>c:refreshViewFlowAction</actionName>
        <actionType>component</actionType>
        <connector>
            <targetReference>Blank_Screen</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <nameSegment>c:refreshViewFlowAction</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Refresh_Case_View</name>
        <label>Refresh Case View</label>
        <locationX>578</locationX>
        <locationY>1406</locationY>
        <actionName>c:refreshViewFlowAction</actionName>
        <actionType>component</actionType>
        <connector>
            <targetReference>Warning_Acknowledged</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <nameSegment>c:refreshViewFlowAction</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Refresh_Messaging_Session_Page</name>
        <label>Refresh Messaging Session Page</label>
        <locationX>1106</locationX>
        <locationY>1298</locationY>
        <actionName>c:refreshViewFlowAction</actionName>
        <actionType>component</actionType>
        <connector>
            <targetReference>Blank_Screen</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <nameSegment>c:refreshViewFlowAction</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Refresh_Messaging_Session_View</name>
        <label>Refresh Messaging Session View</label>
        <locationX>50</locationX>
        <locationY>1406</locationY>
        <actionName>c:refreshViewFlowAction</actionName>
        <actionType>component</actionType>
        <connector>
            <targetReference>Warning_Acknowledged</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <nameSegment>c:refreshViewFlowAction</nameSegment>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns a value of TRUE or FALSE if a customer has opted out of the current interaction channel</description>
        <name>Assign_Customer_Channel_Opt_Out</name>
        <label>Assign Customer Channel Opt Out</label>
        <locationX>908</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>varChannelOptOut</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaChannelOptOut</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Has_Customer_Opted_Out</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Customer_Record</name>
        <label>Customer Record</label>
        <locationX>1359</locationX>
        <locationY>650</locationY>
        <defaultConnector>
            <targetReference>Blank_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varCustomerRecord</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Customer_Consent</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Which interaction channel to update</description>
        <name>Email_or_Messaging_Session</name>
        <label>Email or Messaging Session</label>
        <locationX>1370</locationX>
        <locationY>1082</locationY>
        <defaultConnector>
            <targetReference>Blank_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Messaging_Session_Field_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>MessagingSession</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Messaging_Session_Field</targetReference>
            </connector>
            <label>Messaging Session Field Update</label>
        </rules>
        <rules>
            <name>Case_Field_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Case_Field</targetReference>
            </connector>
            <label>Case Field Update</label>
        </rules>
    </decisions>
    <decisions>
        <description>Has the customer opted out of the current interaction channel</description>
        <name>Has_Customer_Opted_Out</name>
        <label>Has Customer Opted Out</label>
        <locationX>908</locationX>
        <locationY>974</locationY>
        <defaultConnector>
            <targetReference>Email_or_Messaging_Session</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Opted In</defaultConnectorLabel>
        <rules>
            <name>Opted_Out_Account_Inactive_or_Self_Excluded</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>varChannelOptOut</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varCustomerRecord.Account_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varCustomerRecord.Self_Excluded__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Warning_Acknowledgment_Screen</targetReference>
            </connector>
            <label>Opted Out Account Inactive or Self Excluded</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is the interaction for a Messaging Session, Voice Call or Case</description>
        <name>Which_interaction_channel</name>
        <label>Which interaction channel</label>
        <locationX>1359</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Messaging_Session</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>MessagingSession</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Messaging_Session</targetReference>
            </connector>
            <label>Messaging Session</label>
        </rules>
        <rules>
            <name>Voice_Call</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VoiceCall</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Voice_Call</targetReference>
            </connector>
            <label>Voice Call</label>
        </rules>
        <rules>
            <name>Case</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Case</targetReference>
            </connector>
            <label>Case</label>
        </rules>
    </decisions>
    <decisions>
        <description>Which interaction channel to update</description>
        <name>Which_interaction_channel_to_update</name>
        <label>Which interaction channel to update</label>
        <locationX>446</locationX>
        <locationY>1190</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Messaging_Session_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>MessagingSession</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Messaging_Session</targetReference>
            </connector>
            <label>Messaging Session Update</label>
        </rules>
        <rules>
            <name>Voice_Call_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VoiceCall</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Voice_Call</targetReference>
            </connector>
            <label>Voice Call Update</label>
        </rules>
        <rules>
            <name>Case_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Case</targetReference>
            </connector>
            <label>Case Update</label>
        </rules>
    </decisions>
    <description>Screen flow is used to display a warning message for the user to acknowledge on interaction records when the customer has opted out of that channel</description>
    <environments>Default</environments>
    <formulas>
        <description>The formula checks the interaction channel and customer consent fields to determine whether the customer has opted out of the given channel. It returns TRUE if the customer has opted OUT of the channel and FALSE if they have opted IN. This is used in the varChannelOptOut variable to determine whether to show the warning message or not</description>
        <name>formulaChannelOptOut</name>
        <dataType>Boolean</dataType>
        <expression>IF(
  AND(
    {!Entity_Definition_Find_Object_Type.varObjectDeveloperName} = &apos;MessagingSession&apos;,
    {!Get_Customer_Consent.Communications_via_RM_SMS__c} = FALSE
  ),
  TRUE,
  IF(
    AND(
      {!Entity_Definition_Find_Object_Type.varObjectDeveloperName} = &apos;VoiceCall&apos;,
      {!varCustomerRecord.Communications_via_RM_Phone__c} = FALSE
    ),
    TRUE,
    IF(
      AND(
        {!Entity_Definition_Find_Object_Type.varObjectDeveloperName} = &apos;Case&apos;,
        {!Get_Customer_Consent.Communications_via_RM_Email__c} = FALSE
      ),
      TRUE,
      FALSE
    )
  )
)</expression>
    </formulas>
    <interviewLabel>Screen Flow: Opted Out Warning Message {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Screen Flow: Opted Out Warning Message</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <description>Gets the current Case record from the recordId</description>
        <name>Get_Case</name>
        <label>Get Case</label>
        <locationX>1491</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Case_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the Case</description>
        <name>Get_Case_Customer</name>
        <label>Get Case Customer</label>
        <locationX>1491</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Customer_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case.AccountId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Account_Status__c</queriedFields>
        <queriedFields>Self_Excluded__c</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Gets the customer&apos;s consent preferences</description>
        <name>Get_Customer_Consent</name>
        <label>Get Customer Consent</label>
        <locationX>908</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Customer_Channel_Opt_Out</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Customer__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varCustomerRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Customer_Consent__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the current Messaging Session from the recordId</description>
        <name>Get_Messaging_Session</name>
        <label>Get Messaging Session</label>
        <locationX>963</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Messaging_Session_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingSession</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the Messaging Session</description>
        <name>Get_Messaging_Session_Customer</name>
        <label>Get Messaging Session Customer</label>
        <locationX>963</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Customer_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Messaging_Session.MessagingEndUser.AccountId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Account_Status__c</queriedFields>
        <queriedFields>Self_Excluded__c</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Gets the current Voice Call record from the recordId</description>
        <name>Get_Voice_Call</name>
        <label>Get Voice Call</label>
        <locationX>1227</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Voice_Call_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the Voice Call</description>
        <name>Get_Voice_Call_Customer</name>
        <label>Get Voice Call Customer</label>
        <locationX>1227</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Customer_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Voice_Call.RelatedRecordId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Communications_via_RM_Phone__c</queriedFields>
        <queriedFields>Account_Status__c</queriedFields>
        <queriedFields>Self_Excluded__c</queriedFields>
    </recordLookups>
    <recordUpdates>
        <description>Updates the current Case with Warning Message Acknowledged = TRUE</description>
        <name>Update_Case</name>
        <label>Update Case</label>
        <locationX>578</locationX>
        <locationY>1298</locationY>
        <connector>
            <targetReference>Refresh_Case_View</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Warning_Message_Acknowledged__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Case with Warning Message Not Shown = TRUE</description>
        <name>Update_Case_Field</name>
        <label>Update Case Field</label>
        <locationX>1370</locationX>
        <locationY>1190</locationY>
        <connector>
            <targetReference>Refresh_Case_Page</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Warning_Message_Not_Shown__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Messaging Session with Warning Message Acknowledged = TRUE</description>
        <name>Update_Messaging_Session</name>
        <label>Update Messaging Session</label>
        <locationX>50</locationX>
        <locationY>1298</locationY>
        <connector>
            <targetReference>Refresh_Messaging_Session_View</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Warning_Message_Ackowledged__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>MessagingSession</object>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Messaging Session with Warning Message Not Shown = TRUE</description>
        <name>Update_Messaging_Session_Field</name>
        <label>Update Messaging Session Field</label>
        <locationX>1106</locationX>
        <locationY>1190</locationY>
        <connector>
            <targetReference>Refresh_Messaging_Session_Page</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Warning_Message_Not_Shown__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>MessagingSession</object>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Voice Call with Warning Message Ackowledged = TRUE</description>
        <name>Update_Voice_Call</name>
        <label>Update Voice Call</label>
        <locationX>314</locationX>
        <locationY>1298</locationY>
        <connector>
            <targetReference>Warning_Acknowledged</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Warning_Message_Acknowledged__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>VoiceCall</object>
    </recordUpdates>
    <screens>
        <description>Blank screen displays if customer is opted in to &apos;hide&apos; the screen flow from the record page</description>
        <name>Blank_Screen</name>
        <label>Blank Screen</label>
        <locationX>1359</locationX>
        <locationY>1982</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <nextOrFinishButtonLabel>Acknowledge</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen to display that the user has acknowledged the warning message</description>
        <name>Warning_Acknowledged</name>
        <label>Warning Acknowledged</label>
        <locationX>446</locationX>
        <locationY>1598</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displaytextWarningMessageAcknowledge</name>
            <fieldText>&lt;p&gt;{!texttemplateWarningSLDS}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;WARNING&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>displaytextWarningMessageAcknowledged</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Thanks! You have acknowledged you&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt; may only send a &lt;/span&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Direct Response to Customer Enquiry&lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen to display a warning message if the customer has opted out of the current interaction channel</description>
        <name>Warning_Acknowledgment_Screen</name>
        <label>Warning Acknowledgment Screen</label>
        <locationX>446</locationX>
        <locationY>1082</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Which_interaction_channel_to_update</targetReference>
        </connector>
        <fields>
            <name>displaytextWarningMessage</name>
            <fieldText>&lt;p&gt;{!texttemplateWarningSLDS}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;WARNING&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;This customer Account is &lt;strong&gt;not active&lt;/strong&gt;, you may only send a &lt;b&gt;Direct Response to Customer Enquiry.&lt;/b&gt; &lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;Please click &lt;strong&gt;Acknowledge&lt;/strong&gt; to confirm you have understood&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>varCustomerRecord.Account_Status__c</leftValueReference>
                    <operator>NotEqualTo</operator>
                    <rightValue>
                        <stringValue>Active</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>displaytextWarningMessageAccountInactive</name>
            <fieldText>&lt;p&gt;{!texttemplateWarningSLDS}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;WARNING&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;This customer Account is &lt;strong&gt;self excluded&lt;/strong&gt;, you may only send a &lt;b&gt;Direct Response to Customer Enquiry.&lt;/b&gt; &lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;Please click &lt;strong&gt;Acknowledge&lt;/strong&gt; to confirm you have understood&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>varCustomerRecord.Self_Excluded__c</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Yes</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>displaytextWarningMessageSelfExcluded</name>
            <fieldText>&lt;p&gt;{!texttemplateWarningSLDS}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;WARNING&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;This customer has &lt;strong&gt;opted out&lt;/strong&gt; of this channel, you may only send a &lt;strong&gt;Direct Response to Customer Enquiry&lt;/strong&gt;. &lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;Please click &lt;strong&gt;Acknowledge&lt;/strong&gt; to confirm you have understood&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>varChannelOptOut</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Acknowledge</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>1233</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Entity_Definition_Find_Object_Type</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <description>Gets the Object type from the recordId to identity the interaction channel</description>
        <name>Entity_Definition_Find_Object_Type</name>
        <label>Entity Definition: Find Object Type</label>
        <locationX>1359</locationX>
        <locationY>134</locationY>
        <connector>
            <targetReference>Which_interaction_channel</targetReference>
        </connector>
        <flowName>Entity_Definition_Find_Object_Type</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <textTemplates>
        <description>Stores the SLDS div class to render warning notifications in display text</description>
        <name>texttemplateWarningSLDS</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-scoped-notification slds-theme_warning&quot; role=&quot;status&quot;&gt;</text>
    </textTemplates>
    <variables>
        <description>Stores the Id of the record the Screen flow is launched from as a text to be used for different purposes throughout the Flow</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the output of the formulaChannelOptOut variable. 
TRUE = Opted Out
FALSE = Opted In</description>
        <name>varChannelOptOut</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varCustomerRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
</Flow>
