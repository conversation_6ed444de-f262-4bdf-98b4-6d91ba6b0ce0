<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>SBET-1042 - Count Guests field throws error when there are no guests</description>
        <name>Assign_0_Guests</name>
        <label>Assign 0 Guests</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>TotalGustNumbers</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Self</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Total_Guests</name>
        <label>Total Guests</label>
        <locationX>490</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>TotalGustNumbers</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>currGuestsNumbers</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>countGuestsloop</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Is_record_the_customer</name>
        <label>Is record the customer</label>
        <locationX>402</locationX>
        <locationY>947</locationY>
        <defaultConnector>
            <targetReference>Update_Parent</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Record_is_customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer_Attendee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Self</targetReference>
            </connector>
            <label>Record is customer</label>
        </rules>
    </decisions>
    <decisions>
        <description>SBET-1042 - Count Guests field throws error when there are no guests</description>
        <name>Is_variable_null</name>
        <label>Were other records found</label>
        <locationX>226</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>countGuestsloop</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>currGuestsNumbers</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_0_Guests</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>SBET-1042 - Count Guests field throws error when there are no guests</description>
    <environments>Default</environments>
    <interviewLabel>Count Guests {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Attendee Triggered Flow - Count Guests</label>
    <loops>
        <name>countGuestsloop</name>
        <label>countGuestsloop</label>
        <locationX>402</locationX>
        <locationY>539</locationY>
        <collectionReference>currGuestsNumbers</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Total_Guests</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Guest_Record</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>SBET-1042 - Count Guests field throws error when there are no guests</description>
        <name>getGuestList</name>
        <label>getGuestList</label>
        <locationX>226</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_variable_null</targetReference>
        </connector>
        <filterLogic>1 AND (2 OR 6) AND (3 AND 4 AND 5)</filterLogic>
        <filters>
            <field>Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Campaign__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Parent_Attendee__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Parent_Attendee__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Guest_Name__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Date_of_Birth__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Is_SportsBet_Customer__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Parent_Attendee__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <object>Attendee__c</object>
        <outputReference>currGuestsNumbers</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Count_Guests__c</queriedFields>
    </recordLookups>
    <recordUpdates>
        <name>Update_Guest_Record</name>
        <label>Update Guest Record</label>
        <locationX>402</locationX>
        <locationY>839</locationY>
        <connector>
            <targetReference>Is_record_the_customer</targetReference>
        </connector>
        <filterLogic>1 AND 2 AND 3</filterLogic>
        <filters>
            <field>Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Campaign__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Parent_Attendee__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Parent_Attendee__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>0122y000000GqbMAAS</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Count_Guests__c</field>
            <value>
                <elementReference>TotalGustNumbers</elementReference>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Parent</name>
        <label>Update Parent</label>
        <locationX>490</locationX>
        <locationY>1055</locationY>
        <inputAssignments>
            <field>Count_Guests__c</field>
            <value>
                <elementReference>TotalGustNumbers</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Parent_Attendee__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Self</name>
        <label>Update Self</label>
        <locationX>226</locationX>
        <locationY>1439</locationY>
        <inputAssignments>
            <field>Count_Guests__c</field>
            <value>
                <elementReference>TotalGustNumbers</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>100</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>getGuestList</targetReference>
        </connector>
        <object>Attendee__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>currGuestsNumbers</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
    <variables>
        <name>TotalGustNumbers</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
</Flow>
