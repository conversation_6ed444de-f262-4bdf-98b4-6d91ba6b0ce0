<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>56.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Check if adjustment approval is triggered</description>
        <name>Adjustment_Approval_Triggered</name>
        <label>Adjustment Approval Triggered</label>
        <locationX>556</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_Customer_Adjustment_Triggered</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0122y000000bpLyAAI</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Approval_Triggered_Adjustment__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ProcessInstance_Object1</targetReference>
            </connector>
            <label>is Customer Adjustment Triggered</label>
        </rules>
        <rules>
            <name>Customer_Adjustment_Actioned</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0122y000000bpLyAAI</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Approval_Triggered_Adjustment__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Approval_Triggered_Adjustment__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ProcessInstance_Object2</targetReference>
            </connector>
            <label>Customer Adjustment Actioned</label>
        </rules>
    </decisions>
    <decisions>
        <name>ProcessInstance1_Not_NULL</name>
        <label>ProcessInstance1 Not NULL</label>
        <locationX>182</locationX>
        <locationY>815</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ProcessInstance1_is_null1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ProcessInstance_Object1.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Approval_Type_field1</targetReference>
            </connector>
            <label>ProcessInstance1 is null1</label>
        </rules>
    </decisions>
    <decisions>
        <name>ProcessInstance_Object2_Not_NULL</name>
        <label>ProcessInstance Object2 Not NULL</label>
        <locationX>622</locationX>
        <locationY>695</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ProcessInstance_Object2_is_valid</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ProcessInstance_Object2.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Approver_Details2</targetReference>
            </connector>
            <label>ProcessInstance Object2 is valid?</label>
        </rules>
    </decisions>
    <description>Flow updates Approver  comments, approval date in Adjustment record</description>
    <environments>Default</environments>
    <interviewLabel>Update Adjustment Approver Details {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Adjustment Approver Details</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Record from ProcessDefinition</description>
        <name>ProcessDefinition_Details</name>
        <label>ProcessDefinition Details</label>
        <locationX>182</locationX>
        <locationY>695</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstance1_Not_NULL</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object1.ProcessDefinitionId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessDefinition</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Record from ProcessInstance Object</description>
        <name>ProcessInstance_Object1</name>
        <label>ProcessInstance Object1</label>
        <locationX>182</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstanceStep_Object1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>TargetObjectId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstance</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>ProcessInstance_Object2</name>
        <label>ProcessInstance Object2</label>
        <locationX>622</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstanceStep_Object2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>TargetObjectId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstance</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Record from ProcessInstanceStep Object</description>
        <name>ProcessInstanceStep_Object1</name>
        <label>ProcessInstanceStep Object1</label>
        <locationX>182</locationX>
        <locationY>575</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessDefinition_Details</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProcessInstanceId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object1.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstanceStep</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>ProcessInstanceStep_Object2</name>
        <label>ProcessInstanceStep Object2</label>
        <locationX>622</locationX>
        <locationY>575</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ProcessInstance_Object2_Not_NULL</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProcessInstanceId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessInstance_Object2.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProcessInstanceStep</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Approval_Type_field1</name>
        <label>Update Approval Type field1</label>
        <locationX>50</locationX>
        <locationY>935</locationY>
        <inputAssignments>
            <field>Approval_Type_Adjustment__c</field>
            <value>
                <elementReference>ProcessDefinition_Details.Name</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Approver_Details2</name>
        <label>Update Approver Details2</label>
        <locationX>490</locationX>
        <locationY>815</locationY>
        <inputAssignments>
            <field>Approval_Comments_Adjustment__c</field>
            <value>
                <elementReference>ProcessInstanceStep_Object2.Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Approval_Date_Adjustment__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>430</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Adjustment_Approval_Triggered</targetReference>
        </connector>
        <object>Adjustment__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
