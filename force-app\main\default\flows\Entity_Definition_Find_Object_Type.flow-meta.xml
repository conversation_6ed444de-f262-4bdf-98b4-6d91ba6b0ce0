<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Stores the Developer Name in a variable to be used by the parent flow</description>
        <name>Assign_Entity_Definition_Values</name>
        <label>Assign Entity Definition Values</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignmentItems>
            <assignToReference>varObjectDeveloperName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Entity_Definition.DeveloperName</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <description>Subflow that uses the recordId from a parent flow to find out what object the record is. Use this subflow when the parent flow needs to perform different actions for different objects</description>
    <environments>Default</environments>
    <formulas>
        <description>Formula to extract the first 3 characters from the {recordId} variable that is used to identify the correct object KeyPrefix from the Entity Defintion record</description>
        <name>formulaKeyPrefix</name>
        <dataType>String</dataType>
        <expression>LEFT({!recordId},3)</expression>
    </formulas>
    <interviewLabel>Entity Definition: Find Object Type {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Entity Definition: Find Object Type</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the Entity Definition to identify the object type of the recordId</description>
        <name>Get_Entity_Definition</name>
        <label>Get Entity Definition</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Entity_Definition_Values</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>KeyPrefix</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaKeyPrefix</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EntityDefinition</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Entity_Definition</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>Variable to store the recordId from the parent flow</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to store the Developer Name from the Entity Definition record that can be used in the parent flow</description>
        <name>varObjectDeveloperName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
