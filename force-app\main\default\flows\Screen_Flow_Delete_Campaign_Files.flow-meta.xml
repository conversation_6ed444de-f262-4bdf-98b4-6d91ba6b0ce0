<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Add identified File to new collection for deletion at the end of the flow, and the file name for addition to a task at the end of the flow.</description>
        <name>Add_to_Deletion_Collection</name>
        <label>Add to Deletion Collection</label>
        <locationX>138</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>contentDocumentsToDelete</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_Content_Document_from_Link</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>deletedFileNamesString</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>newLineForFileName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>For_Each_Content_Document_link</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Introduced as part of SBET-1280. Assign all values relevant to the newly created task to record the Campaign File deletion action on the Campaign.</description>
        <name>Assign_Task_Values</name>
        <label>Assign Task Values</label>
        <locationX>50</locationX>
        <locationY>1190</locationY>
        <assignmentItems>
            <assignToReference>fileDeletionTask.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>fileDeletionTask.WhatId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Campaign.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>fileDeletionTask.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>taskSubject</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>fileDeletionTask.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>fileDeletionTask.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Task_Record_Type.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>fileDeletionTask.Priority</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>fileDeletionTask.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>taskDescription</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Task_on_Campaign</targetReference>
        </connector>
    </assignments>
    <constants>
        <name>emptyString</name>
        <dataType>String</dataType>
        <value>
            <stringValue></stringValue>
        </value>
    </constants>
    <decisions>
        <description>Check that records were found that require deletion</description>
        <name>Were_file_links_found</name>
        <label>Were file links found?</label>
        <locationX>422</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>No_Files_Found</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No File Links Found</defaultConnectorLabel>
        <rules>
            <name>Records_were_found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Campaign_Files</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Delete_Campaign_Tickets</targetReference>
            </connector>
            <label>Records were found</label>
        </rules>
    </decisions>
    <description>SBET-1280 - Record Delete Campaign Tickets action completion as a Task</description>
    <environments>Default</environments>
    <interviewLabel>Screen Flow - Delete Campaign Files {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Screen Flow - Delete Campaign Files</label>
    <loops>
        <description>Loop through each link to retrieve the related document and add to a collection for deletion later</description>
        <name>For_Each_Content_Document_link</name>
        <label>For Each Content Document link</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <collectionReference>Get_Campaign_Files</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Get_Content_Document_from_Link</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Delete_Campaign_Files</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Introduced as part of SBET-1280. Create task against the campaign to record who actioned the deletion, when, and what files were deleted.</description>
        <name>Create_Task_on_Campaign</name>
        <label>Create Task on Campaign</label>
        <locationX>50</locationX>
        <locationY>1298</locationY>
        <inputReference>fileDeletionTask</inputReference>
    </recordCreates>
    <recordDeletes>
        <description>Delete all items added to the record collection in the previous loop</description>
        <name>Delete_Campaign_Files</name>
        <label>Delete Campaign Files</label>
        <locationX>50</locationX>
        <locationY>974</locationY>
        <connector>
            <targetReference>Get_Task_Record_Type</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <inputReference>contentDocumentsToDelete</inputReference>
    </recordDeletes>
    <recordLookups>
        <description>Get the Campaign record for use in this flow later</description>
        <name>Get_Campaign</name>
        <label>Get Campaign</label>
        <locationX>422</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Campaign_Files</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Campaign</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get all existing links for campaign files to locate campaign files later</description>
        <name>Get_Campaign_Files</name>
        <label>Get Campaign File Links</label>
        <locationX>422</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Were_file_links_found</targetReference>
        </connector>
        <faultConnector>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>LinkedEntityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Campaign.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContentDocumentLink</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the content Document associated with the link loop item</description>
        <name>Get_Content_Document_from_Link</name>
        <label>Get Content Document from Link</label>
        <locationX>138</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Add_to_Deletion_Collection</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>For_Each_Content_Document_link.ContentDocumentId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContentDocument</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the Record Type ID to be used later in the flow to create a Task of the Task record type</description>
        <name>Get_Task_Record_Type</name>
        <label>Get Task Record Type</label>
        <locationX>50</locationX>
        <locationY>1082</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Task_Values</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <description>Screen to verify prior to proceeding that the files require deletion</description>
        <name>Delete_Campaign_Tickets</name>
        <label>Delete Campaign Tickets</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>For_Each_Content_Document_link</targetReference>
        </connector>
        <fields>
            <name>warningText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;You are about to delete all files associated with this Campaign.&lt;/strong&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt; &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Note that this will not delete tickets already assigned to Attendee records. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;To proceed, select Delete Tickets, otherwise close this window. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Delete Tickets</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen to display flow error if they occur to the user for faster debugging</description>
        <name>errorScreen</name>
        <label>errorScreen</label>
        <locationX>1058</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>errorText</name>
            <fieldText>&lt;p&gt;The following error occurred during this process. Please pass on the details to your System Administrator:&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen to prompt running user that no files were found associated with this campaign</description>
        <name>No_Files_Found</name>
        <label>No Files Found</label>
        <locationX>794</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>noFilesFoundText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;No files are associated with this Campaign.&lt;/strong&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt; &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Please close this window. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>296</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Campaign</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>Text Template to format the file names as a new bullet point on a new line in the task description</description>
        <name>newLineForFileName</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>• {!Get_Content_Document_from_Link.Title} {!emptyString}
{!emptyString}</text>
    </textTemplates>
    <textTemplates>
        <description>Variable to use as a template for the description of the task created at the end of the flow</description>
        <name>taskDescription</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>The following files were deleted from the Campaign by {!$User.FirstName} {!$User.LastName}:
{!deletedFileNamesString}</text>
    </textTemplates>
    <textTemplates>
        <description>Subject formula for Task created at the end of the flow</description>
        <name>taskSubject</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Campaign Files deleted by {!$User.FirstName} {!$User.LastName}</text>
    </textTemplates>
    <variables>
        <description>Collection variable for content documents that require deletion</description>
        <name>contentDocumentsToDelete</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContentDocument</objectType>
    </variables>
    <variables>
        <description>Collection of the file names that have been deleted</description>
        <name>deletedFileNames</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>deletedFileNamesString</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to store values for creation of a task at the end of the flow</description>
        <name>fileDeletionTask</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
