<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Inbound_Messaging_User</name>
        <label>Assign Inbound Messaging User</label>
        <locationX>264</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>varMessagingUser</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaCustomerName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Message_to_Formatted_Chat_Transcript</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Message_to_Formatted_Chat_Transcript</name>
        <label>Assign Message to Formatted Chat Transcript</label>
        <locationX>396</locationX>
        <locationY>839</locationY>
        <assignmentItems>
            <assignToReference>varDisplayData</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>varMessagingSessionSummary</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Conversation_Session_Message_Summary</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Outbound_Messaging_User</name>
        <label>Assign Outbound Messaging User</label>
        <locationX>528</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>varMessagingUser</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formuaAgentName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Message_to_Formatted_Chat_Transcript</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_If_Message_Is_Inbound</name>
        <label>Check If Message Is Inbound</label>
        <locationX>396</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>Assign_Outbound_Messaging_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Outbound Message</defaultConnectorLabel>
        <rules>
            <name>Is_Inbound_Message</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Create_Conversation_Session_Message_Summary.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Inbound</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Inbound_Messaging_User</targetReference>
            </connector>
            <label>Is Inbound Message</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>formuaAgentName</name>
        <dataType>String</dataType>
        <expression>{!Create_Conversation_Session_Message_Summary.Agent_User__r.FirstName} + &quot; &quot; + {!Create_Conversation_Session_Message_Summary.Agent_User__r.LastName}</expression>
    </formulas>
    <formulas>
        <name>formulaCustomerName</name>
        <dataType>String</dataType>
        <expression>{!$Record.Customer__r.FirstName} + &quot; &quot; + {!$Record.Customer__r.LastName}</expression>
    </formulas>
    <formulas>
        <name>formulaLineBreak</name>
        <dataType>String</dataType>
        <expression>BR()</expression>
    </formulas>
    <formulas>
        <description>Formula used to take the Message from the SMS and remove the Betstop message at the end</description>
        <name>formulaMessageNoBetStop</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE(
  {!Create_Conversation_Session_Message_Summary.Message__c},
  &quot;Betstop? Visit Sportsbet.com.au To Opt Out reply STOP&quot;,
  &quot;&quot;
)</expression>
    </formulas>
    <interviewLabel>Messaging Session Summary Save {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Messaging Session Summary Save</label>
    <loops>
        <description>This loop runs for each message in Enhanced Conversation Entry records</description>
        <name>Create_Conversation_Session_Message_Summary</name>
        <label>Create Conversation Session Message Summary</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <collectionReference>Get_Enhanced_Conversation_Entry_Records</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_If_Message_Is_Inbound</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Summary_To_Transcript</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>This node gets all conversation entry records related to the message session</description>
        <name>Get_Enhanced_Conversation_Entry_Records</name>
        <label>Get Enhanced Conversation Entry Records</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Conversation_Session_Message_Summary</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Messaging_Session__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Entry_Time__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.CreatedDate</elementReference>
            </value>
        </filters>
        <filters>
            <field>Entry_Time__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.EndTime</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Enhanced_Conversation_Entry__c</object>
        <sortField>Entry_Time__c</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>This node updates the Messaging Session summary if the message summary is not available.</description>
        <name>Update_Summary_To_Transcript</name>
        <label>Update Summary To Transcript</label>
        <locationX>176</locationX>
        <locationY>1031</locationY>
        <inputAssignments>
            <field>Messaging_Session_Summary__c</field>
            <value>
                <elementReference>varDisplayData</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Enhanced_Conversation_Entry_Records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Messaging_Session_Summary__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>summary unavailable</stringValue>
            </value>
        </filters>
        <object>MessagingSession</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>varMessagingSessionSummary</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!varMessagingUser} @ {!Create_Conversation_Session_Message_Summary.Entry_Time__c}: {!formulaMessageNoBetStop}{!formulaLineBreak}</text>
    </textTemplates>
    <variables>
        <name>varDisplayData</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varEnhancedMessagingCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>varEntriesCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>varFaultMessage</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Summary is unavailable and failed to include message session transcript</stringValue>
        </value>
    </variables>
    <variables>
        <name>varMessagingUser</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varSummaryUnavailableMessage</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>summary unavailable</stringValue>
        </value>
    </variables>
</Flow>
