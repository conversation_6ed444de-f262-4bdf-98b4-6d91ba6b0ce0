<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionCalls>
        <name>Submit_For_Approval</name>
        <label>Submit For Approval</label>
        <locationX>1172</locationX>
        <locationY>2198</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>Assign_case_to_Manager</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>var_CaseToCreate.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <elementReference>for_ApprovalComments</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Premium_Customer_Review_Approval</stringValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_AML_Team</name>
        <label>Assign: AML Team</label>
        <locationX>2162</locationX>
        <locationY>1166</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Raised_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>AML Team</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_Status_and_Origin</name>
        <label>Assign: Case Status and Origin</label>
        <locationX>1238</locationX>
        <locationY>1550</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>New</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Other</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Premium_Customer_Review_Record_Type.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Premium_Customer_Review_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Priority</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Medium</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Screen_CaseType</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_Status_Ready_To_Action</name>
        <label>Assign: Case Status - Ready To Action</label>
        <locationX>1964</locationX>
        <locationY>2954</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Ready to Action</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_Subject</name>
        <label>Assign: Case Subject &amp; Description</label>
        <locationX>1700</locationX>
        <locationY>2306</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>for_CampaignSubject</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Further_Details</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.PCR_Preferences__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Guest_List_Preferences</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_new_values_to_Campaign_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_E_E</name>
        <label>Assign: E&amp;E</label>
        <locationX>1370</locationX>
        <locationY>1166</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Raised_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>E&amp;E</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Premium_Review_Case_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Event List Request</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_new_values_to_Campaign_Record</name>
        <label>Assign new values to Campaign Record</label>
        <locationX>1700</locationX>
        <locationY>2414</locationY>
        <assignmentItems>
            <assignToReference>Get_Campaign.Start_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Event_Date</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Campaign.Customer_Ticket_Allocation__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Customer_Ticket_Allocation</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Campaign.Waitlist_Capacity__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Waitlist_Size</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Campaign.Target_Tier__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Target_Tier2</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Campaign.State__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>State</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Campaign_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Premium_Relationship_Manager_Customer_Centric</name>
        <label>Assign: Premium Relationship Manager - Customer Centric</label>
        <locationX>314</locationX>
        <locationY>1274</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Raised_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Premium Relationship Manager Customer Centric</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Premium_Relationship_Manager_NonCustomer_Centric</name>
        <label>Assign: Premium Relationship Manager - NonCustomer Centric</label>
        <locationX>578</locationX>
        <locationY>1274</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Raised_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Premium Relationship Manager - NonCustomer Centric</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Premium_State_Manager</name>
        <label>Assign: Premium State Manager</label>
        <locationX>1634</locationX>
        <locationY>1166</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Raised_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Premium State Manager</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Premium_Team_Manager_Customer_Centric</name>
        <label>Assign: Premium Team Manager - Customer Centric</label>
        <locationX>842</locationX>
        <locationY>1274</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Raised_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Premium Team Manager Customer Centric</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Premium_Team_Manager_NonCustomer_Centric</name>
        <label>Assign: Premium Team Manager - NonCustomer Centric</label>
        <locationX>1106</locationX>
        <locationY>1274</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Raised_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Premium Team Manager - NonCustomer Centric</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_RG_Team</name>
        <label>Assign: RG Team</label>
        <locationX>1898</locationX>
        <locationY>1166</locationY>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Raised_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>RG Team</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignisCampaign</name>
        <label>Assign: isCampaign</label>
        <locationX>1238</locationX>
        <locationY>542</locationY>
        <assignmentItems>
            <assignToReference>isCampaign</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.Campaign__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Campaign</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignisCustomer</name>
        <label>Assign: isCustomer</label>
        <locationX>974</locationX>
        <locationY>542</locationY>
        <assignmentItems>
            <assignToReference>isCustomer</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CaseToCreate.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Premium_Customer_Review_Record_Type</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignisNotFound</name>
        <label>Assign: isNotFound</label>
        <locationX>1502</locationX>
        <locationY>542</locationY>
        <assignmentItems>
            <assignToReference>isNotFound</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Premium_Customer_Review_Record_Type</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assignment_15</name>
        <label>Check Validation Flag</label>
        <locationX>908</locationX>
        <locationY>1874</locationY>
        <assignmentItems>
            <assignToReference>validationError</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_CaseType</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Clear_Validation_Flag</name>
        <label>Clear Validation Flag</label>
        <locationX>1568</locationX>
        <locationY>1874</locationY>
        <assignmentItems>
            <assignToReference>validationError</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_var_CaseToCreate</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_Make_Raised_By_field_Visible</name>
        <label>Make Raised By field Visible</label>
        <locationX>50</locationX>
        <locationY>1166</locationY>
        <assignmentItems>
            <assignToReference>raisedByIsVisible</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Make_Raised_By_field_Visible</name>
        <label>Make Raised By field Visible</label>
        <locationX>2426</locationX>
        <locationY>1166</locationY>
        <assignmentItems>
            <assignToReference>raisedByIsVisible</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_Status_and_Origin</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Closed_Self_Excluded</name>
        <choiceText>Closed/Self Excluded</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Closed/Self Excluded</stringValue>
        </value>
    </choices>
    <choices>
        <name>Customer_Request</name>
        <choiceText>Customer Request</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Customer Request</stringValue>
        </value>
    </choices>
    <choices>
        <name>Customer_Swap</name>
        <choiceText>Customer Swap</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Customer Swap</stringValue>
        </value>
    </choices>
    <choices>
        <name>Event_Blacklist</name>
        <choiceText>Event Blacklist</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Event Blacklist</stringValue>
        </value>
    </choices>
    <choices>
        <name>Exclusion</name>
        <choiceText>Exclusion</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Exclusion</stringValue>
        </value>
    </choices>
    <choices>
        <name>From_PUM</name>
        <choiceText>From PUM</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>From PUM</stringValue>
        </value>
    </choices>
    <choices>
        <name>From_Standard</name>
        <choiceText>From Standard</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>From Standard</stringValue>
        </value>
    </choices>
    <choices>
        <name>Inactive</name>
        <choiceText>Inactive</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Inactive</stringValue>
        </value>
    </choices>
    <choices>
        <name>Issues</name>
        <choiceText>Issues</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Issues</stringValue>
        </value>
    </choices>
    <choices>
        <name>Low_DL</name>
        <choiceText>Low DL</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Low DL</stringValue>
        </value>
    </choices>
    <choices>
        <name>Management_Decision</name>
        <choiceText>Management Decision</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Management Decision</stringValue>
        </value>
    </choices>
    <choices>
        <name>Not_to_be_Managed</name>
        <choiceText>Not to be Managed</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Not to be Managed</stringValue>
        </value>
    </choices>
    <choices>
        <name>Offboard</name>
        <choiceText>Offboard</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Offboard</stringValue>
        </value>
    </choices>
    <choices>
        <name>Onboard</name>
        <choiceText>Onboard</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Onboard</stringValue>
        </value>
    </choices>
    <choices>
        <name>Previous_Relationship</name>
        <choiceText>Previous Relationship</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Previous Relationship</stringValue>
        </value>
    </choices>
    <choices>
        <name>Re_Allocate_Customer_to_RM</name>
        <choiceText>Re-Allocate Customer to RM</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Re-Allocate Customer to RM</stringValue>
        </value>
    </choices>
    <choices>
        <name>Rebase_Customer_Review</name>
        <choiceText>Rebase Customer Review</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Rebase Customer Review</stringValue>
        </value>
    </choices>
    <choices>
        <name>Remove_from_Premium</name>
        <choiceText>Remove from Premium</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Remove from Premium</stringValue>
        </value>
    </choices>
    <choices>
        <name>RG_Score</name>
        <choiceText>RG Score</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>RG Score</stringValue>
        </value>
    </choices>
    <choices>
        <name>Uncontactable</name>
        <choiceText>Uncontactable</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Uncontactable</stringValue>
        </value>
    </choices>
    <decisions>
        <name>Decision_Customer_Centric</name>
        <label>Decision: Relationship Manager: Customer Centric?</label>
        <locationX>446</locationX>
        <locationY>1166</locationY>
        <defaultConnector>
            <targetReference>Assign_Premium_Relationship_Manager_NonCustomer_Centric</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Customer_Centric</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isCustomer</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Premium_Relationship_Manager_Customer_Centric</targetReference>
            </connector>
            <label>Customer Centric</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_isCampaign</name>
        <label>Decision: isCampaign?</label>
        <locationX>1964</locationX>
        <locationY>2198</locationY>
        <defaultConnector>
            <targetReference>Assign_Case_Status_Ready_To_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Campaign_True</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isCampaign</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_Subject</targetReference>
            </connector>
            <label>is Campaign</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_RecordId_Null</name>
        <label>Decision: RecordId = Null?</label>
        <locationX>1238</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>Decision_Which_Object</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Subflow_Find_Object_Type</targetReference>
            </connector>
            <label>Not Null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_Role_Name</name>
        <label>Decision: Role Name</label>
        <locationX>1238</locationX>
        <locationY>1058</locationY>
        <defaultConnector>
            <targetReference>Make_Raised_By_field_Visible</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>System_Admin_User</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Profile.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>System Administrator</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Make_Raised_By_field_Visible</targetReference>
            </connector>
            <label>System Admin User</label>
        </rules>
        <rules>
            <name>Relationship_Manager</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Relationship Manager</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Decision_Customer_Centric</targetReference>
            </connector>
            <label>Relationship Manager</label>
        </rules>
        <rules>
            <name>Team_Manager</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Team Manager</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Decision_TeamManagerCustomer_Centric</targetReference>
            </connector>
            <label>Team Manager</label>
        </rules>
        <rules>
            <name>E_E</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>E&amp;E</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Events &amp; Experiences</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_E_E</targetReference>
            </connector>
            <label>E&amp;E</label>
        </rules>
        <rules>
            <name>State_Manager</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>State Manager</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Premium_State_Manager</targetReference>
            </connector>
            <label>State Manager</label>
        </rules>
        <rules>
            <name>RG_Team</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>RG</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_RG_Team</targetReference>
            </connector>
            <label>RG Team</label>
        </rules>
        <rules>
            <name>AML_Team</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>AML</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_AML_Team</targetReference>
            </connector>
            <label>AML Team</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_Submit_For_Approval</name>
        <label>Decision: Submit For Approval?</label>
        <locationX>1568</locationX>
        <locationY>2090</locationY>
        <defaultConnector>
            <targetReference>Decision_isCampaign</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Premium_Relationship_Manager</name>
            <conditionLogic>1 AND (2 OR 3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>$UserRole.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Relationship Manager</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_CaseToCreate.Premium_Review_Case_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Discretionary Budget</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_CaseToCreate.Premium_Review_Case_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Re-Allocate Customer to RM</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_CaseToCreate.Premium_Review_Case_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Remove from Premium</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_CaseToCreate.Premium_Review_Case_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer Swap</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Submit_For_Approval</targetReference>
            </connector>
            <label>Premium Relationship Manager</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_TeamManagerCustomer_Centric</name>
        <label>Decision: Team Manager: Customer Centric?</label>
        <locationX>974</locationX>
        <locationY>1166</locationY>
        <defaultConnector>
            <targetReference>Assign_Premium_Team_Manager_NonCustomer_Centric</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_2_of_Customer_Centric</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isCustomer</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Premium_Team_Manager_Customer_Centric</targetReference>
            </connector>
            <label>Customer Centric</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_Which_Object</name>
        <label>Decision: Which Object?</label>
        <locationX>1238</locationX>
        <locationY>434</locationY>
        <defaultConnector>
            <targetReference>AssignisNotFound</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_ObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Account</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignisCustomer</targetReference>
            </connector>
            <label>Customer</label>
        </rules>
        <rules>
            <name>Campaign</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_ObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Campaign</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignisCampaign</targetReference>
            </connector>
            <label>Campaign</label>
        </rules>
    </decisions>
    <decisions>
        <name>Has_Validation_rule_been_triggered</name>
        <label>Has Validation rule been triggered?</label>
        <locationX>1238</locationX>
        <locationY>1766</locationY>
        <defaultConnector>
            <targetReference>Clear_Validation_Flag</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>1 AND (2 OR 3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>var_CaseToCreate.Raised_By__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>E&amp;E</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_CaseToCreate.PCR_Due_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_CaseToCreate.Targeted_Guest_List_Size__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_CaseToCreate.Premium_Review_Case_Subtype__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Target_Tier2</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_15</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>SBET-1194 - Update to E&amp;E - Guest List Request</description>
    <dynamicChoiceSets>
        <name>statePicklistOptions</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>State__c</picklistField>
        <picklistObject>Campaign</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>targetTierChoiceSet</name>
        <dataType>Multipicklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Target_Tier__c</picklistField>
        <picklistObject>Campaign</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>for_ApprovalComments</name>
        <dataType>String</dataType>
        <expression>TEXT({!var_CaseToCreate.Premium_Review_Case_Type__c}) + &apos; - &apos; + TEXT({!var_CaseToCreate.Premium_Review_Case_Subtype__c}) + &apos; - &apos; + {!var_CaseToCreate.Description}</expression>
    </formulas>
    <formulas>
        <name>for_CampaignSubject</name>
        <dataType>String</dataType>
        <expression>TEXT({!var_CaseToCreate.Premium_Review_Case_Type__c}) + &apos; - &apos; + TEXT({!var_CaseToCreate.Premium_Review_Case_Subtype__c}) + &apos; - &apos; + {!Get_Campaign.Name}</expression>
    </formulas>
    <formulas>
        <name>for_SubjectName</name>
        <dataType>String</dataType>
        <expression>&apos;New Proposition Change -&apos; + {!var_CaseToCreate.Account.Name}</expression>
    </formulas>
    <formulas>
        <name>for_ThreadId</name>
        <dataType>String</dataType>
        <expression>&quot;ref:!00D&quot;&amp;MID({!var_CaseToCreate.Id},4,1)&amp;RIGHT({!$Organization.Id}, 4) &amp;&quot;.!&quot;&amp; LEFT({!var_CaseToCreate.Id},4)&amp;RIGHT({!var_CaseToCreate.Id},5) &amp;&quot;:ref&quot;</expression>
    </formulas>
    <interviewLabel>Screen Flow: Create Customer Review Case {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Screen Flow: Create Customer Review Case</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_var_CaseToCreate</name>
        <label>Create var_CaseToCreate</label>
        <locationX>1568</locationX>
        <locationY>1982</locationY>
        <connector>
            <targetReference>Decision_Submit_For_Approval</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <inputReference>var_CaseToCreate</inputReference>
    </recordCreates>
    <recordLookups>
        <name>Get_Campaign</name>
        <label>Get Campaign</label>
        <locationX>1238</locationX>
        <locationY>650</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Premium_Customer_Review_Record_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Campaign</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Premium_Customer_Review_Queue</name>
        <label>Get Premium Customer Review Queue</label>
        <locationX>1238</locationX>
        <locationY>950</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Decision_Role_Name</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Customer_Review_Inbox_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Premium_Customer_Review_Record_Type</name>
        <label>Get Premium Customer Review Record Type</label>
        <locationX>1238</locationX>
        <locationY>842</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Premium_Customer_Review_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Customer_Review</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Assign_case_to_Manager</name>
        <label>Assign case to Manager</label>
        <locationX>1172</locationX>
        <locationY>2306</locationY>
        <connector>
            <targetReference>Screen_Submitted_for_Approval</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>var_CaseToCreate.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$User.ManagerId</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Campaign_Record</name>
        <label>Update Campaign Record</label>
        <locationX>1700</locationX>
        <locationY>2522</locationY>
        <connector>
            <targetReference>Assign_Case_Status_Ready_To_Action</targetReference>
        </connector>
        <faultConnector>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <inputReference>Get_Campaign</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Case</name>
        <label>Update Case</label>
        <locationX>1964</locationX>
        <locationY>3062</locationY>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <inputReference>var_CaseToCreate</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>errorScreen</name>
        <label>errorScreen</label>
        <locationX>1964</locationX>
        <locationY>2630</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>errorMessage</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;An error occurred:&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Screen_CaseType</name>
        <label>Screen_CaseType</label>
        <locationX>1238</locationX>
        <locationY>1658</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Has_Validation_rule_been_triggered</targetReference>
        </connector>
        <fields>
            <name>Screen_CaseType_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Screen_CaseType_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.AccountId</objectFieldReference>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>isCustomer</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.Subject</objectFieldReference>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>isCampaign</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>campaignValidation</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;You must enter a description for this case&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>isCampaign</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.Description</objectFieldReference>
                    <visibilityRule>
                        <conditionLogic>or</conditionLogic>
                        <conditions>
                            <leftValueReference>isCampaign</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Screen_CaseType_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.Raised_By__c</objectFieldReference>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>raisedByIsVisible</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.Premium_Review_Case_Type__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.Premium_Review_Case_Subtype__c</objectFieldReference>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>isCampaign</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>false</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>isCampaign</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ValidationFlagEandE</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Warning: You must enter the Due Date, Target Guest List Size, Case Subtype (Target Cohort) and Target Tier to submit this request. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>validationError</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Screen_CaseType_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Screen_CaseType_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.Campaign__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.Premium_Review_Case_Type__c</objectFieldReference>
                </fields>
                <fields>
                    <name>Customer_Ticket_Allocation</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Get_Campaign.Customer_Ticket_Allocation__c</elementReference>
                    </defaultValue>
                    <fieldText>Customer Ticket Allocation</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <scale>0</scale>
                </fields>
                <fields>
                    <name>Waitlist_Size</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Get_Campaign.Waitlist_Capacity__c</elementReference>
                    </defaultValue>
                    <fieldText>Waitlist Size</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <scale>0</scale>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.Targeted_Guest_List_Size__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.KM_Radius_Considered__c</objectFieldReference>
                </fields>
                <fields>
                    <name>Guest_List_Preferences</name>
                    <defaultValue>
                        <stringValue>No Blacklisted from Events</stringValue>
                    </defaultValue>
                    <fieldText>Guest List Preferences</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <helpText>&lt;p&gt;Preferences for event attendees as populated when requesting an Event Guest List.&lt;/p&gt;</helpText>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Screen_CaseType_Section2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.Premium_Review_Case_Subtype__c</objectFieldReference>
                </fields>
                <fields>
                    <name>Event_Date</name>
                    <dataType>Date</dataType>
                    <defaultValue>
                        <elementReference>Get_Campaign.Start_Date__c</elementReference>
                    </defaultValue>
                    <fieldText>Event Date</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>var_CaseToCreate.PCR_Due_Date__c</objectFieldReference>
                </fields>
                <fields>
                    <name>State</name>
                    <choiceReferences>statePicklistOptions</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_Campaign.State__c</elementReference>
                    </defaultValue>
                    <fieldText>State</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Target_Tier2</name>
                    <choiceReferences>targetTierChoiceSet</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_Campaign.Target_Tier__c</elementReference>
                    </defaultValue>
                    <fieldText>Target Tier</fieldText>
                    <fieldType>MultiSelectPicklist</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>isCampaign</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Further_Details</name>
            <defaultValue>
                <stringValue>Betting Preferences:
Remove Customers:
KM Radius (if applicable): 100km</stringValue>
            </defaultValue>
            <fieldText>Further Details</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>isCampaign</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Screen_Submitted_for_Approval</name>
        <label>Screen: Submitted for Approval</label>
        <locationX>1172</locationX>
        <locationY>2414</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>display_SubmittedForApproval</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 22px;&quot;&gt;Case has been submitted for Approval&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>1112</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Decision_RecordId_Null</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Subflow_Find_Object_Type</name>
        <label>Subflow: Find Object Type</label>
        <locationX>1106</locationX>
        <locationY>242</locationY>
        <connector>
            <targetReference>Decision_Which_Object</targetReference>
        </connector>
        <flowName>Entity_Definition_Find_Object_Type</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>var_ObjectDeveloperName</assignToReference>
            <name>varObjectDeveloperName</name>
        </outputAssignments>
    </subflows>
    <textTemplates>
        <name>tt_EmailBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;{!$User.FirstName} {!$User.LastName} has requested a proposition change for their customer.&lt;/p&gt;&lt;p&gt;SB Customer ID: {!var_CaseToCreate.AccountId}&lt;/p&gt;&lt;p&gt;Change type: {!var_CaseToCreate.Premium_Review_Case_Subtype__c}&lt;/p&gt;&lt;p&gt;Comments: {!var_CaseToCreate.Description}&lt;/p&gt;&lt;p&gt;Please reply to this email with any updates&lt;/p&gt;&lt;p&gt;{!for_ThreadId}&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>isCampaign</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>isCustomer</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>isNotFound</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <description>variable that determines if the Raised By field should be visible or not</description>
        <name>raisedByIsVisible</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>validationError</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>var_CaseToCreate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>var_ObjectDeveloperName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
