<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Deactivate_User</name>
        <label>Deactivate User</label>
        <locationX>264</locationX>
        <locationY>468</locationY>
        <assignmentItems>
            <assignToReference>Loop_Users.IsActive</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Users</targetReference>
        </connector>
    </assignments>
    <description>Deactivates AML Portal Users when the date meets todays date. Runs daily.</description>
    <environments>Default</environments>
    <formulas>
        <name>Date_Today</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <interviewLabel>AML User Deactivation Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML User Deactivation Flow</label>
    <loops>
        <name>Loop_Users</name>
        <label>Loop Users</label>
        <locationX>176</locationX>
        <locationY>360</locationY>
        <collectionReference>Get_AML_Portal_Users</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Deactivate_User</targetReference>
        </nextValueConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_AML_Portal_Users</name>
        <label>Get AML Portal Users</label>
        <locationX>176</locationX>
        <locationY>252</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Users</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AML_User_Deactivation_Date__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Date_Today</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_AML_Portal_Users</targetReference>
        </connector>
        <object>User</object>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2024-03-13</startDate>
            <startTime>01:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
</Flow>
