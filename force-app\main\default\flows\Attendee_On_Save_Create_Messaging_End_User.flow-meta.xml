<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Does an existing Messaging End User record exist</description>
        <name>Does_Messaging_End_User_Exist</name>
        <label>Does Messaging End User Exist</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Get_Messaging_Channel</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Messaging_End_User</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Customer_Attendee_Checkbox</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>Used when a Customer is added as an Attendee to an event, a Messaging End User is created for this customer if one does not already exist and marks field on customer record to designate them as Customer Attendee</description>
    <environments>Default</environments>
    <interviewLabel>Attendee: On Save - Create Messaging End User {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Attendee: On Save - Create Messaging End User</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>Creates a Messaging End User record for the Attendee&apos;s related Customer</description>
        <name>Create_Messaging_End_User</name>
        <label>Create Messaging End User</label>
        <locationX>264</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Update_Customer_Attendee_Checkbox</targetReference>
        </connector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>$Record.Account__r.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>$Record.Account__r.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessageType</field>
            <value>
                <stringValue>Text</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessagingChannelId</field>
            <value>
                <elementReference>Get_Messaging_Channel.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessagingConsentStatus</field>
            <value>
                <stringValue>ImplicitlyOptedIn</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessagingPlatformKey</field>
            <value>
                <elementReference>$Record.Account__r.PersonMobilePhone</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>$Record.Account__r.PersonMobilePhone</elementReference>
            </value>
        </inputAssignments>
        <object>MessagingEndUser</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <description>Gets the SMS Messaging Channel Id</description>
        <name>Get_Messaging_Channel</name>
        <label>Get Messaging Channel</label>
        <locationX>264</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Messaging_End_User</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium Managed SMS</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingChannel</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Messaging End User record for the Attendee&apos;s related Customer</description>
        <name>Get_Messaging_End_User</name>
        <label>Get Messaging End User</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Does_Messaging_End_User_Exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MessagingPlatformKey</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account__r.PersonMobilePhone</elementReference>
            </value>
        </filters>
        <filters>
            <field>MessageType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Text</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingEndUser</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Marks the customer attendee checkbox on related customer record as TRUE</description>
        <name>Update_Customer_Attendee_Checkbox</name>
        <label>Update Customer Attendee Checkbox</label>
        <locationX>176</locationX>
        <locationY>839</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account__r.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Attendee__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Messaging_End_User</targetReference>
        </connector>
        <filterFormula>OR(
AND(
ISNEW(),
{!$Record.RecordType.DeveloperName} = &apos;Customer_Attendee&apos;,
{!$Record.Campaign__r.RecordType.DeveloperName} = &apos;Event&apos;,
NOT(ISBLANK({!$Record.Account__c})),
NOT(ISBLANK({!$Record.Account__r.PersonMobilePhone}))
),
AND(
ISCHANGED({!$Record.RecordType.DeveloperName}),
{!$Record.RecordType.DeveloperName} = &apos;Customer_Attendee&apos;,
{!$Record.Campaign__r.RecordType.DeveloperName} = &apos;Event&apos;,
NOT(ISBLANK({!$Record.Account__c})),
NOT(ISBLANK({!$Record.Account__r.PersonMobilePhone}))
)
)</filterFormula>
        <object>Attendee__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
