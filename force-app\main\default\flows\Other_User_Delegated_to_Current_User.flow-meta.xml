<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>AssignText</name>
        <label>AssignText</label>
        <locationX>228</locationX>
        <locationY>531</locationY>
        <assignmentItems>
            <assignToReference>VarTextCurrent</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>formulaText</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>LoopAllUsers</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignText_0</name>
        <label>AssignText</label>
        <locationX>604</locationX>
        <locationY>516</locationY>
        <assignmentItems>
            <assignToReference>varTextFuture</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>formulaText</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>LoopAllUsers_0</targetReference>
        </connector>
    </assignments>
    <formulas>
        <name>formulaCurrentUserId</name>
        <dataType>String</dataType>
        <expression>CASESAFEID({!$User.Id})</expression>
    </formulas>
    <formulas>
        <name>formulaText</name>
        <dataType>String</dataType>
        <expression>&quot;From User: &quot; + {!varUser.Name} + BR() +
&quot;Delegation Start Date: &quot; + Text({!varUser.Delegate_Assignment_Start_Date__c}) + BR() +
&quot;Delegation End Date: &quot; + Text({!varUser.Delegate_Assignment_End_Date__c}) + BR() +
&quot;------&quot; + BR()</expression>
    </formulas>
    <interviewLabel>Other User Delegated to Current User {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Other User Delegated to Current User</label>
    <loops>
        <name>LoopAllUsers</name>
        <label>LoopAllUsers</label>
        <locationX>228</locationX>
        <locationY>333</locationY>
        <assignNextValueToReference>varUser</assignNextValueToReference>
        <collectionReference>GetUserRecord</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>AssignText</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>GetUserRecord_0</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>LoopAllUsers_0</name>
        <label>LoopAllUsers</label>
        <locationX>608</locationX>
        <locationY>324</locationY>
        <assignNextValueToReference>varUser</assignNextValueToReference>
        <collectionReference>GetUserRecord_0</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>AssignText_0</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Screen_1</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>GetUserRecord</name>
        <label>GetUserRecord</label>
        <locationX>238</locationX>
        <locationY>49</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>LoopAllUsers</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Delegate_Assignment_End_Date__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </filters>
        <filters>
            <field>Delegate_Assignment_Start_Date__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </filters>
        <filters>
            <field>Delegate_Assignment_User_ID__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaCurrentUserId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetUserRecord_0</name>
        <label>GetUserRecord</label>
        <locationX>606</locationX>
        <locationY>53</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>LoopAllUsers_0</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Delegate_Assignment_End_Date__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </filters>
        <filters>
            <field>Delegate_Assignment_Start_Date__c</field>
            <operator>GreaterThan</operator>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </filters>
        <filters>
            <field>Delegate_Assignment_User_ID__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaCurrentUserId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Screen_1</name>
        <label>Screen 1</label>
        <locationX>1033</locationX>
        <locationY>54</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>DisplayText</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 13px; color: rgb(0, 0, 0);&quot;&gt;Team members who have delegated their Salesforce to you:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(0, 0, 0);&quot;&gt;Current:&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 13px; color: rgb(0, 0, 0);&quot;&gt;{!VarTextCurrent}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 13px; color: rgb(0, 0, 0);&quot;&gt;Upcoming:&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!varTextFuture}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>GetUserRecord</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>VarTextCurrent</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varTextFuture</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varUser</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
</Flow>
