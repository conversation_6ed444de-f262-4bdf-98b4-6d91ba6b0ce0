<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>50.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <formulas>
        <name>FormulaSubject</name>
        <dataType>String</dataType>
        <expression>{!VarSportsbetCustomerNumber}+&quot; - &quot;+{!GetCaseDetails.Contact.FirstName}+&quot; &quot;+ {!GetCaseDetails.Contact.LastName}+&quot; - &quot;</expression>
    </formulas>
    <formulas>
        <name>FormulaSubjectNoCustomer</name>
        <dataType>String</dataType>
        <expression>{!VarSportsbetCustomerNumber}+&quot; - &quot;+{!VarFirstCaseFirstName}+&quot; &quot;+ {!VarFirstCaseLastName}+&quot; - &quot;</expression>
    </formulas>
    <formulas>
        <name>TodayDate</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate}</expression>
    </formulas>
    <formulas>
        <name>TodayDateText</name>
        <dataType>String</dataType>
        <expression>Text({!TodayDate})</expression>
    </formulas>
    <interviewLabel>Invoke Automated ECDD Case Creation {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Invoke Automated ECDD Case Creation</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_AML_Queue</name>
        <label>Get AML Queue</label>
        <locationX>357</locationX>
        <locationY>216</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Case_record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>AML_Analyst</stringValue>
            </value>
        </filters>
        <object>Group</object>
        <outputAssignments>
            <assignToReference>VarAMLQueue</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>GetCaseDetails</name>
        <label>GetCaseWithCustomer</label>
        <locationX>157</locationX>
        <locationY>219</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetFirstCase</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>VarCaseId</elementReference>
            </value>
        </filters>
        <filters>
            <field>ContactId</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>ContactId</queriedFields>
        <sortField>CreatedDate</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetFirstCase</name>
        <label>GetFirstCase</label>
        <locationX>250</locationX>
        <locationY>349</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_AML_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ECDD_SportsBet_Account_Number__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>VarSportsbetCustomerNumber</elementReference>
            </value>
        </filters>
        <object>Case</object>
        <outputAssignments>
            <assignToReference>VarFirstCaseFirstName</assignToReference>
            <field>ECDD_First_Name__c</field>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>VarFirstCaseLastName</assignToReference>
            <field>ECDD_Last_Name__c</field>
        </outputAssignments>
        <sortField>CreatedDate</sortField>
        <sortOrder>Asc</sortOrder>
    </recordLookups>
    <recordUpdates>
        <name>Update_Case_Name</name>
        <label>Update Case Name With customer related</label>
        <locationX>639</locationX>
        <locationY>215</locationY>
        <connector>
            <targetReference>Update_Case_with_no_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>VarCaseId</elementReference>
            </value>
        </filters>
        <filters>
            <field>ContactId</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>ECDD_First_Name__c</field>
            <value>
                <elementReference>GetCaseDetails.Contact.FirstName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ECDD_Last_Name__c</field>
            <value>
                <elementReference>GetCaseDetails.Contact.LastName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ECDD_Relationship_Manager__c</field>
            <value>
                <elementReference>GetCaseDetails.Account.Owner__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>{!FormulaSubject}{!TodayDateText}</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Case_record</name>
        <label>Update Case record</label>
        <locationX>459</locationX>
        <locationY>347</locationY>
        <connector>
            <targetReference>Update_Case_Name</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>VarCaseId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>VarAMLQueue</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>FormulaSubjectNoCustomer</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Case_with_no_Customer</name>
        <label>Update Case with no Customer</label>
        <locationX>805</locationX>
        <locationY>331</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>VarCaseId</elementReference>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>ECDD_First_Name__c</field>
            <value>
                <elementReference>VarFirstCaseFirstName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ECDD_Last_Name__c</field>
            <value>
                <elementReference>VarFirstCaseLastName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>{!FormulaSubjectNoCustomer}{!TodayDateText}</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <start>
        <locationX>30</locationX>
        <locationY>65</locationY>
        <connector>
            <targetReference>GetCaseDetails</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>VarAMLQueue</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>VarCaseId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>VarCollectionCaseId</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>VarContact</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>VarContactFirstName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>VarFirstCaseFirstName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>VarFirstCaseLastName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>VarSportsbetCustomerNumber</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
</Flow>
