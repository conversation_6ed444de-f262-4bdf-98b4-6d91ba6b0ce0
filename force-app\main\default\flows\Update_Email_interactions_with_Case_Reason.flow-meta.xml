<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Reason_and_summary</name>
        <label>Assign Reason and summary</label>
        <locationX>138</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>Loop_Through_Tasks.Reason_for_Contact__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Reason</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Through_Tasks.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Subject</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Through_Tasks.WhatId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Through_Tasks.WhoId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Tasks</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Through_Tasks</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Through_Tasks.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Case_Summary__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_Tasks</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Are_there_any_email_interaction_tasks</name>
        <label>Are there any email interaction tasks</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Email_Interactions</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Through_Tasks</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>SBET-960 - Change voice call record call summary from Interaction_Summary__c to Comments</description>
    <environments>Default</environments>
    <formulas>
        <name>Subject</name>
        <dataType>String</dataType>
        <expression>&quot;Email - &quot; + TEXT({!Loop_Through_Tasks.Direction__c} )+ &quot; - &quot; + TEXT({!$Record.Reason})</expression>
    </formulas>
    <interviewLabel>Update Email interactions with Case Reason {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case - Update Email interactions with Case Reason</label>
    <loops>
        <name>Loop_Through_Tasks</name>
        <label>Loop Through Tasks</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <collectionReference>Get_Email_Interactions</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Reason_and_summary</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Tasks</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Email_Interactions</name>
        <label>Get Email Interactions</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Are_there_any_email_interaction_tasks</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Case__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Tasks</name>
        <label>Update Tasks</label>
        <locationX>50</locationX>
        <locationY>839</locationY>
        <inputReference>Tasks</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Email_Interactions</targetReference>
        </connector>
        <filterFormula>AND(
{!$Record.RecordType.DeveloperName} = &apos;Premium_Managed_Case&apos;,
OR(
ISCHANGED({!$Record.Reason}),
ISCHANGED({!$Record.AccountId})
)
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>Tasks</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
