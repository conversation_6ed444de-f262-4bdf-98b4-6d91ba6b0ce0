<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>assign_Fastrack</name>
        <label>assign Fastrack</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>recordId.FastTrack_Required__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_RecordType</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Fast_Track_SMR</name>
        <choiceText>Fast-Track SMR</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Fast-Track SMR</stringValue>
        </value>
    </choices>
    <choices>
        <name>Non_Terrorism_Financing_Related</name>
        <choiceText>Non Terrorism Financing Related</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Non Terrorism Financing Related</stringValue>
        </value>
    </choices>
    <choices>
        <name>Standard_SMR</name>
        <choiceText>Standard SMR</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Standard SMR</stringValue>
        </value>
    </choices>
    <choices>
        <name>Terrorism_Financing_Related</name>
        <choiceText>Terrorism Financing Related</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Terrorism Financing Related</stringValue>
        </value>
    </choices>
    <decisions>
        <name>Fasttrack_selected</name>
        <label>Fasttrack selected</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Get_RecordType</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_Fasttrack</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Fast_Track_SMR</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assign_Fastrack</targetReference>
            </connector>
            <label>is Fasttrack</label>
        </rules>
    </decisions>
    <description>Used in Create SMR Action on ECDD RecordType page.
- Creates Standard or Fast Track SMR case.</description>
    <environments>Default</environments>
    <formulas>
        <name>formulaCaseURL</name>
        <dataType>String</dataType>
        <expression>LEFT({!$Api.Partner_Server_URL_260}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_260})) &amp; {!SMRCaseId}</expression>
    </formulas>
    <interviewLabel>Create SMR Screen flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Create SMR Screen flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_SMR</name>
        <label>Create SMR</label>
        <locationX>182</locationX>
        <locationY>650</locationY>
        <assignRecordIdToReference>SMRCaseId</assignRecordIdToReference>
        <connector>
            <targetReference>Get_Created_SMR_Case</targetReference>
        </connector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>recordId.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>recordId.ContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FastTrack_Required__c</field>
            <value>
                <elementReference>recordId.FastTrack_Required__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <elementReference>recordId.Origin</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>recordId.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_RecordType.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TF_or_Non_TF_related__c</field>
            <value>
                <elementReference>recordId.TF_or_Non_TF_related__c</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordCreates>
    <recordLookups>
        <name>Get_Created_SMR_Case</name>
        <label>Get Created SMR Case</label>
        <locationX>182</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>End_screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>SMRCaseId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>CaseNumber</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_RecordType</name>
        <label>Get RecordType</label>
        <locationX>182</locationX>
        <locationY>542</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_SMR</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>What_type_of_SMR_to_create</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>Create_new_SMR</name>
        <label>Create new SMR</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Fasttrack_selected</targetReference>
        </connector>
        <fields>
            <name>What_type_of_SMR_to_create</name>
            <choiceReferences>Standard_SMR</choiceReferences>
            <choiceReferences>Fast_Track_SMR</choiceReferences>
            <dataType>String</dataType>
            <fieldText>What type of SMR to create?</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Create_new_SMR_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Create_new_SMR_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>recordId.TF_or_Non_TF_related__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>1</conditionLogic>
                <conditions>
                    <leftValueReference>What_type_of_SMR_to_create</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>Fast_Track_SMR</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Create Case</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>End_screen</name>
        <label>End screen</label>
        <locationX>182</locationX>
        <locationY>866</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>End_screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>End_screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>End_screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>SMRMessage</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;SMR Case has been successfully created.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Case Number :&lt;/strong&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt; &lt;/span&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;{!texttemplateCaseURL}&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>End_screen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Create_new_SMR</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>texttemplateCaseURL</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;a href=&quot;{!formulaCaseURL}&quot; target=&quot;_self&quot;&gt;{!Get_Created_SMR_Case.CaseNumber}&lt;/a&gt;</text>
    </textTemplates>
    <variables>
        <name>OwnerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$User.Id</elementReference>
        </value>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>SMRCaseId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
