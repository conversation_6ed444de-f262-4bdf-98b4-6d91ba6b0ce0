<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <choices>
        <name>Action_Required</name>
        <choiceText>Action Required</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Action_Required</stringValue>
        </value>
    </choices>
    <choices>
        <name>FYI</name>
        <choiceText>FYI</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>FYI</stringValue>
        </value>
    </choices>
    <choices>
        <name>Medium</name>
        <choiceText>Medium</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Medium</stringValue>
        </value>
    </choices>
    <description>Removed the RB Review Comments</description>
    <dynamicChoiceSets>
        <name>Task</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Priority</picklistField>
        <picklistObject>Task</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>UserValidation</name>
        <dataType>Boolean</dataType>
        <expression>OR(
CONTAINS(LOWER({!Assigned_To.recordName}), &apos;premium service&apos;),
CONTAINS(LOWER({!Assigned_To.recordName}), &apos;integration&apos;),
CONTAINS(LOWER({!Assigned_To.recordName}), &apos;kafka integration&apos;)
)</expression>
    </formulas>
    <formulas>
        <name>varStatus</name>
        <dataType>String</dataType>
        <expression>CASE({!Type}, &apos;FYI&apos;, &apos;Completed&apos;, &apos;Action Required&apos;, &apos;Not Started&apos;,&apos;Not Started&apos;)</expression>
    </formulas>
    <formulas>
        <name>varSubject</name>
        <dataType>String</dataType>
        <expression>{!Type} +&apos; RM Allocated Task &apos;+{!Get_Voice_Call_Details.Customer__r.Sports_Bet_Account_Number__c}</expression>
    </formulas>
    <formulas>
        <name>varTodayDate</name>
        <dataType>Date</dataType>
        <expression>Today()+4</expression>
    </formulas>
    <interviewLabel>RM To RM Task From Voice Call {!$Flow.CurrentDateTime}</interviewLabel>
    <label>RM To RM Task From Voice Call</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Task_in_messaging_Session</name>
        <label>Create Task in MS</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>Task_Created</targetReference>
        </connector>
        <faultConnector>
            <targetReference>ErrorScreen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>ActivityDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>Description</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Assigned_To.recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <elementReference>PriorityChoice</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <elementReference>varStatus</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>varSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <elementReference>Type</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Voice_Call__c</field>
            <value>
                <elementReference>Get_Voice_Call_Details.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>Get_Voice_Call_Details.Customer__r.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>Get_Voice_Call_Details.Customer__r.Contact__r.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Voice_Call_Details</name>
        <label>Get Voice Call Details</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>RM2RM_Task_Create_from_VC</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <name>ErrorScreen</name>
        <label>ErrorScreen</label>
        <locationX>440</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Errortext</name>
            <fieldText>&lt;p&gt;Error Creating Task - Please try again&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>RM2RM_Task_Create_from_VC</name>
        <label>RM2RM_Task_Create from VC</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Create_Task_in_messaging_Session</targetReference>
        </connector>
        <fields>
            <name>Assigned_To</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>OwnerId</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Assigned To</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>Account</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>Get_Voice_Call_Details.Customer__r.OwnerId</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <validationRule>
                <errorMessage>&lt;p&gt;Please select another user that is not Premium Service, Integration, Kafka Integration&lt;/p&gt;</errorMessage>
                <formulaExpression>!{!UserValidation}</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>ActivityDate</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>varTodayDate</elementReference>
            </defaultValue>
            <fieldText>Due Date</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>PriorityChoice</name>
            <choiceReferences>Task</choiceReferences>
            <dataType>String</dataType>
            <defaultValue>
                <stringValue>Medium</stringValue>
            </defaultValue>
            <fieldText>Priority</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Description</name>
            <fieldText>Comments</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Type</name>
            <choiceReferences>FYI</choiceReferences>
            <choiceReferences>Action_Required</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Type</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Create Task</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Task_Created</name>
        <label>Task Created</label>
        <locationX>176</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessText</name>
            <fieldText>&lt;p&gt;RM Task created successfully&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Voice_Call_Details</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>var_assigned_to</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Date</description>
        <name>varDate</name>
        <dataType>Date</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
