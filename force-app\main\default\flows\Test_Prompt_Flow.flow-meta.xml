<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Send_keywords_for_the_prompt_template</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Send keywords for the prompt template</label>
        <locationX>264</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>{!Loop_keywords.Description__c};
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_keywords</targetReference>
        </connector>
    </assignments>
    <environments>Default</environments>
    <interviewLabel>Prompt Template: {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Prompt Template: Test Prompt Flow</label>
    <loops>
        <name>Loop_keywords</name>
        <label>Loop keywords</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <collectionReference>get_Keywords</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Send_keywords_for_the_prompt_template</targetReference>
        </nextValueConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>PromptFlow</processType>
    <recordLookups>
        <name>get_Keywords</name>
        <label>get Keywords</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_keywords</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Regulatory_Keywords__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>get_Keywords</targetReference>
        </connector>
        <triggerType>Capability</triggerType>
    </start>
    <status>Active</status>
</Flow>
