Link: SBET-1428: SNSM-238: Modify the Guest List Generator to allow multiple customer selection at once
In Progress
 

Got it. Below is a full Confluence page ready to paste, covering your user story SNSM-238, including:

Title & Metadata

User Story in BA format

Background

Current Limitations

Proposed Solution

Acceptance Criteria (BA-style)

Test Scenarios

Modified Components

Implementation Notes

Attachments

📝 Modify Guest List Generator to Allow Multi-Customer Selection
Jira: SNSM-238
Status: In Progress
Assignee: <PERSON>
Sprint: SBET Q1 Oct 2024 – Jan 2025
Epic: SBET-1421 Project Tickets

🎯 User Story
As a Relationship Manager
I want to be able to select multiple customers from the Guest List Generator page in Salesforce
So that I can efficiently add customers to events and reduce repetitive actions

📚 Background
This enhancement was identified during Sprint 2 user story grooming but was deferred due to the MVP scope. The current guest list flow and component only support selecting one customer at a time, resulting in repetitive actions and inefficiencies for Relationship Managers.

🚫 Current Limitations
Single Selection Constraint: The max-row-selection="1" attribute on lightning-datatable limits selection to a single customer.

Data Structure Limitation: The firedFromAura() method passes only one accountId, not a collection.

Exclude Functionality: The handleExclude() method only removes one selected row at a time.

Workflow Inefficiency: Users must reselect and run the flow individually for each customer.

🛠️ Proposed Solution
Lightning Web Component (GuestList LWC)
Change max-row-selection="1" to allow multiple selections (e.g. max-row-selection="999")

Update firedFromAura() to return an array of Account IDs

Refactor handleExclude() to support excluding multiple rows

Update parent/Aura component logic to consume an array of accountIds

Flow: Create Customer Review Case - V3
Add an input variable of type Record Collection (Account)

Loop through selected customers and generate a case for each

Update assignments to use currentCustomer.Id inside the loop

Show a summary screen with total processed records

✅ Acceptance Criteria (BA Style)
#

Description

Given

When

Then

AC1

Enable multiple customer selection

I am on the Guest List Generator page

I view the customer selection table

I can select more than one customer at a time

AC2

Event sends multiple Account IDs

I have selected multiple customers

I initiate guest list creation

An array of selected Account IDs is sent to the flow

AC3

Flow accepts & processes each Account

Multiple Account IDs are passed into the flow

The flow is triggered

Each customer is looped through and receives a review case

AC4

Exclude supports multiple rows

I have selected multiple customers to exclude

I click “Exclude”

All selected rows are removed at once

AC5

Confirmation screen summarizes outcome

The flow finishes processing all customers

The final screen is displayed

A message confirms the total number of review cases created

🧪 Test Scenarios
#

Test Scenario

Expected Result

TS1

Select 3 customers and submit

Flow creates 3 customer review cases

TS2

Select multiple customers and trigger Exclude

All selected rows are removed from UI

TS3

Select no customers and submit

Flow exits or displays an error message

TS4

Submit single customer (backward compatibility)

Flow behaves as it did before the enhancement

TS5

Check confirmation screen after processing

Success screen shows correct number of created cases

🧩 Modified Components
LWC: GuestList LWC

Flow: Create Customer Review Case - V3

Aura Event Handling: Updated to support collections

⚙️ Implementation Notes
LWC Changes:
Adjust max-row-selection

Change selection handler to support arrays

Modify exclude method

Flow Changes:
Create input variable: selectedCustomers (Record Collection - Account)

Add loop: currentCustomer

Move logic inside loop and use currentCustomer.Id in assignments

Add final confirmation screen

📎 Attachments



Let me know if you’d like a separate section for Dev Tasks or a table for deployment steps as part of your sprint readiness.

Architecture Review: Guest List Generator Multi-Selection Enhancement
Current Architecture Analysis
Based on the provided code and requirements, I can see that the current Guest List Generator has the following architecture:

guestList.js

force-app/main/default/lwc/guestList

Copy







export default class GuestList_clone extends NavigationMixin
(LightningElement) {
    // ...
    @track preSelectedRows = [];
    
    // Current implementation only allows single row selection
    getRecordId(event) {
        var selectedRows = event.detail.selectedRows;
        if(selectedRows.length>0) {
            var el = this.template.querySelector
            ('lightning-datatable');
            selectedRows=el.selectedRows=el.selectedRows;
            this.preSelectedRows = selectedRows;
            event.preventDefault();
            console.log('Selected row value',this.preSelectedRows);
            return;
        }
        return;
    }
    @api
    firedFromAura(){
        // Currently only passes the first selected row
        if (this.preSelectedRows.length > 0) {
            const value = {accountId:this.preSelectedRows[0],
            campaignId:this.selectedEventId};
            const selectedEvent = new CustomEvent('selectedRow', {
                detail:{value},
            });
            this.dispatchEvent(selectedEvent);
        }
        // ...
    }
}


guestList.html

force-app/main/default/lwc/guestList

Copy







<lightning-datatable
    key-field="Id"
    columns={columns}
    data={guests}
    enable-infinite-loading={enableinfiniteLoading}
    onloadmore={loadMoreData}
    load-more-offset={rowLoad}
    selected-rows={preSelectedRows}
    default-sort-direction={defaultSortDirection}
    sorted-direction={sortDirection}
    sorted-by={sortedBy}
    onrowselection={getRecordId}
    onsort={onHandleSort}
    max-row-selection="1"
>
</lightning-datatable>


Current Limitations
Single Selection Constraint: The max-row-selection="1" attribute in the lightning-datatable explicitly limits users to selecting only one customer at a time.

Data Structure Limitation: The firedFromAura() method is designed to pass only a single accountId to the parent component or flow, not supporting multiple selections.

Exclude Functionality: The handleExclude() method only removes a single selected row from the list.

Workflow Inefficiency: Relationship Managers must repeat the selection process for each customer they want to add to an event or contact, which is time-consuming.

Proposed Changes
Enable Multi-Selection in the Datatable:

Remove the max-row-selection="1" constraint or change it to a higher number (e.g., "999")

Update the Data Handling Methods:

Modify firedFromAura() to handle multiple selections by creating an array of account IDs

Update handleExclude() to handle multiple selections

Update Event Dispatching:

Change the event payload structure to support multiple account IDs

Flow Modifications:

Update the receiving flow to process a collection of Account IDs instead of a single ID

Add loop processing to handle each selected account

Implementation Plan
guestList.html- force-app/main/default/lwc/guestList



<lightning-datatable
    key-field="Id"
    columns={columns}
    data={guests}
    enable-infinite-loading={enableinfiniteLoading}
    onloadmore={loadMoreData}
    load-more-offset={rowLoad}
    selected-rows={preSelectedRows}
    default-sort-direction={defaultSortDirection}
    sorted-direction={sortDirection}
    sorted-by={sortedBy}
    onrowselection={getRecordId}
    onsort={onHandleSort}
    max-row-selection="999"
>
</lightning-datatable>
guestList.js - force-app/main/default/lwc/guestList



@api
firedFromAura(){
    console.log('In LWC');
    if (this.preSelectedRows.length > 0) {
        // Create an array of account IDs instead of just using the 
        first one
        const accountIds = this.preSelectedRows.map(row => row.Id);
        const value = {accountIds: accountIds, campaignId: this.
        selectedEventId};
        const selectedEvent = new CustomEvent('selectedRow', {
            detail:{value},
        });
        //dispatching the custom event
        this.dispatchEvent(selectedEvent);
    } else {
        const event = new ShowToastEvent({
            title: 'Warning!',
            message: 'Select at least one row!',
            variant: 'warning',
            mode: 'dismissable'
        });
        this.dispatchEvent(event);
    }
}
@api
handleExclude(){
    console.log('In lwc : ');
    if (this.preSelectedRows.length > 0) {
        // Get all selected IDs
        const selectedIds = this.preSelectedRows.map(row => row.Id);
        
        // Filter out all selected rows at once
        this.guests = this.guests.filter(element => {
            return !selectedIds.includes(element.Id);
        });
        this.preSelectedRows = [];
    } else {
        const event = new ShowToastEvent({
            title: 'Warning!',
            message: 'Select at least one row!',
            variant: 'warning',
            mode: 'dismissable'
        });
        this.dispatchEvent(event);
    }
}
Flow Modifications
For the flow that processes the selected customers (identified as 301RE00000FCOVqYAP), I'll need to:

Update the input variable to accept a collection of Account IDs

Add a loop element to process each Account ID

Update references to use the current Account ID from the loop

Create collection variables to store aggregated results

Benefits of the Solution
Efficiency: Relationship Managers can select multiple customers at once, saving time

Improved User Experience: Reduces repetitive actions and streamlines the workflow

Scalability: The solution can handle any number of selected customers (up to 999)

Minimal Changes: The modifications are focused and don't require a complete rewrite

Potential Challenges
Parent Component Updates: Any parent components or Aura components that receive the selectedRow event will need to be updated to handle the new data structure

Flow Complexity: The flow will become more complex with the addition of loops and collections

Performance: Processing multiple customers at once might impact performance if a large number of customers are selected

This solution addresses the core requirement of allowing Relationship Managers to select multiple customers at once, making their workflow more efficient when creating guest lists or adding customers to events.