<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Email</name>
        <label>Send Email</label>
        <locationX>2291</locationX>
        <locationY>353</locationY>
        <actionName>Case.Event_and_Hospitality_RG_Score_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>CreateCase</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Event_and_Hospitality_RG_Score_Case_Email_Notification</nameSegment>
    </actionCalls>
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_to_Final_Collection</name>
        <label>Add to Final Collection</label>
        <locationX>612</locationX>
        <locationY>1011</locationY>
        <assignmentItems>
            <assignToReference>ClosedCases</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Open_Cases</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Open_Cases</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_New_Values</name>
        <label>Assign New Values</label>
        <locationX>607</locationX>
        <locationY>869</locationY>
        <assignmentItems>
            <assignToReference>Loop_Open_Cases.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Open_Cases.Closure_Comments__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>System auto closed - RG Score has dropped below threshold</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_to_Final_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_to_Combined_Collection</name>
        <label>Assign to Combined Collection</label>
        <locationX>1389</locationX>
        <locationY>124</locationY>
        <assignmentItems>
            <assignToReference>AttendeeCombinedCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>GetOfferAceptedAttendees</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>AttendeeCombinedCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>GetOfferPendingAttendees</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Sort_by_Event_Date</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Values</name>
        <label>Assign Values</label>
        <locationX>1509</locationX>
        <locationY>285</locationY>
        <assignmentItems>
            <assignToReference>CampaignList</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>{!EventDetails}{!LineBreak}</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Combined</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>GetOpenCaseCount</name>
        <label>GetOpenCaseCount</label>
        <locationX>193</locationX>
        <locationY>720</locationY>
        <assignmentItems>
            <assignToReference>OpenCaseCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Open_Hospitality_Cases</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_RG_Score</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>GetRecordCount</name>
        <label>GetRecordCount</label>
        <locationX>1023</locationX>
        <locationY>121</locationY>
        <assignmentItems>
            <assignToReference>OfferAcceptedRecordCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>GetOfferAceptedAttendees</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OfferPendingRecordCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>GetOfferPendingAttendees</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>CheckAttendeesCollection</targetReference>
        </connector>
    </assignments>
    <collectionProcessors>
        <name>Sort_by_Event_Date</name>
        <elementSubtype>SortCollectionProcessor</elementSubtype>
        <label>Sort by Event Date</label>
        <locationX>1510</locationX>
        <locationY>124</locationY>
        <collectionProcessorType>SortCollectionProcessor</collectionProcessorType>
        <collectionReference>AttendeeCombinedCollection</collectionReference>
        <connector>
            <targetReference>Assign_Values</targetReference>
        </connector>
        <sortOptions>
            <doesPutEmptyStringAndNullFirst>false</doesPutEmptyStringAndNullFirst>
            <sortField>Event_Date__c</sortField>
            <sortOrder>Asc</sortOrder>
        </sortOptions>
        <sortOptions>
            <doesPutEmptyStringAndNullFirst>false</doesPutEmptyStringAndNullFirst>
            <sortField>Status__c</sortField>
            <sortOrder>Asc</sortOrder>
        </sortOptions>
    </collectionProcessors>
    <decisions>
        <name>Check_RG_Score</name>
        <label>Check RG Score</label>
        <locationX>385</locationX>
        <locationY>721</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RG_Score_is_Greater_than_Threshold</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Score__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>Get_RG_Threshold.RG_Score__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>OpenCaseCount</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetOfferAceptedAttendees</targetReference>
            </connector>
            <label>RG Score is Greater than Threshold</label>
        </rules>
        <rules>
            <name>RG_Score_is_Less_than_Threshold</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Score__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>Get_RG_Threshold.RG_Score__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>OpenCaseCount</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Open_Cases</targetReference>
            </connector>
            <label>RG Score is Less than Threshold</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckAttendeesCollection</name>
        <label>CheckAttendeesCollection</label>
        <locationX>1199</locationX>
        <locationY>123</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>If_not_null</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>OfferAcceptedRecordCount</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>OfferPendingRecordCount</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_to_Combined_Collection</targetReference>
            </connector>
            <label>If not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Owner_on_Delegation</name>
        <label>Is Owner on Delegation</label>
        <locationX>1964</locationX>
        <locationY>369</locationY>
        <defaultConnector>
            <targetReference>CreateCase</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Not On Delegation</defaultConnectorLabel>
        <rules>
            <name>Is_On_Delegation</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetUser.Delegate_Assignment_Start_Date__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetUser.Delegate_Assignment_End_Date__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetUser.Delegate_Assignment_User_ID__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CreateCase_0</targetReference>
            </connector>
            <label>Is On Delegation</label>
        </rules>
    </decisions>
    <description>- Sends an email and creates an email when RG Score exceeds the threshold
- Checks if there any open cases</description>
    <formulas>
        <name>EventDetails</name>
        <dataType>String</dataType>
        <expression>IF(ISBLANK({!Check_Combined.Id}), &quot;&quot;,

HYPERLINK(LEFT({!$Api.Partner_Server_URL_340}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_340})) 
 &amp; {!Check_Combined.Id},  

{!Check_Combined.Campaign__r.Name} &amp; &quot; | &quot; &amp; 
TEXT(MONTH({!Check_Combined.Event_Date__c}))+&quot;/&quot; +TEXT(DAY({!Check_Combined.Event_Date__c}))+&quot;/&quot; +TEXT(YEAR({!Check_Combined.Event_Date__c})) &amp;

&quot; | &quot; &amp; TEXT({!Check_Combined.Status__c}) 

) &amp; BR()

)</expression>
    </formulas>
    <formulas>
        <name>LineBreak</name>
        <dataType>String</dataType>
        <expression>BR()</expression>
    </formulas>
    <interviewLabel>Create Case to RM if RG Score is Greater than Threshold {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Create Case to RM if RG Score is Greater than Threshold</label>
    <loops>
        <name>Check_Combined</name>
        <label>Check Combined</label>
        <locationX>1662</locationX>
        <locationY>284</locationY>
        <collectionReference>AttendeeCombinedCollection</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Values</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>GetUser</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Open_Cases</name>
        <label>Loop Open Cases</label>
        <locationX>392</locationX>
        <locationY>865</locationY>
        <collectionReference>Get_Open_Hospitality_Cases</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_New_Values</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Close_Cases</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>CreateCase</name>
        <label>CreateCase</label>
        <locationX>2136</locationX>
        <locationY>277</locationY>
        <connector>
            <targetReference>Send_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>$Record.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Refer to the Customer&apos;s Events tab to see the list of events associated with this customer.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Event_s_List__c</field>
            <value>
                <elementReference>CampaignList</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <stringValue>RG Score Alert</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>GetRecordTypeId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Follow Up Event and Hospitality - Customer RG Score is above threshold</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>CreateCase_0</name>
        <label>CreateCase</label>
        <locationX>2132</locationX>
        <locationY>446</locationY>
        <connector>
            <targetReference>Send_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>$Record.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Refer to the Customer&apos;s Events tab to see the list of events associated with this customer.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Event_s_List__c</field>
            <value>
                <elementReference>CampaignList</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <stringValue>RG Score Alert</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>GetUser.Delegate_Assignment_User_ID__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>GetRecordTypeId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Follow Up Event and Hospitality - Customer RG Score is above threshold</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Open_Hospitality_Cases</name>
        <label>Get Open Hospitality Cases</label>
        <locationX>188</locationX>
        <locationY>570</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetOpenCaseCount</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetRecordTypeId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_RG_Threshold</name>
        <label>Get RG Threshold</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Open_Hospitality_Cases</targetReference>
        </connector>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>System_Limits__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetOfferAceptedAttendees</name>
        <label>GetOfferAceptedAttendees</label>
        <locationX>704</locationX>
        <locationY>121</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetOfferPendingAttendees</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Is_Future_Open_Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <sortField>Event_Date__c</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetOfferPendingAttendees</name>
        <label>GetOfferPendingAttendees</label>
        <locationX>845</locationX>
        <locationY>121</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetRecordCount</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Is_Future_Open_Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Pending</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <sortField>Event_Date__c</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetRecordTypeId</name>
        <label>GetRecordTypeId</label>
        <locationX>177</locationX>
        <locationY>296</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_RG_Threshold</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Hospitality_Review</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetUser</name>
        <label>GetUser</label>
        <locationX>1879</locationX>
        <locationY>367</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Owner_on_Delegation</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Close_Cases</name>
        <label>Close Cases</label>
        <locationX>393</locationX>
        <locationY>1071</locationY>
        <inputReference>ClosedCases</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>46</locationY>
        <connector>
            <targetReference>GetRecordTypeId</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RG_Score__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>AttendeeCombinedCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
    <variables>
        <name>CampaignList</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ClosedCases</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>EventDate</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>inputAccount</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>OfferAcceptedRecordCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>OfferPendingRecordCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>OpenCaseCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>Status</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
