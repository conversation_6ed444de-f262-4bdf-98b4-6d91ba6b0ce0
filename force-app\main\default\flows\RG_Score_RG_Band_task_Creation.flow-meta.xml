<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Set status of Task to Closed by System</description>
        <name>Set_Task_to_Close_0</name>
        <label>Set Task to Close</label>
        <locationX>50</locationX>
        <locationY>971</locationY>
        <assignmentItems>
            <assignToReference>Get_Task.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Task.Description</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue> Task closed customer RG band has increased.</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Task_to_close_0_0_0_0_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Set status of Task to Closed by System</description>
        <name>Set_Task_to_Close_0_0</name>
        <label>Set Task to Close</label>
        <locationX>446</locationX>
        <locationY>971</locationY>
        <assignmentItems>
            <assignToReference>Get_Task.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Task.Description</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue> Task closed customer RG band has increased.</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Task_to_close_0_0_0_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Set status of Task to Closed by System</description>
        <name>Set_Task_to_Close_0_0_0</name>
        <label>Set Task to Close</label>
        <locationX>974</locationX>
        <locationY>971</locationY>
        <assignmentItems>
            <assignToReference>Get_Task.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Task.Description</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue> Task closed customer RG band has increased.</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Task_to_close_0_0_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Set status of Task to Closed by System</description>
        <name>Set_Task_to_Close_0_0_0_0</name>
        <label>Set Task to Close</label>
        <locationX>2030</locationX>
        <locationY>1679</locationY>
        <assignmentItems>
            <assignToReference>Get_Task.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Task.Description</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue> Task closed customer RG band has decreased.</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Task_to_close_0_0_0_0_0_0_0_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Set status of Task to Closed by System</description>
        <name>Set_Task_to_Close_0_0_0_0_0</name>
        <label>Set Task to Close</label>
        <locationX>1502</locationX>
        <locationY>971</locationY>
        <assignmentItems>
            <assignToReference>Get_Task.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Task.Description</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue> Task closed customer RG band has increased.</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Task_to_close_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Set status of Task to Closed by System</description>
        <name>Set_Task_to_Close_0_0_0_0_0_0</name>
        <label>Set Task to Close</label>
        <locationX>2030</locationX>
        <locationY>971</locationY>
        <assignmentItems>
            <assignToReference>Get_Task.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Task.Description</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue> Task closed customer RG band has increased.</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Task_to_close</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Set status of Task to Closed by System</description>
        <name>Set_Task_to_Close_0_0_0_0_0_0_0</name>
        <label>Set Task to Close</label>
        <locationX>2558</locationX>
        <locationY>971</locationY>
        <assignmentItems>
            <assignToReference>Get_Task.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Task.Description</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue> Task closed customer RG band has increased.</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Task_to_close_0_0_0_0_0_0</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>ActiveCustomer</name>
        <label>ActiveCustomer</label>
        <locationX>11534</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_Critical_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Critical_Action_0</targetReference>
            </connector>
            <label>Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Conditions</name>
        <label>Conditions</label>
        <locationX>2030</locationX>
        <locationY>863</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Minimal_Risk</name>
            <conditionLogic>1 AND 2 AND 3</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0</targetReference>
            </connector>
            <label>Minimal Risk SC3</label>
        </rules>
        <rules>
            <name>Below_Low_Risk</name>
            <conditionLogic>1 AND 2 AND (3 OR 4)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0</targetReference>
            </connector>
            <label>Below Low Risk SC5</label>
        </rules>
        <rules>
            <name>Low_Risks</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0</targetReference>
            </connector>
            <label>Low Risk SC6</label>
        </rules>
        <rules>
            <name>Medium_Risk</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0_0_0</targetReference>
            </connector>
            <label>Medium Risk SC7</label>
        </rules>
        <rules>
            <name>High_Risk</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0_0_0_0</targetReference>
            </connector>
            <label>High Risk SC8</label>
        </rules>
        <rules>
            <name>Critical</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7 OR 8)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0_0_0_0_0</targetReference>
            </connector>
            <label>Critical SC9</label>
        </rules>
        <rules>
            <name>to_High_Risk</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0_0</targetReference>
            </connector>
            <label>to High Risk</label>
        </rules>
        <rules>
            <name>to_Medium_Risk</name>
            <conditionLogic>1 AND 2 AND (3 OR 4)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0_0</targetReference>
            </connector>
            <label>to Medium Risk</label>
        </rules>
        <rules>
            <name>to_Low_Risk</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0_0</targetReference>
            </connector>
            <label>to Low Risk</label>
        </rules>
        <rules>
            <name>to_below_Low_Risk</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0_0</targetReference>
            </connector>
            <label>to below Low Risk</label>
        </rules>
        <rules>
            <name>to_Minimal_Risk</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0_0</targetReference>
            </connector>
            <label>to Minimal Risk</label>
        </rules>
        <rules>
            <name>close_task_0_5</name>
            <conditionLogic>7 AND 8 AND (1 OR 2 OR 3 OR 4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Task_to_Close_0_0_0_0</targetReference>
            </connector>
            <label>close task &lt;0.5</label>
        </rules>
    </decisions>
    <decisions>
        <name>Conditions_0</name>
        <label>Conditions</label>
        <locationX>10478</locationX>
        <locationY>863</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Minimal_Risk_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Has_Closed_Task</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Minimal Risk SC3</label>
        </rules>
        <rules>
            <name>Below_Low_Risk_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_4_of_ActiveCustomer</targetReference>
            </connector>
            <label>Below Low Risk SC5</label>
        </rules>
        <rules>
            <name>Low_Risks_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_3_of_ActiveCustomer</targetReference>
            </connector>
            <label>Low Risk SC6</label>
        </rules>
        <rules>
            <name>Medium_Risk_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_ActiveCustomer</targetReference>
            </connector>
            <label>Medium Risk SC7</label>
        </rules>
        <rules>
            <name>High_Risk_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_ActiveCustomer</targetReference>
            </connector>
            <label>High Risk SC8</label>
        </rules>
        <rules>
            <name>Critical_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7 OR 8)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ActiveCustomer</targetReference>
            </connector>
            <label>Critical SC9</label>
        </rules>
    </decisions>
    <decisions>
        <name>Conditions_0_0_0</name>
        <label>Conditions</label>
        <locationX>6518</locationX>
        <locationY>863</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Minimal_Risk_0_0_0</name>
            <conditionLogic>1 AND 2 AND 3</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Minimal Risk SC3</label>
        </rules>
        <rules>
            <name>Below_Low_Risk_0_0_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_9_of_ActiveCustomer</targetReference>
            </connector>
            <label>Below Low Risk SC5</label>
        </rules>
        <rules>
            <name>Low_Risks_0_0_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_8_of_ActiveCustomer</targetReference>
            </connector>
            <label>Low Risk SC6</label>
        </rules>
        <rules>
            <name>Medium_Risk_0_0_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_7_of_ActiveCustomer</targetReference>
            </connector>
            <label>Medium Risk SC7</label>
        </rules>
        <rules>
            <name>High_Risk_0_0_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_6_of_ActiveCustomer</targetReference>
            </connector>
            <label>High Risk SC8</label>
        </rules>
        <rules>
            <name>Critical_0_0_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7 OR 8)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_5_of_ActiveCustomer</targetReference>
            </connector>
            <label>Critical SC9</label>
        </rules>
        <rules>
            <name>to_High_Risk_0_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <label>to High Risk</label>
        </rules>
        <rules>
            <name>to_Medium_Risk_0_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <label>to Medium Risk</label>
        </rules>
        <rules>
            <name>to_Low_Risk_0_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <label>to Low Risk</label>
        </rules>
        <rules>
            <name>to_below_Low_Risk_0_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <label>to below Low Risk</label>
        </rules>
        <rules>
            <name>to_Minimal_Risk_0_0</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <label>to Minimal Risk</label>
        </rules>
        <rules>
            <name>close_task_0_5_0_0</name>
            <conditionLogic>7 AND 8 AND (1 OR 2 OR 3 OR 4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Minimal Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Below Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Low Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Medium Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RG_Band__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Critical Risk</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RG_Band__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>close task &lt;0.5</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_10_of_ActiveCustomer</name>
        <label>Copy 10 of ActiveCustomer</label>
        <locationX>2558</locationX>
        <locationY>1187</locationY>
        <defaultConnector>
            <targetReference>Copy_2_of_Critical_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_10_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Critical_Action</targetReference>
            </connector>
            <label>Copy 10 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_11_of_ActiveCustomer</name>
        <label>Copy 11 of ActiveCustomer</label>
        <locationX>2030</locationX>
        <locationY>1187</locationY>
        <defaultConnector>
            <targetReference>Copy_2_of_High_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_11_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>High_Risk_Action</targetReference>
            </connector>
            <label>Copy 11 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_12_of_ActiveCustomer</name>
        <label>Copy 12 of ActiveCustomer</label>
        <locationX>1502</locationX>
        <locationY>1187</locationY>
        <defaultConnector>
            <targetReference>Copy_2_of_Medium_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_12_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Medium_Risk_Action</targetReference>
            </connector>
            <label>Copy 12 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_13_of_ActiveCustomer</name>
        <label>Copy 13 of ActiveCustomer</label>
        <locationX>974</locationX>
        <locationY>1187</locationY>
        <defaultConnector>
            <targetReference>Copy_2_of_Low_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_13_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Low_Risk_Action</targetReference>
            </connector>
            <label>Copy 13 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_14_of_ActiveCustomer</name>
        <label>Copy 14 of ActiveCustomer</label>
        <locationX>446</locationX>
        <locationY>1187</locationY>
        <defaultConnector>
            <targetReference>Copy_2_of_Below_Low_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_14_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Below_Low_Risk_Action</targetReference>
            </connector>
            <label>Copy 14 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_ActiveCustomer</name>
        <label>Copy 1 of ActiveCustomer</label>
        <locationX>11006</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_High_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>High_Risk_Action_0</targetReference>
            </connector>
            <label>Copy 1 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_2_of_ActiveCustomer</name>
        <label>Copy 2 of ActiveCustomer</label>
        <locationX>10478</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_Medium_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_2_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Medium_Risk_Action_0</targetReference>
            </connector>
            <label>Copy 2 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_3_of_ActiveCustomer</name>
        <label>Copy 3 of ActiveCustomer</label>
        <locationX>9950</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_Low_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_3_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Low_Risk_Action_0</targetReference>
            </connector>
            <label>Copy 3 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_4_of_ActiveCustomer</name>
        <label>Copy 4 of ActiveCustomer</label>
        <locationX>9422</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_Below_Low_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_4_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Below_Low_Risk_Action_0</targetReference>
            </connector>
            <label>Copy 4 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_5_of_ActiveCustomer</name>
        <label>Copy 5 of ActiveCustomer</label>
        <locationX>6782</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_3_of_Critical_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_5_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Critical_Action_0_0</targetReference>
            </connector>
            <label>Copy 5 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_6_of_ActiveCustomer</name>
        <label>Copy 6 of ActiveCustomer</label>
        <locationX>6254</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_3_of_High_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_6_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>High_Risk_Action_0_0</targetReference>
            </connector>
            <label>Copy 6 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_7_of_ActiveCustomer</name>
        <label>Copy 7 of ActiveCustomer</label>
        <locationX>5726</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_3_of_Medium_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_7_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Medium_Risk_Action_0_0</targetReference>
            </connector>
            <label>Copy 7 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_8_of_ActiveCustomer</name>
        <label>Copy 8 of ActiveCustomer</label>
        <locationX>5198</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_3_of_Low_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_8_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Low_Risk_Action_0_0</targetReference>
            </connector>
            <label>Copy 8 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_9_of_ActiveCustomer</name>
        <label>Copy 9 of ActiveCustomer</label>
        <locationX>4670</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Copy_3_of_Below_Low_Risk_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_9_of_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Below_Low_Risk_Action_0_0</targetReference>
            </connector>
            <label>Copy 9 of Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Has_Task</name>
        <label>Has Task</label>
        <locationX>6254</locationX>
        <locationY>755</locationY>
        <defaultConnector>
            <targetReference>Conditions_0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Task is Null</defaultConnectorLabel>
        <rules>
            <name>Has_Existing_Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Task</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Conditions</targetReference>
            </connector>
            <label>Has Existing Task</label>
        </rules>
        <rules>
            <name>Has_Expired_Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Expired_Task</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Task</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Conditions_0_0_0</targetReference>
            </connector>
            <label>Has Expired Task</label>
        </rules>
    </decisions>
    <decisions>
        <name>Integration_User</name>
        <label>Integration User</label>
        <locationX>9224</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>End</defaultConnectorLabel>
        <rules>
            <name>Is_not_Integration_User_or_Inactive</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Owner.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>0052y000000He8UAAS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Owner.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>00528000004OfqPAAS</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Task</targetReference>
            </connector>
            <label>Is not Integration User or Inactive</label>
        </rules>
    </decisions>
    <description>Task creation Based on RG Score and RG Band Update SBET-342
Updated for SBET-138 - Create closed tasks for self excluded/inactive users</description>
    <environments>Default</environments>
    <formulas>
        <name>Dateplus3</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate} + 3</expression>
    </formulas>
    <interviewLabel>RG Score RG Band {!$Flow.CurrentDateTime}</interviewLabel>
    <label>RG Score RG Band task Creation</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Below_Low_Risk_Action</name>
        <label>Below Low Risk Action</label>
        <locationX>314</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Risk Band above 0.5</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Below_Low_Risk_Action_0</name>
        <label>Below Low Risk Action</label>
        <locationX>9290</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Risk Band above 0.5</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Below_Low_Risk_Action_0_0</name>
        <label>Below Low Risk Action</label>
        <locationX>4538</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Risk Band above 0.5</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_1_of_Below_Low_Risk_Action</name>
        <label>Copy 1 of Below Low Risk Action</label>
        <locationX>9554</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Risk Band above 0.5</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_1_of_Critical_Action</name>
        <label>Copy 1 of Critical Action</label>
        <locationX>11666</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Critical Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_1_of_High_Risk_Action</name>
        <label>Copy 1 of High Risk Action</label>
        <locationX>11138</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has High Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_1_of_Low_Risk_Action</name>
        <label>Copy 1 of Low Risk Action</label>
        <locationX>10082</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Risk Band above 0.75</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_1_of_Medium_Risk_Action</name>
        <label>Copy 1 of Medium Risk Action</label>
        <locationX>10610</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Risk Band above 0.75</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_2_of_Below_Low_Risk_Action</name>
        <label>Copy 2 of Below Low Risk Action</label>
        <locationX>578</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Risk Band above 0.5</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_2_of_Critical_Action</name>
        <label>Copy 2 of Critical Action</label>
        <locationX>2690</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Critical Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_2_of_High_Risk_Action</name>
        <label>Copy 2 of High Risk Action</label>
        <locationX>2162</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has High Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_2_of_Low_Risk_Action</name>
        <label>Copy 2 of Low Risk Action</label>
        <locationX>1106</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Risk Band above 0.75</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_2_of_Medium_Risk_Action</name>
        <label>Copy 2 of Medium Risk Action</label>
        <locationX>1634</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Risk Band above 0.8</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_3_of_Below_Low_Risk_Action</name>
        <label>Copy 3 of Below Low Risk Action</label>
        <locationX>4802</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Risk Band above 0.5</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_3_of_Critical_Action</name>
        <label>Copy 3 of Critical Action</label>
        <locationX>6914</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Critical Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_3_of_High_Risk_Action</name>
        <label>Copy 3 of High Risk Action</label>
        <locationX>6386</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has High Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_3_of_Low_Risk_Action</name>
        <label>Copy 3 of Low Risk Action</label>
        <locationX>5330</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Risk Band above 0.75</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Copy_3_of_Medium_Risk_Action</name>
        <label>Copy 3 of Medium Risk Action</label>
        <locationX>5858</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>InactiveUserDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI RG Alert - Customer has Risk Band above 0.8</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Critical_Action</name>
        <label>Critical Action</label>
        <locationX>2426</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Critical Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Critical_Action_0</name>
        <label>Critical Action</label>
        <locationX>11402</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Critical Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Critical_Action_0_0</name>
        <label>Critical Action</label>
        <locationX>6650</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Critical Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>High_Risk_Action</name>
        <label>High Risk Action</label>
        <locationX>1898</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has High Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>High_Risk_Action_0</name>
        <label>High Risk Action</label>
        <locationX>10874</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has High Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>High_Risk_Action_0_0</name>
        <label>High Risk Action</label>
        <locationX>6122</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has High Risk RG Score</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Low_Risk_Action</name>
        <label>Low Risk Action</label>
        <locationX>842</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Risk Band above 0.75</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Low_Risk_Action_0</name>
        <label>Low Risk Action</label>
        <locationX>9818</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Risk Band above 0.75</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Low_Risk_Action_0_0</name>
        <label>Low Risk Action</label>
        <locationX>5066</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Risk Band above 0.75</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Medium_Risk_Action</name>
        <label>Medium Risk Action</label>
        <locationX>1370</locationX>
        <locationY>1295</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Risk Band above 0.8</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Medium_Risk_Action_0</name>
        <label>Medium Risk Action</label>
        <locationX>10346</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Risk Band above 0.75</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Medium_Risk_Action_0_0</name>
        <label>Medium Risk Action</label>
        <locationX>5594</locationX>
        <locationY>1079</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>Dateplus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Review RG score &amp; RG dashboard. Do not apply any deposit matches to customer with RG score equal to or greater than 0.5. Marketing suppression has commenced customer is not to receive promotions, generosity, product launches etc. Customer will receive RG Campaign email.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>RG Alert - Customer has Risk Band above 0.8</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Expired_Task</name>
        <label>Get Expired Task</label>
        <locationX>6254</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Has_Closed_Task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Expired</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>RG Alert - Customer has</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Task</name>
        <label>Get Task</label>
        <locationX>6254</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Expired_Task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>RG Alert - Customer has</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Has_Closed_Task</name>
        <label>Has Closed Task</label>
        <locationX>6254</locationX>
        <locationY>647</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Has_Task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>RG Alert - Customer has</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Task_to_close</name>
        <label>Update Task to close</label>
        <locationX>2030</locationX>
        <locationY>1079</locationY>
        <connector>
            <targetReference>Copy_11_of_ActiveCustomer</targetReference>
        </connector>
        <inputReference>Get_Task</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Task_to_close_0</name>
        <label>Update Task to close</label>
        <locationX>1502</locationX>
        <locationY>1079</locationY>
        <connector>
            <targetReference>Copy_12_of_ActiveCustomer</targetReference>
        </connector>
        <inputReference>Get_Task</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Task_to_close_0_0_0</name>
        <label>Update Task to close</label>
        <locationX>974</locationX>
        <locationY>1079</locationY>
        <connector>
            <targetReference>Copy_13_of_ActiveCustomer</targetReference>
        </connector>
        <inputReference>Get_Task</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Task_to_close_0_0_0_0</name>
        <label>Update Task to close</label>
        <locationX>446</locationX>
        <locationY>1079</locationY>
        <connector>
            <targetReference>Copy_14_of_ActiveCustomer</targetReference>
        </connector>
        <inputReference>Get_Task</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Task_to_close_0_0_0_0_0</name>
        <label>Update Task to close</label>
        <locationX>50</locationX>
        <locationY>1079</locationY>
        <inputReference>Get_Task</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Task_to_close_0_0_0_0_0_0</name>
        <label>Update Task to close</label>
        <locationX>2558</locationX>
        <locationY>1079</locationY>
        <connector>
            <targetReference>Copy_10_of_ActiveCustomer</targetReference>
        </connector>
        <inputReference>Get_Task</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Task_to_close_0_0_0_0_0_0_0_0</name>
        <label>Update Task to close</label>
        <locationX>2030</locationX>
        <locationY>1787</locationY>
        <inputReference>Get_Task</inputReference>
    </recordUpdates>
    <stages>
        <name>End</name>
        <isActive>false</isActive>
        <label>End</label>
        <stageOrder>1</stageOrder>
    </stages>
    <start>
        <locationX>9098</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Integration_User</targetReference>
        </connector>
        <object>Account</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>InactiveUserDescription</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Customer is Inactive. Task closed by System. </stringValue>
        </value>
    </variables>
</Flow>
