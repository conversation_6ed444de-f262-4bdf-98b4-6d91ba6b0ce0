<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Get_Count</name>
        <label>Get Count</label>
        <locationX>307</locationX>
        <locationY>443</locationY>
        <assignmentItems>
            <assignToReference>GuestAttendeeCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_All_Guest_Attendee</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_How_Many_Guest_Left_Can_Be_Added</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Attended</name>
        <choiceText>Attended</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Attended</stringValue>
        </value>
    </choices>
    <choices>
        <name>Cancelled</name>
        <choiceText>Cancelled</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Cancelled</stringValue>
        </value>
    </choices>
    <choices>
        <name>NoOfferMade</name>
        <choiceText>No Offer Made</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>No Offer Made</stringValue>
        </value>
    </choices>
    <choices>
        <name>NoShow</name>
        <choiceText>No Show</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>No Show</stringValue>
        </value>
    </choices>
    <choices>
        <name>OfferAccepted</name>
        <choiceText>Offer Accepted</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Offer Accepted</stringValue>
        </value>
    </choices>
    <choices>
        <name>OfferDeclined</name>
        <choiceText>Offer Declined</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Offer Declined</stringValue>
        </value>
    </choices>
    <choices>
        <name>OfferPending</name>
        <choiceText>Offer Pending</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Offer Pending</stringValue>
        </value>
    </choices>
    <decisions>
        <name>Check_How_Many_Guest_Left_Can_Be_Added</name>
        <label>Check How Many Guest Left Can Be Added</label>
        <locationX>446</locationX>
        <locationY>443</locationY>
        <defaultConnector>
            <targetReference>Alert_Message</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Add_More</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>NumberofSeats</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>GuestAttendeeCount</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_Guest_Screen</targetReference>
            </connector>
            <label>Add More</label>
        </rules>
    </decisions>
    <dynamicChoiceSets>
        <name>GuestCheckPCS</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Guest_Check_Confirmed__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>IsSportsBetCustomerPCS</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Is_SportsBet_Customer__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>SelfExcludedSuspendedClosedPCS</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Self_Excluded_Suspended_Closed__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>StatusGuestAttendee</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Status__c</picklistField>
        <picklistObject>Attendee__c</picklistObject>
    </dynamicChoiceSets>
    <formulas>
        <name>ClickHere</name>
        <dataType>String</dataType>
        <expression>LEFT({!$Api.Partner_Server_URL_340}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_340})) 
 &amp; {!GuestAttendeeRecordId}</expression>
    </formulas>
    <formulas>
        <name>NumberofSeats</name>
        <dataType>Number</dataType>
        <expression>{!GetAttendee.Number_Of_Seats__c} - 1</expression>
        <scale>0</scale>
    </formulas>
    <formulas>
        <name>TODAY</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <interviewLabel>Add Guest Flow Mobile {!$Flow.CurrentDateTime}</interviewLabel>
    <isAdditionalPermissionRequiredToRun>true</isAdditionalPermissionRequiredToRun>
    <label>Add Guest Flow Mobile</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Guest_Attendee</name>
        <label>Create Guest Attendee</label>
        <locationX>866</locationX>
        <locationY>447</locationY>
        <assignRecordIdToReference>GuestAttendeeRecordId</assignRecordIdToReference>
        <connector>
            <targetReference>Get_Attendee_Record_Name</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Fault_Screen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Account__c</field>
            <value>
                <elementReference>GetAttendee.Account__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Campaign__c</field>
            <value>
                <elementReference>GetAttendee.Campaign__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_Invited__c</field>
            <value>
                <elementReference>Date_Invited</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_of_Birth__c</field>
            <value>
                <elementReference>Date_of_Birth</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email_Guest__c</field>
            <value>
                <elementReference>Guest_Email</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Guest_Check_Confirmed__c</field>
            <value>
                <elementReference>Guest_Check_Confirmed</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Guest_Name__c</field>
            <value>
                <elementReference>Guest_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Is_SportsBet_Customer__c</field>
            <value>
                <elementReference>Is_SportsBet_Customer</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Notes__c</field>
            <value>
                <elementReference>Notes</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Parent_Attendee__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Phone__c</field>
            <value>
                <elementReference>Phone.value</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>RecordTypeId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Self_Excluded_Suspended_Closed__c</field>
            <value>
                <elementReference>Self_Excluded_Suspended_Closed</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Sports_Bet_Account_Number__c</field>
            <value>
                <elementReference>Sportsbet_Account_Number</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <elementReference>Status</elementReference>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordCreates>
    <recordLookups>
        <name>Get_All_Guest_Attendee</name>
        <label>Get All Guest Attendee</label>
        <locationX>172</locationX>
        <locationY>443</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Count</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>RecordTypeId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Parent_Attendee__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Attendee_Record_Name</name>
        <label>Get Attendee Record Name</label>
        <locationX>867</locationX>
        <locationY>656</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>EndScreen2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GuestAttendeeRecordId</elementReference>
            </value>
        </filters>
        <object>Attendee__c</object>
        <outputAssignments>
            <assignToReference>GuestAttendeeName</assignToReference>
            <field>Name</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>Get_Record_Type_Id</name>
        <label>Get Record Type Id</label>
        <locationX>176</locationX>
        <locationY>197</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetAttendee</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attendee__c</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Guest_Attendee</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>RecordTypeId</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>GetAttendee</name>
        <label>GetAttendee</label>
        <locationX>177</locationX>
        <locationY>315</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_All_Guest_Attendee</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Add_Guest_Screen</name>
        <label>Add Guest Screen</label>
        <locationX>665</locationX>
        <locationY>447</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Create_Guest_Attendee</targetReference>
        </connector>
        <fields>
            <name>Information</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Information&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 14px;&quot;&gt;_________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>ParentAttendee</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(255, 0, 0);&quot;&gt;*&lt;/span&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; color: rgb(0, 0, 0);&quot;&gt;Parent Attendee&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 16px; color: rgb(0, 0, 0);&quot;&gt;{!GetAttendee.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Campaign</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;*&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Campaign&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 16px; background-color: rgb(255, 255, 255);&quot;&gt;{!GetAttendee.Campaign__r.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Status</name>
            <choiceReferences>NoOfferMade</choiceReferences>
            <choiceReferences>OfferPending</choiceReferences>
            <choiceReferences>OfferAccepted</choiceReferences>
            <choiceReferences>OfferDeclined</choiceReferences>
            <choiceReferences>Attended</choiceReferences>
            <choiceReferences>Cancelled</choiceReferences>
            <choiceReferences>NoShow</choiceReferences>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>GetAttendee.Status__c</elementReference>
            </defaultValue>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <helpText>&lt;p&gt;Populate to determine the Status in relation to this Event&lt;/p&gt;</helpText>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Date_Invited</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>GetAttendee.Date_Invited__c</elementReference>
            </defaultValue>
            <fieldText>Date Invited</fieldText>
            <fieldType>InputField</fieldType>
            <helpText>&lt;p&gt;Populate to capture the Date that the Customer was invited to the Event.&lt;/p&gt;</helpText>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Notes</name>
            <defaultValue>
                <stringValue>{!Notes}</stringValue>
            </defaultValue>
            <fieldText>Notes</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <helpText>&lt;p&gt;Populate any relevant Notes for this Attendee/Attendee&apos;s Guest(s). Eg. - Dietary Requirements, Weight (for Chopper) etc.&lt;/p&gt;</helpText>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Blank01</name>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>InteractionSection</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Guest Information&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; color: rgb(18, 106, 178);&quot;&gt;_________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Guest_Name</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>Guest_Name</elementReference>
            </defaultValue>
            <fieldText>Guest Name</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(62, 62, 60);&quot;&gt;This field can only accept up to 80 characters.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>LEN({!Guest_Name}) &lt;= 80</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>Guest_Email</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>Guest_Email</elementReference>
            </defaultValue>
            <fieldText>Guest Email</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 12px; color: rgb(234, 0, 30);&quot;&gt;Please enter a valid email address.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!Guest_Email},&quot;[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}&quot;)</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>Date_of_Birth</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>Date_of_Birth</elementReference>
            </defaultValue>
            <fieldText>Date of Birth</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;Guest cannot be under 18 years old for hosted event&lt;/p&gt;</errorMessage>
                <formulaExpression>TEXT({!GetAttendee.Campaign__r.Is_Hosted_Event__c}) = &quot;Yes&quot; &amp;&amp;

IF(MONTH({!TODAY}) &lt; MONTH({!Date_of_Birth}) || (MONTH({!TODAY}) = MONTH({!Date_of_Birth}) &amp;&amp; DAY({!TODAY}) &lt;= DAY({!Date_of_Birth})), YEAR({!TODAY}) - YEAR({!Date_of_Birth}),(YEAR({!TODAY}) - YEAR({!Date_of_Birth}))-1) &gt; 18</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>Phone</name>
            <extensionName>flowruntime:phone</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>Phone.value</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Guest_Check_Confirmed</name>
            <choiceReferences>GuestCheckPCS</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Guest Check Confirmed?</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Self_Excluded_Suspended_Closed</name>
            <choiceReferences>SelfExcludedSuspendedClosedPCS</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Self Excluded/ Suspended/ Closed?</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Is_SportsBet_Customer</name>
            <choiceReferences>IsSportsBetCustomerPCS</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Is SportsBet Customer</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Sportsbet_Account_Number</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>Sportsbet_Account_Number</elementReference>
            </defaultValue>
            <fieldText>Sportsbet Account Number</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Is_SportsBet_Customer</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Yes</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Alert_Message</name>
        <label>Alert Message</label>
        <locationX>454</locationX>
        <locationY>657</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>AlertEndScreen</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt;Note:&lt;/b&gt;&lt;span style=&quot;font-size: 12px; background-color: rgb(255, 255, 255);&quot;&gt; No more guests can be added to this customer attendee.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 14px;&quot;&gt;_________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>EndScreen2</name>
        <label>EndScreen</label>
        <locationX>873</locationX>
        <locationY>826</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>EndScreen</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 18px;&quot;&gt;Attendee {!GuestAttendeeName} was created.&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;color: rgb(18, 106, 178); font-size: 14px;&quot;&gt;_________________________________________&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 18px;&quot;&gt;Click &lt;/span&gt;&lt;a href=&quot;{!ClickHere}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot; style=&quot;color: rgb(0, 100, 199); font-size: 18px;&quot;&gt;&lt;b&gt;here&lt;/b&gt;&lt;/a&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 18px;&quot;&gt; to view your Guest Attendee record.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Fault_Screen</name>
        <label>Fault Screen</label>
        <locationX>1048</locationX>
        <locationY>446</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FaultMessage</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; color: rgb(18, 106, 178);&quot;&gt;Alert Message&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); font-size: 12px;&quot;&gt;Error:&lt;/b&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 12px;&quot;&gt; {!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;b style=&quot;font-size: 14px; color: rgb(18, 106, 178);&quot;&gt;_________________________________________&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>Get_Record_Type_Id</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>GuestAttendeeCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>GuestAttendeeName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>GuestAttendeeRecordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>RecordTypeId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
