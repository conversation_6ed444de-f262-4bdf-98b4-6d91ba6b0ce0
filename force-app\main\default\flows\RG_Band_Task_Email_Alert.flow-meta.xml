<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Task_email</name>
        <label>Task email</label>
        <locationX>50</locationX>
        <locationY>695</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>EmailTemplate</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>Get_Email.Email</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>$Record.Subject</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>WILL_SEND_EMAIL</name>
        <label>WILL SEND EMAIL?</label>
        <locationX>182</locationX>
        <locationY>575</locationY>
        <defaultConnectorLabel>IS OWNER</defaultConnectorLabel>
        <rules>
            <name>NOT_THE_OWNER</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CreatedById</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>$Record.Account.OwnerId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Task_email</targetReference>
            </connector>
            <label>NOT THE OWNER</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>CustomerName</name>
        <dataType>String</dataType>
        <expression>IF(ISNULL({!$Record.Account.FirstName}),  {!$Record.Account.LastName}, {!$Record.Account.FirstName} + &apos; &apos;+{!$Record.Account.LastName} )</expression>
    </formulas>
    <formulas>
        <name>RecordLink</name>
        <dataType>String</dataType>
        <expression>LEFT($Api.Enterprise_Server_URL_510, FIND( &apos;/services&apos;, $Api.Enterprise_Server_URL_510)) &amp; {!$Record.Id}</expression>
    </formulas>
    <interviewLabel>RG Band Task Email Alert {!$Flow.CurrentDateTime}</interviewLabel>
    <label>RG Band Task Email Alert</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Email</name>
        <label>Get Email</label>
        <locationX>182</locationX>
        <locationY>335</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getUserName</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getUserName</name>
        <label>getUserName</label>
        <locationX>182</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>WILL_SEND_EMAIL</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.CreatedById</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Email</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Subject</field>
            <operator>Contains</operator>
            <value>
                <stringValue>RG Alert</stringValue>
            </value>
        </filters>
        <object>Task</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
    <textTemplates>
        <name>EmailTemplate</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&lt;strong style=&quot;font-size: 15pt; font-family: Calibri, sans-serif; color: rgb(0, 102, 204);&quot;&gt;New Task&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(36, 36, 36); background-color: rgb(255, 255, 255)&quot;&gt;You have been assigned the following new task:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&amp;nbsp;Subject: {!$Record.Subject}&lt;/p&gt;&lt;p&gt;&amp;nbsp;Contact: &lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt; {!CustomerName}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&amp;nbsp;Customer:&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt; {!CustomerName}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&amp;nbsp;Due Date: {!$Record.ActivityDate}&lt;/p&gt;&lt;p&gt;&amp;nbsp;Priority: {!$Record.Priority}&lt;/p&gt;&lt;p&gt;&amp;nbsp;Comments: {!$Record.Description}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&amp;nbsp;For more details, click the following link:&lt;/p&gt;&lt;p&gt;{!RecordLink}&lt;/p&gt;</text>
    </textTemplates>
</Flow>
