<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Send notification to the Messaging Session Owner to notify them of any errors</description>
        <name>Send_Messaging_Error_Notification</name>
        <label>Send Messaging Error Notification</label>
        <locationX>154</locationX>
        <locationY>1440</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>Get_Messaging_Error_Message_Custom_Notification.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>varRecipientId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <stringValue>SMS Failed to Send</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>textNotificationBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
    </actionCalls>
    <actionCalls>
        <description>Send notification to the Messaging Session Owner to notify them that no actual messages were sent.</description>
        <name>Send_Messaging_Session_No_Conversation_Entries_Notification</name>
        <label>Send Messaging Session No Conversation Entries Notification</label>
        <locationX>682</locationX>
        <locationY>924</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>Get_Messaging_Session_No_Conversation_Entries_Custom_Notification_Type.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>varRecipientId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <stringValue>No SMS was sent during Messaging Session</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>textNoMessagesNotificationBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns error message based on error code from formula</description>
        <name>Assign_Error_Message</name>
        <label>Assign Error Message</label>
        <locationX>242</locationX>
        <locationY>1032</locationY>
        <assignmentItems>
            <assignToReference>varErrorMessage</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>formulaErrorMessageFromCode</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Conversation_Entries</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Counts the number of records in the filtered collection</description>
        <name>Assign_Filtered_Record_Count</name>
        <label>Assign Filtered Record Count</label>
        <locationX>286</locationX>
        <locationY>708</locationY>
        <assignmentItems>
            <assignToReference>varCountRecords</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Filter_Message_Code_Status</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Error_Records_Exist</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Session Owner User Id</description>
        <name>Assign_Recipient_Id</name>
        <label>Assign Recipient Id</label>
        <locationX>154</locationX>
        <locationY>1332</locationY>
        <assignmentItems>
            <assignToReference>varRecipientId</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$Record.Owner:User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Send_Messaging_Error_Notification</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Session Owner User Id</description>
        <name>Assign_Recipient_Id_No_Records_Path</name>
        <label>Assign Recipient Id No Records Path</label>
        <locationX>682</locationX>
        <locationY>816</locationY>
        <assignmentItems>
            <assignToReference>varRecipientId</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$Record.Owner:User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Send_Messaging_Session_No_Conversation_Entries_Notification</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Counts the number of records in the collection</description>
        <name>Assign_Record_Count</name>
        <label>Assign Record Count</label>
        <locationX>550</locationX>
        <locationY>384</locationY>
        <assignmentItems>
            <assignToReference>varCountRecords</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Conversation_Entries</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Records_Exist</targetReference>
        </connector>
    </assignments>
    <collectionProcessors>
        <description>Filter Collection to Only include Message Code Errors</description>
        <name>Filter_Message_Code_Status</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Message Code Status</label>
        <locationX>286</locationX>
        <locationY>600</locationY>
        <assignNextValueToReference>currentItem_Filter_Message_Code_Status</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_Conversation_Entries</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Message_Code_Status.MessageStatusCode</leftValueReference>
            <operator>IsNull</operator>
            <rightValue>
                <booleanValue>false</booleanValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Assign_Filtered_Record_Count</targetReference>
        </connector>
    </collectionProcessors>
    <decisions>
        <description>Are there any message error records</description>
        <name>Error_Records_Exist</name>
        <label>Error Records Exist</label>
        <locationX>286</locationX>
        <locationY>816</locationY>
        <defaultConnectorLabel>No Records</defaultConnectorLabel>
        <rules>
            <name>Error_Records</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varCountRecords</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Conversation_Entries</targetReference>
            </connector>
            <label>Error Records</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if the running user of this flow is active to ensure an active user receives the notification.</description>
        <name>Is_running_user_an_active_user</name>
        <label>Is not Automated Process user</label>
        <locationX>814</locationX>
        <locationY>600</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$User.Alias</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>autoproc</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Messaging_Session_No_Conversation_Entries_Custom_Notification_Type</targetReference>
            </connector>
            <label>Not Autoproc user</label>
        </rules>
    </decisions>
    <decisions>
        <description>Are there any Conversation Entry records</description>
        <name>Records_Exist</name>
        <label>Records Exist</label>
        <locationX>550</locationX>
        <locationY>492</locationY>
        <defaultConnector>
            <targetReference>Is_running_user_an_active_user</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Records</defaultConnectorLabel>
        <rules>
            <name>Records</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varCountRecords</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Filter_Message_Code_Status</targetReference>
            </connector>
            <label>Records</label>
        </rules>
    </decisions>
    <description>Used to check if there errors in any messages sent during a Messaging Session. Conversation Entries records are not created until after a Messaging Session ends, if there are errors, custom notification sent to Session Owner to notify them. Notification is also sent if there are no Conversation Entry records indicating no messages were sent.
SBET-1163 - Flow error - No active user to be assigned No Conversation Entries error message</description>
    <environments>Default</environments>
    <formulas>
        <description>Assigns error message based on error codes</description>
        <name>formulaErrorMessageFromCode</name>
        <dataType>String</dataType>
        <expression>CASE(
{!Loop_Conversation_Entries.MessageStatusCode},
&quot;0001&quot;, &quot;Can&apos;t contact the messaging service right now. Try again later.&quot;,
&quot;0002&quot;, &quot;Can&apos;t contact the messaging service right now. Try again later.&quot;,
&quot;0003&quot;, &quot;Can&apos;t contact the messaging service right now. Ask your admin to check the channel settings.&quot;,
&quot;0004&quot;, &quot;This message timed out. You can&apos;t contact this user until they contact you again.&quot;,
&quot;0005&quot;, &quot;Can&apos;t send messages on this channel right now. Try again later.&quot;,
&quot;0006&quot;, &quot;Can&apos;t send this message because another app is controlling the messaging session.&quot;,
&quot;0007&quot;, &quot;Can&apos;t send this message because your organisation violated the messaging service&apos;s policies. Ask your Salesforce admin for help.&quot;,
&quot;0008&quot;, &quot;Can&apos;t send this message. Try sending the information in a different format.&quot;,
&quot;1001&quot;, &quot;Can&apos;t send messages from this channel. Ask your admin to check the channel settings and contact Salesforce Customer Support.&quot;,
&quot;1002&quot;, &quot;Can&apos;t send messages from this channel. Ask your admin to check the channel settings and contact Salesforce Customer Support.&quot;,
&quot;1003&quot;, &quot;Can&apos;t send messages from this channel because the channel&apos;s phone number isn&apos;t verified. Ask your Salesforce admin to verify the number. Learn More&quot;,
&quot;1004&quot;, &quot;Can&apos;t send messages from this channel because the channel&apos;s phone number is blocked. Ask your Salesforce admin to contact Salesforce Customer Support.&quot;,
&quot;2001&quot;, &quot;Can&apos;t send messages to this number. Make sure that the contact information is correct and try again.&quot;,
&quot;2002&quot;, &quot;Can&apos;t send messages to this recipient because they blocked you.&quot;,
&quot;2004&quot;, &quot;Messages to this recipient are getting blocked by their carrier. Try contacting them another way.&quot;,
&quot;3001&quot;, &quot;Can&apos;t send this message because it contains invalid content. Try rewriting it.&quot;,
&quot;3002&quot;, &quot;Can&apos;t send attached files because the file types aren&apos;t supported.&quot;,
&quot;3003&quot;, &quot;Can&apos;t send this message because the message body is flagged as spam.&quot;,
&quot;3004&quot;, &quot;Can&apos;t send this message because the message body is too large.&quot;,
&quot;3005&quot;, &quot;Can&apos;t send this message because the attachment is too large.&quot;,
&quot;3006&quot;, &quot;Too many attachments. Try sending your attachments separately.&quot;,
&quot;3007&quot;, &quot;The message&apos;s intent is invalid.&quot;,
&quot;3008&quot;, &quot;This messaging component&apos;s associated message template isn&apos;t available for this language and locale. Try sending a different messaging component.&quot;,
&quot;3009&quot;, &quot;This messaging component and its associated message template contains different numbers of parameters. Ask your Salesforce admin to check the component settings.&quot;,
&quot;3010&quot;, &quot;Can&apos;t send this message because it contains invalid content. Try rewriting it.&quot;,
&quot;3011&quot;, &quot;You have too many message templates. Ask your admin to check your templates in WhatsApp Business Manager.&quot;,
&quot;3012&quot;, &quot;This messaging component&apos;s associated message template isn&apos;t available for this language and locale, or the message template isn&apos;t yet approved. Try sending a different messaging component.&quot;,
&quot;3013&quot;, &quot;This messaging component exceeds the text length limit. Ask your Salesforce admin to update the associated message template in WhatsApp Business Manager or verify that the component&apos;s parameters don&apos;t add too much text to the message.&quot;,
&quot;3014&quot;, &quot;The message template that&apos;s associated with this messaging component may violate WhatsApp policies. Ask your Salesforce admin to check the template in WhatsApp Business Manager.&quot;,
&quot;3015&quot;, &quot;Something&apos;s wrong with this messaging component&apos;s parameters. Ask your Salesforce admin to check the component settings in Setup.&quot;,
&quot;3016&quot;, &quot;The message template associated with this messaging component was disabled because of quality concerns. Try sending a different messaging component.&quot;,
&quot;9999&quot;, &quot;Can&apos;t send this message right now. If this happens again, ask your Salesforce admin to contact Salesforce Customer Support.&quot;,
&quot;Unknown Error&quot;
)</expression>
    </formulas>
    <interviewLabel>Messaging Session - Error Code Messages {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Messaging Session - On Update - Error Code Messages</label>
    <loops>
        <description>Loops through all conversation entry records that have error codes</description>
        <name>Loop_Conversation_Entries</name>
        <label>Loop Conversation Entries</label>
        <locationX>154</locationX>
        <locationY>924</locationY>
        <collectionReference>Filter_Message_Code_Status</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Error_Message</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Messaging_Error_Message_Custom_Notification</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets all the individual messages with an error code for this Messaging Session</description>
        <name>Get_Conversation_Entries</name>
        <label>Get Conversation Entries</label>
        <locationX>550</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Record_Count</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ConversationId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ConversationEntry</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets Messaging Error Message custom notification Id</description>
        <name>Get_Messaging_Error_Message_Custom_Notification</name>
        <label>Get Messaging Error Message Custom Notification</label>
        <locationX>154</locationX>
        <locationY>1224</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Recipient_Id</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Messaging_Error_Message</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets Messaging Session No Conversation Entries custom notification Id</description>
        <name>Get_Messaging_Session_No_Conversation_Entries_Custom_Notification_Type</name>
        <label>Get Messaging Session No Conversation Entries Custom Notification Type</label>
        <locationX>682</locationX>
        <locationY>708</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Recipient_Id_No_Records_Path</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Messaging_Session_No_Conversation_Entries</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Ended</stringValue>
            </value>
        </filters>
        <object>MessagingSession</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Get_Conversation_Entries</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
    <textTemplates>
        <description>Used in the body of the custom notification to send to user when no messages are present in Messaging Session</description>
        <name>textNoMessagesNotificationBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Hi {!$Record.Owner:User.FirstName}, There were no SMS sent or received during your Messaging Session with {!$Record.Customer_Name__c}

Please start a new conversation attempt to send again.</text>
    </textTemplates>
    <textTemplates>
        <description>Body for custom notification</description>
        <name>textNotificationBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Hi {!$Record.Owner:User.FirstName}, SMS failed to send to {!$Record.Customer_Name__c}
Error:{!varErrorMessage}
Please try again</text>
    </textTemplates>
    <variables>
        <name>currentItem_Filter_Message_Code_Status</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ConversationEntry</objectType>
    </variables>
    <variables>
        <description>Counts number of records</description>
        <name>varCountRecords</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>varErrorMessage</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores User Id from Messaging Session</description>
        <name>varRecipientId</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
