<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns number of filtered records in with Completed or Closed by System reviews</description>
        <name>Assign_Completed_or_Closed_By_System_Review_Record_Count</name>
        <label>Assign Completed or Closed By System Review Record Count</label>
        <locationX>1040</locationX>
        <locationY>600</locationY>
        <assignmentItems>
            <assignToReference>varNumberOfCompletedOrClosedReviewRecord</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Filter_Completed_or_Closed_By_System_Reviews</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>All_Rebase_Reviews_Completed_or_Closed_By_System</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the parent Rebase record Status to Completed</description>
        <name>Assign_Completed_Rebase_Status</name>
        <label>Assign Completed Rebase Status</label>
        <locationX>578</locationX>
        <locationY>1032</locationY>
        <assignmentItems>
            <assignToReference>varRebaseStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Rebase_Record_Status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the parent Rebase record Status to Performance Review</description>
        <name>Assign_Performance_Review_Rebase_Status</name>
        <label>Assign Performance Review Rebase Status</label>
        <locationX>842</locationX>
        <locationY>1032</locationY>
        <assignmentItems>
            <assignToReference>varRebaseStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Performance Review</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Rebase_Record_Status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the Review By field to the user that triggered the Flow</description>
        <name>Assign_Reviewed_By_User</name>
        <label>Assign Reviewed By User</label>
        <locationX>50</locationX>
        <locationY>276</locationY>
        <assignmentItems>
            <assignToReference>$Record.Reviewed_By__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Rebase_Review</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns number of records returned in Get Sibling Rebase Reviews</description>
        <name>Assign_Sibling_Rebase_Review_Record_Count</name>
        <label>Assign Sibling Rebase Review Record Count</label>
        <locationX>1040</locationX>
        <locationY>384</locationY>
        <assignmentItems>
            <assignToReference>varNumberOfSiblingRebaseReviewRecord</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Sibling_Rebase_Reviews</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Filter_Completed_or_Closed_By_System_Reviews</targetReference>
        </connector>
    </assignments>
    <collectionProcessors>
        <description>Filters collection of Rebase Review record to only include records with Completed or Closed By System status</description>
        <name>Filter_Completed_or_Closed_By_System_Reviews</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter Completed or Closed By System Reviews</label>
        <locationX>1040</locationX>
        <locationY>492</locationY>
        <assignNextValueToReference>currentItem_Filter_Completed_Reviews</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_Sibling_Rebase_Reviews</collectionReference>
        <conditionLogic>or</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Completed_Reviews.Rebase_Review_Status__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Completed</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_Filter_Completed_Reviews.Rebase_Review_Status__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Closed By System</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Assign_Completed_or_Closed_By_System_Review_Record_Count</targetReference>
        </connector>
    </collectionProcessors>
    <customErrors>
        <description>Display custom error message when Create Rebase Review Records fails</description>
        <name>Error_Message</name>
        <label>Error Message</label>
        <locationX>1106</locationX>
        <locationY>1440</locationY>
        <customErrorMessages>
            <errorMessage>There was a fault in the Rebase Review: All Rebase Reviews Completed

Flow Fault Message: {!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <description>Is the status for all of the Rebase Reviews records for the parent Rebase Completed or Closed By System?</description>
        <name>All_Rebase_Reviews_Completed_or_Closed_By_System</name>
        <label>All Rebase Reviews Completed or Closed By System?</label>
        <locationX>1040</locationX>
        <locationY>708</locationY>
        <defaultConnectorLabel>Not Completed</defaultConnectorLabel>
        <rules>
            <name>All_Completed_or_Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varNumberOfSiblingRebaseReviewRecord</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>varNumberOfCompletedOrClosedReviewRecord</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Customer_Outcome_Group_SG_CI_R_T</targetReference>
            </connector>
            <label>All Completed or Closed</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks if the Performance Team review is required. If formula logic in subflow evaluates RM Review as Agree and New Customer Group same as RB_Customer_Group__c then no Performance Team review is required</description>
        <name>Is_Performance_Team_Review_Required</name>
        <label>Is Performance Team Review Required?</label>
        <locationX>710</locationX>
        <locationY>924</locationY>
        <defaultConnector>
            <targetReference>Assign_Performance_Review_Rebase_Status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>PM Review Required</defaultConnectorLabel>
        <rules>
            <name>RM_Agree_No_PM_Review</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Assign_Customer_Outcome_Group_SG_CI_R_T.varNewCustomerGroupOutcome</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Record.Rebase_Record__r.RB_Customer_Group__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Relationship_Manager_Review__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agree</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Completed_Rebase_Status</targetReference>
            </connector>
            <label>RM Agree No PM Review</label>
        </rules>
    </decisions>
    <description>Used when a Rebase Review status is updated to Completed the Reviewed By field is updated. It checks all other sibling Rebase Reviews to see if they are also completed. If all are Completed then it applies logic for Customer Outcome Group and updates the parent Rebase record status to the Performance Team to do their final check</description>
    <environments>Default</environments>
    <interviewLabel>Rebase Review: All Rebase Reviews Completed {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rebase Review: All Rebase Reviews Completed</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets all the Rebase Reviews records for the parent Rebase record</description>
        <name>Get_Sibling_Rebase_Reviews</name>
        <label>Get Sibling Rebase Reviews</label>
        <locationX>1040</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Sibling_Rebase_Review_Record_Count</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Rebase_Record__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Rebase_Record__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Rebase_Review__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Rebase_Review_Status__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates the parent Rebase record status to Performance Review</description>
        <name>Update_Rebase_Record_Status</name>
        <label>Update Rebase Record Status</label>
        <locationX>710</locationX>
        <locationY>1224</locationY>
        <faultConnector>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Rebase_Record__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Outcome_Group__c</field>
            <value>
                <elementReference>Assign_Customer_Outcome_Group_SG_CI_R_T.varNewCustomerGroupOutcome</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rebase_Status__c</field>
            <value>
                <elementReference>varRebaseStatus</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Rebase_Record__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates Rebase Review record with assigned values</description>
        <name>Update_Rebase_Review</name>
        <label>Update Rebase Review</label>
        <locationX>50</locationX>
        <locationY>384</locationY>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>419</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assign_Reviewed_By_User</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Rebase_Review_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Rebase_Review_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Closed By System</stringValue>
            </value>
        </filters>
        <object>Rebase_Review__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Get_Sibling_Rebase_Reviews</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <description>Subflow that stores the formula for the rules and if else logic to assign value to the Customer Outcome Group following reviews for Safer Gambling, Customer Integrity and Risk &amp; Trade team</description>
        <name>Assign_Customer_Outcome_Group_SG_CI_R_T</name>
        <label>Assign Customer Outcome Group - SG, CI, R&amp;T</label>
        <locationX>710</locationX>
        <locationY>816</locationY>
        <connector>
            <targetReference>Is_Performance_Team_Review_Required</targetReference>
        </connector>
        <flowName>Rebase_Review_Subflow_Assign_Customer_Outcome_Group_SG_CI_R_T</flowName>
        <inputAssignments>
            <name>varCustomerIntegrityReview</name>
            <value>
                <elementReference>$Record.Rebase_Record__r.Customer_Integrity_Review__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varIftheywerenotRGBlocked</name>
            <value>
                <elementReference>$Record.Rebase_Record__r.If_they_were_not_RG_Blocked__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varRBNewCustomerGroup</name>
            <value>
                <elementReference>$Record.Rebase_Record__r.RB_Customer_Group__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varRiskTradeReview</name>
            <value>
                <elementReference>$Record.Rebase_Record__r.Risk_Trade_Review__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varSaferGamblingReview</name>
            <value>
                <elementReference>$Record.Rebase_Record__r.Safer_Gambling_Review__c</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <subflows>
        <description>Subflow to handle Flow Faults, sends email to Performance Team members</description>
        <name>Rebase_Flow_Fault_Handler</name>
        <label>Rebase Flow Fault Handler</label>
        <locationX>1106</locationX>
        <locationY>1332</locationY>
        <connector>
            <targetReference>Error_Message</targetReference>
        </connector>
        <flowName>Rebase_Flow_Fault_Handler</flowName>
        <inputAssignments>
            <name>varFlowCurrentDateTime</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowFaultMessage</name>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowName</name>
            <value>
                <stringValue>Rebase Review: All Rebase Reviews Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowUser</name>
            <value>
                <elementReference>$User.Username</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <triggerOrder>300</triggerOrder>
    <variables>
        <name>currentItem_Filter_Completed_Reviews</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Rebase_Review__c</objectType>
    </variables>
    <variables>
        <description>Stores number of Completed or or Closed By System Review records</description>
        <name>varNumberOfCompletedOrClosedReviewRecord</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <description>Stores number of Sibling Rebase Review records</description>
        <name>varNumberOfSiblingRebaseReviewRecord</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <description>Stores the value of the Rebase Status field to update</description>
        <name>varRebaseStatus</name>
        <dataType>Picklist</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
