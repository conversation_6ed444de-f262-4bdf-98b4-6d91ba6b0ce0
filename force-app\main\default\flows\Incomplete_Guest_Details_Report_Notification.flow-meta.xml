<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>EmailtoCampaignOwner</name>
        <label>EmailtoCampaignOwner</label>
        <locationX>50</locationX>
        <locationY>1092</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>OwnerEmail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>Subject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>EmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>58.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_HTML_row_to_list</name>
        <label>Add HTML row to list</label>
        <locationX>138</locationX>
        <locationY>684</locationY>
        <assignmentItems>
            <assignToReference>AttendeeList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>ListRow</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_Attendees</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>CloseList</name>
        <label>CloseList</label>
        <locationX>50</locationX>
        <locationY>876</locationY>
        <assignmentItems>
            <assignToReference>AttendeeList</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>&lt;/ul&gt;</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Campaign</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Event_Less_Than_5_Days_Away</name>
        <label>Event Less Than 5 Days Away</label>
        <locationX>182</locationX>
        <locationY>252</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>EventLessThan5DaysAway</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetAttendees</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>EventLessThan5DaysAway</name>
        <dataType>Boolean</dataType>
        <expression>IF((DATEVALUE({!$Record.Start_Date__c}) - TODAY()) = 5, TRUE, FALSE)</expression>
    </formulas>
    <interviewLabel>Incomplete Guest Details Report Notification {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Incomplete Guest Details Report Notification</label>
    <loops>
        <name>Loop_through_Attendees</name>
        <label>Loop through Attendees</label>
        <locationX>50</locationX>
        <locationY>576</locationY>
        <collectionReference>GetAttendees</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_HTML_row_to_list</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>CloseList</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Campaign_Owner_Email</name>
        <label>Get Campaign Owner Email</label>
        <locationX>50</locationX>
        <locationY>468</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_through_Attendees</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Owner.Id</elementReference>
            </value>
        </filters>
        <object>User</object>
        <outputAssignments>
            <assignToReference>OwnerEmail</assignToReference>
            <field>Email</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>GetAttendees</name>
        <label>GetAttendees</label>
        <locationX>50</locationX>
        <locationY>360</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Campaign_Owner_Email</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>0122y000000GqbLAAS</stringValue>
            </value>
        </filters>
        <filters>
            <field>Incomplete_Guest_Details__c</field>
            <operator>LessThan</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Campaign</name>
        <label>Update Campaign</label>
        <locationX>50</locationX>
        <locationY>984</locationY>
        <connector>
            <targetReference>EmailtoCampaignOwner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Attendee_List__c</field>
            <value>
                <elementReference>AttendeeList</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Event_Less_Than_5_Days_Away</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Is_Hosted_Event__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Campaign</object>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2023-10-01</startDate>
            <startTime>09:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>EmailBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;{!$Record.Name} will commence in 5 days. &lt;/p&gt;&lt;p&gt;The following customers still require their guest details to be collected for ticket allocation:&lt;/p&gt;&lt;p&gt;{!AttendeeList}&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>ListRow</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;li&gt;{!Loop_through_Attendees.Account__r.FirstName} {!Loop_through_Attendees.Account__r.LastName} - {!Loop_through_Attendees.Incomplete_Guest_Details__c} - {!Loop_through_Attendees.Customer_Owner__c}&lt;/li&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>Subject</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!$Record.Name} will commence in 5 days</text>
    </textTemplates>
    <variables>
        <name>AttendeeList</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>&lt;ul&gt; &lt;b&gt;Customer Name - Incomplete Details - Owner&lt;/b&gt;</stringValue>
        </value>
    </variables>
    <variables>
        <name>OwnerEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
