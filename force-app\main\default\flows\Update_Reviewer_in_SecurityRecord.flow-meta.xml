<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Checks whether IsReportReviewed__c field is updated</description>
        <name>IsReportReviewed_is_Updated</name>
        <label>IsReportReviewed is Updated?</label>
        <locationX>314</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsReportReviewed_updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.IsReportReviewed__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.IsReportReviewed__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Reviewer_in_SecurityRecord</targetReference>
            </connector>
            <label>IsReportReviewed updated</label>
        </rules>
        <rules>
            <name>isReportReviewed_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.IsReportReviewed__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.IsReportReviewed__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Reviewer_to_Null</targetReference>
            </connector>
            <label>isReportReviewed Null</label>
        </rules>
    </decisions>
    <description>Updates the Reviewer field based on IsReportReviewed field</description>
    <interviewLabel>Update Reviewer in SecurityRecord {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Reviewer in SecurityRecord</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Update Reviewer field in secruity record that triggered the flow</description>
        <name>Update_Reviewer_in_SecurityRecord</name>
        <label>Update Reviewer in SecurityRecord</label>
        <locationX>50</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Reviewer__c</field>
            <value>
                <elementReference>$Record.LastModifiedById</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Reviewer_to_Null</name>
        <label>Update Reviewer to Null</label>
        <locationX>314</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>Reviewer__c</field>
            <value>
                <stringValue></stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>IsReportReviewed_is_Updated</targetReference>
        </connector>
        <object>SecurityRecord__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
