/**
 * @description       : 
 * <AUTHOR> <PERSON><PERSON>C
 * @group             : 
 * @last modified on  : 05-29-2025
 * @last modified by  : jam<PERSON><PERSON><PERSON>@gmail.com
**/
({
    doInit : function(component, event, helper) {
      console.log('in doinit');
    },
    handleModal:function(component, event, helper) {
       console.log('in function : ',JSON.stringify(event.getParam('value')));
       const params = event.getParam('value');

       // Check if params exists and has data
       if (!params) {
           console.log('DEBUG: No params received - no customers selected');
           return;
       }

       console.log('DEBUG: params.accountIds:', params.accountIds);
       console.log('DEBUG: params.accountIds type:', typeof params.accountIds);
       console.log('DEBUG: params.accountIds length:', params.accountIds ? params.accountIds.length : 'undefined');
       const temp = component.get("v.isModalOpen");
       console.log('In modal function',temp);
       if (temp == true) {
        component.set("v.isModalOpen",false);

       } else {
        component.set("v.isModalOpen",true);
        var flow = component.find("addCustomerFlow");
        //Put input variable values
        var inputVariables = [
            {
                name : "recordId",
                type : "String",
                value :params.accountId
            }
        ];

        // Handle multi-selection by passing collection to flow
        if (params.accountIds && params.accountIds.length > 1) {
            console.log('DEBUG: Multi-selection detected, passing', params.accountIds.length, 'customers to flow');

            // Pass the collection as a delimited string (Flow limitation workaround)
            var accountIdsString = params.accountIds.join(',');
            console.log('DEBUG: accountIdsString:', accountIdsString);

            inputVariables.push({
                name: "accountIdsString",
                type: "String",
                value: accountIdsString
            });

            // Also pass the count for flow logic
            inputVariables.push({
                name: "isMultiSelection",
                type: "Boolean",
                value: true
            });

            console.log('DEBUG: Multi-selection input variables:', JSON.stringify(inputVariables));
        }

        if (params.campaignId) {
            inputVariables.push({
                name:"CampaignIdOutput",
                type:"String",
                value:params.campaignId
            })
        }
        //Reference flow's Unique Name
        flow.startFlow("Add_Attendee_Flow_Desktop", inputVariables);
       
       }
    },
    
    handleConnection:function(component, helper, event){
        console.log('in aura');
        component.find('guestlist').firedFromAura();
    },

    handleExclude : function(component, helper, event){
        console.log('in aura');
        component.find('guestlist').handleExclude();
    },
    handleflowstatuschange : function (component, event) {    
    //alert('event.getParam '+event.getParam('status'));
    debugger;
    console.log("event.getParam('status')=",event.getParam('status'));
    if(event.getParam('status') === "FINISHED") {
        component.set("v.isModalOpen",false);
        //$A.get("e.force:closeQuickAction").fire();
    }        
}
   
})