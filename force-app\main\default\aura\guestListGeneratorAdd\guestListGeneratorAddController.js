/**
 * @description       : 
 * <AUTHOR> <PERSON><PERSON>XC
 * @group             : 
 * @last modified on  : 05-29-2025
 * @last modified by  : jam<PERSON><PERSON><PERSON>@gmail.com
**/
({
    doInit : function(component, event, helper) {
      console.log('in doinit');
    },
    handleModal:function(component, event, helper) {
       console.log('in function : ',JSON.stringify(event.getParam('value')));
       const params = event.getParam('value');
       console.log('DEBUG: params.accountIds:', params.accountIds);
       console.log('DEBUG: params.accountIds type:', typeof params.accountIds);
       console.log('DEBUG: params.accountIds length:', params.accountIds ? params.accountIds.length : 'undefined');
       const temp = component.get("v.isModalOpen");
       console.log('In modal function',temp);
       if (temp == true) {
        component.set("v.isModalOpen",false);

       } else {
        component.set("v.isModalOpen",true);
        var flow = component.find("addCustomerFlow");
        //Put input variable values
        var inputVariables = [
            {
                name : "recordId",
                type : "String",
                value :params.accountId
            }
        ];

        // Add accountIds collection for multi-selection support
        if (params.accountIds && params.accountIds.length > 0) {
            console.log('DEBUG: Adding accountIds to flow input:', params.accountIds);
            console.log('DEBUG: accountIds length:', params.accountIds.length);

            // Try passing as collection using proper format
            inputVariables.push({
                name: "accountIds",
                type: "String",
                value: params.accountIds,
                isCollection: true
            });
        }

        if (params.campaignId) {
            inputVariables.push({
                name:"CampaignIdOutput",
                type:"String",
                value:params.campaignId
            })
        }
        //Reference flow's Unique Name
        flow.startFlow("Add_Attendee_Flow_Desktop", inputVariables);
       
       }
    },
    
    handleConnection:function(component, helper, event){
        console.log('in aura');
        component.find('guestlist').firedFromAura();
    },

    handleExclude : function(component, helper, event){
        console.log('in aura');
        component.find('guestlist').handleExclude();
    },
    handleflowstatuschange : function (component, event) {    
    //alert('event.getParam '+event.getParam('status'));
    debugger;
    console.log("event.getParam('status')=",event.getParam('status'));
    if(event.getParam('status') === "FINISHED") {
        component.set("v.isModalOpen",false);
        //$A.get("e.force:closeQuickAction").fire();
    }        
}
   
})