/**
 * @description       : 
 * <AUTHOR> <PERSON><PERSON>XC
 * @group             : 
 * @last modified on  : 05-29-2025
 * @last modified by  : jam<PERSON><PERSON><PERSON>@gmail.com
**/
({
    doInit : function(component, event, helper) {
      console.log('in doinit');
    },
    handleModal:function(component, event, helper) {
       console.log('in function : ',JSON.stringify(event.getParam('value')));
       const params = event.getParam('value');
       console.log('DEBUG: params.accountIds:', params.accountIds);
       console.log('DEBUG: params.accountIds type:', typeof params.accountIds);
       console.log('DEBUG: params.accountIds length:', params.accountIds ? params.accountIds.length : 'undefined');
       const temp = component.get("v.isModalOpen");
       console.log('In modal function',temp);
       if (temp == true) {
        component.set("v.isModalOpen",false);

       } else {
        component.set("v.isModalOpen",true);
        var flow = component.find("addCustomerFlow");
        //Put input variable values
        var inputVariables = [
            {
                name : "recordId",
                type : "String",
                value :params.accountId
            }
        ];

        // Handle multi-selection by calling flow multiple times
        if (params.accountIds && params.accountIds.length > 1) {
            console.log('DEBUG: Multi-selection detected, processing', params.accountIds.length, 'customers');

            // Process each customer individually
            for (var i = 0; i < params.accountIds.length; i++) {
                var singleInputVariables = [
                    {
                        name: "recordId",
                        type: "String",
                        value: params.accountIds[i]
                    }
                ];

                if (params.campaignId) {
                    singleInputVariables.push({
                        name: "CampaignIdOutput",
                        type: "String",
                        value: params.campaignId
                    });
                }

                console.log('DEBUG: Starting flow for customer', i + 1, 'ID:', params.accountIds[i]);

                // Start flow for each customer
                flow.startFlow("Add_Attendee_Flow_Desktop", singleInputVariables);
            }

            // Exit early since we've processed all customers
            return;
        }

        if (params.campaignId) {
            inputVariables.push({
                name:"CampaignIdOutput",
                type:"String",
                value:params.campaignId
            })
        }
        //Reference flow's Unique Name
        flow.startFlow("Add_Attendee_Flow_Desktop", inputVariables);
       
       }
    },
    
    handleConnection:function(component, helper, event){
        console.log('in aura');
        component.find('guestlist').firedFromAura();
    },

    handleExclude : function(component, helper, event){
        console.log('in aura');
        component.find('guestlist').handleExclude();
    },
    handleflowstatuschange : function (component, event) {    
    //alert('event.getParam '+event.getParam('status'));
    debugger;
    console.log("event.getParam('status')=",event.getParam('status'));
    if(event.getParam('status') === "FINISHED") {
        component.set("v.isModalOpen",false);
        //$A.get("e.force:closeQuickAction").fire();
    }        
}
   
})