<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assign Sportsbet Acct No to Task</description>
        <name>Assign_Sportsbet_Account_Number_From_Account</name>
        <label>Assign Sportsbet Account Number From Account</label>
        <locationX>176</locationX>
        <locationY>492</locationY>
        <assignmentItems>
            <assignToReference>$Record.Sportsbet_Acct_No__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Account_Record.Sports_Bet_Account_Number__c</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Assign Sportsbet Acct No to Task</description>
        <name>Assign_Sportsbet_Account_Number_From_Contact</name>
        <label>Assign Sportsbet Account Number From Contact</label>
        <locationX>440</locationX>
        <locationY>492</locationY>
        <assignmentItems>
            <assignToReference>$Record.Sportsbet_Acct_No__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Contact_Record.Account.Sports_Bet_Account_Number__c</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <decisions>
        <description>Checks if the WhatId is an Account or Contact record</description>
        <name>WhatId_is_Account_or_Contact</name>
        <label>WhatId is Account or Contact</label>
        <locationX>440</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>None</defaultConnectorLabel>
        <rules>
            <name>Account</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>001</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Account_Record</targetReference>
            </connector>
            <label>Account</label>
        </rules>
        <rules>
            <name>Contact</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>003</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Contact_Record</targetReference>
            </connector>
            <label>Contact</label>
        </rules>
    </decisions>
    <description>Assigns the Sportsbet Acct No from WhatId using Account or Contact</description>
    <environments>Default</environments>
    <interviewLabel>Task: Sportsbet Acct No Assign From WhatId {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Task: Sportsbet Acct No Assign From WhatId</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the Account Record</description>
        <name>Get_Account_Record</name>
        <label>Get Account Record</label>
        <locationX>176</locationX>
        <locationY>384</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Sportsbet_Account_Number_From_Account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Sports_Bet_Account_Number__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Contact Record</description>
        <name>Get_Contact_Record</name>
        <label>Get Contact Record</label>
        <locationX>440</locationX>
        <locationY>384</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Sportsbet_Account_Number_From_Contact</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>AccountId</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterFormula>AND(
  ISBLANK({!$Record.Sportsbet_Acct_No__c}), 
  NOT(ISBLANK({!$Record.WhatId})),
  OR(
    BEGINS({!$Record.WhatId}, &quot;001&quot;), 
    BEGINS({!$Record.WhatId}, &quot;003&quot;)
  )
)</filterFormula>
        <object>Task</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>WhatId_is_Account_or_Contact</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
