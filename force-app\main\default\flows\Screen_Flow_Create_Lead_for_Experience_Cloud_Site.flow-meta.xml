<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assign the Lead Owner ID as the Campaign owner once a Campaign is retrieved</description>
        <name>Assign_Lead_Owner_ID</name>
        <label>Assign Lead Owner ID</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>leadOwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Campaign.OwnerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>RegisterScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign the ID of the SysAdmin user as the lead owner for leads created without campaigns attached</description>
        <name>Assign_Queue_as_Lead_Owner</name>
        <label>Assign SysAdmin as Lead Owner</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>leadOwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_SysAdmin_User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>RegisterScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign the recordID as null if a campaign can&apos;t be found to ensure the correct welcome message is displayed</description>
        <name>Clear_Input_RecordID</name>
        <label>Clear Input Record ID</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>recordId</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_SysAdmin_User</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Check if the recordID is null from the flow inputs</description>
        <name>Is_there_an_associated_Campaign</name>
        <label>Is there an associated Campaign?</label>
        <locationX>314</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>Get_SysAdmin_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Variable_is_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Campaign</targetReference>
            </connector>
            <label>Variable is not null</label>
        </rules>
    </decisions>
    <description>SBET-1117 Post-Demo Form Enhancements (1)</description>
    <dynamicChoiceSets>
        <name>existingCustomerChoice</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>QR_Are_you_a_customer__c</picklistField>
        <picklistObject>Lead</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>optInPicklistChoice</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>QR_Opt_In_to_Comms__c</picklistField>
        <picklistObject>Lead</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <description>Date 18 years ago from today to validate that form entrants are over the age of 18</description>
        <name>eighteenYearsAgoDate</name>
        <dataType>Date</dataType>
        <expression>TODAY() - (365 * 18)</expression>
    </formulas>
    <interviewLabel>Screen Flow - Create Lead (for Experience Cloud Site) {!$Flow.CurrentDateTime}</interviewLabel>
    <isAdditionalPermissionRequiredToRun>true</isAdditionalPermissionRequiredToRun>
    <label>Screen Flow - Create Lead (for Experience Cloud Site)</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Create a lead with the information provided on the RegisterScreen now that all validation rules have passed.</description>
        <name>Create_Lead</name>
        <label>Create Lead</label>
        <locationX>314</locationX>
        <locationY>740</locationY>
        <connector>
            <targetReference>SuccessScreen</targetReference>
        </connector>
        <faultConnector>
            <targetReference>errorScreen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Campaign__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_of_Birth__c</field>
            <value>
                <elementReference>Date_of_Birth</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FirstName</field>
            <value>
                <elementReference>First_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LastName</field>
            <value>
                <elementReference>Last_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MobilePhone</field>
            <value>
                <elementReference>phoneField.value</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>leadOwnerId</elementReference>
            </value>
        </inputAssignments>
        <object>Lead</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <description>Get the associated campaign that is related to the recordId input for the flow</description>
        <name>Get_Campaign</name>
        <label>Get Campaign</label>
        <locationX>50</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Lead_Owner_ID</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Clear_Input_RecordID</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Campaign</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the ID of the SysAdmin User (i.e. default owner of leads).</description>
        <name>Get_SysAdmin_User</name>
        <label>Get SysAdmin User</label>
        <locationX>578</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Queue_as_Lead_Owner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Alias</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>sadmi</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <description>Screen for any errors that occur in the process</description>
        <name>errorScreen</name>
        <label>Error Screen</label>
        <locationX>842</locationX>
        <locationY>848</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>errorText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 20px; color: rgb(0, 0, 0);&quot;&gt;An error has occurred.&lt;/strong&gt;&lt;strong style=&quot;font-size: 20px; color: rgb(255, 0, 0);&quot;&gt; &lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen to be presented for Registration at partnerships events</description>
        <name>RegisterScreen</name>
        <label>RegisterScreen</label>
        <locationX>314</locationX>
        <locationY>632</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Create_Lead</targetReference>
        </connector>
        <fields>
            <name>formTitle</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 24px;&quot;&gt;{!Get_Campaign.Form_Title__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>recordId</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Get_Campaign.Form_Title__c</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>instructionStandard</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;To hear from Sportsbet regarding event invitations, experiences and special offers, please provide the below details.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>recordId</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Get_Campaign.Form_Instructions__c</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>formInstructionsVariable</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;{!Get_Campaign.Form_Instructions__c}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>recordId</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Get_Campaign.Form_Instructions__c</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>infoTextNullCampaign</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 24px;&quot;&gt;Welcome&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;To hear from Sportsbet regarding event invitations, experiences and special offers, please provide the below details.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>recordId</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>First_Name</name>
            <dataType>String</dataType>
            <fieldText>First Name</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Last_Name</name>
            <dataType>String</dataType>
            <fieldText>Last Name</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Date_of_Birth</name>
            <dataType>Date</dataType>
            <fieldText>Date of Birth</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;You must be over the age of 18 to register for Sportsbet offers. &lt;/p&gt;</errorMessage>
                <formulaExpression>{!Date_of_Birth} &lt;={!eighteenYearsAgoDate}</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>phoneField</name>
            <extensionName>flowruntime:phone</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Mobile (Please use mobile linked to your Sportsbet account)</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <validationRule>
                <errorMessage>&lt;p&gt;Please enter a valid mobile number, starting with +61 (country code) or 0.&lt;/p&gt;</errorMessage>
                <formulaExpression>AND(
  NOT(ISBLANK({!phoneField.value})),
  REGEX({!phoneField.value}, &quot;^(\\+\\d{1,3}[0-9]{7,12}|0[0-9]{9})$&quot;)
)</formulaExpression>
            </validationRule>
        </fields>
        <nextOrFinishButtonLabel>Register</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen for showing success in creating a lead record</description>
        <name>SuccessScreen</name>
        <label>Success Screen</label>
        <locationX>314</locationX>
        <locationY>848</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>successText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;﻿Thankyou&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;You can close this screen now&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_there_an_associated_Campaign</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>blankValue</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>leadOwnerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to assist in displaying the error message on the screen</description>
        <name>showErrorMessage</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
</Flow>
