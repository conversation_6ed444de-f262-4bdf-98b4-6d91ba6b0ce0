<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Customer</name>
        <label>Assign Customer</label>
        <locationX>578</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>Get_End_User_Record.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_End_User_Record.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Records_1</targetReference>
        </connector>
    </assignments>
    <customErrors>
        <name>Flow_Error</name>
        <label>Flow Error</label>
        <locationX>314</locationX>
        <locationY>755</locationY>
        <customErrorMessages>
            <errorMessage>An Error Occurred when trying to automatically create a messaging end user for this customer. 
Please review error and manually create the messaging end user record.
{!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Does_MessagingEndUser_Exist</name>
        <label>Does MessagingEndUser Exist</label>
        <locationX>446</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_End_User_Record</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Messaging_Chanel</targetReference>
            </connector>
            <label>No</label>
        </rules>
        <rules>
            <name>end_user_exists_and_is_not_linked_to_customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_End_User_Record.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Customer</targetReference>
            </connector>
            <label>end user exists and is not linked to customer</label>
        </rules>
    </decisions>
    <description>Used when a new Customer record is created or mobile number is updated, a new Messaging End User record is created to be used for SMS</description>
    <environments>Default</environments>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>AND(
ISNEW(),
NOT(ISBLANK({!$Record.PersonMobilePhone}))
)</expression>
    </formulas>
    <interviewLabel>Create Messaging End User {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer-Create Messaging End User</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>MessagingEndUser</name>
        <label>MessagingEndUser</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <faultConnector>
            <targetReference>Flow_Error</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>$Record.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessageType</field>
            <value>
                <stringValue>Text</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessagingChannelId</field>
            <value>
                <elementReference>Get_Messaging_Chanel.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessagingConsentStatus</field>
            <value>
                <stringValue>ImplicitlyOptedIn</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MessagingPlatformKey</field>
            <value>
                <elementReference>$Record.PersonMobilePhone</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>$Record.PersonMobilePhone</elementReference>
            </value>
        </inputAssignments>
        <object>MessagingEndUser</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_End_User_Record</name>
        <label>Get End User Record</label>
        <locationX>446</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Does_MessagingEndUser_Exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MessagingPlatformKey</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.PersonMobilePhone</elementReference>
            </value>
        </filters>
        <filters>
            <field>MessageType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Text</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingEndUser</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Messaging_Chanel</name>
        <label>Get Messaging Chanel</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>MessagingEndUser</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MessagingPlatformKey</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>+614</stringValue>
            </value>
        </filters>
        <filters>
            <field>MessageType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Text</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingChannel</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Records_1</name>
        <label>Update Records 1</label>
        <locationX>578</locationX>
        <locationY>647</locationY>
        <inputReference>Get_End_User_Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_End_User_Record</targetReference>
        </connector>
        <filterFormula>OR(
  AND(
    NOT(ISBLANK({!$Record.PersonMobilePhone})),
    ISNEW()
  ),
  AND(
    ISCHANGED({!$Record.PersonMobilePhone}),
    NOT(ISBLANK({!$Record.PersonMobilePhone}))
  ),
  AND(
    ISCHANGED({!$Record.Elite_Status__c}),
    {!$Record.Elite_Status__c} = &apos;Elite&apos;,
    NOT(ISBLANK({!$Record.PersonMobilePhone}))
  )
)</filterFormula>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
