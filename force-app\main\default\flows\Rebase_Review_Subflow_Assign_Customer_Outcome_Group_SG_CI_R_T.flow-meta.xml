<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns the value for New Customer Group based on formula output</description>
        <name>Assign_New_Customer_Group</name>
        <label>Assign New Customer Group</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignmentItems>
            <assignToReference>varNewCustomerGroupOutcome</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaNewCustomerGroup</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <description>Subflow that stores the formula for the rules and if else logic to assign value to the Customer Outcome Group following reviews for Safer Gambling, Customer Integrity and Risk &amp; Trade team</description>
    <environments>Default</environments>
    <formulas>
        <description>Calculates the value for the New Customer Group</description>
        <name>formulaNewCustomerGroup</name>
        <dataType>String</dataType>
        <expression>IF(
  AND(
    ISPICKVAL({!varRiskTradeReview}, &quot;Remove&quot;),
    {!varRBNewCustomerGroup} = &quot;MBS Excluded&quot;
  ),
  &quot;MBS Excluded&quot;,
  IF(
    AND(
      OR(
        ISPICKVAL({!varSaferGamblingReview}, &quot;Remove&quot;),
        ISPICKVAL({!varCustomerIntegrityReview}, &quot;Remove&quot;)
      ),
      NOT(ISPICKVAL({!varRiskTradeReview}, &quot;Remove&quot;))
    ),
    &quot;Standard&quot;,
    IF(
      OR(
        ISPICKVAL({!varSaferGamblingReview}, &quot;Keep&quot;),
        ISPICKVAL({!varCustomerIntegrityReview}, &quot;Keep&quot;),
        ISPICKVAL({!varRiskTradeReview}, &quot;Keep&quot;)
      ),
      {!varIftheywerenotRGBlocked},
      &quot;Standard&quot;
    )
  )
)</expression>
    </formulas>
    <interviewLabel>Rebase Review: Subflow Assign Customer Outcome Group - SG, CI, R&amp;T {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rebase Review: Subflow Assign Customer Outcome Group - SG, CI, R&amp;T</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assign_New_Customer_Group</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>Stores value of the Customer_Integrity_Review__c field from the parent flow</description>
        <name>varCustomerIntegrityReview</name>
        <dataType>Picklist</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value of the If_they_were_not_RG_Blocked__c field from the parent flow</description>
        <name>varIftheywerenotRGBlocked</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the value for the Customer Group Outcome to apply to the Rebase record</description>
        <name>varNewCustomerGroupOutcome</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <description>Stores value of the RB_New_Customer_Group__c field from the parent flow</description>
        <name>varRBNewCustomerGroup</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value of the Risk_Trade_Review__c field from the parent flow</description>
        <name>varRiskTradeReview</name>
        <dataType>Picklist</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value of the Safer_Gambling_Review__c field from the parent flow</description>
        <name>varSaferGamblingReview</name>
        <dataType>Picklist</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
