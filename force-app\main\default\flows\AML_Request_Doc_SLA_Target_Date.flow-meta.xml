<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Check if Milestone was found in the above element.</description>
        <name>Milestone_Found</name>
        <label>Milestone Found?</label>
        <locationX>374</locationX>
        <locationY>384</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Case_Milestone</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_HRE_SLA_Target_Date</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>Populates Request Doc SLA Target Date field when Waiting for documents milestone is triggered.</description>
    <environments>Default</environments>
    <interviewLabel>AML Request Doc SLA Target Date {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Request Doc SLA Target Date</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>This element retrieves the most recent, incomplete Case Milestone associated with the current Case.</description>
        <name>Get_Case_Milestone</name>
        <label>Get Case Milestone</label>
        <locationX>374</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Milestone_Found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCompleted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>TargetResponseInMins</field>
            <operator>EqualTo</operator>
            <value>
                <numberValue>10080.0</numberValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update Request_Doc_SLA_Target_Date__c on case with SLA target date.</description>
        <name>Update_HRE_SLA_Target_Date</name>
        <label>Update Request Doc SLA Target Date</label>
        <locationX>242</locationX>
        <locationY>492</locationY>
        <inputAssignments>
            <field>Request_Doc_SLA_Completed_Date__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>Request_Doc_SLA_Target_Date__c</field>
            <value>
                <elementReference>Get_Case_Milestone.TargetDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterFormula>AND(
{!$Record.RecordType.DeveloperName} = &quot;ECDD_Case&quot;,
OR(
TEXT({!$Record.Sub_Status__c}) = &quot;Pending_for_customer_response&quot;,
TEXT({!$Record.Sub_Status__c}) = &quot;Further Documents requested&quot;
)
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Get_Case_Milestone</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>ChatterPost</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;This case has just been escalated to {!$Record.Priority}. Please action this case as soon as possible&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>Recipient</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Record.OwnerId</elementReference>
        </value>
    </variables>
    <variables>
        <name>recipients</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
