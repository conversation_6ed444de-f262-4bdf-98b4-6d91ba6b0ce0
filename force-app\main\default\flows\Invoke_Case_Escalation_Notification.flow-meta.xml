<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Custom_Notification</name>
        <label>Send Custom Notification</label>
        <locationX>1604</locationX>
        <locationY>238</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>GetRGCaseNotification.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>NotificationBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>formulaNotificationTitle</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>UserIds</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>inputCase.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Send_Escalation_Email_Notification</name>
        <label>Send Escalation Email Notification</label>
        <locationX>881</locationX>
        <locationY>238</locationY>
        <actionName>Case.Case_Escalation_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>CheckinputManagerId</targetReference>
        </connector>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>inputCase.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Case_Escalation_Email_Notification</nameSegment>
    </actionCalls>
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Escalated</name>
        <label>Assign Escalated</label>
        <locationX>769</locationX>
        <locationY>49</locationY>
        <assignmentItems>
            <assignToReference>inputCase.IsEscalated</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>UpdateCase_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignManagerEmail</name>
        <label>AssignManagerEmail</label>
        <locationX>648</locationX>
        <locationY>49</locationY>
        <assignmentItems>
            <assignToReference>inputCase.RM_Manager_Email__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaInputUserManagerEmail</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Escalated</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignOwner</name>
        <label>AssignOwner</label>
        <locationX>462</locationX>
        <locationY>49</locationY>
        <assignmentItems>
            <assignToReference>inputCase.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputUserId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetInputOwner_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignUserIds</name>
        <label>AssignUserIds</label>
        <locationX>1278</locationX>
        <locationY>238</locationY>
        <assignmentItems>
            <assignToReference>UserIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>inputCase.OwnerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetRGCaseNotification</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignUserIds_0</name>
        <label>AssignUserIds</label>
        <locationX>1143</locationX>
        <locationY>165</locationY>
        <assignmentItems>
            <assignToReference>UserIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>inputUserRecord.ManagerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>AssignUserIds</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_Case_Owner</name>
        <label>Check Case Owner</label>
        <locationX>282</locationX>
        <locationY>52</locationY>
        <defaultConnector>
            <targetReference>AssignOwner</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Not User</defaultConnectorLabel>
        <rules>
            <name>Is_User</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>inputCase.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>005</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetInputOwner</targetReference>
            </connector>
            <label>Is User</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_RM_Email_Field</name>
        <label>Check RM Email Field</label>
        <locationX>639</locationX>
        <locationY>237</locationY>
        <defaultConnector>
            <targetReference>AssignManagerEmail</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Not Match</defaultConnectorLabel>
        <rules>
            <name>Is_Match</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>inputCase.RM_Manager_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>formulaInputUserManagerEmail</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Escalated</targetReference>
            </connector>
            <label>Is Match</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckinputManagerId</name>
        <label>CheckinputManagerId</label>
        <locationX>987</locationX>
        <locationY>240</locationY>
        <defaultConnector>
            <targetReference>AssignUserIds_0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Not Null</defaultConnectorLabel>
        <rules>
            <name>Is_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>inputUserRecord.ManagerId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignUserIds</targetReference>
            </connector>
            <label>Is Null</label>
        </rules>
    </decisions>
    <formulas>
        <name>formulaInputUserManagerEmail</name>
        <dataType>String</dataType>
        <expression>If(!ISBLANK({!inputUserRecord.ManagerId}),{!inputUserRecord.Manager.Email},&apos;&apos;)</expression>
    </formulas>
    <formulas>
        <name>formulaNotificationTitle</name>
        <dataType>String</dataType>
        <expression>Text({!inputCase.Priority}) + &quot; Responsible Gambling Alert for Customer - &quot; + {!inputCase.Customer_Name__c} + &quot; has been escalated&quot;</expression>
    </formulas>
    <interviewLabel>Invoke_Case_Escalation_Notification {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Invoke_Case_Escalation_Notification</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetInputOwner</name>
        <label>GetInputOwner</label>
        <locationX>291</locationX>
        <locationY>236</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_RM_Email_Field</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputCase.OwnerId</elementReference>
            </value>
        </filters>
        <object>User</object>
        <outputReference>inputUserRecord</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>ManagerId</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>GetInputOwner_0</name>
        <label>GetInputOwner</label>
        <locationX>553</locationX>
        <locationY>49</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>AssignManagerEmail</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputUserId</elementReference>
            </value>
        </filters>
        <object>User</object>
        <outputReference>inputUserRecord</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>ManagerId</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>GetRGCaseNotification</name>
        <label>GetRGCaseNotification</label>
        <locationX>1455</locationX>
        <locationY>238</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Send_Custom_Notification</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>RG_Case_Notification</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetUpdatedCaseRecord</name>
        <label>GetUpdatedCaseRecord</label>
        <locationX>164</locationX>
        <locationY>49</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Case_Owner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputCase.Id</elementReference>
            </value>
        </filters>
        <object>Case</object>
        <outputReference>inputCase</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>OwnerId</queriedFields>
        <queriedFields>AccountId</queriedFields>
        <queriedFields>RM_Manager_Email__c</queriedFields>
        <queriedFields>CaseNumber</queriedFields>
        <queriedFields>Alert_Date_Time__c</queriedFields>
        <queriedFields>Priority</queriedFields>
        <queriedFields>Subject</queriedFields>
        <queriedFields>Customer_Name__c</queriedFields>
        <queriedFields>Type</queriedFields>
    </recordLookups>
    <recordUpdates>
        <name>UpdateCase_0</name>
        <label>UpdateCase</label>
        <locationX>881</locationX>
        <locationY>48</locationY>
        <connector>
            <targetReference>Send_Escalation_Email_Notification</targetReference>
        </connector>
        <inputReference>inputCase</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>GetUpdatedCaseRecord</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>NotificationBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>*** CASE ESCALATION ALERT ***
The following case has been escalated due to no follow up in period of time:
Customer Name: {!inputCase.Customer_Name__c}
Case Number: {!inputCase.CaseNumber}
Subject: {!inputCase.Subject}
Alert Date/Time: {!inputCase.Alert_Date_Time__c}
Alert Level: {!inputCase.Priority}
Case Type: {!inputCase.Type}</text>
    </textTemplates>
    <variables>
        <name>inputCase</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>inputUserId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>inputUserRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
    <variables>
        <name>UserIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
