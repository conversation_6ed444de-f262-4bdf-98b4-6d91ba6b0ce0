<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_WhoId</name>
        <label>Assign WhoId</label>
        <locationX>182</locationX>
        <locationY>503</locationY>
        <assignmentItems>
            <assignToReference>$Record.WhoId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_API_Profile_ID</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignStatusAndDescription2</name>
        <label>Assign Status and Description (Active)</label>
        <locationX>314</locationX>
        <locationY>911</locationY>
        <assignmentItems>
            <assignToReference>$Record.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>FYI Alert: Viewed Sky Racing but no Racing Bet in last 7 days</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>FYI Alert: Viewed Sky Racing but no Racing Bet in last 7 days</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>AssignStatusAndDescriptionActive</name>
        <label>Assign Status and Description (Active)</label>
        <locationX>50</locationX>
        <locationY>911</locationY>
        <assignmentItems>
            <assignToReference>$Record.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>FYI Alert: Logged in and hasn&apos;t bet for 7+ days</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>FYI Alert: Logged in and hasn&apos;t bet for 7+ days</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <decisions>
        <name>Check_WhatId</name>
        <label>Check WhatId</label>
        <locationX>314</locationX>
        <locationY>287</locationY>
        <defaultConnector>
            <targetReference>Get_API_Profile_ID</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Account</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>001</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WhoId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Account</targetReference>
            </connector>
            <label>Is Account</label>
        </rules>
    </decisions>
    <decisions>
        <name>DoesTaskRequireAutomaticClosure</name>
        <label>Does Task Require Automatic Closure</label>
        <locationX>314</locationX>
        <locationY>803</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decAlertLoggedIn</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Subject</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Alert - Logged in and hasn&apos;t bet for 7+ days</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignStatusAndDescriptionActive</targetReference>
            </connector>
            <label>Alert - Logged in and hasn&apos;t bet for 7+ days</label>
        </rules>
        <rules>
            <name>decAlertViewedSkyRacing</name>
            <conditionLogic>1 AND (2 OR 3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Subject</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Alert - Viewed Sky Racing but no Racing Bet in last 7 days</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Account_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Self_Excluded__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Premium_Tier__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>-1</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account.Owner.ProfileId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Get_API_Profile_ID.Id</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignStatusAndDescription2</targetReference>
            </connector>
            <label>Alert - Viewed Sky Racing but no Racing Bet in last 7 days</label>
        </rules>
    </decisions>
    <description>SBET-777 Change Viewed Sky Racing Tasks so task is not created as a closed task</description>
    <environments>Default</environments>
    <interviewLabel>Task Flow Trigger When Created {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Task Flow Trigger When Created</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Account</name>
        <label>Get Account</label>
        <locationX>182</locationX>
        <locationY>395</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_WhoId</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>PersonContactId</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_API_Profile_ID</name>
        <label>Get API Profile ID</label>
        <locationX>314</locationX>
        <locationY>695</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>DoesTaskRequireAutomaticClosure</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>API</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Profile</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_WhatId</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>IntroTaskUser</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
</Flow>
