<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Invoke Automated Task Creation</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_11_A1</name>
        <label>Create Bonus Expiry task</label>
        <locationX>1100</locationX>
        <locationY>200</locationY>
        <actionName>Invoke_Automated_Task_Creation</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision12</targetReference>
        </connector>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>recordId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>recordId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varTaskOwner</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>varTaskOwner</name>
            <value>
                <elementReference>myVariable_current.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varAutomatedTaskType</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <name>varAutomatedTaskType</name>
            <value>
                <stringValue>ExpiringBonus</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varBonusExpiryDate</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>varBonusExpiryDate</name>
            <value>
                <elementReference>myVariable_current.Bonus_Expiry_Date__c</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Invoke_Automated_Task_Creation</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Invoke Automated Task Creation</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_13_A1</name>
        <label>Create Customer Introduction Task</label>
        <locationX>1300</locationX>
        <locationY>200</locationY>
        <actionName>Invoke_Automated_Task_Creation</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision14</targetReference>
        </connector>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>recordId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>recordId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varTaskOwner</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>varTaskOwner</name>
            <value>
                <elementReference>myVariable_current.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varAutomatedTaskType</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <name>varAutomatedTaskType</name>
            <value>
                <stringValue>Introduction</stringValue>
            </value>
        </inputParameters>
        <nameSegment>Invoke_Automated_Task_Creation</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Invoke Automated Task Creation</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_15_A1</name>
        <label>Create Customer Introduction Task</label>
        <locationX>1500</locationX>
        <locationY>200</locationY>
        <actionName>Invoke_Automated_Task_Creation</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision16</targetReference>
        </connector>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>recordId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>recordId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varTaskOwner</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>varTaskOwner</name>
            <value>
                <elementReference>myVariable_current.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varAutomatedTaskType</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <name>varAutomatedTaskType</name>
            <value>
                <stringValue>Introduction</stringValue>
            </value>
        </inputParameters>
        <nameSegment>Invoke_Automated_Task_Creation</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Create Case to RM if Customer is self Excluded and has future event</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_1_A1</name>
        <label>Create Case to RM</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <actionName>Create_Case_to_RM_if_Customer_is_self_Excluded_and_has_future_event</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision2</targetReference>
        </connector>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>SObject</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>inputAccount</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue>Account</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>SObject</stringValue>
                </value>
            </processMetadataValues>
            <name>inputAccount</name>
            <value>
                <elementReference>myVariable_current</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Create_Case_to_RM_if_Customer_is_self_Excluded_and_has_future_event</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Invoke Automated Task Creation</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_3_A1</name>
        <label>Create Account Locked Alert Task</label>
        <locationX>300</locationX>
        <locationY>200</locationY>
        <actionName>Invoke_Automated_Task_Creation</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision4</targetReference>
        </connector>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>recordId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>recordId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varAutomatedTaskType</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <name>varAutomatedTaskType</name>
            <value>
                <stringValue>AccountLocked</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varTaskOwner</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>varTaskOwner</name>
            <value>
                <elementReference>myVariable_current.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Invoke_Automated_Task_Creation</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Invoke Automated Task Completion</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_5_A1</name>
        <label>Close Account Locked Alert Tasks</label>
        <locationX>500</locationX>
        <locationY>200</locationY>
        <actionName>Invoke_Automated_Task_Completion</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision6</targetReference>
        </connector>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>recordId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>recordId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varAutomatedTaskType</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <name>varAutomatedTaskType</name>
            <value>
                <stringValue>AccountLocked</stringValue>
            </value>
        </inputParameters>
        <nameSegment>Invoke_Automated_Task_Completion</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Invoke Automated Task Creation</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_7_A1</name>
        <label>Create Negative Balance Alert</label>
        <locationX>700</locationX>
        <locationY>200</locationY>
        <actionName>Invoke_Automated_Task_Creation</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision8</targetReference>
        </connector>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>recordId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>recordId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varTaskOwner</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>varTaskOwner</name>
            <value>
                <elementReference>myVariable_current.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varAutomatedTaskType</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <name>varAutomatedTaskType</name>
            <value>
                <stringValue>NegativeBalance</stringValue>
            </value>
        </inputParameters>
        <nameSegment>Invoke_Automated_Task_Creation</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Invoke Automated Task Completion</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_9_A1</name>
        <label>Close Negative Balance Alert Tasks</label>
        <locationX>900</locationX>
        <locationY>200</locationY>
        <actionName>Invoke_Automated_Task_Completion</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision10</targetReference>
        </connector>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>recordId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>recordId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>varAutomatedTaskType</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <name>varAutomatedTaskType</name>
            <value>
                <stringValue>NegativeBalance</stringValue>
            </value>
        </inputParameters>
        <nameSegment>Invoke_Automated_Task_Completion</nameSegment>
    </actionCalls>
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_pmetdec</targetReference>
            </connector>
            <label>Self Excluded is Yes</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>5.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision10</name>
        <label>myDecision10</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision12</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_11</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_11</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_11_A1</targetReference>
            </connector>
            <label>Bonus Expiry Date Changed</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>6.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision12</name>
        <label>myDecision12</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision14</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_13</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_13</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_13_A1</targetReference>
            </connector>
            <label>Customer Created</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>7.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision14</name>
        <label>myDecision14</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision16</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_15</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_15</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_15_A1</targetReference>
            </connector>
            <label>Owner Changed</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>8.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision16</name>
        <label>myDecision16</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_17</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_17</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_17_A1</targetReference>
            </connector>
            <label>Email Update</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision2</name>
        <label>myDecision2</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision4</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_3</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_3_A1</targetReference>
            </connector>
            <label>Account Locked</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>2.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision4</name>
        <label>myDecision4</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision6</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_5</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_5</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_5_A1</targetReference>
            </connector>
            <label>Account Unlocked</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>3.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision6</name>
        <label>myDecision6</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision8</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_7</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_7</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_7_A1</targetReference>
            </connector>
            <label>Negative Balance</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>4.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision8</name>
        <label>myDecision8</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision10</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_9</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_9</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_9_A1</targetReference>
            </connector>
            <label>Positive Balance</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_1_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>100</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_1_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_1_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_1_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1_pmetrule</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision2</targetReference>
            </connector>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <description>This process is used for triggered from Customer (Account) object</description>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Account_PB_Entry_1__c 
&amp;&amp;
(
[Account].Self_Excluded__c = &apos;Yes&apos;
||
[Account].Account_Status__c = &apos;Suspended&apos;
||
[Account].Account_Status__c = &apos;Closed&apos;
||
[Account].Account_Status__c = &apos;Timeout&apos;
)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Account_PB_Entry_1__c} 
&amp;&amp;
(
{!myVariable_current.Self_Excluded__c} = &apos;Yes&apos;
||
{!myVariable_current.Account_Status__c} = &apos;Suspended&apos;
||
{!myVariable_current.Account_Status__c} = &apos;Closed&apos;
||
{!myVariable_current.Account_Status__c} = &apos;Timeout&apos;
)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>NOT($Setup.Process_Automation_Control_Panel__c.Account_PB_Bonus_Expiry_Date_Entry__c) &amp;&amp; 
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c &amp;&amp;
ISCHANGED([Account].Bonus_Expiry_Date__c) &amp;&amp;
[Account].Bonus_Expiry_Date__c &gt; TODAY() </stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_11</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Bonus_Expiry_Date_Entry__c}) &amp;&amp; 
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c} &amp;&amp;
ISCHANGED({!myVariable_current.Bonus_Expiry_Date__c}) &amp;&amp;
{!myVariable_current.Bonus_Expiry_Date__c} &gt; TODAY()</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>NOT($Setup.Process_Automation_Control_Panel__c.Account_PB_Customer_Created_Entry__c) &amp;&amp; 
ISNEW()  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_13</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Customer_Created_Entry__c}) &amp;&amp; 
ISNEW()  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c}</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>NOT($Setup.Process_Automation_Control_Panel__c.Account_PB_Owner_Changed_Entry__c) &amp;&amp; 
ISCHANGED([Account].OwnerId) &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_15</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Owner_Changed_Entry__c}) &amp;&amp; 
ISCHANGED({!myVariable_current.OwnerId}) &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c}</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>NOT($Setup.Process_Automation_Control_Panel__c.Account_PB_Email_Update_Entry__c) &amp;&amp;
NOT(ISBLANK([Account].Email__c) ) &amp;&amp; 
ISBLANK([Account].PersonEmail ) || 
NOT($Setup.Process_Automation_Control_Panel__c.Account_PB_Email_Update_Entry__c) &amp;&amp;
ISCHANGED([Account].Email__c) </stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_17</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Email_Update_Entry__c}) &amp;&amp;
NOT(ISBLANK({!myVariable_current.Email__c}) ) &amp;&amp; 
ISBLANK({!myVariable_current.PersonEmail} ) || 
NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Email_Update_Entry__c}) &amp;&amp;
ISCHANGED({!myVariable_current.Email__c})</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Account_PB_Entry_1__c 
&amp;&amp;
(
[Account].Self_Excluded__c = &apos;Yes&apos;
||
[Account].Account_Status__c = &apos;Suspended&apos;
||
[Account].Account_Status__c = &apos;Closed&apos;
||
[Account].Account_Status__c = &apos;Timeout&apos;
)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1_pmetrule</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Account_PB_Entry_1__c} 
&amp;&amp;
(
{!myVariable_old.Self_Excluded__c} = &apos;Yes&apos;
||
{!myVariable_old.Account_Status__c} = &apos;Suspended&apos;
||
{!myVariable_old.Account_Status__c} = &apos;Closed&apos;
||
{!myVariable_old.Account_Status__c} = &apos;Timeout&apos;
)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>NOT($Setup.Process_Automation_Control_Panel__c.Account_PB_Account_Locked_Entry__c)&amp;&amp; 
ISCHANGED([Account].Account_Locked_Out__c )  &amp;&amp; 
[Account].Account_Locked_Out__c  &amp;&amp; 
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c </stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_3</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Account_Locked_Entry__c})&amp;&amp; 
ISCHANGED({!myVariable_current.Account_Locked_Out__c} )  &amp;&amp; 
{!myVariable_current.Account_Locked_Out__c}  &amp;&amp; 
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c}</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>NOT($Setup.Process_Automation_Control_Panel__c.Account_PB_Account_Unlocked_Entry__c)&amp;&amp; 
ISCHANGED([Account].Account_Locked_Out__c )  &amp;&amp; 
NOT([Account].Account_Locked_Out__c)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_5</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Account_Unlocked_Entry__c})&amp;&amp; 
ISCHANGED({!myVariable_current.Account_Locked_Out__c} )  &amp;&amp; 
NOT({!myVariable_current.Account_Locked_Out__c})</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>NOT($Setup.Process_Automation_Control_Panel__c.Account_PB_Negative_Balance_Entry__c)&amp;&amp; 
ISCHANGED([Account].Current_Balance__c)  &amp;&amp; 
[Account].Current_Balance__c&lt; 0 &amp;&amp; 
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c  &amp;&amp;
[Account].OwnerId &lt;&gt; $CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c </stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_7</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Negative_Balance_Entry__c})&amp;&amp; 
ISCHANGED({!myVariable_current.Current_Balance__c})  &amp;&amp; 
{!myVariable_current.Current_Balance__c}&lt; 0 &amp;&amp; 
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Integration_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Managed_Team_Service_User_ID.Value__c}  &amp;&amp;
{!myVariable_current.OwnerId} &lt;&gt; {!$CustomMetadata.General_Setting__mdt.Premium_Service_User_ID.Value__c}</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>NOT($Setup.Process_Automation_Control_Panel__c.Account_PB_Positive_Balance_Entry__c)&amp;&amp; 
ISCHANGED([Account].Current_Balance__c)  &amp;&amp; 
[Account].Current_Balance__c &gt;= 0 &amp;&amp; 
PRIORVALUE([Account].Current_Balance__c )&lt; 0
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_9</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Positive_Balance_Entry__c})&amp;&amp; 
ISCHANGED({!myVariable_current.Current_Balance__c})  &amp;&amp; 
{!myVariable_current.Current_Balance__c} &gt;= 0 &amp;&amp; 
PRIORVALUE({!myVariable_current.Current_Balance__c} )&lt; 0</expression>
    </formulas>
    <interviewLabel>Customer_Process-14_InterviewLabel</interviewLabel>
    <label>Customer Process</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Account</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>ObjectVariable</name>
        <value>
            <elementReference>myVariable_current</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OldObjectVariable</name>
        <value>
            <elementReference>myVariable_old</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>TriggerType</name>
        <value>
            <stringValue>onAllChanges</stringValue>
        </value>
    </processMetadataValues>
    <processType>Workflow</processType>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Account]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_17_A1</name>
        <label>Update PersonEmail</label>
        <locationX>1700</locationX>
        <locationY>200</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Email</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>PersonEmail</field>
            <value>
                <elementReference>myVariable_current.Email__c</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <startElementReference>myDecision</startElementReference>
    <status>Obsolete</status>
    <variables>
        <name>myVariable_current</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>myVariable_old</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
</Flow>
