<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>57.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Email_Opt_In</name>
        <label>Email Opt In?</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>Copy_1_of_Update_Customer_Opt_Out_to_False</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Customer_Field_to_False</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Record_New_Changed</name>
        <label>Is Record New/Changed</label>
        <locationX>380</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>New_Changed</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Customer_Record</targetReference>
            </connector>
            <label>New/Changed</label>
        </rules>
    </decisions>
    <description>SBET-609 - Accommodate newly created record</description>
    <environments>Default</environments>
    <interviewLabel>Customer Consent - Set Person Has Opted Out of Email {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer Consent - Set Person Has Opted Out of Email</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Customer_Record</name>
        <label>Get Customer Record</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Email_Opt_In</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Customer__r.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Copy_1_of_Update_Customer_Opt_Out_to_False</name>
        <label>Update Customer Opt Out to True</label>
        <locationX>314</locationX>
        <locationY>647</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer_Record.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Customer_Field_to_False</name>
        <label>Update Customer Opt Out to False</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer_Record.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_Record_New_Changed</targetReference>
        </connector>
        <object>Customer_Consent__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
