<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>TASK_RECORD_TYPE_NAME</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Task</stringValue>
        </value>
    </constants>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Any_Success</name>
        <label>Any Success?</label>
        <locationX>182</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>No_Success_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Launch_Bulk_Interactions_Flow.RecipientsSuccessOutput</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Launch_Bulk_Interactions_Flow.RecipientsSuccessOutput</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_TaskId_List</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Found_Task_Records</name>
        <label>Found Task Records?</label>
        <locationX>512</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>No_Customers_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No, none returned</defaultConnectorLabel>
        <rules>
            <name>Yes_found_records</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Selected_Task_Records</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_WhoId_List</targetReference>
            </connector>
            <label>Yes, found records</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>BulkInteractionsMessageLimit</name>
        <dataType>Number</dataType>
        <expression>MAX({!$Setup.System_Limits__c.Bulk_Interactions_Limit__c}, 2)</expression>
        <scale>0</scale>
    </formulas>
    <interviewLabel>Task - Bulk Interactions {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Task: Bulk Interactions</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Contact_Recipients</name>
        <label>Get Contact Recipients</label>
        <locationX>182</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Launch_Bulk_Interactions_Flow</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>No_Customers_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>Get_WhoId_List</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <limit>
            <elementReference>BulkInteractionsMessageLimit</elementReference>
        </limit>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Selected_Task_Records</name>
        <label>Get Selected Task Records</label>
        <locationX>512</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Found_Task_Records</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>No_Customers_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>ids</elementReference>
            </value>
        </filters>
        <filters>
            <field>WhoId</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Task_Task_Record_Type.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <limit>
            <elementReference>BulkInteractionsMessageLimit</elementReference>
        </limit>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Task_Task_Record_Type</name>
        <label>Get Task Task Record Type</label>
        <locationX>512</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Selected_Task_Records</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>No_Customers_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>TASK_RECORD_TYPE_NAME</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates all the Tasks that had a successful email send to complete</description>
        <name>Update_Successful_Tasks_to_Complete</name>
        <label>Update Successful Tasks to Complete</label>
        <locationX>50</locationX>
        <locationY>1106</locationY>
        <connector>
            <targetReference>Results_Screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhoId</field>
            <operator>In</operator>
            <value>
                <elementReference>Get_Success_ContactId_List</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>Get_TaskId_List</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>No_Customers_Screen</name>
        <label>No Customers Screen</label>
        <locationX>842</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>NoRecordsDisplay</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;No Customers were received as input from the Tasks selected.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;There were no records found that were related to a Task where record type equals Task, or no Customer records were selected where the record type equals Person Account.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Please ensure that the tasks that were selected have a record type that equals Task, are related to a Person Account, and that tasks have a value for &apos;Customer Name&apos; (WhoId) before proceeding.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;No messages have been sent.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>No_Success_Screen</name>
        <label>No Success Screen</label>
        <locationX>314</locationX>
        <locationY>890</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>NoSuccessDisplay</name>
            <fieldText>&lt;p&gt;There was no message sent.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Results_Screen</name>
        <label>Results Screen</label>
        <locationX>50</locationX>
        <locationY>1214</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessDisplay</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(0, 0, 0);&quot;&gt;Your {!Launch_Bulk_Interactions_Flow.MessageTypeOutput} messages have been successfully queued to send to the following recipients!:&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>SuccessfulRecipientsDataTableEmail</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Launch_Bulk_Interactions_Flow.RecipientsSuccessOutput</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-209f&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Email&quot;,&quot;guid&quot;:&quot;column-bba9&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Standard Email&quot;,&quot;type&quot;:&quot;email&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Launch_Bulk_Interactions_Flow.MessageTypeOutput</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Email</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>SuccessfulRecipientsDataTableSMS</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Launch_Bulk_Interactions_Flow.RecipientsSuccessOutput</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-a6f4&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;MobilePhone&quot;,&quot;guid&quot;:&quot;column-7794&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Standard Mobile&quot;,&quot;type&quot;:&quot;phone&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Launch_Bulk_Interactions_Flow.MessageTypeOutput</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>SMS</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ErrorDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(255, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;Please note&lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;: {!Launch_Bulk_Interactions_Flow.MessageTypeOutput} messages were &lt;/span&gt;&lt;strong style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;NOT &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;sent to the following recipients:&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DisplayErrors</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>FailuresDataTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Launch_Bulk_Interactions_Flow.RecipientsFailOutput</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-e173&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DisplayErrors</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>386</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Task_Task_Record_Type</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Launch_Bulk_Interactions_Flow</name>
        <label>Launch Bulk Interactions Flow</label>
        <locationX>182</locationX>
        <locationY>674</locationY>
        <connector>
            <targetReference>Any_Success</targetReference>
        </connector>
        <flowName>Bulk_Interactions_Send_Bulk_Email_SMS_Screen_Flow</flowName>
        <inputAssignments>
            <name>RecipientsInput</name>
            <value>
                <elementReference>Get_Contact_Recipients</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <transforms>
        <name>Get_Success_ContactId_List</name>
        <label>Get Success ContactId List</label>
        <locationX>50</locationX>
        <locationY>998</locationY>
        <connector>
            <targetReference>Update_Successful_Tasks_to_Complete</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Launch_Bulk_Interactions_Flow.RecipientsSuccessOutput[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>Get_TaskId_List</name>
        <label>Get TaskId List</label>
        <locationX>50</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Get_Success_ContactId_List</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Selected_Task_Records[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>Get_WhoId_List</name>
        <label>Get WhoId List</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <connector>
            <targetReference>Get_Contact_Recipients</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Selected_Task_Records[$EachItem].WhoId</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <variables>
        <name>currentItem_Get_Tasks_to_Update</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>currentItemFromSourceCollection</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>DisplayErrors</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>NumRecipients</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
</Flow>
