<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Checks if the customer owner who is their primary relationship manager is available</description>
        <name>Check_Primary_Relationship_Manager_Availability</name>
        <label>Check Primary Relationship Manager Availability</label>
        <locationX>5638</locationX>
        <locationY>1640</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_Primary_Relationship_Manager_Available</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if the customer owner as the primary relationship manager is available</description>
        <name>Check_Primary_Relationship_Manager_Availability_for_Grow_VM</name>
        <label>Check Primary Relationship Manager Availability for Grow VM</label>
        <locationX>4318</locationX>
        <locationY>2288</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_Grow_Primary_RM_available_for_VM</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if the customer owner as the primary relationship manager is available</description>
        <name>Check_Primary_Relationship_Manager_Availability_for_VM</name>
        <label>Check Primary Relationship Manager Availability for VM</label>
        <locationX>1062</locationX>
        <locationY>1886</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_Primary_RM_available_for_VM</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if the customer owner as the primary relationship manager is available</description>
        <name>Check_Protect_Primary_Relationship_Manager_Availability</name>
        <label>Check Protect Primary Relationship Manager Availability</label>
        <locationX>2382</locationX>
        <locationY>1238</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_Protect_Primary_RM_Available</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if a team member from the primary relationship managers state is available for VM</description>
        <name>Check_Relationship_Manager_Grow_State_Available_VM</name>
        <label>Check Relationship Manager Grow State Available VM</label>
        <locationX>4714</locationX>
        <locationY>2720</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_a_State_Grow_Member_Available_VM</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Grow_VM_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if a team member from the primary relationship managers state is available</description>
        <name>Check_Relationship_Manager_State_Available</name>
        <label>Check Relationship Manager State Available</label>
        <locationX>6034</locationX>
        <locationY>2072</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_a_State_Member_Available</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if a team member from the primary relationship managers state is available</description>
        <name>Check_Relationship_Manager_State_Available1</name>
        <label>Check Relationship Manager State Available</label>
        <locationX>2778</locationX>
        <locationY>1670</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_a_State_Member_Available1</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Queue1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if a team member from the primary relationship managers state is available for VM</description>
        <name>Check_Relationship_Manager_State_Available_VM</name>
        <label>Check Relationship Manager State Available VM</label>
        <locationX>1458</locationX>
        <locationY>2318</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Copy_1_of_Is_a_State_Member_Available_VM</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_VM_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Check_within_Grow_Business_Hours</name>
        <label>Check within Grow Business Hours</label>
        <locationX>4719</locationX>
        <locationY>674</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>IsWIthinGrowBusinessHours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>GetGrowBusinessHours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined OOH AM Business Hours</description>
        <name>Check_Within_Grow_OOH_AM_Business_Hours</name>
        <label>Check Within Grow OOH AM Business Hours</label>
        <locationX>3801</locationX>
        <locationY>1748</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Grow_OOH_AM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Grow_OOH_AM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined OOH PM Business Hours</description>
        <name>Check_Within_Grow_OOH_PM_Business_Hours</name>
        <label>Check Within Grow OOH PM Business Hours</label>
        <locationX>4032</locationX>
        <locationY>2072</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Grow_OOH_PM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Grow_OOH_PM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Check_within_Protect_Business_Hours</name>
        <label>Check within Protect Business Hours</label>
        <locationX>1463</locationX>
        <locationY>674</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>IsWIthinProtectBusinessHours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>GetProtectBusinessHours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined OOH AM Business Hours</description>
        <name>Check_Within_Protect_OOH_AM_Business_Hours</name>
        <label>Check Within Protect OOH AM Business Hours</label>
        <locationX>545</locationX>
        <locationY>1346</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Protect_OOH_AM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Protect_OOH_AM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined OOH PM Business Hours</description>
        <name>Check_Within_Protect_OOH_PM_Business_Hours</name>
        <label>Check Within Protect OOH PM Business Hours</label>
        <locationX>776</locationX>
        <locationY>1670</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Protect_OOH_PM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Protect_OOH_PM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Routes customer to the Event &amp; Experience Queue</description>
        <name>Route_to_Event_Experience_Queue</name>
        <label>Route to Event Experience Queue</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00GRE000002fdyj2AA</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>Events &amp; Experience General</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>VM is routed to primary relationship manager for protect customer</description>
        <name>Route_to_Grow_Primary_Relationship_Manager_for_VM</name>
        <label>Route to Grow Primary Relationship Manager for VM</label>
        <locationX>3922</locationX>
        <locationY>2504</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Assign_Relationship_Managers_Grow_VM_State</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VDEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Grow Voicemail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Call is routed to a VM state member</description>
        <name>Route_to_Grow_VM_State</name>
        <label>Route to Grow VM State</label>
        <locationX>4450</locationX>
        <locationY>2936</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Grow_VM_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Grow_VM_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Call is routed to the wider general queue for any relationship manager to action</description>
        <name>Route_to_National_Grow_Queue</name>
        <label>Route to National Grow Queue</label>
        <locationX>6298</locationX>
        <locationY>2288</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VCEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Grow Voice</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Voicemail is routed to the wider National queue for any relationship manager to action</description>
        <name>Route_to_National_Grow_VM_Queue</name>
        <label>Route to National Grow VM Queue</label>
        <locationX>4978</locationX>
        <locationY>2936</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VDEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Grow Voicemail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Voicemail is routed to the wider National queue for any relationship manager to action</description>
        <name>Route_to_National_Protect_Queue</name>
        <label>Route to National Protect Queue</label>
        <locationX>3042</locationX>
        <locationY>1886</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VFEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Protect Voice</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Voicemail is routed to the wider National queue for any relationship manager to action</description>
        <name>Route_to_National_Protect_VM_Queue</name>
        <label>Route to National Protect VM Queue</label>
        <locationX>1722</locationX>
        <locationY>2534</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VGEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Protect Voicemail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Voicemail is routed to the wider National queue for any relationship manager to action</description>
        <name>Route_to_National_Protect_VM_Queue1</name>
        <label>Route to National Protect VM Queue</label>
        <locationX>6562</locationX>
        <locationY>674</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VGEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Protect Voicemail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Voicemail is routed to the wider National queue for any relationship manager to action</description>
        <name>Route_to_National_Queue</name>
        <label>Route to National Queue</label>
        <locationX>6826</locationX>
        <locationY>674</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VFEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Protect Voice</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Call is routed to primary relationship manager</description>
        <name>Route_to_Primary_Relationship_Manager</name>
        <label>Route to Primary Relationship Manager</label>
        <locationX>5242</locationX>
        <locationY>1856</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Assign_Relationship_Managers_State</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VCEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Grow Voice</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>VM is routed to primary relationship manager for protect customer</description>
        <name>Route_to_Protect_Primary_Relationship_Manager_for_VM</name>
        <label>Route to Protect Primary Relationship Manager for VM</label>
        <locationX>666</locationX>
        <locationY>2102</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Assign_Relationship_Managers_VM_State</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VGEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Protect Voicemail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Call is routed to primary relationship manager for Protect customer</description>
        <name>Route_to_Protect_Sensitive_Primary_Relationship_Manager</name>
        <label>Route to Protect Primary Relationship Manager</label>
        <locationX>1986</locationX>
        <locationY>1454</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Assign_Relationship_Managers_State1</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>psrInteractionInfo</name>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <stringValue>00G9j000004f4VFEAY</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
            <value>
                <stringValue>National Protect Voice</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Call is routed to a state member</description>
        <name>Route_to_State</name>
        <label>Route to State</label>
        <locationX>5770</locationX>
        <locationY>2288</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Grow_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Call is routed to a state member</description>
        <name>Route_to_State1</name>
        <label>Route to State</label>
        <locationX>2514</locationX>
        <locationY>1886</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Queue1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Call is routed to a VM state member</description>
        <name>Route_to_VM_State</name>
        <label>Route to VM State</label>
        <locationX>1194</locationX>
        <locationY>2534</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_VM_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_VM_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Call is routed to a VM state member</description>
        <name>Route_to_VM_State1</name>
        <label>Route to VM State</label>
        <locationX>545</locationX>
        <locationY>3302</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_VM_Queue1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Call is routed to a VM state member</description>
        <name>Route_to_VM_State2</name>
        <label>Route to VM State</label>
        <locationX>3801</locationX>
        <locationY>3704</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_VM_Queue2.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns the relationship manager state to a variable to use for routing to the correct queue</description>
        <name>Assign_Relationship_Managers_Grow_VM_State</name>
        <label>Assign Relationship Managers Grow VM State</label>
        <locationX>4714</locationX>
        <locationY>2504</locationY>
        <assignmentItems>
            <assignToReference>varQueueDeveloperName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaCalculateGrowTeamVMQueue</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_State_Members_Grow_VM_Queue</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the relationship manager state to a variable to use for routing to the correct queue</description>
        <name>Assign_Relationship_Managers_Grow_VM_State1</name>
        <label>Assign Relationship Managers Grow VM State</label>
        <locationX>3801</locationX>
        <locationY>3488</locationY>
        <assignmentItems>
            <assignToReference>varQueueDeveloperName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaCalculateGrowTeamVMQueue</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_State_Members_VM_Queue2</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the relationship manager state to a variable to use for routing to the correct queue</description>
        <name>Assign_Relationship_Managers_State</name>
        <label>Assign Relationship Managers State</label>
        <locationX>6034</locationX>
        <locationY>1856</locationY>
        <assignmentItems>
            <assignToReference>varQueueDeveloperName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaCalculateStateGrowQueue</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_State_Members_Queue</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the relationship manager state to a variable to use for routing to the correct queue</description>
        <name>Assign_Relationship_Managers_State1</name>
        <label>Assign Relationship Managers State</label>
        <locationX>2778</locationX>
        <locationY>1454</locationY>
        <assignmentItems>
            <assignToReference>varQueueDeveloperName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaCalculateStateQueue</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_State_Members_Queue1</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the relationship manager state to a variable to use for routing to the correct queue</description>
        <name>Assign_Relationship_Managers_VM_State</name>
        <label>Assign Relationship Managers VM State</label>
        <locationX>1458</locationX>
        <locationY>2102</locationY>
        <assignmentItems>
            <assignToReference>varQueueDeveloperName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaCalculateTeamVMQueue</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_State_Members_VM_Queue</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the relationship manager state to a variable to use for routing to the correct queue</description>
        <name>Assign_Relationship_Managers_VM_State1</name>
        <label>Assign Relationship Managers VM State</label>
        <locationX>545</locationX>
        <locationY>3086</locationY>
        <assignmentItems>
            <assignToReference>varQueueDeveloperName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaCalculateTeamVMQueue</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_State_Members_VM_Queue1</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Is a state member available</description>
        <name>Copy_1_of_Is_a_State_Member_Available_VM</name>
        <label>Is a State Member Available VM</label>
        <locationX>1458</locationX>
        <locationY>2426</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Protect_VM_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_State_Member_Available_VM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Relationship_Manager_State_Available_VM.onlineAgentsCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_VM_State</targetReference>
            </connector>
            <label>State Member Available VM</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is a state member available</description>
        <name>Is_a_State_Grow_Member_Available_VM</name>
        <label>Is a State Grow Member Available VM</label>
        <locationX>4714</locationX>
        <locationY>2828</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Grow_VM_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>State_Grow_Member_Available_VM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Relationship_Manager_Grow_State_Available_VM.onlineAgentsCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_Grow_VM_State</targetReference>
            </connector>
            <label>State Grow Member Available VM</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is a state member available</description>
        <name>Is_a_State_Member_Available</name>
        <label>Is a State Member Available</label>
        <locationX>6034</locationX>
        <locationY>2180</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Grow_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>State_Member_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Relationship_Manager_State_Available.onlineAgentsCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_State</targetReference>
            </connector>
            <label>State Member Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is a state member available</description>
        <name>Is_a_State_Member_Available1</name>
        <label>Is a State Member Available</label>
        <locationX>2778</locationX>
        <locationY>1778</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>State_Member_Available1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Relationship_Manager_State_Available1.onlineAgentsCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_State1</targetReference>
            </connector>
            <label>State Member Available</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Event_Attendee</name>
        <label>Is Event Attendee?</label>
        <locationX>5280</locationX>
        <locationY>1106</locationY>
        <defaultConnector>
            <targetReference>Is_Incoming_Call_a_Grow_VM_Record</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not an Event Attendee</defaultConnectorLabel>
        <rules>
            <name>Event_Attendee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Next_Event_Attendance__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Assign_Relationship_Managers_State1</targetReference>
            </connector>
            <label>Event Attendee?</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Grow_Primary_RM_available_for_VM</name>
        <label>Is Grow Primary RM available for VM?</label>
        <locationX>4318</locationX>
        <locationY>2396</locationY>
        <defaultConnector>
            <targetReference>Assign_Relationship_Managers_Grow_VM_State</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Grow_Primary_RM_available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Primary_Relationship_Manager_Availability_for_Grow_VM.onlineAgentsCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_Grow_Primary_Relationship_Manager_for_VM</targetReference>
            </connector>
            <label>Grow Primary RM available?</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks if the incoming call record is a voicemail call</description>
        <name>Is_Incoming_Call_a_Grow_VM_Record</name>
        <label>Is Incoming Call a Grow VM Record?</label>
        <locationX>4719</locationX>
        <locationY>1532</locationY>
        <defaultConnector>
            <targetReference>Check_Primary_Relationship_Manager_Availability</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not a Voicemail</defaultConnectorLabel>
        <rules>
            <name>GrowVoicemail</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Voice_Call.CallOrigin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Voicemail</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Grow_OOH_AM_Business_Hours</targetReference>
            </connector>
            <label>GrowVoicemail</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check if the incoming call is a voicemail call</description>
        <name>Is_Incoming_Call_a_Voicemail</name>
        <label>Is Incoming Call a Voicemail?</label>
        <locationX>1463</locationX>
        <locationY>1130</locationY>
        <defaultConnector>
            <targetReference>Check_Protect_Primary_Relationship_Manager_Availability</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Voicemail</defaultConnectorLabel>
        <rules>
            <name>Voicemail</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Voice_Call.CallOrigin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Voicemail</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Protect_OOH_AM_Business_Hours</targetReference>
            </connector>
            <label>Voicemail</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Incoming_call_a_Voicemail1</name>
        <label>Is Incoming call a Voicemail?</label>
        <locationX>5082</locationX>
        <locationY>998</locationY>
        <defaultConnector>
            <targetReference>Is_Event_Attendee</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not an Voicemail</defaultConnectorLabel>
        <rules>
            <name>Voicemail1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Voice_Call.CallOrigin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Voicemail</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Get_Grow_OOH_AM_Business_Hours</targetReference>
            </connector>
            <label>Voicemail</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Incoming_call_a_Voicemail2</name>
        <label>Is Incoming call a Voicemail?</label>
        <locationX>6694</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not an Voicemail</defaultConnectorLabel>
        <rules>
            <name>Voicemail2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Voice_Call.CallOrigin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Voicemail</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_National_Protect_VM_Queue1</targetReference>
            </connector>
            <label>Voicemail</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is the contact owner of the customer record available to take calls?</description>
        <name>Is_Primary_Relationship_Manager_Available</name>
        <label>Is Primary Relationship Manager Available?</label>
        <locationX>5638</locationX>
        <locationY>1748</locationY>
        <defaultConnector>
            <targetReference>Assign_Relationship_Managers_State</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>Relationship_Manager_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Primary_Relationship_Manager_Availability.onlineAgentsCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_Primary_Relationship_Manager</targetReference>
            </connector>
            <label>Relationship Manager Available</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Primary_RM_available_for_VM</name>
        <label>Is Primary RM available for VM?</label>
        <locationX>1062</locationX>
        <locationY>1994</locationY>
        <defaultConnector>
            <targetReference>Assign_Relationship_Managers_VM_State</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Primary_RM_available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Primary_Relationship_Manager_Availability_for_VM.onlineAgentsCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_Protect_Primary_Relationship_Manager_for_VM</targetReference>
            </connector>
            <label>Primary RM available?</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks if the primary relationship manager is available for the protect customer</description>
        <name>Is_Protect_Primary_RM_Available</name>
        <label>Is Protect Primary RM Available</label>
        <locationX>2382</locationX>
        <locationY>1346</locationY>
        <defaultConnector>
            <targetReference>Assign_Relationship_Managers_State1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>RM Not Available</defaultConnectorLabel>
        <rules>
            <name>RM_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Protect_Primary_Relationship_Manager_Availability.onlineAgentsCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_Protect_Sensitive_Primary_Relationship_Manager</targetReference>
            </connector>
            <label>RM Available</label>
        </rules>
    </decisions>
    <decisions>
        <name>IsWIthinGrowBusinessHours</name>
        <label>IsWIthinGrowBusinessHours?</label>
        <locationX>4719</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Update_Customer_Portfolio_on_VoiceCallRecord_for_Grow</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>OutsideBusinessHours</defaultConnectorLabel>
        <rules>
            <name>Yes_Within_Grow</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_within_Grow_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Flag_within_Grow_Business_Hours_on_VoiceCallRecord</targetReference>
            </connector>
            <label>Yes Within Grow</label>
        </rules>
    </decisions>
    <decisions>
        <name>IsWIthinProtectBusinessHours</name>
        <label>IsWIthinProtectBusinessHours?</label>
        <locationX>1463</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Update_Customer_Portfolio_on_VoiceCallRecord_for_Protect</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>OutsideBusinessHours</defaultConnectorLabel>
        <rules>
            <name>Yes_Within_Protect</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_within_Protect_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Flag_within_Protect_Business_Hours_on_VoiceCallRecord</targetReference>
            </connector>
            <label>Yes Within Protect</label>
        </rules>
    </decisions>
    <decisions>
        <description>What is the Portfolio of the customer</description>
        <name>Protect_or_Grow</name>
        <label>Protect or Grow?</label>
        <locationX>4078</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Is_Incoming_call_a_Voicemail2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Match</defaultConnectorLabel>
        <rules>
            <name>Protect</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Protect</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetProtectBusinessHours</targetReference>
            </connector>
            <label>Protect</label>
        </rules>
        <rules>
            <name>Grow</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Grow</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetGrowBusinessHours</targetReference>
            </connector>
            <label>Grow</label>
        </rules>
    </decisions>
    <decisions>
        <name>Unmanaged_Attendee</name>
        <label>Unmanaged Attendee?</label>
        <locationX>2064</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Protect_or_Grow</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Elite_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Elite</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Customer.Customer_Attendee__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_Event_Experience_Queue</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this SMS sent inside OOH AM business hours?</description>
        <name>Within_Grow_OOH_AM_Business_Hours</name>
        <label>Within Grow OOH AM Business Hours</label>
        <locationX>3801</locationX>
        <locationY>1856</locationY>
        <defaultConnector>
            <targetReference>Get_Grow_OOH_PM_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Grow OOH AM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Grow_OOH_AM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Grow_OOH_AM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Relationship_Managers_Grow_VM_State1</targetReference>
            </connector>
            <label>Inside Grow OOH AM Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this sent inside OOH PM business hours?</description>
        <name>Within_Grow_OOH_PM_Business_Hours</name>
        <label>Within Grow OOH PM Business Hours</label>
        <locationX>4032</locationX>
        <locationY>2180</locationY>
        <defaultConnector>
            <targetReference>Check_Primary_Relationship_Manager_Availability_for_Grow_VM</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Grow OOH PM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Grow_OOH_PM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Grow_OOH_PM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Relationship_Managers_Grow_VM_State1</targetReference>
            </connector>
            <label>Inside Grow OOH PM Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this SMS sent inside OOH AM business hours?</description>
        <name>Within_Protect_OOH_AM_Business_Hours</name>
        <label>Within Protect OOH AM Business Hours</label>
        <locationX>545</locationX>
        <locationY>1454</locationY>
        <defaultConnector>
            <targetReference>Get_Protect_OOH_PM_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Protect OOH AM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Protect_OOH_AM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Protect_OOH_AM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Relationship_Managers_VM_State1</targetReference>
            </connector>
            <label>Inside Protect OOH AM Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this SMS sent inside OOH PM business hours?</description>
        <name>Within_Protect_OOH_PM_Business_Hours</name>
        <label>Within Protect OOH PM Business Hours</label>
        <locationX>776</locationX>
        <locationY>1778</locationY>
        <defaultConnector>
            <targetReference>Check_Primary_Relationship_Manager_Availability_for_VM</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Protect OOH PM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Protect_OOH_PM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Protect_OOH_PM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Relationship_Managers_VM_State1</targetReference>
            </connector>
            <label>Inside Protect OOH PM Business Hours</label>
        </rules>
    </decisions>
    <description>Omni-Channel Flow with logic for routing phone calls and voicemails to the appropriate relationship manager or a queue and also updates related record and business hours fields on voice call record.
SBET-1250 - Route Unmanaged Customers to E&amp;E Queue</description>
    <environments>Default</environments>
    <formulas>
        <name>formulaCalculateGrowTeamVMQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A&apos;,
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B&apos;,  
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;National_Grow_Voicemail&apos;
)</expression>
    </formulas>
    <formulas>
        <description>Formula outputs the DeveloperName of the Queue based on the Role of customers relationship manager to assign to the Grow state queue</description>
        <name>formulaCalculateStateGrowQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A_Voice&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B_Voice&apos;,
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A_Voice&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B_Voice&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A_Voice&apos;,
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B_Voice&apos;,  
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA_Voice&apos;,
    &apos;National_Grow_Voice&apos;
)</expression>
    </formulas>
    <formulas>
        <description>Formula outputs the DeveloperName of the Queue based on the Role of customers relationship manager to assign to the state queue</description>
        <name>formulaCalculateStateQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A_Voice&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B_Voice&apos;,
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A_Voice&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B_Voice&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A_Voice&apos;,
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B_Voice&apos;,  
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA_Voice&apos;,
    &apos;National_Protect_Voice&apos;
)</expression>
    </formulas>
    <formulas>
        <description>Formula outputs the DeveloperName of the Queue based on the Role of customers relationship manager</description>
        <name>formulaCalculateTeamQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},
    &apos;Premium_State_Manager_NSW&apos;, &apos;Premium_Managed_NSW_Voice&apos;,
    &apos;Premium_Team_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A_Voice&apos;,
    &apos;Premium_Team_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B_Voice&apos;,
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A_Voice&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B_Voice&apos;,
    &apos;Premium_State_Manager_VIC&apos;, &apos;Premium_Managed_VIC_Voice&apos;,
    &apos;Premium_Team_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A_Voice&apos;,
    &apos;Premium_Team_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B_Voice&apos;,
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A_Voice&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B_Voice&apos;,
    &apos;Premium_State_Manager_QLD&apos;, &apos;Premium_Managed_QLD_Voice&apos;,
    &apos;Premium_Team_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_Voice&apos;,
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_Voice&apos;,
    &apos;Premium_Team_Manager_WA&apos;, &apos;Premium_Managed_WA_Voice&apos;,
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA_Voice&apos;,
    &apos;SCV_Basic_Queue_CCQ_04v9j0000006exF&apos;
)</expression>
    </formulas>
    <formulas>
        <name>formulaCalculateTeamVMQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A&apos;,
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B&apos;,  
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;National_Protect_Voicemail&apos;
)</expression>
    </formulas>
    <interviewLabel>Voice: Premium Phone Routing {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Voice: Premium Phone Routing</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>RoutingFlow</processType>
    <recordLookups>
        <description>get associated customer record of the incoming caller.</description>
        <name>Get_Customer</name>
        <label>Get Customer</label>
        <locationX>2064</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Unmanaged_Attendee</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Grow_Queue</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Voice_Call.Customer__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Grow_OOH_AM_Business_Hours</name>
        <label>Get Grow OOH AM Business Hours</label>
        <locationX>3801</locationX>
        <locationY>1640</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Grow_OOH_AM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Grow Out of hours AM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Grow_OOH_PM_Business_Hours</name>
        <label>Get Grow OOH PM Business Hours</label>
        <locationX>4032</locationX>
        <locationY>1964</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Grow_OOH_PM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Grow Out of hours PM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Protect_OOH_AM_Business_Hours</name>
        <label>Get Protect OOH AM Business Hours</label>
        <locationX>545</locationX>
        <locationY>1238</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Protect_OOH_AM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Protect Out of hours AM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Protect_OOH_PM_Business_Hours</name>
        <label>Get Protect OOH PM Business Hours</label>
        <locationX>776</locationX>
        <locationY>1562</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Protect_OOH_PM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Protect Out of hours PM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get State Members VM Queue</description>
        <name>Get_State_Members_Grow_VM_Queue</name>
        <label>Get State Members Grow VM Queue</label>
        <locationX>4714</locationX>
        <locationY>2612</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Relationship_Manager_Grow_State_Available_VM</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varQueueDeveloperName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get State Members Queue</description>
        <name>Get_State_Members_Queue</name>
        <label>Get State Members Queue</label>
        <locationX>6034</locationX>
        <locationY>1964</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Relationship_Manager_State_Available</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varQueueDeveloperName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get State Members Queue</description>
        <name>Get_State_Members_Queue1</name>
        <label>Get State Members Queue</label>
        <locationX>2778</locationX>
        <locationY>1562</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Relationship_Manager_State_Available1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varQueueDeveloperName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get State Members VM Queue</description>
        <name>Get_State_Members_VM_Queue</name>
        <label>Get State Members VM Queue</label>
        <locationX>1458</locationX>
        <locationY>2210</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Relationship_Manager_State_Available_VM</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varQueueDeveloperName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get State Members VM Queue</description>
        <name>Get_State_Members_VM_Queue1</name>
        <label>Get State Members VM Queue</label>
        <locationX>545</locationX>
        <locationY>3194</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_VM_State1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varQueueDeveloperName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get State Members VM Queue</description>
        <name>Get_State_Members_VM_Queue2</name>
        <label>Get State Members VM Queue</label>
        <locationX>3801</locationX>
        <locationY>3596</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_VM_State2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varQueueDeveloperName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the incoming voice call record that has been created in salesforce</description>
        <name>Get_Voice_Call</name>
        <label>Get Voice Call</label>
        <locationX>2064</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetGrowBusinessHours</name>
        <label>GetGrowBusinessHours</label>
        <locationX>4719</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_within_Grow_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National Grow</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetProtectBusinessHours</name>
        <label>GetProtectBusinessHours</label>
        <locationX>1463</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_within_Protect_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National Protect</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Flag_within_Grow_Business_Hours_on_VoiceCallRecord</name>
        <label>Flag within Grow Business Hours on VoiceCallRecord</label>
        <locationX>4356</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Is_Incoming_Call_a_Grow_VM_Record</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Check_Primary_Relationship_Manager_Availability</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>Get_Customer.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>Check_within_Grow_Business_Hours.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>VoiceCall</object>
    </recordUpdates>
    <recordUpdates>
        <name>Flag_within_Protect_Business_Hours_on_VoiceCallRecord</name>
        <label>Flag within Protect Business Hours on VoiceCallRecord</label>
        <locationX>1199</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Is_Incoming_Call_a_Voicemail</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Is_Incoming_Call_a_Voicemail</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>Get_Customer.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>Check_within_Protect_Business_Hours.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>VoiceCall</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Customer_Portfolio_on_VoiceCallRecord_for_Grow</name>
        <label>Update Customer Portfolio on VoiceCallRecord for Grow</label>
        <locationX>5082</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Is_Incoming_call_a_Voicemail1</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Is_Incoming_call_a_Voicemail1</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>Get_Customer.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <object>VoiceCall</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Customer_Portfolio_on_VoiceCallRecord_for_Protect</name>
        <label>Update Customer Portfolio on VoiceCallRecord for Protect</label>
        <locationX>1727</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Is_Incoming_Call_a_Voicemail</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Is_Incoming_Call_a_Voicemail</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>Get_Customer.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <object>VoiceCall</object>
    </recordUpdates>
    <start>
        <locationX>1938</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Voice_Call</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>callernumber</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Customer/Account record Id</description>
        <name>customerID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the Developer Name of the Queue to route work to</description>
        <name>varQueueDeveloperName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
