<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>53.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Auto Close Cases with
Subject: Managed Self Exclusions
OwnerName: Premium Service</description>
    <interviewLabel>Case Auto {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case Auto Close</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Get User with 
Name: Premium Service</description>
        <name>Managed_Self_Exclusions_Cases</name>
        <label>Managed Self Exclusions Cases</label>
        <locationX>175</locationX>
        <locationY>368</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Auto_Close_Case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>QueueId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>00G2y000000USFaEAO</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>QueueSobject</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Subject: Managed Self Exclusions
OwnerName: Premium Service</description>
        <name>Auto_Close_Case</name>
        <label>Auto Close Case</label>
        <locationX>349</locationX>
        <locationY>365</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OwnerId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Managed_Self_Exclusions_Cases.QueueId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Closure_Comments__c</field>
            <value>
                <stringValue>Customer Owner By Premium Service - No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Customer Owner By Premium Service - No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Outcome__c</field>
            <value>
                <stringValue>Not Contacted</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>Managed_Self_Exclusions_Cases</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Managed Self Exclusions</stringValue>
            </value>
        </filters>
        <object>Case</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
