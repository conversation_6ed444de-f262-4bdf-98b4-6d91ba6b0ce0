<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns the CI Queue for the CI Rebase Review</description>
        <name>Assign_CI_Queue</name>
        <label>Assign CI Queue</label>
        <locationX>1546</locationX>
        <locationY>1308</locationY>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_CI_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_CI_Rebase_Review</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the values for the CI Rebase Review and adds to collection</description>
        <name>Assign_CI_Rebase_Review</name>
        <label>Assign CI Rebase Review</label>
        <locationX>1678</locationX>
        <locationY>1500</locationY>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Team__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Customer Integrity</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Rebase_Record__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Rebase_Review_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>varRebaseReviewRecordSingle</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Is_R_T_Review_Required</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the Customer Id to Rebase Record</description>
        <name>Assign_Customer</name>
        <label>Assign Customer</label>
        <locationX>50</locationX>
        <locationY>1200</locationY>
        <assignmentItems>
            <assignToReference>$Record.Customer__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Customer.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Performance_Team_Queue</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the R&amp;T Queue for the R&amp;T Rebase Review</description>
        <name>Assign_R_T_Queue</name>
        <label>Assign R&amp;T Queue</label>
        <locationX>1546</locationX>
        <locationY>2016</locationY>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_R_T_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_R_T_Rebase_Review</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the values for the R&amp;T Rebase Review and adds to collection</description>
        <name>Assign_R_T_Rebase_Review</name>
        <label>Assign R&amp;T Rebase Review</label>
        <locationX>1678</locationX>
        <locationY>2208</locationY>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Team__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Risk &amp; Trade</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Rebase_Record__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Rebase_Review_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>varRebaseReviewRecordSingle</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Is_SG_Review_Required</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Performance Team Queue to Rebase record</description>
        <name>Assign_Rebase_Queue</name>
        <label>Assign Rebase Queue</label>
        <locationX>50</locationX>
        <locationY>1608</locationY>
        <assignmentItems>
            <assignToReference>$Record.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Performance_Team_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Rebase_Review_Status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Rebase Status to either Team Review or Performance Review depending if any reviews are required</description>
        <name>Assign_Rebase_Review_Status</name>
        <label>Assign Rebase Review Status</label>
        <locationX>182</locationX>
        <locationY>1800</locationY>
        <assignmentItems>
            <assignToReference>$Record.Rebase_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaRebaseStatus</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Rebase_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the User Id for the Relationship Manager to the Rebase Record</description>
        <name>Assign_Relationship_Manager</name>
        <label>Assign Relationship Manager</label>
        <locationX>162</locationX>
        <locationY>708</locationY>
        <assignmentItems>
            <assignToReference>$Record.Relationship_Manager__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Single_Record.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Single_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the RM as the OwnerId and Assigned To for the RM Rebase Review</description>
        <name>Assign_RM_Owner_Assigned_To</name>
        <label>Assign RM Owner &amp; Assigned To</label>
        <locationX>1678</locationX>
        <locationY>600</locationY>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Relationship_Manager__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Assigned_To__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Relationship_Manager__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_RM_Rebase_Review</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the values for the RM Rebase Review and adds to collection</description>
        <name>Assign_RM_Rebase_Review</name>
        <label>Assign RM Rebase Review</label>
        <locationX>1810</locationX>
        <locationY>792</locationY>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Team__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Relationship Manager</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Rebase_Record__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Rebase_Review_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>varRebaseReviewRecordSingle</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Is_CI_Review_Required</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the SG Queue for the SG Rebase Review</description>
        <name>Assign_SG_Queue</name>
        <label>Assign SG Queue</label>
        <locationX>1546</locationX>
        <locationY>2724</locationY>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_SG_Queue.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_SG_Rebase_Review</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the values for the SG Rebase Review and adds to collection</description>
        <name>Assign_SG_Rebase_Review</name>
        <label>Assign SG Rebase Review</label>
        <locationX>1678</locationX>
        <locationY>2916</locationY>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Team__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Safer Gambling</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Rebase_Record__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordSingle.Rebase_Review_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varRebaseReviewRecordCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>varRebaseReviewRecordSingle</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Rebase_Review_Records</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the number of User records returned</description>
        <name>Assign_User_Record_Count</name>
        <label>Assign User Record Count</label>
        <locationX>182</locationX>
        <locationY>384</locationY>
        <assignmentItems>
            <assignToReference>numberCountofUserRecords</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Relationship_Manager</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>User_Records_Returned</targetReference>
        </connector>
    </assignments>
    <customErrors>
        <description>Display custom error message when there is a flow fault</description>
        <name>Error_Message</name>
        <label>Error Message</label>
        <locationX>490</locationX>
        <locationY>2124</locationY>
        <customErrorMessages>
            <errorMessage>There was a fault in the Rebase: Create Rebase Review Records Flow

Flow Fault Message: {!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <description>Display custom error message when there is a flow fault</description>
        <name>Error_Message_Async_Path</name>
        <label>Error Message Async Path</label>
        <locationX>2426</locationX>
        <locationY>3324</locationY>
        <customErrorMessages>
            <errorMessage>There was a fault in the Rebase: Create Rebase Review Records Flow

Flow Fault Message: {!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <description>Are any reviews required?</description>
        <name>Are_Rebase_Reviews_Required</name>
        <label>Are Rebase Reviews Required?</label>
        <locationX>2327</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>No Reviews Required</defaultConnectorLabel>
        <rules>
            <name>Reviews_Required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Review_Required_Relationship_Manager__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Review_Required_Customer_Integrity__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Review_Required_Risk_Trade__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Review_Required_Safer_Gambling__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_RM_Review_Required</targetReference>
            </connector>
            <label>Reviews Required</label>
        </rules>
    </decisions>
    <decisions>
        <description>Does Customer Integrity need a Rebase Review?</description>
        <name>Is_CI_Review_Required</name>
        <label>Is CI Review Required?</label>
        <locationX>1964</locationX>
        <locationY>984</locationY>
        <defaultConnector>
            <targetReference>Is_R_T_Review_Required</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Review</defaultConnectorLabel>
        <rules>
            <name>CI_Review</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Review_Required_Customer_Integrity__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_CI_Queue</targetReference>
            </connector>
            <label>CI Review</label>
        </rules>
    </decisions>
    <decisions>
        <description>Does Risk &amp; Trade need a Rebase Review?</description>
        <name>Is_R_T_Review_Required</name>
        <label>Is R&amp;T Review Required?</label>
        <locationX>1964</locationX>
        <locationY>1692</locationY>
        <defaultConnector>
            <targetReference>Is_SG_Review_Required</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Review</defaultConnectorLabel>
        <rules>
            <name>R_T_Review</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Review_Required_Risk_Trade__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_R_T_Queue</targetReference>
            </connector>
            <label>R&amp;T Review</label>
        </rules>
    </decisions>
    <decisions>
        <description>Does a Relationship Manager need a Rebase Review?</description>
        <name>Is_RM_Review_Required</name>
        <label>Is RM Review Required?</label>
        <locationX>1964</locationX>
        <locationY>384</locationY>
        <defaultConnector>
            <targetReference>Is_CI_Review_Required</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Review</defaultConnectorLabel>
        <rules>
            <name>RM_Review</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Review_Required_Relationship_Manager__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Relationship_Manager_on_Rebase</targetReference>
            </connector>
            <label>RM Review</label>
        </rules>
    </decisions>
    <decisions>
        <description>Does Safer Gambling need a Rebase Review?</description>
        <name>Is_SG_Review_Required</name>
        <label>Is SG Review Required?</label>
        <locationX>1964</locationX>
        <locationY>2400</locationY>
        <defaultConnector>
            <targetReference>Create_Rebase_Review_Records</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Review</defaultConnectorLabel>
        <rules>
            <name>SG_Review</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Review_Required_Safer_Gambling__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_SG_Queue</targetReference>
            </connector>
            <label>SG Review</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was the Rebase Customer Integrity Team Queue found?</description>
        <name>Null_Check_CI_Queue_Found</name>
        <label>Null Check CI Queue Found</label>
        <locationX>1678</locationX>
        <locationY>1200</locationY>
        <defaultConnector>
            <targetReference>Assign_CI_Rebase_Review</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Queue</defaultConnectorLabel>
        <rules>
            <name>CI_Queue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_CI_Queue</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_CI_Queue</targetReference>
            </connector>
            <label>CI Queue</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was a Customer found?</description>
        <name>Null_Check_Customer_Found</name>
        <label>Null Check Customer Found</label>
        <locationX>182</locationX>
        <locationY>1092</locationY>
        <defaultConnector>
            <targetReference>Get_Performance_Team_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Customer</defaultConnectorLabel>
        <rules>
            <name>Customer_Found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Customer</targetReference>
            </connector>
            <label>Customer Found</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was the Performance Team Queue found?</description>
        <name>Null_Check_Performance_Team_Queue_Found</name>
        <label>Null Check Performance Team Queue Found</label>
        <locationX>182</locationX>
        <locationY>1500</locationY>
        <defaultConnector>
            <targetReference>Assign_Rebase_Review_Status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Found</defaultConnectorLabel>
        <rules>
            <name>Queue_Found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Performance_Team_Queue</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Rebase_Queue</targetReference>
            </connector>
            <label>Queue Found</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was the Rebase Risk &amp; Trade Team Queue found?</description>
        <name>Null_Check_R_T_Queue_Found</name>
        <label>Null Check R&amp;T Queue Found</label>
        <locationX>1678</locationX>
        <locationY>1908</locationY>
        <defaultConnector>
            <targetReference>Assign_R_T_Rebase_Review</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Queue</defaultConnectorLabel>
        <rules>
            <name>R_T_Queue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_R_T_Queue</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_R_T_Queue</targetReference>
            </connector>
            <label>R&amp;T Queue</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was the Rebase Safer Gambling Team Queue found?</description>
        <name>Null_Check_SG_Queue_Found</name>
        <label>Null Check SG Queue Found</label>
        <locationX>1678</locationX>
        <locationY>2616</locationY>
        <defaultConnector>
            <targetReference>Assign_SG_Rebase_Review</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Queue</defaultConnectorLabel>
        <rules>
            <name>SG_Queue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_R_T_Queue</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_SG_Queue</targetReference>
            </connector>
            <label>SG Queue</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is there a Relationship Manager on the parent Rebase record</description>
        <name>Relationship_Manager_on_Rebase</name>
        <label>Relationship Manager on Rebase?</label>
        <locationX>1810</locationX>
        <locationY>492</locationY>
        <defaultConnector>
            <targetReference>Assign_RM_Rebase_Review</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No RM</defaultConnectorLabel>
        <rules>
            <name>RM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Relationship_Manager__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_RM_Owner_Assigned_To</targetReference>
            </connector>
            <label>RM</label>
        </rules>
    </decisions>
    <decisions>
        <description>How many user records were returned?</description>
        <name>User_Records_Returned</name>
        <label>User Records Returned?</label>
        <locationX>182</locationX>
        <locationY>492</locationY>
        <defaultConnector>
            <targetReference>Get_Customer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>None or Multiple Records</defaultConnectorLabel>
        <rules>
            <name>Single_Record</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>numberCountofUserRecords</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Single_Record</targetReference>
            </connector>
            <label>Single Record</label>
        </rules>
    </decisions>
    <description>Flows runs on creation of Rebase record. Creates all the relevant Rebase Review records based on if they are required from the review required fields on Rebase record. Runs asynchronously to allow separate transaction and allow longer processing time to handle larger volume</description>
    <environments>Default</environments>
    <formulas>
        <description>Stores value for Rebase Status depending on if any reviews are required</description>
        <name>formulaRebaseStatus</name>
        <dataType>String</dataType>
        <expression>IF(
  OR(
    {!$Record.Review_Required_Customer_Integrity__c},
    {!$Record.Review_Required_Relationship_Manager__c},
    {!$Record.Review_Required_Risk_Trade__c},
    {!$Record.Review_Required_Safer_Gambling__c}
  ),
  &quot;Team Review&quot;,
  &quot;Performance Review&quot;
)</expression>
    </formulas>
    <interviewLabel>Rebase: Create Rebase Review Records {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rebase: Create Rebase Review Records</label>
    <loops>
        <description>Loops through the single record in collection to extract the User Id</description>
        <name>Loop_Single_Record</name>
        <label>Loop Single Record</label>
        <locationX>74</locationX>
        <locationY>600</locationY>
        <collectionReference>Get_Relationship_Manager</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Relationship_Manager</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Customer</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>Creates all of the relevant Rebase Reviews in one collection for each team</description>
        <name>Create_Rebase_Review_Records</name>
        <label>Create Rebase Review Records</label>
        <locationX>1964</locationX>
        <locationY>3108</locationY>
        <faultConnector>
            <targetReference>Rebase_Flow_Fault_Handler_Async_Path</targetReference>
        </faultConnector>
        <inputReference>varRebaseReviewRecordCollection</inputReference>
    </recordCreates>
    <recordLookups>
        <description>Gets the Queue Id for the Rebase Customer Integrity Team Queue</description>
        <name>Get_CI_Queue</name>
        <label>Get CI Queue</label>
        <locationX>1678</locationX>
        <locationY>1092</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_CI_Queue_Found</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler_Async_Path</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Rebase_Customer_Integrity_Team_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the unique RB Customer Id value</description>
        <name>Get_Customer</name>
        <label>Get Customer</label>
        <locationX>182</locationX>
        <locationY>984</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_Customer_Found</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Customer_Id__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.RB_Customer_ID__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Queue for the Rebase Performance Team</description>
        <name>Get_Performance_Team_Queue</name>
        <label>Get Performance Team Queue</label>
        <locationX>182</locationX>
        <locationY>1392</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_Performance_Team_Queue_Found</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Rebase_Performance_Team_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Queue Id for the Rebase Risk &amp; Trade Team Queue</description>
        <name>Get_R_T_Queue</name>
        <label>Get R&amp;T Queue</label>
        <locationX>1678</locationX>
        <locationY>1800</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_R_T_Queue_Found</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler_Async_Path</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Rebase_Risk_Trade_Team_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the User record that matches the Full Name from RB_Relationship_Manager__c field</description>
        <name>Get_Relationship_Manager</name>
        <label>Get Relationship Manager</label>
        <locationX>182</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_User_Record_Count</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.RB_Relationship_Manager__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <queriedFields>Id</queriedFields>
        <sortField>LastLoginDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Queue Id for the Rebase Safer Gambling Team Queue</description>
        <name>Get_SG_Queue</name>
        <label>Get SG Queue</label>
        <locationX>1678</locationX>
        <locationY>2508</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_SG_Queue_Found</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rebase_Flow_Fault_Handler_Async_Path</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Rebase_Safer_Gambling_Team_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates the Rebase record with new values</description>
        <name>Update_Rebase_Record</name>
        <label>Update Rebase Record</label>
        <locationX>182</locationX>
        <locationY>1908</locationY>
        <faultConnector>
            <targetReference>Rebase_Flow_Fault_Handler</targetReference>
        </faultConnector>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>1128</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Relationship_Manager</targetReference>
        </connector>
        <object>Rebase__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Are_Rebase_Reviews_Required</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <description>Subflow to handle Flow Faults, sends email to Performance Team members</description>
        <name>Rebase_Flow_Fault_Handler</name>
        <label>Rebase Flow Fault Handler</label>
        <locationX>490</locationX>
        <locationY>2016</locationY>
        <connector>
            <targetReference>Error_Message</targetReference>
        </connector>
        <flowName>Rebase_Flow_Fault_Handler</flowName>
        <inputAssignments>
            <name>varFlowCurrentDateTime</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowFaultMessage</name>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowName</name>
            <value>
                <stringValue>Rebase: Create Rebase Review Records</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowUser</name>
            <value>
                <elementReference>$User.Username</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <description>Subflow to handle Flow Faults, sends email to Performance Team members</description>
        <name>Rebase_Flow_Fault_Handler_Async_Path</name>
        <label>Rebase Flow Fault Handler Async Path</label>
        <locationX>2426</locationX>
        <locationY>3216</locationY>
        <connector>
            <targetReference>Error_Message_Async_Path</targetReference>
        </connector>
        <flowName>Rebase_Flow_Fault_Handler</flowName>
        <inputAssignments>
            <name>varFlowCurrentDateTime</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowFaultMessage</name>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowName</name>
            <value>
                <stringValue>Rebase: Create Rebase Review Records</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varFlowUser</name>
            <value>
                <elementReference>$User.Username</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <description>Stores the number of User records returned from Get records element</description>
        <name>numberCountofUserRecords</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <description>Stores the collection of Rebase Review records to be created</description>
        <name>varRebaseReviewRecordCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Rebase_Review__c</objectType>
    </variables>
    <variables>
        <description>Stores the values for the single Rebase Review record for each type of review</description>
        <name>varRebaseReviewRecordSingle</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Rebase_Review__c</objectType>
    </variables>
</Flow>
