<!--
  @description       : 
  <AUTHOR> <PERSON><PERSON>C
  @group             : 
  @last modified on  : 09-12-2021
  @last modified by  : <PERSON><PERSON> DXC
-->
<aura:component implements="lightning:appHomeTemplate"
description="Three Columns layout"
access="global">
<aura:attribute name="header" type="Aura.Component[]" access="global"/>
<aura:attribute name="left" type="Aura.Component[]" access="global"/>
<aura:attribute name="center" type="Aura.Component[]" access="global"/>


    <div aura:id="container">
       
        <lightning:layout>
            <lightning:layoutItem  aura:id="leftColumn" size="2">
                {!v.left}
            </lightning:layoutItem>
            <lightning:layoutItem  aura:id="centerColumn" size="10" class="center">
                {!v.header}
            </lightning:layoutItem>
            
        </lightning:layout>
    </div>

</aura:component>