<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assign the Customer record from the End User Customer field to the Customer field</description>
        <name>Assign_End_User_to_Customer</name>
        <label>Assign End User to Customer</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <assignmentItems>
            <assignToReference>$Record.Customer__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.MessagingEndUser.AccountId</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <description>Used to copy the value from the End User Customer field to the Customer field so that the Messaging Session record appears in the related list on the Customer record</description>
    <environments>Default</environments>
    <interviewLabel>Messaging Session: On Create Assign Customer {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Messaging Session: On Create Assign Customer</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assign_End_User_to_Customer</targetReference>
        </connector>
        <object>MessagingSession</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
