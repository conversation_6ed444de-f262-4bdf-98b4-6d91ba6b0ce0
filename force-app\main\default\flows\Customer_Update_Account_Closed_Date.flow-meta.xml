<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Status_Checker</name>
        <label>Status Checker</label>
        <locationX>473</locationX>
        <locationY>127</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Account_is_Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Closed_Date_to_Blank</targetReference>
            </connector>
            <label>Account is Active</label>
        </rules>
        <rules>
            <name>Account_is_Closed</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Suspended</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active Suspended</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Timeout</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Closed_Date_to_TODAY</targetReference>
            </connector>
            <label>Account is Closed</label>
        </rules>
    </decisions>
    <description>- Wipes out/populates the Account Closed Date Value when the Account is reactivated or deactivated</description>
    <interviewLabel>Customer: Update Account Closed Date {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer: Update Account Closed Date</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Wipes out the Account Closed Date Value when the Account is reactivated</description>
        <name>Update_Closed_Date_to_Blank</name>
        <label>Update Closed Date to Blank</label>
        <locationX>480</locationX>
        <locationY>283</locationY>
        <inputAssignments>
            <field>Account_Closed_Date__c</field>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Populates the Account Closed Date with today&apos;s date when the Account is deactivated</description>
        <name>Update_Closed_Date_to_TODAY</name>
        <label>Update Closed Date to TODAY</label>
        <locationX>717</locationX>
        <locationY>128</locationY>
        <inputAssignments>
            <field>Account_Closed_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>Status_Checker</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account_Status__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
