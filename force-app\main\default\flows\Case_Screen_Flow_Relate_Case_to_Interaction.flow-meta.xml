<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Case_Reason_and_Summary</name>
        <label>Assign Case Reason and Summary</label>
        <locationX>842</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>varCaseRecord.Reason</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Voice_Call.Call_Reason__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varCaseRecord.Case_Summary__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Voice_Call.Call_Summary__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Cases</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the Case Id from the selected Case in the data table to a variable</description>
        <name>Assign_Existing_Case_Id</name>
        <label>Assign Existing Case Id</label>
        <locationX>974</locationX>
        <locationY>1082</locationY>
        <assignmentItems>
            <assignToReference>varSelectedCaseId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>datatableExistingCases.firstSelectedRow.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>AssignCaseReason</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Subject</name>
        <label>Assign Subject</label>
        <locationX>710</locationX>
        <locationY>1406</locationY>
        <assignmentItems>
            <assignToReference>varCaseRecord.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varCaseRecord.Reason</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>CreateCase</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignCaseId</name>
        <label>AssignCaseId</label>
        <locationX>710</locationX>
        <locationY>1622</locationY>
        <assignmentItems>
            <assignToReference>varSelectedCaseId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varCaseRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>CaseReasonVAR</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varCaseRecord.Reason</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>CaseSummaryVAR</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varCaseRecord.Case_Summary__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Case_for_which_interaction_channel</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignCaseReason</name>
        <label>AssignCaseReason</label>
        <locationX>974</locationX>
        <locationY>1190</locationY>
        <assignmentItems>
            <assignToReference>CaseReasonVAR</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>datatableExistingCases.firstSelectedRow.Reason</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>CaseSummaryVAR</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>datatableExistingCases.firstSelectedRow.Case_Summary__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Case_for_which_interaction_channel</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignCaseVariables</name>
        <label>AssignCaseVariables</label>
        <locationX>710</locationX>
        <locationY>1190</locationY>
        <assignmentItems>
            <assignToReference>varCaseRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetPMCaseRecordType.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varCaseRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varCustomerRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varCaseRecord.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>In Progress</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varCaseRecord.Customer_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varCustomerRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varCaseRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varCustomerRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_New_Case_Screen</targetReference>
        </connector>
    </assignments>
    <choices>
        <description>Choice used in radio button for user to select</description>
        <name>choiceNewCase</name>
        <choiceText>Create new Case</choiceText>
        <dataType>String</dataType>
    </choices>
    <choices>
        <description>Choice used in radio button for user to select</description>
        <name>choiceRelateToExistingCase</name>
        <choiceText>Relate to existing Case</choiceText>
        <dataType>String</dataType>
    </choices>
    <decisions>
        <description>Is the new Case for a Messaging Session or Voice Call</description>
        <name>Case_for_which_interaction_channel</name>
        <label>Case for which interaction channel</label>
        <locationX>842</locationX>
        <locationY>1814</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Messaging_Session_Case</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>MessagingSession</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Messaging_Session</targetReference>
            </connector>
            <label>Messaging Session Case</label>
        </rules>
        <rules>
            <name>Voice_Call_Case</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VoiceCall</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Voice_Call</targetReference>
            </connector>
            <label>Voice Call Case</label>
        </rules>
    </decisions>
    <decisions>
        <description>Did the user select to create a new Case or relate an existing Case?</description>
        <name>New_or_existing_Case_selected</name>
        <label>New or existing Case selected</label>
        <locationX>842</locationX>
        <locationY>974</locationY>
        <defaultConnector>
            <targetReference>Assign_Existing_Case_Id</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Existing Case</defaultConnectorLabel>
        <rules>
            <name>New_Case</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>choiceNewCase</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetPMCaseRecordType</targetReference>
            </connector>
            <label>New Case</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is the interaction for a Messaging Session or Voice Call</description>
        <name>Which_interaction_channel</name>
        <label>Which interaction channel</label>
        <locationX>842</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Messaging_Session</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>MessagingSession</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Messaging_Session</targetReference>
            </connector>
            <label>Messaging Session</label>
        </rules>
        <rules>
            <name>Voice_Call</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VoiceCall</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Voice_Call</targetReference>
            </connector>
            <label>Voice Call</label>
        </rules>
    </decisions>
    <description>Screen pop when receiving Messagaging Session or Voice Call through Omnichannel so the User can relate the interaction to a new or existing Case - change to Long Text Area</description>
    <dynamicChoiceSets>
        <description>Used to display values of Case Reason field from the Case object</description>
        <name>choiceCaseReason</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Reason</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>LinkedCaseNumberFormula</name>
        <dataType>String</dataType>
        <expression>IF( {!Entity_Definition_Find_Object_Type.varObjectDeveloperName} = &quot;MessagingSession&quot;,  {!Get_Messaging_Session.Case.CaseNumber}, {!Get_Voice_Call.Case__r.CaseNumber})</expression>
    </formulas>
    <interviewLabel>Case Screen Flow: Relate Case to Interaction {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case Screen Flow: Relate Case to Interaction</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>CreateCase</name>
        <label>CreateCase</label>
        <locationX>710</locationX>
        <locationY>1514</locationY>
        <connector>
            <targetReference>AssignCaseId</targetReference>
        </connector>
        <inputReference>varCaseRecord</inputReference>
    </recordCreates>
    <recordLookups>
        <description>Gets all the open Cases related to this Customer</description>
        <name>Get_Cases</name>
        <label>Get Cases</label>
        <locationX>842</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Select_New_or_Existing_Case</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Fault_Path_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsClosed</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varCustomerRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the current Messaging Session from the recordId</description>
        <name>Get_Messaging_Session</name>
        <label>Get Messaging Session</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Messaging_Session_Customer</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Fault_Path_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingSession</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the Messaging Session</description>
        <name>Get_Messaging_Session_Customer</name>
        <label>Get Messaging Session Customer</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Cases</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Fault_Path_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Messaging_Session.MessagingEndUser.AccountId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Gets the current Voice Call record from the recordId</description>
        <name>Get_Voice_Call</name>
        <label>Get Voice Call</label>
        <locationX>842</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Voice_Call_Customer</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Fault_Path_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the Voice Call</description>
        <name>Get_Voice_Call_Customer</name>
        <label>Get Voice Call Customer</label>
        <locationX>842</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Case_Reason_and_Summary</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Fault_Path_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Voice_Call.RelatedRecordId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>GetPMCaseRecordType</name>
        <label>GetPMCaseRecordType</label>
        <locationX>710</locationX>
        <locationY>1082</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>AssignCaseVariables</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates the current Messaging Session with the Case Id of the new or existing selected Case</description>
        <name>Update_Messaging_Session</name>
        <label>Update Messaging Session</label>
        <locationX>314</locationX>
        <locationY>1922</locationY>
        <connector>
            <targetReference>Success</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Fault_Path_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>CaseId</field>
            <value>
                <elementReference>varSelectedCaseId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Message_Reason__c</field>
            <value>
                <elementReference>CaseReasonVAR</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Messaging_Session_Summary__c</field>
            <value>
                <elementReference>CaseSummaryVAR</elementReference>
            </value>
        </inputAssignments>
        <object>MessagingSession</object>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the current Voice Call with the Case Id of the new or existing selected Case</description>
        <name>Update_Voice_Call</name>
        <label>Update Voice Call</label>
        <locationX>842</locationX>
        <locationY>1922</locationY>
        <connector>
            <targetReference>Success</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Fault_Path_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Call_Reason__c</field>
            <value>
                <elementReference>varCaseRecord.Reason</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Call_Summary__c</field>
            <value>
                <elementReference>varCaseRecord.Case_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Case__c</field>
            <value>
                <elementReference>varSelectedCaseId</elementReference>
            </value>
        </inputAssignments>
        <object>VoiceCall</object>
    </recordUpdates>
    <screens>
        <description>Screen used to enter details of the new Case</description>
        <name>Create_New_Case_Screen</name>
        <label>Create New Case Screen</label>
        <locationX>710</locationX>
        <locationY>1298</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_Subject</targetReference>
        </connector>
        <fields>
            <fieldType>ObjectProvided</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <objectFieldReference>varCaseRecord.Reason</objectFieldReference>
        </fields>
        <fields>
            <fieldType>ObjectProvided</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <objectFieldReference>varCaseRecord.Case_Summary__c</objectFieldReference>
        </fields>
        <nextOrFinishButtonLabel>Create Case</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen to display an error message to the user in case of a fault during the flow</description>
        <name>Fault_Path_Screen</name>
        <label>Fault Path Screen</label>
        <locationX>1898</locationX>
        <locationY>866</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displaytextErrorMessage</name>
            <fieldText>&lt;p&gt;{!texttemplateErrorSLDS}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;Error&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>displaytextFlowFaultMessage</name>
            <fieldText>&lt;p&gt;An error has occurred during this flow.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;{!$Flow.FaultMessage}&lt;/p&gt;&lt;p&gt;{!$Flow.CurrentDateTime}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Please click finish and manually relate a Case to this interaction&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen for the user to select to create a new Case or choose from existing open Cases to relate to the current interaction</description>
        <name>Select_New_or_Existing_Case</name>
        <label>Select New or Existing Case</label>
        <locationX>842</locationX>
        <locationY>866</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>New_or_existing_Case_selected</targetReference>
        </connector>
        <fields>
            <name>LinkedCaseDisplayText</name>
            <fieldText>&lt;p&gt;This Interaction is already associated to case {!LinkedCaseNumberFormula}. Please choose from the options below if you would like to &lt;span style=&quot;font-size: 13px;&quot;&gt;reassociate&lt;/span&gt; it to a different case.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>LinkedCaseNumberFormula</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Radio_Select_New_or_Existing_Case</name>
            <choiceReferences>choiceNewCase</choiceReferences>
            <choiceReferences>choiceRelateToExistingCase</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>choiceNewCase</defaultSelectedChoiceReference>
            <fieldText>Select New or Existing Case</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>datatableExistingCases</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Existing Cases</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Cases</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;LastModifiedDate&quot;,&quot;guid&quot;:&quot;column-00c2&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Last Modified Date&quot;,&quot;type&quot;:&quot;customDateTime&quot;},{&quot;apiName&quot;:&quot;Reason&quot;,&quot;guid&quot;:&quot;column-1c2a&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Case Reason&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Subject&quot;,&quot;guid&quot;:&quot;column-bfbb&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Subject&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Case_Summary__c&quot;,&quot;guid&quot;:&quot;column-f984&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Case Summary&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>choiceRelateToExistingCase</leftValueReference>
                    <operator>WasSelected</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Next</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Success</name>
        <label>Success</label>
        <locationX>842</locationX>
        <locationY>2162</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessText</name>
            <fieldText>&lt;p&gt;This Interaction was successfully Linked to case {!LinkedCaseNumberFormula}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>716</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Entity_Definition_Find_Object_Type</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <description>Gets the Object type from the recordId to identity the interaction channel</description>
        <name>Entity_Definition_Find_Object_Type</name>
        <label>Entity Definition: Find Object Type</label>
        <locationX>842</locationX>
        <locationY>134</locationY>
        <connector>
            <targetReference>Which_interaction_channel</targetReference>
        </connector>
        <flowName>Entity_Definition_Find_Object_Type</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <textTemplates>
        <description>Stores the SLDS div class to render error notifications in display text</description>
        <name>texttemplateErrorSLDS</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-scoped-notification slds-theme_warning&quot; role=&quot;status&quot;&gt;</text>
    </textTemplates>
    <variables>
        <name>CaseReasonVAR</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CaseSummaryVAR</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varCaseRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>varChannel</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varCustomerRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <description>Stores the value of the CaseId that the user selected from the data table</description>
        <name>varSelectedCaseId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
