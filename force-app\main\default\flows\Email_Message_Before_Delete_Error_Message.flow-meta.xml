<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <description>Error message the User receives when attempting delete</description>
        <name>Error_Message</name>
        <label>Error Message</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <customErrorMessages>
            <errorMessage>Email Messages for Premium Managed Cases cannot be deleted</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <environments>Default</environments>
    <interviewLabel>Email Message: Before Delete - Error Message {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Email Message: Before Delete - Error Message</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Error_Message</targetReference>
        </connector>
        <filterFormula>AND(
NOT(ISNULL({!$Record.ParentId})),
{!$Record.Parent.RecordType.DeveloperName} = &quot;Premium_Managed_Case&quot;
)</filterFormula>
        <object>EmailMessage</object>
        <recordTriggerType>Delete</recordTriggerType>
        <triggerType>RecordBeforeDelete</triggerType>
    </start>
    <status>Active</status>
</Flow>
