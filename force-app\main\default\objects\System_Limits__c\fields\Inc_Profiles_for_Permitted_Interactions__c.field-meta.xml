<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Inc_Profiles_for_Permitted_Interactions__c</fullName>
    <defaultValue>&quot;Team Member;API&quot;</defaultValue>
    <description>Only Customers (Person Accounts) owned by these Profiles will have the number of Inbound/Outbound Interactions limit enforced.</description>
    <encryptionScheme>None</encryptionScheme>
    <externalId>false</externalId>
    <inlineHelpText>Enter either a Profile Id (00e prefix), or a Profile Name.  Enter multiple values using a semicolon separator; e.g. the following would allow 3 profiles Team Member;API;System Administrator</inlineHelpText>
    <label>Inc Profiles for Permitted Interactions</label>
    <length>255</length>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
