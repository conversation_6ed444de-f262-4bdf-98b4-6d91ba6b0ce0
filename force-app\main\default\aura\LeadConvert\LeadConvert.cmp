<aura:component controller="LeadConvertCtrl" implements="force:hasRecordId,force:lightningQuickAction" 
access="global">
    <aura:handler name="init" value="{!this}" action="{!c.cmpInit}"/>
    <!--aura handler with waiting and donewaiting events-->
    <aura:handler event="aura:waiting" action="{!c.showSpinner}"/>
    <aura:handler event="aura:doneWaiting" action="{!c.hideSpinner}"/>

    <aura:attribute name="spinner" type="boolean" default="false" description="Attribute to show/hide spinner component"/>
    <aura:attribute name="successUI" type="boolean" default="false" description="To show success page after lead conversion"/>
    <aura:attribute name = "responseData" type="Map" description="Attribute to store the data returned by apex"></aura:attribute>
    <aura:attribute name = "errorMessage" type="string" description="To show error message returned by apex"></aura:attribute>
    <aura:attribute name="showErrorMessage" type="boolean" description="Attribute to show/hide error message"/>

    <!--loading spinner start-->
    <aura:if isTrue="{!v.spinner}">
        <div aura:id="spinnerId" class="slds-spinner_container">
            <lightning:spinner alternativeText="Loading" size="large" />
        </div>
    </aura:if>
    <!-- Loading spinner end-->   
    <aura:if isTrue="{!v.showErrorMessage}">
        <div class="slds-notify slds-notify_alert slds-theme_alert-texture slds-theme_error" role="alert">
            <span class="slds-assistive-text">error</span>
            <h2>{!v.errorMessage}</h2>
        </div>
    </aura:if>
    <aura:if isTrue="{!v.successUI}">
        <div style="width:100%">
            <lightning:layout multipleRows="true">
                <lightning:layoutItem size="12">
                    <p class="slds-text-heading_large">Your lead has been converted</p>
                </lightning:layoutItem>
                <lightning:layoutItem padding="around-small" size="12">
                    <img src="{!$Resource.LeadConvertImage}"/>
                </lightning:layoutItem>
                <lightning:layoutItem size="12">
                    <lightning:icon iconName="standard:account" alternativeText="Account" />
                    &nbsp;&nbsp;<a href="{!'/' + v.responseData.Id}"><b>{!v.responseData.Name}</b></a>
                </lightning:layoutItem>
                <lightning:layoutItem padding="around-small" size="12">
                    <lightning:layout>
                        <lightning:layoutItem size="6">
                            <b>Sportsbet Account Number: </b>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="6">
                            {!v.responseData.Sports_Bet_Account_Number__c}
                        </lightning:layoutItem>
                    </lightning:layout>
                </lightning:layoutItem>
                <lightning:layoutItem padding="around-small" size="12">
                    <lightning:layout>
                        <lightning:layoutItem size="6">
                            <b>Account Status: </b>
                        </lightning:layoutItem>
                        <lightning:layoutItem size="6">
                            {!v.responseData.Account_Status__c}
                        </lightning:layoutItem>
                    </lightning:layout>
                </lightning:layoutItem>
            </lightning:layout>
        </div>
    </aura:if>
</aura:component>