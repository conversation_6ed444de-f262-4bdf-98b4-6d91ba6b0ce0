<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>53.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>is_there_an_related_to_record</name>
        <label>is there an related to record</label>
        <locationX>182</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>check_related_to</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Related_to_Customer_Account</targetReference>
            </connector>
            <label>check related to</label>
        </rules>
    </decisions>
    <description>Update Last Interaction Date and Last Interaction Date (Without Bulk) with Task ActivityDate when Task Related To gets updated with Customer lookup</description>
    <interviewLabel>Interaction Task update on Customer Record {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Interaction Task update on Customer Record</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Get the Customer selected on the Related To Field</description>
        <name>Get_Related_to_Customer_Account</name>
        <label>Get Related to Customer(Account)</label>
        <locationX>50</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Related_To_Customer_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update Last Interaction Date and Last Interaction Date (Without Bulk) fields on the Customer Object based on Date of latest related Task(Interaction) Record</description>
        <name>Update_Related_To_Customer_Record</name>
        <label>Update Related To Customer Record</label>
        <locationX>50</locationX>
        <locationY>575</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Interaction_Date_Without_Bulk__c</field>
            <value>
                <elementReference>$Record.ActivityDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Last_Interaction_Date__c</field>
            <value>
                <elementReference>$Record.ActivityDate</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>is_there_an_related_to_record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Log_Method__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Dubber Integration</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>0122y0000004HSoAAM</stringValue>
            </value>
        </filters>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
