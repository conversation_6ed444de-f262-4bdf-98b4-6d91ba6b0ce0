<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Calls out to the Premium Managed Customer Summary prompt template to generate summary</description>
        <name>Premium_Managed_Customer_Summary_Not_on_Customer_Record</name>
        <label>Premium Managed Customer Summary Not on Customer Record</label>
        <locationX>1700</locationX>
        <locationY>1130</locationY>
        <actionName>0hfRE0000006VabNotFound</actionName>
        <actionType>generatePromptResponse</actionType>
        <connector>
            <targetReference>Update_AI_Account_Summary_Field_Not_On_Customer_Record</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>Input:Customer</name>
            <value>
                <elementReference>varCustomerRecord</elementReference>
            </value>
        </inputParameters>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Calls out to the Premium Managed Customer Summary prompt template to generate summary</description>
        <name>Premium_Managed_Customer_Summary_on_Customer_Record</name>
        <label>Premium Managed Customer Summary on Customer Record</label>
        <locationX>908</locationX>
        <locationY>1130</locationY>
        <actionName>0hfRE0000006VabNotFound</actionName>
        <actionType>generatePromptResponse</actionType>
        <connector>
            <targetReference>Update_AI_Account_Summary_Field_on_Customer_Record</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>Input:Customer</name>
            <value>
                <elementReference>varCustomerRecord</elementReference>
            </value>
        </inputParameters>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Reload_Customer_Account_Record_Page</name>
        <label>Reload Customer Account Record Page</label>
        <locationX>908</locationX>
        <locationY>1346</locationY>
        <actionName>c:navigate</actionName>
        <actionType>component</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>destinationName</name>
            <value>
                <stringValue>Account</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>destinationRecordId</name>
            <value>
                <elementReference>varCustomerRecord.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>destinationType</name>
            <value>
                <stringValue>record</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>destinationAction</name>
            <value>
                <stringValue>view</stringValue>
            </value>
        </inputParameters>
        <nameSegment>c:navigate</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>The purpose of this decision is to determine what screen the user is currently on.</description>
        <name>Current_Record_Screen</name>
        <label>Current Record Screen</label>
        <locationX>1304</locationX>
        <locationY>914</locationY>
        <defaultConnector>
            <targetReference>Generate_Summary_Not_On_Customer_Record</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>User_on_Customer_Record</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Account</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Generate_Summary_On_Customer_Record</targetReference>
            </connector>
            <label>User on Customer Record</label>
        </rules>
    </decisions>
    <decisions>
        <name>Customer_Record</name>
        <label>Customer Record</label>
        <locationX>1766</locationX>
        <locationY>806</locationY>
        <defaultConnector>
            <targetReference>Customer_Summary_Unavailable</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Customer</defaultConnectorLabel>
        <rules>
            <name>Customer_Found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varCustomerRecord.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varCustomerRecord</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varCustomerRecord</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Current_Record_Screen</targetReference>
            </connector>
            <label>Customer Found</label>
        </rules>
    </decisions>
    <decisions>
        <description>Which object was this Screen Flow launched from</description>
        <name>Which_object</name>
        <label>Which object</label>
        <locationX>1766</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Customer_Summary_Unavailable</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Object</defaultConnectorLabel>
        <rules>
            <name>Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Account</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Customer_Record</targetReference>
            </connector>
            <label>Customer</label>
        </rules>
        <rules>
            <name>Messaging_Session</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>MessagingSession</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Messaging_Session</targetReference>
            </connector>
            <label>Messaging Session</label>
        </rules>
        <rules>
            <name>Voice_Call</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VoiceCall</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Voice_Call</targetReference>
            </connector>
            <label>Voice Call</label>
        </rules>
        <rules>
            <name>Case</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Case</targetReference>
            </connector>
            <label>Case</label>
        </rules>
        <rules>
            <name>Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Entity_Definition_Find_Object_Type.varObjectDeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Task</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Task</targetReference>
            </connector>
            <label>Task</label>
        </rules>
    </decisions>
    <description>Screen flow that calls the Premium Managed Customer Summary Prompt Template Action and displays the output on screen.</description>
    <environments>Default</environments>
    <interviewLabel>Prompt: Screen - Customer Summary Prompt Output {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Prompt: Screen - Customer Summary Prompt Output</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Account</name>
        <label>Get Account</label>
        <locationX>1766</locationX>
        <locationY>698</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Customer_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varCustomerRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the current Case record from the recordId</description>
        <name>Get_Case</name>
        <label>Get Case</label>
        <locationX>1898</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Case_Customer</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the Case</description>
        <name>Get_Case_Customer</name>
        <label>Get Case Customer</label>
        <locationX>1898</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Account</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case.AccountId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customers account record</description>
        <name>Get_Customer_Record</name>
        <label>Get Customer Record</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Gets the current Messaging Session from the recordId</description>
        <name>Get_Messaging_Session</name>
        <label>Get Messaging Session</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Messaging_Session_Customer</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingSession</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the Messaging Session</description>
        <name>Get_Messaging_Session_Customer</name>
        <label>Get Messaging Session Customer</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Account</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Messaging_Session.MessagingEndUser.AccountId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Gets the current Task record from the recordId</description>
        <name>Get_Task</name>
        <label>Get Task</label>
        <locationX>2690</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Task_Customer</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the Task</description>
        <name>Get_Task_Customer</name>
        <label>Get Task Customer</label>
        <locationX>2690</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Account</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Task.WhatId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Gets the current Voice Call record from the recordId</description>
        <name>Get_Voice_Call</name>
        <label>Get Voice Call</label>
        <locationX>1106</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Voice_Call_Customer</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record from the Voice Call</description>
        <name>Get_Voice_Call_Customer</name>
        <label>Get Voice Call Customer</label>
        <locationX>1106</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Account</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Voice_Call.RelatedRecordId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>varCustomerRecord</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordUpdates>
        <name>Update_AI_Account_Summary_Field_Not_On_Customer_Record</name>
        <label>Update AI Account Summary Field Not On Customer Record</label>
        <locationX>1700</locationX>
        <locationY>1238</locationY>
        <connector>
            <targetReference>Re_Generate_Summary_Not_On_Customer_Record</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varCustomerRecord.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Einstein_Customer_Summary_Date_Time__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Einstein_Customer_Summary__c</field>
            <value>
                <elementReference>Premium_Managed_Customer_Summary_Not_on_Customer_Record.promptResponse</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_AI_Account_Summary_Field_on_Customer_Record</name>
        <label>Update AI Account Summary Field on Customer Record</label>
        <locationX>908</locationX>
        <locationY>1238</locationY>
        <connector>
            <targetReference>Reload_Customer_Account_Record_Page</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Account.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Einstein_Customer_Summary_Date_Time__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Einstein_Customer_Summary__c</field>
            <value>
                <elementReference>Premium_Managed_Customer_Summary_on_Customer_Record.promptResponse</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <description>Screen to advise user summary is unavailable</description>
        <name>Customer_Summary_Unavailable</name>
        <label>Customer Summary Unavailable</label>
        <locationX>2228</locationX>
        <locationY>914</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displayError</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;{!texttemplateWarningSLDS}Customer Summary Unavailable&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>displayNoObjectOrCustomer</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;A Einstein generated summary is not available to on this record&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;No customer was found related to this record or this object is not yet available to use this prompt &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Acknowledge</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Used to show user Flow Error when there is a fault or error in this Screen Flow</description>
        <name>Flow_Error_Screen</name>
        <label>Flow Error Screen</label>
        <locationX>1436</locationX>
        <locationY>1238</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displayFaultError</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;{!texttemplateWarningSLDS}Flow Error&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>displayFaultMessage</name>
            <fieldText>&lt;p&gt;An error has occurred. Please try again or send the below details to your Salesforce admin&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Flow Start Time: {!$Flow.InterviewStartTime}&lt;/p&gt;&lt;p&gt;Flow Name: Prompt: Screen - Customer Summary Prompt Output&lt;/p&gt;&lt;p&gt;Flow Error Message: {!$Flow.FaultMessage}&lt;/p&gt;&lt;p&gt;Flow User: {!$User.Username}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Try Again</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen for Customer to press button to initiate summary</description>
        <name>Generate_Summary_Not_On_Customer_Record</name>
        <label>Generate Summary Not On Customer Record</label>
        <locationX>1700</locationX>
        <locationY>1022</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Premium_Managed_Customer_Summary_Not_on_Customer_Record</targetReference>
        </connector>
        <fields>
            <name>displaySuccessForGenerateSummaryNotOnCustomerRecord</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;{!texttemplateSuccessSLDS}Customer Summary&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>GenerateSummaryNotOnCustomerRecord</name>
            <fieldText>&lt;p&gt;{!texttemplateInfoBox}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Einstein Account Summary Last Updated:&lt;/strong&gt; {!Get_Account.Einstein_Customer_Summary_Date_Time__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;{!Get_Account.Einstein_Customer_Summary__c}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>GenerateSummaryNotOnCustomerRecordPrompt</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;If you would like to refresh the Einstein Account Summary, select the &lt;/span&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Generate Summary&lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt; button.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Generate Summary</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen for Customer to press button to initiate summary</description>
        <name>Generate_Summary_On_Customer_Record</name>
        <label>Generate Summary On Customer Record</label>
        <locationX>908</locationX>
        <locationY>1022</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Premium_Managed_Customer_Summary_on_Customer_Record</targetReference>
        </connector>
        <fields>
            <name>displayGenerateSummary</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Einstein Account Summary Last Updated: {!Get_Account.Einstein_Customer_Summary_Date_Time__c}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;If you would like to refresh the Einstein Account Summary, select the &lt;strong&gt;Generate Summary&lt;/strong&gt; button.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Generate Summary</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen for Customer to press button to initiate summary</description>
        <name>Re_Generate_Summary_Not_On_Customer_Record</name>
        <label>Re-Generate Summary Not On Customer Record</label>
        <locationX>1700</locationX>
        <locationY>1346</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displaySuccessForRegenerateSummary</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;{!texttemplateSuccessSLDS}Customer Summary&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>displayReGenerateSummary</name>
            <fieldText>&lt;p&gt;{!texttemplateInfoBox}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Einstein Account Summary Last Updated:&lt;/strong&gt; {!$Flow.CurrentDateTime}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;{!Premium_Managed_Customer_Summary_Not_on_Customer_Record.promptResponse}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Generate Summary</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>1640</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Entity_Definition_Find_Object_Type</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <description>Gets the Object type from the recordId to identity the interaction channel</description>
        <name>Entity_Definition_Find_Object_Type</name>
        <label>Entity Definition: Find Object Type</label>
        <locationX>1766</locationX>
        <locationY>134</locationY>
        <connector>
            <targetReference>Which_object</targetReference>
        </connector>
        <flowName>Entity_Definition_Find_Object_Type</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <textTemplates>
        <name>texttemplateBlueBox</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-box slds-box_small slds-theme_alt-inverse&quot;&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>texttemplateInfoBox</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-box slds-box_small slds-theme_info&quot;&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>texttemplateSuccessSLDS</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-scoped-notification slds-theme_success&quot; role=&quot;status&quot;&gt;</text>
    </textTemplates>
    <textTemplates>
        <description>Stores the SLDS div class to render warning notifications in display text</description>
        <name>texttemplateWarningSLDS</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-scoped-notification slds-theme_warning&quot; role=&quot;status&quot;&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>txtPromptOutput</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;{!texttemplateInfoBox}{!Premium_Managed_Customer_Summary_on_Customer_Record.promptResponse}&lt;/strong&gt;&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <description>Stores the Id of the record the Screen flow is launched from as a text to be used for different purposes throughout the Flow</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varCustomerRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
</Flow>
