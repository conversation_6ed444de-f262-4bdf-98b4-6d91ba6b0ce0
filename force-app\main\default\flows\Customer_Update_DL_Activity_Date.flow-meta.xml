<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>57.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Limit_change</name>
        <label>Limit change</label>
        <locationX>314</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Increased_Above_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.API_Deposit_Limit_Amount__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.API_Deposit_Limit_Amount__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>0.1</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Update_Customer_DL_Activity_Date_TODAY</targetReference>
            </connector>
            <label>Increased - Above 0</label>
        </rules>
        <rules>
            <name>Decreased_to_0_or_Null</name>
            <conditionLogic>1 AND (2 OR 3)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.API_Deposit_Limit_Amount__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.API_Deposit_Limit_Amount__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.API_Deposit_Limit_Amount__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Remove_the_Date</targetReference>
            </connector>
            <label>Decreased to 0 or Null</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>NullDate</name>
        <dataType>Date</dataType>
        <expression>DATEVALUE(&quot;&quot;)</expression>
    </formulas>
    <formulas>
        <name>TodaysDate</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <interviewLabel>Customer - Update DL Activity Date {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer - Update DL Activity Date</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Copy_1_of_Update_Customer_DL_Activity_Date_TODAY</name>
        <label>Copy 1 of Update Customer DL Activity Date = TODAY</label>
        <locationX>50</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>DL_Active_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Remove_the_Date</name>
        <label>Remove the Date</label>
        <locationX>314</locationX>
        <locationY>455</locationY>
        <inputAssignments>
            <field>DL_Active_Date__c</field>
            <value>
                <elementReference>NullDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Limit_change</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>API_Deposit_Limit_Amount__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
