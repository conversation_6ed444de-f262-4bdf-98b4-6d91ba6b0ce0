<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Action to submit the case for approval and trigger the dedicated approval process.</description>
        <name>Submit_Case_for_Approval</name>
        <label>Submit Case for Approval</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>Assign_Case_to_Submitter_s_Manager</targetReference>
        </connector>
        <faultConnector>
            <targetReference>flowErrorMessage</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>Get_Case.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <elementReference>submissionComments</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Premium_Customer_Review_Approval_Manual</stringValue>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Check that all fields required for submission are populated on the Case</description>
        <name>Are_All_fields_Populated_on_Case</name>
        <label>Are All fields Populated on Case?</label>
        <locationX>446</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Error_Occurred</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not all fields populated</defaultConnectorLabel>
        <rules>
            <name>All_fields_are_populated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Case.Description</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Case.Premium_Review_Case_Type__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Case.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Submit_Case_for_Approval</targetReference>
            </connector>
            <label>All fields are populated</label>
        </rules>
    </decisions>
    <description>Flow that submits a PCR case for approval manually from the case record page.</description>
    <environments>Default</environments>
    <formulas>
        <description>Formula to create submission comments based on Case Field values</description>
        <name>submissionComments</name>
        <dataType>String</dataType>
        <expression>TEXT({!Get_Case.Premium_Review_Case_Type__c}) + &apos; - &apos; + TEXT({!Get_Case.Premium_Review_Case_Subtype__c}) + &apos; - &apos; + {!Get_Case.Description}</expression>
    </formulas>
    <interviewLabel>Screen Flow - Submit for Approval (Premium Customer Review) {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Screen Flow - Submit for Approval (Premium Customer Review)</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <description>Get case associated with input recordId</description>
        <name>Get_Case</name>
        <label>Get Case</label>
        <locationX>446</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Customer_Owner_s_Manager</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>flowErrorMessage</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Action to populate with the customer owner&apos;s manager where this action is triggered</description>
        <name>Get_Customer_Owner_s_Manager</name>
        <label>Get Customer Owner&apos;s Manager</label>
        <locationX>446</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Manager_field_on_Case</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>flowErrorMessage</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case.Account.Owner.Manager.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Action to reassign the</description>
        <name>Assign_Case_to_Submitter_s_Manager</name>
        <label>Assign Case to Submitter&apos;s Manager</label>
        <locationX>50</locationX>
        <locationY>674</locationY>
        <connector>
            <targetReference>SuccessMessage</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>flowErrorMessage</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Customer_Owner_s_Manager.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the field on the case that captures who the intended approver of this case is.</description>
        <name>Update_Manager_field_on_Case</name>
        <label>Update Manager field on Case</label>
        <locationX>446</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>Are_All_fields_Populated_on_Case</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>flowErrorMessage</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Owner_Manager__c</field>
            <value>
                <elementReference>Get_Customer_Owner_s_Manager.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <description>Error Screen which displays when case cannot be submitted</description>
        <name>Error_Occurred</name>
        <label>Missing Information</label>
        <locationX>842</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>instructionText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;color: rgb(247, 1, 1); font-size: 16px;&quot;&gt;The following fields need to be populated prior to submitting this case for approval:&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>customerMissing</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Customer Name&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Get_Case.Customer_Name__c</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>descriptionMissing</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Case Description&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Get_Case.Description</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>typeMissing</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Case Type&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Get_Case.Premium_Review_Case_Type__c</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen to show the flow error message if one occurs</description>
        <name>flowErrorMessage</name>
        <label>flowErrorMessage</label>
        <locationX>578</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>errorText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 18px; color: rgb(250, 0, 0);&quot;&gt;An error occurred&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 12px; color: rgb(8, 8, 8);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Success message after case has been successfully submitted</description>
        <name>SuccessMessage</name>
        <label>SuccessMessage</label>
        <locationX>50</locationX>
        <locationY>782</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>successText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Success&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;You have submitted this case for approval to {!Get_Customer_Owner_s_Manager.Name}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
