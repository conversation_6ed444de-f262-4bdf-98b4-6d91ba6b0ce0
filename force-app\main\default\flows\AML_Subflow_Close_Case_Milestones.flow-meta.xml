<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Milestone_ID_assignment</name>
        <label>Milestone ID assignment</label>
        <locationX>264</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>varMilestonesCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Case_Milestones.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Case_Milestones</targetReference>
        </connector>
    </assignments>
    <environments>Default</environments>
    <interviewLabel>AML Subflow: Close Case Milestones {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Subflow: Close Case Milestones</label>
    <loops>
        <name>Loop_Case_Milestones</name>
        <label>Loop Case Milestones</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <collectionReference>Get_Case_Milestones</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Milestone_ID_assignment</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Close_Case_Milestones</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Case_Milestones</name>
        <label>Get Case Milestones</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Case_Milestones</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>caseId</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCompleted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Close_Case_Milestones</name>
        <label>Close Case Milestones</label>
        <locationX>176</locationX>
        <locationY>542</locationY>
        <connector>
            <targetReference>Update_SLA_Completed_Date</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>caseId</elementReference>
            </value>
        </filters>
        <filters>
            <field>CompletionDate</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>varMilestonesCollection</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>CompletionDate</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <object>CaseMilestone</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_SLA_Completed_Date</name>
        <label>Update SLA Completed Date</label>
        <locationX>176</locationX>
        <locationY>650</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>caseId</elementReference>
            </value>
        </filters>
        <filters>
            <field>SLA_Completed_Date__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>SLA_Completed_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case_Milestones</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>Get Case Id from parent flow</description>
        <name>caseId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varMilestones</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>varMilestonesCollection</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
