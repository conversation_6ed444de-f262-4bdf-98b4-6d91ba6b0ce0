<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionCalls>
        <description>Redirects user to Case record</description>
        <name>Redirect_to_Case</name>
        <label>Redirect to Case</label>
        <locationX>176</locationX>
        <locationY>458</locationY>
        <actionName>c:navigate</actionName>
        <actionType>component</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>destinationName</name>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>destinationRecordId</name>
            <value>
                <elementReference>Create_Case_For_Email</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>destinationType</name>
            <value>
                <stringValue>record</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>destinationAction</name>
            <value>
                <stringValue>view</stringValue>
            </value>
        </inputParameters>
        <nameSegment>c:navigate</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Screen flow launched from Customer record to initiate outbound Case. Clicking start interaction will redirect to Case
SBET-960 Change voice call record call summary from Interaction_Summary__c to Comments</description>
    <dynamicChoiceSets>
        <description>Case Reason Picklist</description>
        <name>CaseReason</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Reason</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>Customer: Screen Flow Start Outbound Case {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer: Screen Flow Start Outbound Case</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Creates a new Case for the user to send an outbound email</description>
        <name>Create_Case_For_Email</name>
        <label>Create Case For Email</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>Redirect_to_Case</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_Fault_Message</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Case_Summary__c</field>
            <value>
                <elementReference>Case_Summary</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason</field>
            <value>
                <elementReference>Case_Reason</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Case_Record_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>In Progress</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>Subject</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <description>Gets the Premium Managed Case Record Type</description>
        <name>Get_Case_Record_Type</name>
        <label>Get Case Record Type</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Case_For_Email</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Screen_Fault_Message</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Case_Screen</name>
        <label>Case Screen</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Get_Case_Record_Type</targetReference>
        </connector>
        <fields>
            <name>Subject</name>
            <dataType>String</dataType>
            <fieldText>Subject</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Case_Reason</name>
            <choiceReferences>CaseReason</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Case Reason</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Case_Summary</name>
            <fieldText>Case Summary</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Screen to display fault message if a fault occurs</description>
        <name>Screen_Fault_Message</name>
        <label>Screen Fault Message</label>
        <locationX>704</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displaytextErrorMessage</name>
            <fieldText>&lt;p&gt;{!texttemplateErrorSLDS}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;ERROR&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>DisplayTextFaultMessage</name>
            <fieldText>&lt;p&gt;There was an error when initiating outbound interaction. Please click the &lt;strong&gt;finish&lt;/strong&gt; button and try again. If the problem persists contact your Manager or Salesforce Admin&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Error Message: {!$Flow.FaultMessage}&lt;/p&gt;&lt;p&gt;Error Time: {!$Flow.CurrentDateTime}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Case_Screen</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>Stores the SLDS div class to render error notifications in display text</description>
        <name>texttemplateErrorSLDS</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-scoped-notification slds-theme_error&quot; role=&quot;status&quot;&gt;</text>
    </textTemplates>
    <variables>
        <description>Stores the Customer record</description>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
</Flow>
