<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>57.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Intro_User</name>
        <label>Assign Intro User</label>
        <locationX>50</locationX>
        <locationY>755</locationY>
        <assignmentItems>
            <assignToReference>IntroTaskUser</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetNameOfUser.Name</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Populate_Intro_Field_on_Task</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Is_Name_of_User_populated</name>
        <label>Is Name of User populated</label>
        <locationX>182</locationX>
        <locationY>647</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetNameOfUser.Name</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Intro_User</targetReference>
            </connector>
            <label>Is Not Null</label>
        </rules>
    </decisions>
    <decisions>
        <name>TaskRecordType</name>
        <label>Task Record Type</label>
        <locationX>336</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decNotInteraction</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.Name</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Interaction</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Customer</targetReference>
            </connector>
            <label>Is Not Interaction</label>
        </rules>
    </decisions>
    <description>Modify to bypass if task is Interaction</description>
    <environments>Default</environments>
    <interviewLabel>Assign Intro Task User to Task {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Assign Intro Task User to Task</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Customer</name>
        <label>Get Customer</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetNameOfUser</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Intro_Task_Completed_By__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetNameOfUser</name>
        <label>Get Name of User</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Name_of_User_populated</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer.Intro_Task_Completed_By__r.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Populate_Intro_Field_on_Task</name>
        <label>Populate Intro Field on Task</label>
        <locationX>50</locationX>
        <locationY>863</locationY>
        <inputAssignments>
            <field>Intro_Task_Completed_By2__c</field>
            <value>
                <elementReference>IntroTaskUser</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>210</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>TaskRecordType</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>IntroTaskUser</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>IntroUser</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
</Flow>
