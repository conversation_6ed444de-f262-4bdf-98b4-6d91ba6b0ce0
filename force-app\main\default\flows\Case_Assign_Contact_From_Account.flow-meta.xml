<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns Contact Id using PersonContactId from Account</description>
        <name>Assign_Contact_Id</name>
        <label>Assign Contact Id</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <description>Used to assign the PersonContactId from the Account when Case is created with an AccountId or when the AccountId is changed</description>
    <environments>Default</environments>
    <interviewLabel>Case: Assign Contact From Account {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case: Assign Contact From Account</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assign_Contact_Id</targetReference>
        </connector>
        <filterFormula>OR(
AND(
ISNEW(),
NOT(ISBLANK({!$Record.AccountId})),
ISBLANK({!$Record.ContactId})
),
ISCHANGED({!$Record.AccountId})
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
