<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_NSER_Email_to_Customer_Owner</name>
        <label>Send NSER Email to Customer Owner</label>
        <locationX>204</locationX>
        <locationY>4271</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Customer_is_Blacklisted</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>ownerEmail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Premium Customer Excluded via BetStop</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>bestopEmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>IntroductionExisting</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Introduction Existing</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Account_Locked</name>
        <label>Account Locked</label>
        <locationX>336</locationX>
        <locationY>1463</locationY>
        <defaultConnector>
            <targetReference>AccountUnlocked</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decAccountLocked</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula2</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CreateAccountLockedAlertTask</targetReference>
            </connector>
            <label>Account Locked</label>
        </rules>
    </decisions>
    <decisions>
        <name>AccountUnlocked</name>
        <label>Account Unlocked</label>
        <locationX>336</locationX>
        <locationY>1763</locationY>
        <defaultConnector>
            <targetReference>Negative_Balance</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decAccountUnlocked</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula3</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Close_Account_Locked_Alert_Tasks</targetReference>
            </connector>
            <label>Account Unlocked1</label>
        </rules>
    </decisions>
    <decisions>
        <name>Bonus_Expiry_Date_Changed</name>
        <label>Bonus Expiry Date Changed</label>
        <locationX>336</locationX>
        <locationY>2663</locationY>
        <defaultConnector>
            <targetReference>Is_Customer_Active</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Bonus_Expiry_Date_Changed1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula6</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Bonus_Expiry_task</targetReference>
            </connector>
            <label>Bonus Expiry Date Changed1</label>
        </rules>
    </decisions>
    <decisions>
        <name>changesMadeToNserFields</name>
        <label>Changes made to NSER fields</label>
        <locationX>336</locationX>
        <locationY>4055</locationY>
        <defaultConnector>
            <targetReference>Customer_is_Blacklisted</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTrue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.NSER_Excluded__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.NSER_Excluded__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isNotIntegrationManagedTeamPremiumService</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>createTask</targetReference>
            </connector>
            <label>Changes were made</label>
        </rules>
    </decisions>
    <decisions>
        <name>Customer_Created</name>
        <label>Customer Created</label>
        <locationX>182</locationX>
        <locationY>3071</locationY>
        <defaultConnector>
            <targetReference>Owner_Changed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Customer_Created1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula7</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Customer_Introduction_Task3</targetReference>
            </connector>
            <label>Customer Created1</label>
        </rules>
    </decisions>
    <decisions>
        <name>Customer_is_Blacklisted</name>
        <label>Customer is Blacklisted</label>
        <locationX>336</locationX>
        <locationY>4463</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Blacklisted_changed_to_true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Blacklist_from_Events__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Blacklist_from_Events__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Populate_Blacklist_Date</targetReference>
            </connector>
            <label>Blacklisted changed to true</label>
        </rules>
        <rules>
            <name>Blacklisted_changed_to_false</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Blacklist_from_Events__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Blacklist_from_Events__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Clear_Blacklist_Date</targetReference>
            </connector>
            <label>Blacklisted changed to false</label>
        </rules>
    </decisions>
    <decisions>
        <name>Does_Case_Exist</name>
        <label>Does Case Exist?</label>
        <locationX>160</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>subCreateCaseToRM</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decCaseExists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Existing_Case_Records.Account.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>subCloseTasksOfInactiveUser</targetReference>
            </connector>
            <label>Case Exists</label>
        </rules>
    </decisions>
    <decisions>
        <name>Email_Update</name>
        <label>Email Update</label>
        <locationX>336</locationX>
        <locationY>3755</locationY>
        <defaultConnector>
            <targetReference>changesMadeToNserFields</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Email_Update1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula9</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_PersonEmail</targetReference>
            </connector>
            <label>Email Update1</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Customer_Active</name>
        <label>Is Customer Active</label>
        <locationX>336</locationX>
        <locationY>2963</locationY>
        <defaultConnector>
            <targetReference>Email_Update</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decIsActive</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Customer_Created</targetReference>
            </connector>
            <label>Is Active</label>
        </rules>
    </decisions>
    <decisions>
        <name>Negative_Balance</name>
        <label>Negative Balance</label>
        <locationX>336</locationX>
        <locationY>2063</locationY>
        <defaultConnector>
            <targetReference>Positive_Balance</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Negative_Balance1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula4</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Negative_Balance_Alert</targetReference>
            </connector>
            <label>Negative Balance1</label>
        </rules>
    </decisions>
    <decisions>
        <name>Owner_Changed</name>
        <label>Owner Changed</label>
        <locationX>182</locationX>
        <locationY>3371</locationY>
        <defaultConnector>
            <targetReference>Email_Update</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Owner_Changed1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula8</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Customer_Introduction_Task2</targetReference>
            </connector>
            <label>Owner Changed1</label>
        </rules>
    </decisions>
    <decisions>
        <name>Positive_Balance</name>
        <label>Positive Balance</label>
        <locationX>336</locationX>
        <locationY>2363</locationY>
        <defaultConnector>
            <targetReference>Bonus_Expiry_Date_Changed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Positive_Balance_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula5</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Close_Negative_Balance_Alert_Tasks</targetReference>
            </connector>
            <label>Positive Balance 1</label>
        </rules>
    </decisions>
    <decisions>
        <name>Self_Ex</name>
        <label>Self Excluded is Yes</label>
        <locationX>336</locationX>
        <locationY>755</locationY>
        <defaultConnector>
            <targetReference>Account_Locked</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Self_Excluded_is_Yes</name>
            <conditionLogic>1 AND (2 OR 3)</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Account_PB_Entry_1__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Self_Excluded__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Yes</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Existing_Case_Records</targetReference>
            </connector>
            <label>Self Excluded is Yes</label>
        </rules>
    </decisions>
    <description>SBET-502 - Blacklist from Events</description>
    <environments>Default</environments>
    <formulas>
        <name>dateToday</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <name>Formula2</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Account_Locked_Entry__c}) &amp;&amp;
ISCHANGED({!$Record.Account_Locked_Out__c})  &amp;&amp; 
{!$Record.Account_Locked_Out__c}  &amp;&amp; 
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Integration_UserId.Value__c} &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Managed_Team_Service_User_ID.Value__c} &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Premium_Service_User_ID.Value__c}</expression>
    </formulas>
    <formulas>
        <name>Formula3</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Account_Unlocked_Entry__c})&amp;&amp; 
ISCHANGED({!$Record.Account_Locked_Out__c})  &amp;&amp; 
NOT({!$Record.Account_Locked_Out__c})</expression>
    </formulas>
    <formulas>
        <name>Formula4</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Negative_Balance_Entry__c})&amp;&amp; 
ISCHANGED({!$Record.Current_Balance__c})  &amp;&amp; 
{!$Record.Current_Balance__c}&lt; 0 &amp;&amp; 
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Integration_UserId.Value__c} &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Managed_Team_Service_User_ID.Value__c} &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Premium_Service_User_ID.Value__c}</expression>
    </formulas>
    <formulas>
        <name>Formula5</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Positive_Balance_Entry__c})&amp;&amp; 
ISCHANGED({!$Record.Current_Balance__c})  &amp;&amp; 
{!$Record.Current_Balance__c} &gt;= 0 &amp;&amp; 
PRIORVALUE({!$Record.Current_Balance__c})&lt; 0</expression>
    </formulas>
    <formulas>
        <name>Formula6</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Bonus_Expiry_Date_Entry__c}) &amp;&amp; 
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Integration_UserId.Value__c} &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Managed_Team_Service_User_ID.Value__c}  &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Premium_Service_User_ID.Value__c} &amp;&amp;
ISCHANGED({!$Record.Bonus_Expiry_Date__c}) &amp;&amp;
{!$Record.Bonus_Expiry_Date__c} &gt; TODAY()</expression>
    </formulas>
    <formulas>
        <name>Formula7</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Customer_Created_Entry__c}) &amp;&amp; 
ISNEW()  &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Managed_Team_Service_User_ID.Value__c}</expression>
    </formulas>
    <formulas>
        <name>Formula8</name>
        <dataType>Boolean</dataType>
        <expression>NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Owner_Changed_Entry__c}) &amp;&amp; 
ISCHANGED({!$Record.OwnerId}) &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Managed_Team_Service_User_ID.Value__c}</expression>
    </formulas>
    <formulas>
        <name>Formula9</name>
        <dataType>Boolean</dataType>
        <expression>(NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Email_Update_Entry__c}) &amp;&amp;
NOT(ISBLANK({!$Record.Email__c}) ) &amp;&amp; 
ISBLANK({!$Record.PersonEmail})) || 
(NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Email_Update_Entry__c}) &amp;&amp;
ISCHANGED({!$Record.Email__c}))</expression>
    </formulas>
    <formulas>
        <name>isNotIntegrationManagedTeamPremiumService</name>
        <dataType>Boolean</dataType>
        <expression>{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Integration_UserId.Value__c} &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Custom_Metadata_Premium_Service_User_ID.Value__c}  &amp;&amp;
{!$Record.OwnerId} &lt;&gt; {!Get_Kafka_integration_Id.Id}</expression>
    </formulas>
    <interviewLabel>Customer Process {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer Process Flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>createTask</name>
        <label>Create FYI Task</label>
        <locationX>204</locationX>
        <locationY>4163</locationY>
        <connector>
            <targetReference>Send_NSER_Email_to_Customer_Owner</targetReference>
        </connector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>dateToday</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Customer has closed account via the National Self Exclusion Register. No contact is to be made with Customer.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>0122y0000004HSpAAM</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI Alert: Customer Closed Account VIA BetStop / NSER - Do not contact</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Custom_Metadata_Integration_UserId</name>
        <label>Custom Metadata Integration UserId</label>
        <locationX>336</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Custom_Metadata_Managed_Team_Service_User_ID</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>IntegrationUserID</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Custom_Metadata_Managed_Team_Service_User_ID</name>
        <label>Custom Metadata Managed Team Service User ID</label>
        <locationX>336</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Custom_Metadata_Premium_Service_User_ID</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ManagedTeamServiceUserID</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Custom_Metadata_Premium_Service_User_ID</name>
        <label>Custom Metadata Premium Service User ID</label>
        <locationX>336</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Kafka_integration_Id</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>PremiumServiceUserID</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Existing_Case_Records</name>
        <label>Get Existing Case Records</label>
        <locationX>160</locationX>
        <locationY>863</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Does_Case_Exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Follow up Event and Hospitality Offered for Customer is self excluded</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Event and Hospitality</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Priority</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>High</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Kafka_integration_Id</name>
        <label>Get Kafka integration Id</label>
        <locationX>336</locationX>
        <locationY>647</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Self_Ex</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Kafka Integration userId</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Clear_Blacklist_Date</name>
        <label>Clear Blacklist Date</label>
        <locationX>336</locationX>
        <locationY>4571</locationY>
        <inputAssignments>
            <field>Blacklist_Date__c</field>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Populate_Blacklist_Date</name>
        <label>Populate Blacklist Date</label>
        <locationX>72</locationX>
        <locationY>4571</locationY>
        <inputAssignments>
            <field>Blacklist_Date__c</field>
            <value>
                <elementReference>dateToday</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_PersonEmail</name>
        <label>Update PersonEmail</label>
        <locationX>204</locationX>
        <locationY>3863</locationY>
        <connector>
            <targetReference>changesMadeToNserFields</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>PersonEmail</field>
            <value>
                <elementReference>$Record.Email__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>210</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Custom_Metadata_Integration_UserId</targetReference>
        </connector>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Close_Account_Locked_Alert_Tasks</name>
        <label>Close Account Locked Alert Tasks</label>
        <locationX>204</locationX>
        <locationY>1871</locationY>
        <connector>
            <targetReference>Negative_Balance</targetReference>
        </connector>
        <flowName>Invoke_Automated_Task_Completion</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varAutomatedTaskType</name>
            <value>
                <elementReference>AccountLocked</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Close_Negative_Balance_Alert_Tasks</name>
        <label>Close Negative Balance Alert Tasks</label>
        <locationX>204</locationX>
        <locationY>2471</locationY>
        <connector>
            <targetReference>Bonus_Expiry_Date_Changed</targetReference>
        </connector>
        <flowName>Invoke_Automated_Task_Completion</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varAutomatedTaskType</name>
            <value>
                <elementReference>NegativeBalance</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Create_Bonus_Expiry_task</name>
        <label>Create Bonus Expiry task</label>
        <locationX>204</locationX>
        <locationY>2771</locationY>
        <connector>
            <targetReference>Is_Customer_Active</targetReference>
        </connector>
        <flowName>Invoke_Automated_Task_Creation</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varAutomatedTaskType</name>
            <value>
                <elementReference>ExpiringBonus</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varBonusExpiryDate</name>
            <value>
                <elementReference>$Record.Bonus_Expiry_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varTaskOwner</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Create_Customer_Introduction_Task2</name>
        <label>Create Customer Introduction Task</label>
        <locationX>50</locationX>
        <locationY>3479</locationY>
        <connector>
            <targetReference>Email_Update</targetReference>
        </connector>
        <flowName>Invoke_Automated_Task_Creation</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varAutomatedTaskType</name>
            <value>
                <elementReference>IntroductionExisting</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varTaskOwner</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Create_Customer_Introduction_Task3</name>
        <label>Create Customer Introduction Task</label>
        <locationX>50</locationX>
        <locationY>3179</locationY>
        <connector>
            <targetReference>Owner_Changed</targetReference>
        </connector>
        <flowName>Invoke_Automated_Task_Creation</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varAutomatedTaskType</name>
            <value>
                <elementReference>Introduction</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varTaskOwner</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Create_Negative_Balance_Alert</name>
        <label>Create Negative Balance Alert</label>
        <locationX>204</locationX>
        <locationY>2171</locationY>
        <connector>
            <targetReference>Positive_Balance</targetReference>
        </connector>
        <flowName>Invoke_Automated_Task_Creation</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varAutomatedTaskType</name>
            <value>
                <elementReference>NegativeBalance</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varTaskOwner</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>CreateAccountLockedAlertTask</name>
        <label>Create Account Locked Alert Task</label>
        <locationX>204</locationX>
        <locationY>1571</locationY>
        <connector>
            <targetReference>AccountUnlocked</targetReference>
        </connector>
        <flowName>Invoke_Automated_Task_Creation</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varAutomatedTaskType</name>
            <value>
                <elementReference>AccountLocked</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varTaskOwner</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <description>SBET-136 - Close tasks for Customer Birthday</description>
        <name>subCloseTasksOfInactiveUser</name>
        <label>Close Tasks of Inactive User</label>
        <locationX>160</locationX>
        <locationY>1271</locationY>
        <connector>
            <targetReference>Account_Locked</targetReference>
        </connector>
        <flowName>InactiveUserTaskClosure</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>AccountID</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>subCreateCaseToRM</name>
        <label>Create Case to RM</label>
        <locationX>248</locationX>
        <locationY>1079</locationY>
        <connector>
            <targetReference>subCloseTasksOfInactiveUser</targetReference>
        </connector>
        <flowName>Create_Case_to_RM_if_Customer_is_self_Excluded_and_has_future_event</flowName>
        <inputAssignments>
            <name>inputAccount</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <textTemplates>
        <name>bestopEmailBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>To {!ownerName},
The following account is closed:
Sportsbet Account Number: {!$Record.Sports_Bet_Account_Number__c}
Customer Owner: Premium Service
Type of closure: NSER
Please ensure you do not contact this customer.</text>
    </textTemplates>
    <variables>
        <name>AccountID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <elementReference>$Record.Id</elementReference>
        </value>
    </variables>
    <variables>
        <name>AccountLocked</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>AccountLocked</stringValue>
        </value>
    </variables>
    <variables>
        <name>Closed</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Closed</stringValue>
        </value>
    </variables>
    <variables>
        <name>ExpiringBonus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>ExpiringBonus</stringValue>
        </value>
    </variables>
    <variables>
        <name>IntegrationUserID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Integration User ID</stringValue>
        </value>
    </variables>
    <variables>
        <name>Introduction</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Introduction New</stringValue>
        </value>
    </variables>
    <variables>
        <name>ManagedTeamServiceUserID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Managed Team Service User ID</stringValue>
        </value>
    </variables>
    <variables>
        <name>myVariable</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>NegativeBalance</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>NegativeBalance</stringValue>
        </value>
    </variables>
    <variables>
        <name>ownerEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Record.Owner.Email</elementReference>
        </value>
    </variables>
    <variables>
        <name>ownerName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Record.Owner__c</elementReference>
        </value>
    </variables>
    <variables>
        <name>PremiumServiceUserID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Premium Service User ID</stringValue>
        </value>
    </variables>
    <variables>
        <name>Suspended</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Suspended</stringValue>
        </value>
    </variables>
    <variables>
        <name>Timeout</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Timeout</stringValue>
        </value>
    </variables>
    <variables>
        <name>Yes</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Yes</stringValue>
        </value>
    </variables>
</Flow>
