<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Approved</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_pmetdec</targetReference>
            </connector>
            <label>Approved</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision2</name>
        <label>myDecision2</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision4</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rejected</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_3_pmetdec</targetReference>
            </connector>
            <label>Rejected</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>2.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision4</name>
        <label>myDecision4</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_5</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rejected</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Approved</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_5_A1</targetReference>
            </connector>
            <label>Approved or Rejected</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_1_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>100</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_1_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_1_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_1_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_old.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Approved</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision2</targetReference>
            </connector>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_3_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>300</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_3_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_3_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_3_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_3_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_old.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rejected</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision4</targetReference>
            </connector>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <interviewLabel>Adjustment_Approval_Updated_only-7_InterviewLabel</interviewLabel>
    <label>Adjustment Approval Updated only</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Adjustment__c</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>ObjectVariable</name>
        <value>
            <elementReference>myVariable_current</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OldObjectVariable</name>
        <value>
            <elementReference>myVariable_old</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>RecursiveCountVariable</name>
        <value>
            <elementReference>RecursiveCountVariable</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>TriggerType</name>
        <value>
            <stringValue>onAllChanges</stringValue>
        </value>
    </processMetadataValues>
    <processType>Workflow</processType>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_1_A1</name>
        <label>Update Customer Approval</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Customer Pending Approval</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>Account</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>GlobalConstant</stringValue>
                </value>
            </processMetadataValues>
            <field>Customer_Pending_Approval__c</field>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_3_A1</name>
        <label>Update Customer Approver</label>
        <locationX>300</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision4</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Customer Pending Approval</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>Account</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>GlobalConstant</stringValue>
                </value>
            </processMetadataValues>
            <field>Customer_Pending_Approval__c</field>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Adjustment__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_5_A1</name>
        <label>Update customer approval</label>
        <locationX>500</locationX>
        <locationY>200</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Customer Pending Approval</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>Account</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>GlobalConstant</stringValue>
                </value>
            </processMetadataValues>
            <field>Customer_Pending_Approval__c</field>
        </inputAssignments>
        <object>Adjustment__c</object>
    </recordUpdates>
    <startElementReference>myDecision</startElementReference>
    <status>Draft</status>
    <variables>
        <name>myVariable_current</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Adjustment__c</objectType>
    </variables>
    <variables>
        <name>myVariable_old</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Adjustment__c</objectType>
    </variables>
    <variables>
        <name>RecursiveCountVariable</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
</Flow>
