<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Consent_Preferences_to_<PERSON></name>
        <label>Send Consent Preferences to <PERSON></label>
        <locationX>176</locationX>
        <locationY>755</locationY>
        <actionName>CassieAPI</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>consents</name>
            <value>
                <elementReference>varCustomerConsentRecord</elementReference>
            </value>
        </inputParameters>
        <nameSegment>CassieAPI</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_SMS_Consent_on_Record</name>
        <label>Assign SMS Consent on Record</label>
        <locationX>176</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>varCustomerConsentRecord.Customer_ID__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Customer_Consent.Customer_ID__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varCustomerConsentRecord.Communications_via_RM_SMS__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Send_Consent_Preferences_to_Cassie</targetReference>
        </connector>
    </assignments>
    <description>Used to update the communication preferences on the Customer Consent object when a customer Messaging consent status changes e.g. uses keyword to opt out of SMS
Updated to send preferences to Cassie API</description>
    <environments>Default</environments>
    <interviewLabel>Messaging End User: On Update - SMS Consent Change {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Messaging End User: On Update - SMS Consent Change</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the Customer Consent record for this Customer</description>
        <name>Get_Customer_Consent</name>
        <label>Get Customer Consent</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Customer_Consent_Opted_Out</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Customer__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Customer_Consent__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates the Customer Consent record to Opt out of SMS</description>
        <name>Update_Customer_Consent_Opted_Out</name>
        <label>Update Customer Consent Opted Out</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <connector>
            <targetReference>Update_Messaging_End_User</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer_Consent.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Communications_via_RM_SMS__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <object>Customer_Consent__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Updates Messaging End User consent to Implicity Opted-In</description>
        <name>Update_Messaging_End_User</name>
        <label>Update Messaging End User</label>
        <locationX>176</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Assign_SMS_Consent_on_Record</targetReference>
        </connector>
        <inputAssignments>
            <field>MessagingConsentStatus</field>
            <value>
                <stringValue>ImplicitlyOptedIn</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Customer_Consent</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MessagingConsentStatus</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>MessagingConsentStatus</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>OptedOut</stringValue>
            </value>
        </filters>
        <object>MessagingEndUser</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>varCustomerConsentRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Customer_Consent__c</objectType>
    </variables>
</Flow>
