<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Updates current Case warning acknowledgement field to false for component to be hidden again ready for next email</description>
        <name>Assign_Case_Warning_Message</name>
        <label>Assign Case Warning Message</label>
        <locationX>446</locationX>
        <locationY>755</locationY>
        <assignmentItems>
            <assignToReference>Get_Case.Warning_Message_Acknowledged__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Parent_Case</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Is the Email incoming or outgoing?</description>
        <name>Email_Direction</name>
        <label>Email Direction</label>
        <locationX>314</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>Outgoing_Current_Case_Status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outgoing</defaultConnectorLabel>
        <rules>
            <name>Incoming</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Incoming</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Incoming_Current_Case_Status</targetReference>
            </connector>
            <label>Incoming</label>
        </rules>
    </decisions>
    <decisions>
        <description>What is the current Status of the Case</description>
        <name>Incoming_Current_Case_Status</name>
        <label>Incoming Current Case Status</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <defaultConnector>
            <targetReference>Update_Case_Status_to_Reply_Received</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Other</defaultConnectorLabel>
        <rules>
            <name>New</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Parent.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <label>New</label>
        </rules>
    </decisions>
    <decisions>
        <description>What is the current Status of the Case</description>
        <name>Outgoing_Current_Case_Status</name>
        <label>Outgoing Current Case Status</label>
        <locationX>578</locationX>
        <locationY>647</locationY>
        <defaultConnector>
            <targetReference>Update_Case_Status_to_Awaiting_Customer_Response</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Open</defaultConnectorLabel>
        <rules>
            <name>Closed</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Parent.IsClosed</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_Warning_Message</targetReference>
            </connector>
            <label>Closed</label>
        </rules>
    </decisions>
    <description>When an Email is sent or received updates the parent Case status</description>
    <environments>Default</environments>
    <interviewLabel>Email Message: After Create - Update Case Status {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Email Message: After Create - Update Case Status</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the Case record related to the Email</description>
        <name>Get_Case</name>
        <label>Get Case</label>
        <locationX>314</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Email_with_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>AccountId</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates current Case status to  Awaiting Customer Response</description>
        <name>Update_Case_Status_to_Awaiting_Customer_Response</name>
        <label>Update Case Status to Awaiting Customer Response</label>
        <locationX>710</locationX>
        <locationY>755</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Waiting Customer Response</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Warning_Message_Acknowledged__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <description>Updates current Case status to Reply Received</description>
        <name>Update_Case_Status_to_Reply_Received</name>
        <label>Update Case Status to Reply Received</label>
        <locationX>182</locationX>
        <locationY>755</locationY>
        <connector>
            <targetReference>Case_Premium_Email_to_Case_Routing</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Reply Received</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Email_with_Customer</name>
        <label>Update Email with Customer</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <connector>
            <targetReference>Email_Direction</targetReference>
        </connector>
        <inputAssignments>
            <field>Customer__c</field>
            <value>
                <elementReference>Get_Case.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates Parent Case</description>
        <name>Update_Parent_Case</name>
        <label>Update Parent Case</label>
        <locationX>446</locationX>
        <locationY>863</locationY>
        <inputReference>Get_Case</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case</targetReference>
        </connector>
        <filterFormula>OR(
AND(
NOT(ISNULL({!$Record.ParentId})),
{!$Record.Parent.RecordType.DeveloperName} = &quot;Premium_Managed_Case&quot;)
,
AND(
{!$Record.Incoming},
NOT(ISNULL({!$Record.ParentId})),
{!$Record.Parent.RecordType.DeveloperName} = &quot;Premium_Managed_Case&quot;)
)</filterFormula>
        <object>EmailMessage</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <description>Subflow to route Case to appropriate relationshop manager</description>
        <name>Case_Premium_Email_to_Case_Routing</name>
        <label>Case: Premium Email-to-Case Routing</label>
        <locationX>182</locationX>
        <locationY>863</locationY>
        <flowName>Case_Premium_Email_to_Case_Routing</flowName>
        <inputAssignments>
            <name>input_record</name>
            <value>
                <elementReference>Get_Case</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>Get_Case.Id</elementReference>
            </value>
        </inputAssignments>
    </subflows>
</Flow>
