<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Created_ECDD</name>
        <label>Created ECDD</label>
        <locationX>446</locationX>
        <locationY>385</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Created_ECDD1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>ECDD_Case</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Due_Date</targetReference>
            </connector>
            <label>Created ECDD</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>Duedate</name>
        <dataType>Date</dataType>
        <expression>TODAY () + 14</expression>
    </formulas>
    <interviewLabel>Case Created Only {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case Created only flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Due_Date</name>
        <label>Due Date</label>
        <locationX>686</locationX>
        <locationY>485</locationY>
        <connector>
            <targetReference>ECDD_Case_created</targetReference>
        </connector>
        <inputAssignments>
            <field>ECDD_Due_Date__c</field>
            <value>
                <elementReference>Duedate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>320</locationX>
        <locationY>41</locationY>
        <connector>
            <targetReference>Created_ECDD</targetReference>
        </connector>
        <object>Case</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>ECDD_Case_created</name>
        <label>ECDD Case created</label>
        <locationX>802</locationX>
        <locationY>486</locationY>
        <flowName>Invoke_Automated_ECDD_Case_Creation</flowName>
        <inputAssignments>
            <name>VarCaseId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>VarSportsbetCustomerNumber</name>
            <value>
                <elementReference>$Record.ECDD_SportsBet_Account_Number__c</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <variables>
        <name>ECDD_Case</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>ECDD_Case_Old</stringValue>
        </value>
    </variables>
</Flow>
