<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>57.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>InteractionRecordId</name>
        <dataType>String</dataType>
        <value>
            <stringValue>0122y0000004HSoAAM</stringValue>
        </value>
    </constants>
    <constants>
        <name>SubjectEmailTask</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Manager Review: Check Email Interactions &amp; Customer Comms preferences</stringValue>
        </value>
    </constants>
    <constants>
        <name>SubjectSMSTask</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Manager Review: Check SMS Interactions &amp; Customer Comms preferences</stringValue>
        </value>
    </constants>
    <constants>
        <name>taskComment</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Interaction has been logged against a channel that customer has opted out of. Manager to review interactions &amp; add comments to Task summary and complete task. Manager to provide feedback to Customer owner &amp; Premium Ops team. </stringValue>
        </value>
    </constants>
    <constants>
        <name>TaskRecordId</name>
        <dataType>String</dataType>
        <value>
            <stringValue>0122y0000008uvKAAQ</stringValue>
        </value>
    </constants>
    <decisions>
        <name>DoesCustomerOwnerHaveAManager</name>
        <label>Does Customer Owner Have a Manager</label>
        <locationX>182</locationX>
        <locationY>755</locationY>
        <defaultConnector>
            <targetReference>Create_Task_for_Email</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decNoManager</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Details.Owner.ManagerId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CreateTaskForEmailNoManager</targetReference>
            </connector>
            <label>No Manager</label>
        </rules>
    </decisions>
    <decisions>
        <name>DoesCustomerOwnerHaveManager2</name>
        <label>Does Customer Owner Have a Manager</label>
        <locationX>710</locationX>
        <locationY>755</locationY>
        <defaultConnector>
            <targetReference>Create_Task_for_SMS</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decNoManager2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Details.Owner.ManagerId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CreateTaskForSMSNoManager</targetReference>
            </connector>
            <label>No Manager</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Email_Interaction</name>
        <label>Is Email Interaction?</label>
        <locationX>644</locationX>
        <locationY>647</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Email_Sent_when_RM_Opt_in_Email_is_No</name>
            <conditionLogic>(1 OR 5) AND 2 AND 3 AND 4</conditionLogic>
            <conditions>
                <leftValueReference>$Record.TaskSubtype</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Email</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Outbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Consent_Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>InteractionRecordId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Channel__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Email</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>DoesCustomerOwnerHaveAManager</targetReference>
            </connector>
            <label>Email Sent when RM Opt in Email  is No</label>
        </rules>
        <rules>
            <name>SMS_Sent_when_RM_Opt_in_SMS_is_No</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Outbound</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Channel__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SMS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Consent_Record.Communications_via_RM_SMS__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>InteractionRecordId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>DoesCustomerOwnerHaveManager2</targetReference>
            </connector>
            <label>SMS Sent when RM Opt in SMS  is No</label>
        </rules>
    </decisions>
    <decisions>
        <name>Task_Whatid_is_not_null</name>
        <label>Task Whatid is not null</label>
        <locationX>1007</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_whatid_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedBy.Profile.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Team Member</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Customer_Details</targetReference>
            </connector>
            <label>is whatid not null</label>
        </rules>
    </decisions>
    <description>https://coe8squad.atlassian.net/browse/SBET-1348 Stop Creation of Manager Review for non premium users</description>
    <environments>Default</environments>
    <formulas>
        <name>TodayPlusOne</name>
        <dataType>Date</dataType>
        <expression>TODAY()+1</expression>
    </formulas>
    <interviewLabel>Create Task when RM Opt in Email/SMS is No {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Create Task when RM Opt in Email/SMS is No</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Task_for_Email</name>
        <label>Create Task for Email</label>
        <locationX>314</locationX>
        <locationY>863</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>TodayPlusOne</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Case__c</field>
            <value>
                <elementReference>$Record.Case__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>taskComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Customer_Details.Owner.ManagerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>TaskRecordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectEmailTask</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Task_for_SMS</name>
        <label>Create Task for SMS</label>
        <locationX>842</locationX>
        <locationY>863</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>TodayPlusOne</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>taskComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Messaging_Session__c</field>
            <value>
                <elementReference>$Record.Messaging_Session__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Customer_Details.Owner.ManagerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>TaskRecordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectSMSTask</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>CreateTaskForEmailNoManager</name>
        <label>Create Task for Email - No Manager</label>
        <locationX>50</locationX>
        <locationY>863</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>TodayPlusOne</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Case__c</field>
            <value>
                <elementReference>$Record.Case__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>taskComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Customer_Details.Owner.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>TaskRecordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectEmailTask</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>CreateTaskForSMSNoManager</name>
        <label>Create Task for SMS - No Manager</label>
        <locationX>578</locationX>
        <locationY>863</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>TodayPlusOne</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>taskComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Messaging_Session__c</field>
            <value>
                <elementReference>$Record.Messaging_Session__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Customer_Details.Owner.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>TaskRecordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectSMSTask</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Consent_Record</name>
        <label>Get Consent Record</label>
        <locationX>644</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Email_Interaction</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Customer__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer_Details.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Customer_Consent__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Customer_Details</name>
        <label>Get Customer Details</label>
        <locationX>644</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Consent_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>881</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Task_Whatid_is_not_null</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
