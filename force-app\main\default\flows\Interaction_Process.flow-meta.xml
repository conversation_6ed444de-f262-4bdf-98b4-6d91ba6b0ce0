<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_A1</targetReference>
            </connector>
            <label>When task is created for Self Exclusion Alert Case</label>
        </rules>
    </decisions>
    <description>This process is used to default Customer Contacted on Self Exclusion Cases to which Task is associated with</description>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>$CustomMetadata.Case_Email_Alert_Mapping__mdt.Self_Exclusion_Record_Type_ID.Default_Value__c </stringValue>
            </value>
        </processMetadataValues>
        <name>formula_2_myRule_1_A1_4921927149</name>
        <dataType>String</dataType>
        <expression>{!$CustomMetadata.Case_Email_Alert_Mapping__mdt.Self_Exclusion_Record_Type_ID.Default_Value__c}</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>ISNEW() &amp;&amp;

[Task].RecordType.DeveloperName = &apos;Interaction&apos; &amp;&amp; 
[Task].Subject =&quot;Interaction - Closure Contact&quot;</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW() &amp;&amp;

{!myVariable_current.RecordType.DeveloperName} = &apos;Interaction&apos; &amp;&amp; 
{!myVariable_current.Subject} =&quot;Interaction - Closure Contact&quot;</expression>
    </formulas>
    <interviewLabel>Interaction_Process-3_InterviewLabel</interviewLabel>
    <label>Interaction Process</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Task</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>ObjectVariable</name>
        <value>
            <elementReference>myVariable_current</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OldObjectVariable</name>
        <value>
            <elementReference>myVariable_old</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>TriggerType</name>
        <value>
            <stringValue>onAllChanges</stringValue>
        </value>
    </processMetadataValues>
    <processType>Workflow</processType>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>criteria</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Task].Related To ID (Case)</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_1_A1</name>
        <label>Update Self Exclusion Case</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.WhatId</elementReference>
            </value>
        </filters>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>inputDataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Record Type ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>RecordType</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>operatorDataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formula_2_myRule_1_A1_4921927149</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Customer Contacted</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Customer_Contacted__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <startElementReference>myDecision</startElementReference>
    <status>Active</status>
    <variables>
        <name>myVariable_current</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>myVariable_old</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
