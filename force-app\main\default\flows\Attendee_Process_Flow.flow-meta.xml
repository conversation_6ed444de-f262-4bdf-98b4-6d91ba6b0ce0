<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Event_Over_Threshold_Approval_Event_Over_Threshold_Approval</name>
        <label>Event Over Threshold Approval - Event_Over_Threshold_Approval</label>
        <locationX>345</locationX>
        <locationY>539</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>Number_of_Seats_is_More_than_2</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Event_Over_Threshold_Approval</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>Customer has exceeded the allowed Number of Events Attended per Year.</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.Account__r.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Number_of_Seats_Guests_Over_Two</name>
        <label>Number of Seats (Guests) Over Two Appro</label>
        <locationX>345</locationX>
        <locationY>839</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>Status_is_Offer_Accepted</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>processDefinitionNameOrId</name>
            <value>
                <stringValue>Number_of_Seats_Guests_Over_Two_Approv</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <stringValue>This customer has more than 1 guest allocated to an event and requires approval to proceed as per the Premium Events &amp; Experiences Policy.</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$Record.Account__r.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Over_Allocation_Notification</name>
        <label>Over Allocation Notification</label>
        <locationX>213</locationX>
        <locationY>1871</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <connector>
            <targetReference>Status_Is_Changed_to_Other_Value_from_Offer_Accepted</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>get_notification_type_id.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>customerOwner</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>overAllocationTitle</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>overAllocationBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Under_Allocation_Notification</name>
        <label>Under Allocation Notification</label>
        <locationX>477</locationX>
        <locationY>1979</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <connector>
            <targetReference>Status_Is_Changed_to_Other_Value_from_Offer_Accepted</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>Copy_1_of_Get_Notification_Type_ID.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>customerOwner</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>underAllocationTitle</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>underAllocationBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
    </actionCalls>
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_new_title</name>
        <label>Assign new title</label>
        <locationX>315</locationX>
        <locationY>3611</locationY>
        <assignmentItems>
            <assignToReference>Get_document.Title</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>cancelledDocumentTitle</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Document_Title</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Owner_Id</name>
        <label>Assign Owner Id</label>
        <locationX>213</locationX>
        <locationY>1763</locationY>
        <assignmentItems>
            <assignToReference>customerOwner</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$Record.Account__r.OwnerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Over_Allocation_Notification</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Assign_Owner_Id</name>
        <label>Assign Owner Id</label>
        <locationX>477</locationX>
        <locationY>1871</locationY>
        <assignmentItems>
            <assignToReference>customerOwner</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$Record.Account__r.OwnerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Under_Allocation_Notification</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Attended_Checkbox_is_set_to_TRUE</name>
        <label>Attended Checkbox is set to TRUE</label>
        <locationX>477</locationX>
        <locationY>2471</locationY>
        <defaultConnector>
            <targetReference>Populate_Guest_Check_Confirmed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Attended_Checkbox_is_set_to_TRUE1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Attended__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Status_to_Attended</targetReference>
            </connector>
            <label>Attended Checkbox is set to TRUE1</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if status has been changed for customer attendee to Cancelled within 24 hours of a campaign start date</description>
        <name>Cancelled_within_24_hours_of_an_event_start_date</name>
        <label>Cancelled within 24 hours of an event start date</label>
        <locationX>477</locationX>
        <locationY>4079</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Cancelled_Within_24_hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer Attendee</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Cancelled</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>eventStartsLessThan24Hours</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Checkbox_to_True</targetReference>
            </connector>
            <label>Cancelled Within 24 hours</label>
        </rules>
        <rules>
            <name>Status_is_changed_back_from_Cancelled</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Cancelled</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Cancelled</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>eventStartsLessThan24Hours</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer Attendee</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Cancelled_Within_24hrs_of_Event__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Checkbox_to_False</targetReference>
            </connector>
            <label>Status is changed back from Cancelled</label>
        </rules>
    </decisions>
    <decisions>
        <name>Do_links_exist</name>
        <label>Do links exist?</label>
        <locationX>335</locationX>
        <locationY>3287</locationY>
        <defaultConnector>
            <targetReference>Cancelled_within_24_hours_of_an_event_start_date</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decLinksExist</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Document_Links</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>For_each_link</targetReference>
            </connector>
            <label>Links Exist</label>
        </rules>
    </decisions>
    <decisions>
        <name>Interaction_is_created_and_campaign</name>
        <label>Is Attendee over Event Threshold</label>
        <locationX>477</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Number_of_Seats_is_More_than_2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Interaction_is_created_and_campaign_1</name>
            <conditionLogic>(1 AND 2 AND 3 AND 4 AND 5 AND 7 AND 10) AND (6 OR (8 OR 9))</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.Name</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Guest Attendee</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Event_Participation_Cap_Exceeded__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Setup.Process_Automation_Control_Panel__c.Attendee_PB_Entry_1__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Offer Accepted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Pending Approval</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Event_Over_Threshold_Approval_Event_Over_Threshold_Approval</targetReference>
            </connector>
            <label>Attendee over Threshold</label>
        </rules>
    </decisions>
    <decisions>
        <name>IsUnconfirmed</name>
        <label>Is Unconfirmed Record Type</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <defaultConnector>
            <targetReference>Interaction_is_created_and_campaign</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Unconfirmed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Unconfirmed Attendee</stringValue>
                </rightValue>
            </conditions>
            <label>Is Unconfirmed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Number_of_Seats_is_More_than_2</name>
        <label>Number of Seats is More than 2</label>
        <locationX>477</locationX>
        <locationY>731</locationY>
        <defaultConnector>
            <targetReference>Status_is_Offer_Accepted</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Number_of_Seats_is_More_than_21</name>
            <conditionLogic>(1 AND 3 AND 4) AND ((5 AND 12) OR (11 AND 13) OR ((2 OR 6) AND (((7 OR 8) AND 9 AND 10) OR (5 AND NOT 7))))</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Number_Of_Seats__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>2.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Offer Accepted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer_Attendee</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Event_Participation_Cap_Exceeded__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordTypeId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending Approval</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Waitlisted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Approval_Process_Triggered__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Number_Of_Seats__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Number_Of_Seats__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>$Record.Number_Of_Seats__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Number_of_Seats_Guests_Over_Two</targetReference>
            </connector>
            <label>Number of Seats is More than 21</label>
        </rules>
    </decisions>
    <decisions>
        <name>NumberOfSeatsChanged</name>
        <label>Number of Seats has changed</label>
        <locationX>477</locationX>
        <locationY>1547</locationY>
        <defaultConnector>
            <targetReference>Status_Is_Changed_to_Other_Value_from_Offer_Accepted</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decTooManyTickets</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Number_Of_Seats__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>$Record.Allocated_Tickets__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Ticket_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Tickets Allocated</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Tickets_Sent__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>get_notification_type_id</targetReference>
            </connector>
            <label>Too Many Tickets Allocated</label>
        </rules>
        <rules>
            <name>Not_Enough_Tickets_Allocated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Ticket_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Tickets Allocated</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Number_Of_Seats__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>$Record.Allocated_Tickets__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Change_Ticket_Status</targetReference>
            </connector>
            <label>Not Enough Tickets Allocated</label>
        </rules>
    </decisions>
    <decisions>
        <name>Populate_Guest_Check_Confirmed</name>
        <label>Populate Guest Check Confirmed</label>
        <locationX>477</locationX>
        <locationY>2771</locationY>
        <defaultConnector>
            <targetReference>Status_Changed_from_Offer_Accepted_to_Cancelled</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Populate_Guest_Check_Confirmed1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Guest_Name__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Date_of_Birth__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Is_SportsBet_Customer__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Guest_Completed_Date</targetReference>
            </connector>
            <label>Populate Guest Check Confirmed1</label>
        </rules>
    </decisions>
    <decisions>
        <name>Status_Changed_from_Offer_Accepted_to_Cancelled</name>
        <label>Status Changed from Offer Accepted to Cancelled with Tickets Allocated</label>
        <locationX>477</locationX>
        <locationY>3071</locationY>
        <defaultConnector>
            <targetReference>Cancelled_within_24_hours_of_an_event_start_date</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decDontReallocate</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Tickets_Sent__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Ticket_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Tickets Allocated</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Cancelled</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Offer Accepted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Allocated_Tickets__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Campaign__r.Allow_Reallocation_of_Cancelled_Attendee__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Document_Links</targetReference>
            </connector>
            <label>Don&apos;t Allow Reallocation</label>
        </rules>
    </decisions>
    <decisions>
        <name>Status_Is_Changed_to_Other_Value_from_Offer_Accepted</name>
        <label>Status Is Changed to Other Value from Offer Accepted</label>
        <locationX>477</locationX>
        <locationY>2171</locationY>
        <defaultConnector>
            <targetReference>Attended_Checkbox_is_set_to_TRUE</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Status_Is_Changed_to_Other_Value_from_Offer_Accepted1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Offer Accepted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Pending Approval</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.Name</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Guest Attendee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Call_Flow</targetReference>
            </connector>
            <label>Status Is Changed to Other Value from Offer Accepted</label>
        </rules>
    </decisions>
    <decisions>
        <name>Status_is_Offer_Accepted</name>
        <label>Status is Offer Accepted</label>
        <locationX>477</locationX>
        <locationY>1031</locationY>
        <defaultConnector>
            <targetReference>NumberOfSeatsChanged</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Status_is_Offer_Accepted1</name>
            <conditionLogic>(1 AND 2) AND ((7 AND 8 AND NOT 3) OR (3 AND 4 AND ((7 AND 8) OR (5 AND ((7 AND NOT 8) OR (6 AND 9 AND 10 AND (8 OR (NOT 8))))))))</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Offer Accepted</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Guest_Attendee</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending Approval</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Number_Of_Seats__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>2.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Number_Of_Seats__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>2.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Event_Participation_Cap_Exceeded__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.No_of_Guest_Approval_Process_Triggered__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.No_of_Guest_Approval_Process_Triggered__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Attendee_Last_Event_Accepted_Date</targetReference>
            </connector>
            <label>Status is Offer Accepted1</label>
        </rules>
    </decisions>
    <description>SBET-1170 - Allow Attendees to Cancel within 24 hours of an Event - add in rollback of change where status is changed to Cancelled</description>
    <environments>Default</environments>
    <formulas>
        <name>cancelledDocumentTitle</name>
        <dataType>String</dataType>
        <expression>&quot;Cancelled: &quot;+TRIM(RIGHT({!Get_document.Title}, LEN({!Get_document.Title})-LEN({!$Record.Account__r.Last_Name__c})-3))</expression>
    </formulas>
    <formulas>
        <name>Date</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <name>Eventofferaccepteddate</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <description>Formula to determine if the event start date is within 24 hours</description>
        <name>eventStartsLessThan24Hours</name>
        <dataType>Boolean</dataType>
        <expression>{!$Record.Campaign_Start_Date__c}-TODAY() &lt;= 1</expression>
    </formulas>
    <formulas>
        <name>GetDateToday</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <name>InteractionEventInviteConversation</name>
        <dataType>String</dataType>
        <expression>&quot;Interaction - Event Invite Conversation&quot;</expression>
    </formulas>
    <interviewLabel>Attendee Process Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Attendee Process Flow</label>
    <loops>
        <name>For_each_link</name>
        <label>For each link</label>
        <locationX>227</locationX>
        <locationY>3395</locationY>
        <collectionReference>Get_Document_Links</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Get_document</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Cancelled_within_24_hours_of_an_event_start_date</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Copy_1_of_Get_Notification_Type_ID</name>
        <label>Get Notification Type ID</label>
        <locationX>477</locationX>
        <locationY>1763</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_1_of_Assign_Owner_Id</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CustomNotifTypeName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Ticket Allocation Notification</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_document</name>
        <label>Get document</label>
        <locationX>315</locationX>
        <locationY>3503</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_new_title</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>For_each_link.ContentDocumentId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContentDocument</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Document_Links</name>
        <label>Get Document Links</label>
        <locationX>335</locationX>
        <locationY>3179</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Do_links_exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>LinkedEntityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContentDocumentLink</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_notification_type_id</name>
        <label>Get Notification Type ID</label>
        <locationX>213</locationX>
        <locationY>1655</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Owner_Id</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CustomNotifTypeName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Ticket Allocation Notification</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Attendee_Last_Event_Accepted_Date</name>
        <label>Attendee: Last Event Accepted Date</label>
        <locationX>345</locationX>
        <locationY>1139</locationY>
        <connector>
            <targetReference>Update_Last_event_accepted_date</targetReference>
        </connector>
        <inputAssignments>
            <field>Event_Offer_Accepted_Date__c</field>
            <value>
                <elementReference>Eventofferaccepteddate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Change_Ticket_Status</name>
        <label>Tickets Requested</label>
        <locationX>477</locationX>
        <locationY>1655</locationY>
        <connector>
            <targetReference>Copy_1_of_Get_Notification_Type_ID</targetReference>
        </connector>
        <inputAssignments>
            <field>Ticket_Status__c</field>
            <value>
                <stringValue>Tickets Requested</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Revert value of field back to false in scenario where the attendee is no longer in the Cancelled status</description>
        <name>Update_Checkbox_to_False</name>
        <label>Update Checkbox to False</label>
        <locationX>477</locationX>
        <locationY>4187</locationY>
        <inputAssignments>
            <field>Cancelled_Within_24hrs_of_Event__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update the triggering record to reflect that the attendee cancelled within 24 hours of an event.</description>
        <name>Update_Checkbox_to_True</name>
        <label>Update Checkbox to True</label>
        <locationX>213</locationX>
        <locationY>4187</locationY>
        <inputAssignments>
            <field>Cancelled_Within_24hrs_of_Event__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Document_Title</name>
        <label>Update Document Title</label>
        <locationX>315</locationX>
        <locationY>3719</locationY>
        <connector>
            <targetReference>For_each_link</targetReference>
        </connector>
        <inputReference>Get_document</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Guest_Completed_Date</name>
        <label>Update Guest Completed Date</label>
        <locationX>345</locationX>
        <locationY>2879</locationY>
        <connector>
            <targetReference>Status_Changed_from_Offer_Accepted_to_Cancelled</targetReference>
        </connector>
        <inputAssignments>
            <field>Guest_Check_Confirmed__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Guest_Completed_Date__c</field>
            <value>
                <elementReference>Date</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Last_event_accepted_date</name>
        <label>Update Last event accepted date</label>
        <locationX>345</locationX>
        <locationY>1247</locationY>
        <connector>
            <targetReference>Update_Ticket_Status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Event_Accepted_Date__c</field>
            <value>
                <elementReference>GetDateToday</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Status_to_Attended</name>
        <label>Update Status to Attended</label>
        <locationX>345</locationX>
        <locationY>2579</locationY>
        <connector>
            <targetReference>Populate_Guest_Check_Confirmed</targetReference>
        </connector>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Attended</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Ticket_Status</name>
        <label>Tickets Requested</label>
        <locationX>345</locationX>
        <locationY>1355</locationY>
        <connector>
            <targetReference>NumberOfSeatsChanged</targetReference>
        </connector>
        <inputAssignments>
            <field>Ticket_Status__c</field>
            <value>
                <stringValue>Tickets Requested</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>IsUnconfirmed</targetReference>
        </connector>
        <object>Attendee__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Call_Flow</name>
        <label>Call Flow</label>
        <locationX>345</locationX>
        <locationY>2279</locationY>
        <connector>
            <targetReference>Attended_Checkbox_is_set_to_TRUE</targetReference>
        </connector>
        <flowName>Customer_Event_Last_Event_Accepted_Date</flowName>
        <inputAssignments>
            <name>CustomerId</name>
            <value>
                <elementReference>$Record.Account__c</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <textTemplates>
        <name>overAllocationBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!$Record.Account__r.FirstName} {!$Record.Account__r.LastName} has been allocated more tickets than requested for {!$Record.Campaign__r.Name}. Please review their attendee record and request ticket reallocation if required</text>
    </textTemplates>
    <textTemplates>
        <name>overAllocationTitle</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!$Record.Account__r.FirstName} {!$Record.Account__r.LastName} Over-Allocation of Tickets</text>
    </textTemplates>
    <textTemplates>
        <name>underAllocationBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!$Record.Account__r.FirstName} {!$Record.Account__r.LastName} has requested more tickets for {!$Record.Campaign__r.Name} after their tickets have already been allocated. Please review their attendee record and request ticket reallocation if required.</text>
    </textTemplates>
    <textTemplates>
        <name>underAllocationTitle</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!$Record.Account__r.FirstName} {!$Record.Account__r.LastName} Request for More Tickets</text>
    </textTemplates>
    <variables>
        <name>Attendee</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
    <variables>
        <name>customerOwner</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>customersFound</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
</Flow>
