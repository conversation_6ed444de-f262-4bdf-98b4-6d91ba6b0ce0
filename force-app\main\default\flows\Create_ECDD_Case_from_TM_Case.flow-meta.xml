<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>assign_triggering_record_fields</name>
        <label>assign triggering record fields</label>
        <locationX>1810</locationX>
        <locationY>1106</locationY>
        <assignmentItems>
            <assignToReference>recordId.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>recordId.ECDD_Required_Picklist__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>recordId.ECDD_case_created__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Triggering_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_triggering_record_fields_for_LER</name>
        <label>assign triggering record fields for LER</label>
        <locationX>1546</locationX>
        <locationY>1106</locationY>
        <assignmentItems>
            <assignToReference>recordId.ECDD_Required_Picklist__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>recordId.ECDD_case_created__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Triggering_Record</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Check Investigation Steps.</description>
        <name>check_for_completion_of_investigation_steps</name>
        <label>check for completion of investigation steps</label>
        <locationX>996</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>getECDDRecordType</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Validation steps complete</defaultConnectorLabel>
        <rules>
            <name>TM_SG_Referral</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.SG_Referral__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Complete_the_required_fields</targetReference>
            </connector>
            <label>TM SG Referral</label>
        </rules>
        <rules>
            <name>SMR_Validation_Steps</name>
            <conditionLogic>1 AND (2 OR 3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>recordId.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SMR</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Main_reason_trigger_for_SMR__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Reportable_Offence__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Date_SMR_submitted_to_Austrac__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.SMR_Receipt__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Complete_the_required_fields</targetReference>
            </connector>
            <label>SMR Validation Steps</label>
        </rules>
        <rules>
            <name>validation_steps_in_general_to_complete</name>
            <conditionLogic>1 AND (2 OR 3 OR 4 OR 5 OR (6 AND 7))</conditionLogic>
            <conditions>
                <leftValueReference>recordId.RecordType.DeveloperName</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Transaction</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Triage_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.AML_Analyst_Comments_Long_Text__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.ECDD_Required_Picklist__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.FastTrack_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>LER</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Flag_Appled__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Complete_the_required_fields</targetReference>
            </connector>
            <label>validation steps in general to complete</label>
        </rules>
        <rules>
            <name>Adverse_Media_Validation_steps</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.RecordType.DeveloperName</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Transaction</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Adverse Media</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Crime_Typology__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Complete_the_required_fields</targetReference>
            </connector>
            <label>Adverse Media Validation steps</label>
        </rules>
        <rules>
            <name>UAR_Completion_steps</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>recordId.RecordType.DeveloperName</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Transaction</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>UAR</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Date_UAR_review__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Confirm_Activity_is_Unusual__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Victim__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Complete_the_required_fields</targetReference>
            </connector>
            <label>UAR Completion steps</label>
        </rules>
        <rules>
            <name>LER_Completion_steps</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5 OR 6)</conditionLogic>
            <conditions>
                <leftValueReference>recordId.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non_Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>LER</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.LER_Comment__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Request_Type__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Department__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Offence_listed__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Complete_the_required_fields</targetReference>
            </connector>
            <label>LER Completion steps</label>
        </rules>
        <rules>
            <name>CRA_Completion_Steps</name>
            <conditionLogic>(1 OR 2 OR 3 OR 4 OR 5 OR 6 OR 7) AND 8</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Manual_Google_Search_for_Adverse_Media__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Are_Commercial_Cards_Funds_Being_Used__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Open_Source_Search_High_Risk_Occupation__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Triage_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.ECDD_Required_Picklist__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.FastTrack_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.SG_Referral__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer_Risk_Assessment</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Complete_the_required_fields</targetReference>
            </connector>
            <label>CRA Completion Steps</label>
        </rules>
    </decisions>
    <decisions>
        <description>If existing case is of ECDD_Case RecordTypeName then display already existing ECDD case else continue to ECDD Case creation.</description>
        <name>check_if_ECDD_is_existing</name>
        <label>check if ECDD is existing</label>
        <locationX>523</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>check_for_completion_of_investigation_steps</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>ECDD does not exist</defaultConnectorLabel>
        <rules>
            <name>ECDD_Exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>check_if_ECDD_case_is_already_present.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>show_the_Existing_ECDD_info</targetReference>
            </connector>
            <label>ECDD Exists</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_parent_record_type</name>
        <label>Check parent record type</label>
        <locationX>1678</locationX>
        <locationY>998</locationY>
        <defaultConnector>
            <targetReference>assign_triggering_record_fields</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>LER</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>LER</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assign_triggering_record_fields_for_LER</targetReference>
            </connector>
            <label>LER</label>
        </rules>
    </decisions>
    <description>Used by Create ECDD Action on Transaction Monitoring RecordType
- Creates ECDD Case and closes the TM case.

Edit 13/1/2025
-Added CRA Completion steps decision for Customer Risk Assessment record type.&apos;

15/1/2025
-Updated CRA Completion steps with more fields.</description>
    <environments>Default</environments>
    <formulas>
        <name>formulaECDDCaseURL</name>
        <dataType>String</dataType>
        <expression>LEFT({!$Api.Partner_Server_URL_260}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_260})) &amp; {!NewECDDcaseID}</expression>
    </formulas>
    <interviewLabel>Create ECDD Case from TM Case {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Create ECDD Case from TM Case</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>this is an object of case, referencing an instance of ECDD case</description>
        <name>ECDD_case</name>
        <label>ECDD case</label>
        <locationX>1678</locationX>
        <locationY>782</locationY>
        <assignRecordIdToReference>NewECDDcaseID</assignRecordIdToReference>
        <connector>
            <targetReference>ECDD_case_details</targetReference>
        </connector>
        <inputAssignments>
            <field>AML_Analyst_Comments_Long_Text__c</field>
            <value>
                <elementReference>recordId.AML_Analyst_Comments_Long_Text__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>recordId.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>recordId.Description</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FastTrack_Required__c</field>
            <value>
                <elementReference>recordId.FastTrack_Required__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Insert_Date__c</field>
            <value>
                <elementReference>recordId.Insert_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <elementReference>recordId.Origin</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>ECDD_Unassigned_Queue.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>ECDD_RecordTypeID</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TF_or_Non_TF_related__c</field>
            <value>
                <elementReference>recordId.TF_or_Non_TF_related__c</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordCreates>
    <recordLookups>
        <description>Get current child cases and store Id, Case Number and RecordTypeId.</description>
        <name>check_if_ECDD_case_is_already_present</name>
        <label>check if ECDD case is already present</label>
        <locationX>523</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>check_if_ECDD_is_existing</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ParentId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>CaseNumber</queriedFields>
        <queriedFields>RecordTypeId</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get New ECDD Case Id.</description>
        <name>ECDD_case_details</name>
        <label>ECDD case details</label>
        <locationX>1678</locationX>
        <locationY>890</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_parent_record_type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>NewECDDcaseID</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get ECDD Unassigned Queue.</description>
        <name>ECDD_Unassigned_Queue</name>
        <label>ECDD Unassigned Queue</label>
        <locationX>1678</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ECDD_case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Unassigned_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get ECDD Case Record Type.</description>
        <name>getECDDRecordType</name>
        <label>getECDDRecordType</label>
        <locationX>1678</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ECDD_Unassigned_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Case</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>ECDD_RecordTypeID</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordUpdates>
        <name>Update_Triggering_Record</name>
        <label>Update Triggering Record</label>
        <locationX>1678</locationX>
        <locationY>1298</locationY>
        <connector>
            <targetReference>confirm_screen</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Error_Page</targetReference>
        </faultConnector>
        <inputReference>recordId</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <description>Show</description>
        <name>Complete_the_required_fields</name>
        <label>Complete the required fields</label>
        <locationX>996</locationX>
        <locationY>1730</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Complete_the_required_fields_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Complete_the_required_fields_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Complete_the_required_fields_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Complete_steps_Information</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Please complete the investigation steps, before creating ECDD case.&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>10</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Complete_the_required_fields_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>confirm_screen</name>
        <label>confirm screen</label>
        <locationX>1678</locationX>
        <locationY>1406</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>confirm_screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>confirm_screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>confirm_screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>confirmation_text</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;ECDD case has been successfully created! &lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Case number : {!texttemplateECDDCaseURL}&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>confirm_screen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Check Fast Track Required and TF or Non TF Related fields.</description>
        <name>Confirm_section</name>
        <label>Confirm section</label>
        <locationX>523</locationX>
        <locationY>134</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Cancel</backButtonLabel>
        <connector>
            <targetReference>check_if_ECDD_case_is_already_present</targetReference>
        </connector>
        <fields>
            <name>Confirm_screen_text</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;﻿Do you want to create ECDD?&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Please ensure the below fields are correct before proceeding.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <fieldType>ObjectProvided</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <objectFieldReference>recordId.FastTrack_Required__c</objectFieldReference>
        </fields>
        <fields>
            <fieldType>ObjectProvided</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <objectFieldReference>recordId.TF_or_Non_TF_related__c</objectFieldReference>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>recordId.FastTrack_Required__c</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Yes</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Error_Page</name>
        <label>Error Page</label>
        <locationX>2074</locationX>
        <locationY>1406</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>FlowError</name>
            <fieldText>&lt;p&gt;There was an error when running this process. Please review the below error for more information.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Display the link to existing ECDD Case.</description>
        <name>show_the_Existing_ECDD_info</name>
        <label>show the Existing ECDD info</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>show_the_Existing_ECDD_info_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>show_the_Existing_ECDD_info_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>2</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>show_the_Existing_ECDD_info_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>ECDD_info</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;This {!recordId.RecordType.Name} case already has one ECDD case.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;ECDD Case Number: &lt;/strong&gt;&lt;a href=&quot;/{!check_if_ECDD_case_is_already_present.Id}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot; style=&quot;font-size: 16px;&quot;&gt;&lt;strong&gt;{!check_if_ECDD_case_is_already_present.CaseNumber}&lt;/strong&gt;&lt;/a&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>9</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>show_the_Existing_ECDD_info_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>1</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>397</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Confirm_section</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>texttemplateECDDCaseURL</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;a href=&quot;{!formulaECDDCaseURL}&quot; target=&quot;_self&quot;&gt;{!ECDD_case_details.CaseNumber}&lt;/a&gt;</text>
    </textTemplates>
    <variables>
        <name>ECDD_Case_Number</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ECDD_RecordTypeID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>existing_ecdd_record</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>NewECDDcaseID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
</Flow>
