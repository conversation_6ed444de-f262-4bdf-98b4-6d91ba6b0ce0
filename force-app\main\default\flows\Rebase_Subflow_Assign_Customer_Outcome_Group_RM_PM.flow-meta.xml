<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns the value for New Customer Group based on formula output</description>
        <name>Assign_New_Customer_Group</name>
        <label>Assign New Customer Group</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignmentItems>
            <assignToReference>varNewCustomerGroupOutcome</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaNewCustomerGroup</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <description>Subflow that stores the formula for the rules and if else logic to assign value to the Customer Outcome Group following reviews from Relationship Manager &amp; Performance Team</description>
    <environments>Default</environments>
    <formulas>
        <description>Calculates the value for the New Customer Group</description>
        <name>formulaNewCustomerGroup</name>
        <dataType>String</dataType>
        <expression>IF(
    OR(
        ISPICKVAL({!varSaferGamblingReview}, &quot;Remove&quot;),
        ISPICKVAL({!varCustomerIntegrityReview}, &quot;Remove&quot;)
    ),
    &quot;Standard&quot;,
    IF(
        AND(
            ISPICKVAL({!varPerformanceTeamReview}, &quot;Remove&quot;),
            ISPICKVAL({!varPerformanceTeamCustomerGroupOutcome}, &quot;Standard&quot;)
        ),
        &quot;Standard&quot;,
        IF(
            AND(
                ISPICKVAL({!varPerformanceTeamReview}, &quot;Remove&quot;),
                NOT(ISPICKVAL({!varPerformanceTeamCustomerGroupOutcome}, &quot;Standard&quot;))
            ),
            &quot;Premium Service&quot;,
            IF(
                AND(
                    ISPICKVAL({!varPerformanceTeamReview}, &quot;Keep&quot;),
                    ISPICKVAL({!varRelationshipManagerReview}, &quot;Reallocate to Other RM&quot;),
                    NOT(ISBLANK({!varRelationshipManagertoReallocateto}))
                ),
                {!varRelationshipManagertoReallocateto},
                IF(
                    AND(
                        ISPICKVAL({!varPerformanceTeamReview}, &quot;Keep&quot;),
                        ISPICKVAL({!varRelationshipManagerReview}, &quot;Reallocate to Other RM&quot;),
                        ISBLANK({!varRelationshipManagertoReallocateto})
                    ),
                    &quot;State Manager&quot;,
                    IF(
                        AND(
                            ISPICKVAL({!varPerformanceTeamReview}, &quot;Keep&quot;),
                            NOT(ISPICKVAL({!varRelationshipManagerReview}, &quot;Reallocate to Other RM&quot;))
                        ),
                        {!varRBNewCustomerGroup},
                        {!varCustomerGroupOutcome}
                    )
                )
            )
        )
    )
)</expression>
    </formulas>
    <interviewLabel>Rebase: Subflow Assign Customer Outcome Group - RM &amp; PM {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rebase: Subflow Assign Customer Outcome Group - RM &amp; PM</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assign_New_Customer_Group</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>Stores the value of the current Customer Group Outcome before the formula compiles</description>
        <name>varCustomerGroupOutcome</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value of the Customer_Integrity_Review__c field from the parent flow</description>
        <name>varCustomerIntegrityReview</name>
        <dataType>Picklist</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the output of the formula after it compiles with the value for the Customer Group Outcome to apply to the Rebase record</description>
        <name>varNewCustomerGroupOutcome</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <description>Stores value of the Performance Team Customer Group Outcome field from Rebase</description>
        <name>varPerformanceTeamCustomerGroupOutcome</name>
        <dataType>Picklist</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value of the Performance_Team_Review__c field from the parent flow</description>
        <name>varPerformanceTeamReview</name>
        <dataType>Picklist</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value of the RB_New_Customer_Group__c field from the parent flow</description>
        <name>varRBNewCustomerGroup</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the value of the Relationship Manager Review field</description>
        <name>varRelationshipManagerReview</name>
        <dataType>Picklist</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value of the Relationship_Manager_to_Reallocate_to__c field from the parent flow</description>
        <name>varRelationshipManagertoReallocateto</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value of the Safer_Gambling_Review__c field from the parent flow</description>
        <name>varSaferGamblingReview</name>
        <dataType>Picklist</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
