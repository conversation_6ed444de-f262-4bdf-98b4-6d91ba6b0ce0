<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assign Bonus Expiry Date for Open Task</description>
        <name>Assign_Bonus_Expiry_Date_for_Open_Task</name>
        <label>Assign Bonus Expiry Date for Open Task</label>
        <locationX>842</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>varBonusExpiryTask.ActivityDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varBonusExpiryDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varBonusExpiryTask.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varBonusExpiryTask.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>FYI Alert: Expiring Bonus/Deposit Offer</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Open_Bonus_Expiry_Task</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_close_task_for_Kafka_premium_Integration_0</name>
        <label>Update close task for Kafka/premium/Integration 0</label>
        <locationX>1634</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>varOpenIntroductionTask.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_close_task_for_Kafka_premium_Integration</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_OPen_Inro_task_NOT_Kafka_Premium_Integration_0</name>
        <label>Update OPen Inro task NOT Kafka/Premium/Integration 0</label>
        <locationX>2162</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>varOpenIntroductionTask.ActivityDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>formulaTodayPlus14</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varOpenIntroductionTask.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varTaskOwner</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_OPen_Inro_task_NOT_Kafka_Premium_Integration_2</targetReference>
        </connector>
    </assignments>
    <constants>
        <name>varTaskDescription</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Introduction phone call and preference gathering required. Try and ascertain where else the client is wagering, who is their main provider and any other relevant information. Ensure your contact details are communicated to the client.</stringValue>
        </value>
    </constants>
    <constants>
        <name>varTaskSubject</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Alert: Introduction to Customer</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Decision to check if Open Bonus Expiry Task Available</description>
        <name>Bonus_Expiry_Task_Available</name>
        <label>Open Bonus Expiry Task Available</label>
        <locationX>974</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Create_Bonus_Expiry_Task</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>decOpen_Bonus_Expiry_Task_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varBonusExpiryTask</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Bonus_Expiry_Date_for_Open_Task</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Open Introduction Task Available</description>
        <name>Open_Introduction_Task_Available</name>
        <label>Open Introduction Task Available</label>
        <locationX>1898</locationX>
        <locationY>782</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>is_New_NOT_Premium_Kafka_Integration</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Integration_User_ID.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Kafka_Integration_Id.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Premium_Service_User_ID.Value__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Intro_task_for_New_customer</targetReference>
            </connector>
            <label>is New NOT Premium/Kafka/Integration</label>
        </rules>
        <rules>
            <name>Is_Existing_Premium_Kafka_Integration</name>
            <conditionLogic>1 AND (2 OR 3 OR 4) AND 5</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Existing</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Integration_User_ID.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Kafka_Integration_Id.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Premium_Service_User_ID.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varOpenIntroductionTask</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_close_task_for_Kafka_premium_Integration_0</targetReference>
            </connector>
            <label>Is Existing Premium/Kafka/Integration</label>
        </rules>
        <rules>
            <name>no_Open_Existing_NOT_Premium_Kafka_Integration</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Existing</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Integration_User_ID.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Kafka_Integration_Id.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Premium_Service_User_ID.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varOpenIntroductionTask</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Intro_task_for_existing_Customer</targetReference>
            </connector>
            <label>no Open Existing NOT Premium/Kafka/Integration</label>
        </rules>
        <rules>
            <name>Open_Tasks_Esiting_Cust_NOT_Premium_kafka_Integration</name>
            <conditionLogic>1 AND (2 OR 3 OR 4) AND 5</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Existing</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Integration_User_ID.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Kafka_Integration_Id.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varTaskOwner</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Premium_Service_User_ID.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varOpenIntroductionTask</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_OPen_Inro_task_NOT_Kafka_Premium_Integration_0</targetReference>
            </connector>
            <label>Open Tasks Esiting Cust NOT Premium/kafka/Integration</label>
        </rules>
    </decisions>
    <decisions>
        <description>OpenNegativeAlertAvailable</description>
        <name>OpenNegativeAlertAvailable</name>
        <label>OpenNegativeAlertAvailable</label>
        <locationX>446</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Create_Negative_Balance_Alert_Task</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varNegativeAlertTask</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Router</description>
        <name>Router</name>
        <label>Router</label>
        <locationX>1370</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decAccount_Locked_Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>AccountLocked</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Account_Locked_Alert_task</targetReference>
            </connector>
            <label>Account Locked Task</label>
        </rules>
        <rules>
            <name>decNegative_Balance_Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>NegativeBalance</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetNegativeAccountTaskDetails</targetReference>
            </connector>
            <label>Negative Balance Task</label>
        </rules>
        <rules>
            <name>decBonus_Expiry_Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ExpiringBonus</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Open_Bonus_Expiry_Tasks</targetReference>
            </connector>
            <label>Bonus Expiry Task</label>
        </rules>
        <rules>
            <name>decIntroduction_Task</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varAutomatedTaskType</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Introduction</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Open_Introduction_Task</targetReference>
            </connector>
            <label>Introduction Task</label>
        </rules>
    </decisions>
    <description>This flow can be used to Invoke Automated Task Creation for various scenarios
Modified for SBET-143 - Update existing open tasks to closed with FYI in subject</description>
    <environments>Default</environments>
    <formulas>
        <description>formula for Today  + 1</description>
        <name>formulaTodayPlus1</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate}+1</expression>
    </formulas>
    <formulas>
        <description>Formula Today Plus 14 days</description>
        <name>formulaTodayPlus14</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate}+14</expression>
    </formulas>
    <formulas>
        <description>Formula for Today Plus 3</description>
        <name>formulaTodayPlus3</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate}+3</expression>
    </formulas>
    <interviewLabel>Invoke Automated Task Creation {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Invoke Automated Task Creation</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>Create Account Locked Alert task</description>
        <name>Create_Account_Locked_Alert_task</name>
        <label>Create Account Locked Alert task</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formulaTodayPlus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Customer account locked. Review account &amp; contact customer to assist in logging in.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>varTaskOwner</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordTypeID</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Alert: User Locked Out</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TaskSubtype</field>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <description>Create Bonus Expiry Task</description>
        <name>Create_Bonus_Expiry_Task</name>
        <label>Create Bonus Expiry Task</label>
        <locationX>1106</locationX>
        <locationY>566</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>varBonusExpiryDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>FYI Alert: Expiring Bonus/Deposit Offer</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>varTaskOwner</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordTypeID</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>FYI Alert: Expiring Bonus/Deposit Offer</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TaskSubtype</field>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Intro_task_for_existing_Customer</name>
        <label>Create Intro task for existing Customer</label>
        <locationX>1898</locationX>
        <locationY>890</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formulaTodayPlus14</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>varTaskDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>varTaskOwner</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordTypeID</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>varTaskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TaskSubtype</field>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Intro_task_for_New_customer</name>
        <label>Create Intro task for New customer</label>
        <locationX>1370</locationX>
        <locationY>890</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formulaTodayPlus14</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>varTaskDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>varTaskOwner</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordTypeID</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>varTaskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TaskSubtype</field>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <description>Create the Negative Balance Alert Task</description>
        <name>Create_Negative_Balance_Alert_Task</name>
        <label>Create Negative Balance Alert Task</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formulaTodayPlus3</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Customer Account has a negative balance. Review customer account to adjust negative balance. Once account is $0 contact customer to let them know nothing is owed and the account is free to use the account again as per normal.</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>varTaskOwner</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordTypeID</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Alert: Negative Account Balance</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TaskSubtype</field>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <description>Get Bonus Expiry Tasks</description>
        <name>Get_Open_Bonus_Expiry_Tasks</name>
        <label>Get Open Bonus Expiry Tasks</label>
        <locationX>974</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Bonus_Expiry_Task_Available</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Alert: Expiring Bonus/Deposit Offer</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <object>Task</object>
        <outputReference>varBonusExpiryTask</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>ActivityDate</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Get Open Introduction Task</description>
        <name>Get_Open_Introduction_Task</name>
        <label>Get Open Introduction Task</label>
        <locationX>1898</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Kafka_Integration_Id</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Alert: Introduction to Customer</stringValue>
            </value>
        </filters>
        <object>Task</object>
        <outputReference>varOpenIntroductionTask</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>ActivityDate</queriedFields>
        <queriedFields>OwnerId</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Get Task Record Type ID</description>
        <name>Get_Task_Record_Type_ID</name>
        <label>Get Task Record Type ID</label>
        <locationX>1370</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Router</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>varTaskRecordTypeID</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>GetNegativeAccountTaskDetails</description>
        <name>GetNegativeAccountTaskDetails</name>
        <label>GetNegativeAccountTaskDetails</label>
        <locationX>446</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>OpenNegativeAlertAvailable</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Alert: Negative Account Balance</stringValue>
            </value>
        </filters>
        <object>Task</object>
        <outputReference>varNegativeAlertTask</outputReference>
        <queriedFields>Id</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>Integration_User_ID</name>
        <label>Integration User ID</label>
        <locationX>1898</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Premium_Service_User_ID</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Integration User ID</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Kafka_Integration_Id</name>
        <label>Kafka Integration Id</label>
        <locationX>1898</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Integration_User_ID</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Kafka Integration userId</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Premium_Service_User_ID</name>
        <label>Premium Service User ID</label>
        <locationX>1898</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Open_Introduction_Task_Available</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium Service User ID</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_close_task_for_Kafka_premium_Integration</name>
        <label>Update close task for Kafka/premium/Integration</label>
        <locationX>1634</locationX>
        <locationY>998</locationY>
        <inputReference>varOpenIntroductionTask</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update Open Bonus Expiry Task</description>
        <name>Update_Open_Bonus_Expiry_Task</name>
        <label>Update Open Bonus Expiry Task</label>
        <locationX>842</locationX>
        <locationY>674</locationY>
        <inputReference>varBonusExpiryTask</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_OPen_Inro_task_NOT_Kafka_Premium_Integration_2</name>
        <label>Update OPen Inro task NOT Kafka/Premium/Integration 2</label>
        <locationX>2162</locationX>
        <locationY>998</locationY>
        <inputReference>varOpenIntroductionTask</inputReference>
    </recordUpdates>
    <start>
        <locationX>1244</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Task_Record_Type_ID</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>recordId Input variable</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to capture Automated Task Type which will be used in the Router to create Specific task</description>
        <name>varAutomatedTaskType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to capture Bonus Expiry Date from Process Builder</description>
        <name>varBonusExpiryDate</name>
        <dataType>Date</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to store if any open Bonus Expiry Task is available for the Customer</description>
        <name>varBonusExpiryTask</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <description>Variable to store Negative Alert Task</description>
        <name>varNegativeAlertTask</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>varNewCustomerOwnerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to store the Open Introduction Task for the Customer</description>
        <name>varOpenIntroductionTask</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>varTaskOwner</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to store Task Record Type ID</description>
        <name>varTaskRecordTypeID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
