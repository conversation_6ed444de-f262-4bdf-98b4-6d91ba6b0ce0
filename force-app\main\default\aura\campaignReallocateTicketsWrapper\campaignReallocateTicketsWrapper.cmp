<aura:component implements="force:lightningQuickActionWithoutHeader,force:hasRecordId">
    <aura:attribute name="invoked" type="Boolean" default="false"></aura:attribute>
    <aura:handler name="render" value="{!this}" action="{!c.handleRender}"></aura:handler>
    <c:campaignAllocateTickets aura:id="lwc" omg="{!v.recordId}" onfireclose="{!c.handleFireClose}"></c:campaignAllocateTickets>
</aura:component>