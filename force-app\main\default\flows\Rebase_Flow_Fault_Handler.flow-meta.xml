<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Sends Emails with Flow Fault details to all the user emails in the collection</description>
        <name>Send_Email_to_Users</name>
        <label>Send Email to Users</label>
        <locationX>50</locationX>
        <locationY>998</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddressesArray</name>
            <value>
                <elementReference>Extract_User_Emails</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>DefaultWorkflowUser</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>texttemplateFlowFaultEmailSubject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>texttemplateFlowFaultEmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Null Check for Public Group Members Records</description>
        <name>Null_Check_for_Public_Group_Members_Records</name>
        <label>Null Check for Public Group Members Records</label>
        <locationX>380</locationX>
        <locationY>458</locationY>
        <defaultConnectorLabel>No Records</defaultConnectorLabel>
        <rules>
            <name>Public_Group_Members_Records</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Group_Members_in_Rebase_Performance_Team</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Group_Members_in_Rebase_Performance_Team</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Extract_User_Ids</targetReference>
            </connector>
            <label>Public Group Members Records</label>
        </rules>
    </decisions>
    <decisions>
        <description>Null Check for Public Group Records</description>
        <name>Null_Check_for_Public_Group_Records</name>
        <label>Null Check for Public Group Records</label>
        <locationX>611</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>No Records</defaultConnectorLabel>
        <rules>
            <name>Public_Group_Records</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Rebase_Performance_Team_Public_Group</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Rebase_Performance_Team_Public_Group</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Group_Members_in_Rebase_Performance_Team</targetReference>
            </connector>
            <label>Public Group Records</label>
        </rules>
    </decisions>
    <decisions>
        <description>Null Check for User Records</description>
        <name>Null_Check_for_User_Records</name>
        <label>Null Check for User Records</label>
        <locationX>182</locationX>
        <locationY>782</locationY>
        <defaultConnectorLabel>No Records</defaultConnectorLabel>
        <rules>
            <name>User_Records</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_User_Emails_from_Group_Members</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_User_Emails_from_Group_Members</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Extract_User_Emails</targetReference>
            </connector>
            <label>User Records</label>
        </rules>
    </decisions>
    <description>Autolaunched Flow used as a subflow to handle any Flow fault in any Flows that are part of the Rebase process</description>
    <environments>Default</environments>
    <interviewLabel>Rebase Flow Fault Handler {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rebase Flow Fault Handler</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the Ids of all the individuals in the Rebase Performance Team Public Group</description>
        <name>Get_Group_Members_in_Rebase_Performance_Team</name>
        <label>Get Group Members in Rebase Performance Team</label>
        <locationX>380</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_for_Public_Group_Members_Records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Rebase_Performance_Team_Public_Group.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GroupMember</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>UserOrGroupId</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the Id for the Rebase Performance Team Public Group</description>
        <name>Get_Rebase_Performance_Team_Public_Group</name>
        <label>Get Rebase Performance Team Public Group</label>
        <locationX>611</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_for_Public_Group_Records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Regular</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Rebase_Performance_Team</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the emails of the users in the Rebase Performance Team Public Group</description>
        <name>Get_User_Emails_from_Group_Members</name>
        <label>Get User Emails from Group Members</label>
        <locationX>182</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Null_Check_for_User_Records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>Extract_User_Ids</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Email</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>485</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Rebase_Performance_Team_Public_Group</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>Stores the body of the email that is sent to the Performance Team when there is a Flow Fault</description>
        <name>texttemplateFlowFaultEmailBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;Hi Performance Team&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;There was an error in this Flow, please investigate&lt;/p&gt;&lt;p&gt;Flow DateTime: {!varFlowCurrentDateTime}&lt;/p&gt;&lt;p&gt;Flow Name: {!varFlowName}&lt;/p&gt;&lt;p&gt;Flow Fault Message: {!varFlowFaultMessage}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Thanks&lt;/p&gt;&lt;p&gt;Salesforce&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <description>Stores the subject of the email that is sent to the Performance Team when there is a Flow Fault</description>
        <name>texttemplateFlowFaultEmailSubject</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;Flow Error - {!varFlowName}&lt;/p&gt;</text>
    </textTemplates>
    <transforms>
        <description>Extracts Emails from record collection to Transform text collection</description>
        <name>Extract_User_Emails</name>
        <label>Extract User Emails</label>
        <locationX>50</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Send_Email_to_Users</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_User_Emails_from_Group_Members[$EachItem].Email</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <description>Extracts User Ids from record collection to Transform text collection</description>
        <name>Extract_User_Ids</name>
        <label>Extract User Ids</label>
        <locationX>182</locationX>
        <locationY>566</locationY>
        <connector>
            <targetReference>Get_User_Emails_from_Group_Members</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Group_Members_in_Rebase_Performance_Team[$EachItem].UserOrGroupId</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <variables>
        <description>Stores the FlowCurrentDateTime from the parent Flow</description>
        <name>varFlowCurrentDateTime</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the FlowFaultMessage from the parent Flow</description>
        <name>varFlowFaultMessage</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the varFlowName from the parent Flow</description>
        <name>varFlowName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the RecordId from the parent Flow</description>
        <name>varFlowRecordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the FlowUser from the parent Flow</description>
        <name>varFlowUser</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
