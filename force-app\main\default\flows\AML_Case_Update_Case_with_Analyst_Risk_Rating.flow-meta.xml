<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Only populate Date_Analyst_Risk_Rating_Provided__c for the first time.</description>
        <name>Check_Date_Analyst_Risk_Rating</name>
        <label>Check Date Analyst Risk Rating</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>Update_Records_with_RRR_and_ARR</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Case__r.Date_Analyst_Risk_Rating_Provided__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Records_with_RRR_and_ARR_with_Date_ARR</targetReference>
            </connector>
            <label>IsNull</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Analyst_risk_rating_needs_update</name>
        <label>Check if Analyst risk rating needs update</label>
        <locationX>380</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Update_case_record_with_RRR</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Update only RRR</defaultConnectorLabel>
        <rules>
            <name>Update_Analyst_risk_rating_too</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Analyst_Rating__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Analyst_Rating__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Date_Analyst_Risk_Rating</targetReference>
            </connector>
            <label>Update Analyst risk rating too</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_If_Residual_Rating_Update_is_Required</name>
        <label>Check If Residual Rating Update is Required</label>
        <locationX>974</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Update_Records_with_ARR_only</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Update Analyst Rating Only</defaultConnectorLabel>
        <rules>
            <name>Update_Residual_Rating_Also</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Residual_Rating__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Residual_Rating__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Records_with_ARR_and_RRR</targetReference>
            </connector>
            <label>Update Residual Rating Also</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check which Risk Rating needs to be updated.</description>
        <name>Update_Analyst_Risk_Rating_or_Residual_Risk_rating</name>
        <label>Update Analyst Risk Rating or Residual Risk rating</label>
        <locationX>875</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>no updates to do</defaultConnectorLabel>
        <rules>
            <name>Update_Residual_risk_rating</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Residual_Rating__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Residual_Rating__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_if_Analyst_risk_rating_needs_update</targetReference>
            </connector>
            <label>Update Residual risk rating</label>
        </rules>
        <rules>
            <name>Update_Analyst_Risk_Rating</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Analyst_Rating__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Analyst_Rating__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_If_Residual_Rating_Update_is_Required</targetReference>
            </connector>
            <label>Update Analyst Risk Rating</label>
        </rules>
    </decisions>
    <description>This flow populates Residual and Analyst Risk Rating from ECDD Form to Case.

19/12/2024
- Fixed issue where the Date_Analyst_Risk_Rating_Provided gets erased after the analyst risk rating is changed.</description>
    <environments>Default</environments>
    <interviewLabel>AML Case - Update Case with Analyst Risk Rating {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML - Update Case with Analyst Risk Rating or Residual Risk Rating</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Update Residual Risk Rating on related Case.</description>
        <name>Update_case_record_with_RRR</name>
        <label>Update case record with RRR</label>
        <locationX>578</locationX>
        <locationY>539</locationY>
        <inputAssignments>
            <field>Residual_Risk_Rating__c</field>
            <value>
                <elementReference>$Record.Residual_Rating__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Case__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update Analyst Risk Rating and Residual Risk Rating on related Case.</description>
        <name>Update_Records_with_ARR_and_RRR</name>
        <label>Update Records with ARR and RRR</label>
        <locationX>842</locationX>
        <locationY>539</locationY>
        <inputAssignments>
            <field>Analyst_Risk_Rating__c</field>
            <value>
                <elementReference>$Record.Analyst_Rating__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Residual_Risk_Rating__c</field>
            <value>
                <elementReference>$Record.Residual_Rating__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Case__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update Analyst Risk Rating related Case.</description>
        <name>Update_Records_with_ARR_only</name>
        <label>Update Records with ARR only</label>
        <locationX>1106</locationX>
        <locationY>539</locationY>
        <inputAssignments>
            <field>Analyst_Risk_Rating__c</field>
            <value>
                <elementReference>$Record.Analyst_Rating__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_Analyst_Risk_Rating_Provided__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Case__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update Analyst Risk Rating and Residual Risk Rating on related Case.</description>
        <name>Update_Records_with_RRR_and_ARR</name>
        <label>Update Records with RRR and ARR</label>
        <locationX>314</locationX>
        <locationY>647</locationY>
        <inputAssignments>
            <field>Analyst_Risk_Rating__c</field>
            <value>
                <elementReference>$Record.Analyst_Rating__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Residual_Risk_Rating__c</field>
            <value>
                <elementReference>$Record.Residual_Rating__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Case__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update Analyst Risk Rating and Residual Risk Rating on related Case.</description>
        <name>Update_Records_with_RRR_and_ARR_with_Date_ARR</name>
        <label>Update Records with RRR and ARR with Date ARR</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <inputAssignments>
            <field>Analyst_Risk_Rating__c</field>
            <value>
                <elementReference>$Record.Analyst_Rating__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_Analyst_Risk_Rating_Provided__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Residual_Risk_Rating__c</field>
            <value>
                <elementReference>$Record.Residual_Rating__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Case__r</inputReference>
    </recordUpdates>
    <start>
        <locationX>749</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Analyst_Risk_Rating_or_Residual_Risk_rating</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Analyst_Rating__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Analyst_Rating__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Residual_Rating__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Residual_Rating__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>ECDD_form_Detail__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>varDateAnalystRiskRatingProvided</name>
        <dataType>Date</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
