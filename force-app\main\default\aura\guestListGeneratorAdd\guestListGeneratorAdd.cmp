<!--
  @description       : 
  <AUTHOR> <PERSON><PERSON> DXC
  @group             : 
  @last modified on  : 10-05-2021
  @last modified by  : <PERSON><PERSON>
-->
<aura:component implements="flexipage:availableForAllPageTypes" access="global">
  <aura:handler name="init" action="{!c.doInit}" value="{!this}"></aura:handler>
  <lightning:buttonGroup>
    <lightning:button label="Exclude" onclick="{!c.handleExclude}" />
    <lightning:button label="Add to Event" onclick="{!c.handleConnection}" />
  </lightning:buttonGroup>
  <aura:attribute
    name="isModalOpen"
    type="boolean"
    default="false"
  ></aura:attribute>
  <aura:if isTrue="{!v.isModalOpen}">
    <!-- Modal/Popup Box starts here-->
    <section
      role="dialog"
      tabindex="-1"
      aria-labelledby="modal-heading-01"
      aria-modal="true"
      aria-describedby="modal-content-id-1"
      class="slds-modal slds-fade-in-open slds-modal_medium"
    >
      <div class="slds-modal__container">
        <!-- Modal/Popup Box Header Starts here-->
        <header class="slds-modal__header">
          <h2
            id="modal-heading-01"
            class="slds-text-heading_medium slds-hyphenate"
          >
            Add Customer
          </h2>
        </header>
        <!--Modal/Popup Box Body Starts here-->
        <div
          class="slds-modal__content slds-p-around_medium"
          id="modal-content-id-1"
        >
          <lightning:flow
            aura:id="addCustomerFlow"
            onstatuschange="{!c.handleflowstatuschange}"
          ></lightning:flow>
        </div>
        <!--Modal/Popup Box Footer Starts here-->
        <footer class="slds-modal__footer">
          <lightning:button
            variant="neutral"
            label="Cancel"
            title="Cancel"
            onclick="{!c.handleModal}"
          />
        </footer>
      </div>
    </section>
    <!-- {!CampaignIdOutput} -->

    <div class="slds-backdrop slds-backdrop_open"></div>
  </aura:if>
  <c:guestList
    onselectedRow="{!c.handleModal}"
    aura:id="guestlist"
  ></c:guestList>
</aura:component>