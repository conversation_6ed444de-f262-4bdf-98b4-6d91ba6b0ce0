<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>56.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>varTaskRecordTypeId</name>
        <dataType>String</dataType>
        <value>
            <stringValue>0122y0000004HSpAAM</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Check_for_open_Tasks_and_Customer_Status</name>
        <label>Check for open Tasks and Customer Status</label>
        <locationX>578</locationX>
        <locationY>1295</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Active_Customer_No_Open_Offboarding_Tasks</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Open_Offboarding_Tasks.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Open_Offboarding_Task_1</targetReference>
            </connector>
            <label>Active Customer &amp; No Open Offboarding Tasks</label>
        </rules>
        <rules>
            <name>InActive_Customer_No_Open_Offboarding_Tasks</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Open_Offboarding_Tasks.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Open_Offboarding_Task_2</targetReference>
            </connector>
            <label>InActive Customer &amp; No Open Offboarding Tasks</label>
        </rules>
        <rules>
            <name>Open_Offboaring_Available_Active_Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Open_Offboarding_Tasks.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Exiting_Open_Offboarding_Task_to_Closure_1</targetReference>
            </connector>
            <label>Open Offboaring Available &amp; Active Customer</label>
        </rules>
        <rules>
            <name>Open_Offboaring_Available_InActive_Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Open_Offboarding_Tasks.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Account_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Exiting_Open_Offboarding_Task_to_Closure_2</targetReference>
            </connector>
            <label>Open Offboaring Available &amp; InActive Customer</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Owner_Changed</name>
        <label>Is Owner Changed?</label>
        <locationX>1304</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Owner_Changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>$Record__Prior.OwnerId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>IntegrationUserId_Metadata</targetReference>
            </connector>
            <label>Owner Changed?</label>
        </rules>
    </decisions>
    <decisions>
        <name>New_Owner_is_not_KafkaPremiumIntegration</name>
        <label>New Owner is not KafkaPremiumIntegration</label>
        <locationX>974</locationX>
        <locationY>815</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Owner_is_not_KafkaPremiumIntegration</name>
            <conditionLogic>(NOT(2) AND NOT (4) AND NOT(6) AND (1 OR 3 OR 5)) OR (NOT(1) AND NOT(3) AND NOT(5) AND NOT(2) AND NOT(4) AND NOT(6))</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationUserId_Metadata.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationUserId_Metadata.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>KafkaIntegrationUserId_Metadata.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>KafkaIntegrationUserId_Metadata.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>PremiumServiceUserId_Metadata.Value__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>PremiumServiceUserId_Metadata.Value__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Open_Offboarding_Tasks</targetReference>
            </connector>
            <label>Owner is not KafkaPremiumIntegration</label>
        </rules>
    </decisions>
    <description>Create Offboarding Task when Customer owner changed</description>
    <environments>Default</environments>
    <formulas>
        <name>newOwnerNOTKafkaPremiumIntgration</name>
        <dataType>Boolean</dataType>
        <expression>AND( 
NOT({!$Setup.Process_Automation_Control_Panel__c.Account_PB_Owner_Changed_Entry__c}),
{!$Record.OwnerId} &lt;&gt;{!IntegrationUserId_Metadata.Value__c},
{!$Record.OwnerId} &lt;&gt; {!KafkaIntegrationUserId_Metadata.Value__c},
{!$Record.OwnerId} &lt;&gt; {!PremiumServiceUserId_Metadata.Value__c}
)</expression>
    </formulas>
    <formulas>
        <name>TodayPlus14Formula</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate}+14</expression>
    </formulas>
    <formulas>
        <name>varMonthYearOffboardingTask</name>
        <dataType>String</dataType>
        <expression>CASE(MONTH(TODAY()), 
1, &quot;Jan&quot;,
2, &quot;Feb&quot;,
3, &quot;Mar&quot;,
4, &quot;Apr&quot;,
5, &quot;May&quot;,
6, &quot;Jun&quot;,
7, &quot;Jul&quot;,
8, &quot;Aug&quot;,
9, &quot;Sep&quot;,
10, &quot;Oct&quot;,
11, &quot;Nov&quot;,
12, &quot;Dec&quot;,
&quot;None&quot;
)
&amp;&apos; &apos; &amp; TEXT(YEAR(TODAY()))</expression>
    </formulas>
    <formulas>
        <name>varOffboardingTaskSubject</name>
        <dataType>String</dataType>
        <expression>&apos;Alert: Offboarding - &apos; &amp; {!varMonthYearOffboardingTask} &amp; &apos; - &apos; &amp; {!Get_Previous_Owner_Details.Name} &amp; &apos; to &apos; &amp; {!Get_Current_Owner_Details.Name}</expression>
    </formulas>
    <interviewLabel>Offboarding Task creation {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Offboarding Task creation</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Open_Offboarding_Task_1</name>
        <label>Create Open Offboarding Task 1</label>
        <locationX>50</locationX>
        <locationY>1415</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>TodayPlus14Formula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Contact customer to advise in change of managment</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record__Prior.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordTypeId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>varOffboardingTaskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Open_Offboarding_Task_2</name>
        <label>Create Open Offboarding Task 2</label>
        <locationX>314</locationX>
        <locationY>1415</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>TodayPlus14Formula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Contact customer to advise in change of managment</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record__Prior.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordTypeId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>varOffboardingTaskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Open_Offboarding_Task_3</name>
        <label>Create Open Offboarding Task 3</label>
        <locationX>578</locationX>
        <locationY>1535</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>TodayPlus14Formula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Contact customer to advise in change of managment</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record__Prior.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordTypeId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>varOffboardingTaskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Open_Offboarding_Task_4</name>
        <label>Create Open Offboarding Task 4</label>
        <locationX>842</locationX>
        <locationY>1535</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>TodayPlus14Formula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <stringValue>Contact customer to advise in change of managment</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record__Prior.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>Normal</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordTypeId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>varOffboardingTaskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Current_Owner_Details</name>
        <label>Get Current Owner Details</label>
        <locationX>578</locationX>
        <locationY>1175</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_for_open_Tasks_and_Customer_Status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Open_Offboarding_Tasks</name>
        <label>Get Open Offboarding Tasks</label>
        <locationX>578</locationX>
        <locationY>935</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Previous_Owner_Details</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Subject</field>
            <operator>Contains</operator>
            <value>
                <stringValue>Alert: Offboarding</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Previous_Owner_Details</name>
        <label>Get Previous Owner Details</label>
        <locationX>578</locationX>
        <locationY>1055</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Current_Owner_Details</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record__Prior.OwnerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>IntegrationUserId_Metadata</name>
        <label>IntegrationUserId Metadata</label>
        <locationX>974</locationX>
        <locationY>455</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>PremiumServiceUserId_Metadata</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>IntegrationUserId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>KafkaIntegrationUserId_Metadata</name>
        <label>KafkaIntegrationUserId Metadata</label>
        <locationX>974</locationX>
        <locationY>695</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>New_Owner_is_not_KafkaPremiumIntegration</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>kafkaIntegrationUserID</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>PremiumServiceUserId_Metadata</name>
        <label>PremiumServiceUserId Metadata</label>
        <locationX>974</locationX>
        <locationY>575</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>KafkaIntegrationUserId_Metadata</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterLabel</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>PremiumServiceUserID</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>General_Setting__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Exiting_Open_Offboarding_Task_to_Closure_1</name>
        <label>Update Exiting Open Offboarding Task to Closure 1</label>
        <locationX>578</locationX>
        <locationY>1415</locationY>
        <connector>
            <targetReference>Create_Open_Offboarding_Task_3</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Open_Offboarding_Tasks.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Exiting_Open_Offboarding_Task_to_Closure_2</name>
        <label>Update Exiting Open Offboarding Task to Closure 2</label>
        <locationX>842</locationX>
        <locationY>1415</locationY>
        <connector>
            <targetReference>Create_Open_Offboarding_Task_4</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Open_Offboarding_Tasks.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <start>
        <locationX>1178</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_Owner_Changed</targetReference>
        </connector>
        <object>Account</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>IntegrationUserId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Integration User ID</stringValue>
        </value>
    </variables>
    <variables>
        <name>kafkaIntegrationUserID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Kafka Integration userId</stringValue>
        </value>
    </variables>
    <variables>
        <name>PremiumServiceUserID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Premium Service User ID</stringValue>
        </value>
    </variables>
</Flow>
