<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns Last Interaction Date</description>
        <name>Assign_Last_Interaction_Date</name>
        <label>Assign Last Interaction Date</label>
        <locationX>506</locationX>
        <locationY>492</locationY>
        <assignmentItems>
            <assignToReference>Get_Customer_Record.Last_Interaction_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.ActivityDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Customer_Record.Last_Interaction_Date_Without_Bulk__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.ActivityDate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Customer_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Proactive Date and Last Interaction Date</description>
        <name>Assign_Proactive_Date</name>
        <label>Assign Proactive Date</label>
        <locationX>242</locationX>
        <locationY>492</locationY>
        <assignmentItems>
            <assignToReference>Get_Customer_Record.Proactive_Contact_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.ActivityDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Customer_Record.Last_Interaction_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.ActivityDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Customer_Record.Last_Interaction_Date_Without_Bulk__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.ActivityDate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Customer_Record</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>This is an outbound interaction by phone or any interaction</description>
        <name>Proactive_or_Any_Interaction</name>
        <label>Proactive or Any Interaction</label>
        <locationX>374</locationX>
        <locationY>384</locationY>
        <defaultConnector>
            <targetReference>Assign_Last_Interaction_Date</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Any Interaction</defaultConnectorLabel>
        <rules>
            <name>Proactive</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Channel__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Call</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Log_Method__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Omni</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Direction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Outbound</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Proactive_Date</targetReference>
            </connector>
            <label>Proactive</label>
        </rules>
    </decisions>
    <description>Flow to replace part of the TaskTriggerHandler to update the Proactive Contact Date and Last Interaction Dates for Interaction Task Logged as Omni</description>
    <environments>Default</environments>
    <interviewLabel>Task: Proactive Contact Date &amp; Last Interaction Dates {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Task: Proactive Contact Date &amp; Last Interaction Dates</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the Customer Account record</description>
        <name>Get_Customer_Record</name>
        <label>Get Customer Record</label>
        <locationX>374</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Proactive_or_Any_Interaction</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Proactive_Contact_Date__c</queriedFields>
        <queriedFields>Last_Interaction_Date__c</queriedFields>
        <queriedFields>Last_Interaction_Date_Without_Bulk__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates the Customer Record with new Assigned values</description>
        <name>Update_Customer_Record</name>
        <label>Update Customer Record</label>
        <locationX>374</locationX>
        <locationY>684</locationY>
        <inputReference>Get_Customer_Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterFormula>AND(
{!$Record.RecordType.DeveloperName} = &quot;Interaction&quot;,
NOT(ISBLANK({!$Record.WhatId})),
ISPICKVAL({!$Record.Log_Method__c}, &quot;Omni&quot;)
)</filterFormula>
        <object>Task</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Get_Customer_Record</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
