({
    //Method to call apex controller method to check sportsbet account number and convert lead.
    convertLead : function(component) {
        var action = component.get("c.checkAccNumberAndConvert");
        action.setParams({ leadId : component.get("v.recordId") });
        action.setCallback(this, function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                if(response.getReturnValue().isSuccess) {
                    component.set("v.responseData", response.getReturnValue().responseData);
                    component.set("v.successUI", true);
                } else {
                    component.set("v.errorMessage",response.getReturnValue().errorMessage);
                    component.set("v.showErrorMessage", true);
                }
            }
            else if (state === "ERROR") {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        component.set("v.errorMessage",response.getReturnValue().errorMessage);
                        component.set("v.showErrorMessage", true);
                    }
                } else {
                    component.set("v.errorMessage",response.getReturnValue().errorMessage);
                    component.set("v.showErrorMessage", true);
                }
            }
        });
        $A.enqueueAction(action);
    },
})