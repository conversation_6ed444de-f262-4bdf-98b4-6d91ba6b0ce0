<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Notify_Admin</name>
        <label>Notify Admin</label>
        <locationX>578</locationX>
        <locationY>458</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Error_FInding_Cases</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>AML Upload Document Screen Flow Infinite Case loop</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>AdminErrorEmail</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Notify_CM_Analyst</name>
        <label>Notify CM Analyst</label>
        <locationX>666</locationX>
        <locationY>2438</locationY>
        <actionName>Documentation__c.Notify_ECDD_Case_owner_when_document_is_uploaded_alert</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>Loop_Count</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>Loop_Selected_Documents.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Documentation__c.Notify_ECDD_Case_owner_when_document_is_uploaded_alert</nameSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_CaseId</name>
        <label>Assign CaseId</label>
        <locationX>270</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>CaseIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Cases.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Cases</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Will count the store the number of documents requested.</description>
        <name>Count_Document_Requests</name>
        <label>Count Document Requests</label>
        <locationX>182</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>Document_Requested_Count</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Documentation_Requests</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Requested_Documents_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Document_Count</name>
        <label>Document Count</label>
        <locationX>556</locationX>
        <locationY>1514</locationY>
        <assignmentItems>
            <assignToReference>DocumentCount</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Count_Uploaded_Files</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Document_count_to_0</name>
        <label>Document count to 0</label>
        <locationX>468</locationX>
        <locationY>1298</locationY>
        <assignmentItems>
            <assignToReference>DocumentCount</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Count_Uploaded_Files</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Count the number of documents that were uploaded.</description>
        <name>Loop_Count</name>
        <label>Loop Count</label>
        <locationX>666</locationX>
        <locationY>2546</locationY>
        <assignmentItems>
            <assignToReference>LoopCount</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Selected_Documents</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Document_Status</name>
        <label>Set Document Status</label>
        <locationX>666</locationX>
        <locationY>1814</locationY>
        <assignmentItems>
            <assignToReference>Loop_Selected_Documents.Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Uploaded</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Documentation_Status</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Are_all_documents_uploaded</name>
        <label>Are all documents uploaded?</label>
        <locationX>182</locationX>
        <locationY>2822</locationY>
        <defaultConnector>
            <targetReference>Final_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>YesUploaded</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>LoopCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Document_Requested_Count</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Document_Requested_Count</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Sub_status_to_Documents_under_review_with_CM</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Are_documents_uploaded</name>
        <label>Are documents uploaded?</label>
        <locationX>468</locationX>
        <locationY>1706</locationY>
        <defaultConnector>
            <targetReference>Set_Document_Status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>DocumentCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Screen_File_Upload</targetReference>
            </connector>
            <label>No</label>
        </rules>
    </decisions>
    <decisions>
        <name>Case_Status</name>
        <label>Case Status</label>
        <locationX>666</locationX>
        <locationY>2138</locationY>
        <defaultConnector>
            <targetReference>Update_Case_Sub_Status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Open</defaultConnectorLabel>
        <rules>
            <name>Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Case.IsClosed</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Re_Open_Case</targetReference>
            </connector>
            <label>Closed</label>
        </rules>
    </decisions>
    <description>Used on the AML Community for logged-in Users to upload requested Documents to an AML Case.</description>
    <environments>Default</environments>
    <formulas>
        <name>SetExpirationDate</name>
        <dataType>Date</dataType>
        <expression>TODAY()+1</expression>
    </formulas>
    <interviewLabel>Uplo {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Upload Document Screen Flow</label>
    <loops>
        <name>Count_Uploaded_Files</name>
        <label>Count Uploaded Files</label>
        <locationX>468</locationX>
        <locationY>1406</locationY>
        <collectionReference>FileUpload1.fileNames</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Document_Count</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Are_documents_uploaded</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Cases</name>
        <label>Loop Cases</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <collectionReference>Get_Customer_Cases</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_CaseId</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Documentation_Requests</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Selected_Documents</name>
        <label>Loop Selected Documents</label>
        <locationX>182</locationX>
        <locationY>1082</locationY>
        <collectionReference>DataTable_RequestedDocuments.selectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Screen_File_Upload</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Are_all_documents_uploaded</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Case</name>
        <label>Get Case</label>
        <locationX>666</locationX>
        <locationY>2030</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Case_Status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Loop_Selected_Documents.Case__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Customer_Cases</name>
        <label>Get Customer Cases</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Cases</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Notify_Admin</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Person_Account.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Documentation_Requests</name>
        <label>Get Documentation Requests</label>
        <locationX>182</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Count_Document_Requests</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending</stringValue>
            </value>
        </filters>
        <filters>
            <field>Case__c</field>
            <operator>In</operator>
            <value>
                <elementReference>CaseIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Documentation__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Person_Account</name>
        <label>Get Person Account</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Customer_Cases</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Notify_Admin</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsPersonAccount</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>PersonContactId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_User.ContactId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_User</name>
        <label>Get User</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Person_Account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Re_Open_Case</name>
        <label>Re-Open Case</label>
        <locationX>534</locationX>
        <locationY>2246</locationY>
        <connector>
            <targetReference>Notify_CM_Analyst</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Loop_Selected_Documents.Case__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Escalated</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Sub_Status__c</field>
            <value>
                <stringValue>Documents under review with CM</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <name>Sub_status_to_Documents_under_review_with_CM</name>
        <label>Sub-status to Documents under review with CM</label>
        <locationX>50</locationX>
        <locationY>2930</locationY>
        <connector>
            <targetReference>Deactivate_user_Final_screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Loop_Cases.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Sub_Status__c</field>
            <value>
                <stringValue>Documents under review with CM</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Case_Sub_Status</name>
        <label>Update Case Sub-Status</label>
        <locationX>798</locationX>
        <locationY>2246</locationY>
        <connector>
            <targetReference>Notify_CM_Analyst</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Loop_Selected_Documents.Case__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Sub_Status__c</field>
            <value>
                <stringValue>Documents under review with CM</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Documentation_Status</name>
        <label>Update Documentation Status</label>
        <locationX>666</locationX>
        <locationY>1922</locationY>
        <connector>
            <targetReference>Get_Case</targetReference>
        </connector>
        <inputReference>Loop_Selected_Documents</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <description>This screen will be shown when all documents have been uploaded.</description>
        <name>Deactivate_user_Final_screen</name>
        <label>Deactivate user Final screen</label>
        <locationX>50</locationX>
        <locationY>3038</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_1_of_Thank_you</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 22px; color: rgb(7, 140, 0);&quot;&gt;All requested documents have been uploaded!&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Thank you for your cooperation with the Sportsbet Customer Integrity Team.&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;We appreciate your assistance and will reach out to you if additional documents are required. &lt;/span&gt;&lt;span style=&quot;font-size: 14px; color: rgb(0, 0, 0);&quot;&gt;You may now close this window.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Upload More</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Error_FInding_Cases</name>
        <label>Error FInding Cases</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Error</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Send this error to System Admin&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(160, 0, 0);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Final_Screen</name>
        <label>Final Screen</label>
        <locationX>314</locationX>
        <locationY>2930</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Thank_you</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Thank you for your cooperation with the Sportsbet Customer Integrity Team.&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;We appreciate your assistance and will reach out to you if additional documents are required. You may now close this window or click Upload More to go back.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Upload More</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Requested_Documents_Screen</name>
        <label>Requested Documents Screen</label>
        <locationX>182</locationX>
        <locationY>974</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Loop_Selected_Documents</targetReference>
        </connector>
        <fields>
            <name>DisplayText1</name>
            <fieldText>&lt;p&gt;Please select the documents you would like to upload.  On the next screen, you can upload each chosen document.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>DataTable_RequestedDocuments</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Documentation__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Requested Documents</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Documentation_Requests</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-51ff&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Document Requested&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Document Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Status__c&quot;,&quot;guid&quot;:&quot;column-72b1&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;CreatedDate&quot;,&quot;guid&quot;:&quot;column-803e&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Date Requested&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Created Date&quot;,&quot;type&quot;:&quot;customDateTime&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Screen_File_Upload</name>
        <label>Screen File Upload</label>
        <locationX>468</locationX>
        <locationY>1190</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Document_count_to_0</targetReference>
        </connector>
        <fields>
            <name>DisplayText2</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: var(--slds-g-color-neutral-base-100, var(--lwc-colorBackgroundInput,rgb(255, 255, 255))); color: var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68))); font-size: 14px; font-family: var(--lwc-fontFamily,-apple-system, BlinkMacSystemFont, &apos;Segoe UI&apos;, Roboto, Helvetica, Arial, sans-serif, &apos;Apple Color Emoji&apos;, &apos;Segoe UI Emoji&apos;, &apos;Segoe UI Symbol&apos;);&quot;&gt;Please upload &lt;/span&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68); font-size: 14px;&quot;&gt;{!Loop_Selected_Documents.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>FileUpload1</name>
            <extensionName>forceContent:fileUpload</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Please select a file</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>multiple</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>Loop_Selected_Documents.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>accept</name>
                <value>
                    <stringValue>.doc,.docx,.xls,.xlsl,.pdf,.jpg,.jpeg,.png,.tiff</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>displayAllNames</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;Your uploaded files: &lt;/span&gt;&lt;strong style=&quot;font-size: 14px; background-color: rgb(255, 255, 255); color: rgb(0, 147, 82);&quot;&gt;{!FileUpload1.fileNames}&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>FileUpload1.fileNames</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Upload_Document</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(255, 0, 0);&quot;&gt;Please upload the documents before proceeding.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DocumentCount</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <numberValue>0.0</numberValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_User</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>AdminErrorEmail</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;Hi Sportsbet Admin,&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;An error occurred in the AML Upload Document Screen Flow.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Here are more details:&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Record Id:&lt;span style=&quot;color: rgb(173, 9, 9);&quot;&gt; &lt;/span&gt;&lt;strong style=&quot;color: rgb(173, 9, 9);&quot;&gt;{!$Flow.CurrentRecord}&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;Running User: &lt;strong style=&quot;color: rgb(183, 4, 4);&quot;&gt;{!$User.FirstName} {!$User.LastName}&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;This is the error message:&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(124, 0, 0);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Good Luck solving it!&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>CaseIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Document_Requested_Count</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>DocumentCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>DocumentsToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Documentation__c</objectType>
    </variables>
    <variables>
        <name>LoopCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
</Flow>
