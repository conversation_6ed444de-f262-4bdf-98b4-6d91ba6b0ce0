<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Check if Milestone was found in the above element.</description>
        <name>Milestone_Found</name>
        <label>Milestone Found?</label>
        <locationX>374</locationX>
        <locationY>384</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Case_MIlestone</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_SLA_Target_Date</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>Update SLA Target Date field on Case which is used in SLA Hours Left field.

Edit 24 Jan 2025
- Added Customer Risk Assessment Record Type in entry criteria.</description>
    <environments>Default</environments>
    <interviewLabel>AML SLA Target Date {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML SLA Target Date</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>This element retrieves the most recent, incomplete Case Milestone associated with the current Case.</description>
        <name>Get_Case_MIlestone</name>
        <label>Get Case MIlestone</label>
        <locationX>374</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Milestone_Found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCompleted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update SLA_Target_Date__c on case with SLA target date.</description>
        <name>Update_SLA_Target_Date</name>
        <label>Update SLA Target Date</label>
        <locationX>242</locationX>
        <locationY>492</locationY>
        <inputAssignments>
            <field>SLA_Target_Date__c</field>
            <value>
                <elementReference>Get_Case_MIlestone.TargetDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterFormula>OR(
    AND(
        {!$Record.RecordType.DeveloperName} = &quot;Standard_SMR&quot;,
        {!$Record.Submit_to_Austrac_check__c} = TRUE
    ),
    AND(
        {!$Record.RecordType.DeveloperName} = &quot;ECDD_Case&quot;,
        OR(
            AND(
                TEXT({!$Record.FastTrack_Required__c}) = &quot;No&quot;,
                TEXT({!$Record.Status}) = &quot;Retrieving ECDD data&quot;
            ),
            AND(
                TEXT({!$Record.FastTrack_Required__c}) = &quot;Yes&quot;,
                TEXT({!$Record.Status}) = &quot;New&quot;
            )
        )
    ),
    AND(
        OR(
            {!$Record.RecordType.DeveloperName} = &quot;Transaction_Monitoring&quot;,
            {!$Record.RecordType.DeveloperName} = &quot;Customer_Risk_Assessment&quot;,
            {!$Record.RecordType.DeveloperName} = &quot;Fast_Track_SMR&quot;
        ),
        TEXT({!$Record.Status}) = &quot;New&quot;
    )
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Get_Case_MIlestone</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>ChatterPost</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;This case has just been escalated to {!$Record.Priority}. Please action this case as soon as possible&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>Recipient</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Record.OwnerId</elementReference>
        </value>
    </variables>
    <variables>
        <name>recipients</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
