/**
 * @description       : 
 * <AUTHOR> <PERSON> (8Squad)
 * @group             : 
 * @last modified on  : 05-23-2025
 * @last modified by  : jam<PERSON><PERSON><PERSON>@gmail.com
**/
// Anonymous Apex to create test data for manual verification

// First, ensure we have the custom setting for generosity limit
Process_Automation_Control_Panel__c settings = Process_Automation_Control_Panel__c.getOrgDefaults();
if (settings.Id == null) {
    settings = new Process_Automation_Control_Panel__c(
        SetupOwnerId = UserInfo.getOrganizationId(), // Set this to org ID for org defaults
        Task_Batch_Customer_Generosity_Limit__c = 25.0
    );
    insert settings;
} else {
    settings.Task_Batch_Customer_Generosity_Limit__c = 25.0;
    update settings;
}

// Get Person Account RecordTypeId
Id personAccountRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId();

// Create test accounts
List<Account> testAccounts = new List<Account>();

// Account with low generosity
Account acc1 = new Account(
    FirstName = 'Test',
    LastName = 'LowGenerosity',
    RecordTypeId = personAccountRecordTypeId,
    Elite_Status__c = 'Elite',
    Portfolio__c = 'Protect',
    Generosity_TPV_Last_12_months__c = 15,
    Sports_Bet_Account_Number__c = 'TEST-GEN-001'
);
testAccounts.add(acc1);

// Account with Ladbrokes as main bookie
Account acc2 = new Account(
    FirstName = 'Test',
    LastName = 'MainBookieLadbrokes',
    RecordTypeId = personAccountRecordTypeId,
    Elite_Status__c = 'Elite',
    Portfolio__c = 'Grow',
    Main_Bookie__c = 'Ladbrokes',
    Sports_Bet_Account_Number__c = 'TEST-MB-001'
);
testAccounts.add(acc2);

// Account that bets with Ladbrokes
Account acc3 = new Account(
    FirstName = 'Test',
    LastName = 'BetsWithLadbrokes',
    RecordTypeId = personAccountRecordTypeId,
    Elite_Status__c = 'Elite',
    Portfolio__c = 'Protect',
    Bets_With_Competitor__c = 'Ladbrokes',
    Sports_Bet_Account_Number__c = 'TEST-COMP-001'
);
testAccounts.add(acc3);

// Account that meets all three conditions
Account acc4 = new Account(
    FirstName = 'Test',
    LastName = 'AllConditions',
    RecordTypeId = personAccountRecordTypeId,
    Elite_Status__c = 'Elite',
    Portfolio__c = 'Grow',
    Generosity_TPV_Last_12_months__c = 10,
    Main_Bookie__c = 'Ladbrokes',
    Bets_With_Competitor__c = 'Ladbrokes',
    Sports_Bet_Account_Number__c = 'TEST-ALL-001'
);
testAccounts.add(acc4);

// Insert the accounts
try {
    insert testAccounts;
    System.debug('Created ' + testAccounts.size() + ' test accounts');
    System.debug('Account IDs: ' + testAccounts);
} catch (Exception e) {
    System.debug('Error creating accounts: ' + e.getMessage());
    
    // If accounts already exist, try to find them
    List<Account> existingAccounts = [
        SELECT Id, LastName, Sports_Bet_Account_Number__c 
        FROM Account 
        WHERE Sports_Bet_Account_Number__c IN ('TEST-GEN-001', 'TEST-MB-001', 'TEST-COMP-001', 'TEST-ALL-001')
    ];
    
    if (!existingAccounts.isEmpty()) {
        System.debug('Found existing test accounts: ' + existingAccounts.size());
        for (Account acc : existingAccounts) {
            System.debug('Existing account: ' + acc.LastName + ' - ' + acc.Sports_Bet_Account_Number__c);
        }
    }
}


