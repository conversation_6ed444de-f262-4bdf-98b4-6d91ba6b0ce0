<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>AddTaskToCollection</name>
        <label>AddTaskToCollection</label>
        <locationX>996</locationX>
        <locationY>223</locationY>
        <assignmentItems>
            <assignToReference>varTaskCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>varTask</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>counter</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>LoopAllAccountIds</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignTaskValue</name>
        <label>AssignTaskValue</label>
        <locationX>670</locationX>
        <locationY>355</locationY>
        <assignmentItems>
            <assignToReference>varTask.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetRecordTypeID.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.WhatId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>indexId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Subject</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Direction__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Direction</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Comments</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Reason_for_Contact__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Reason_For_Contact</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Channel</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.TaskSubtype</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Call</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.ActivityDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Date_Time</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Log_Method__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Manual Bulk</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Temp_Date_Time__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Date_Time</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>AddTaskToCollection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignTaskValue_0</name>
        <label>AssignTaskValue</label>
        <locationX>674</locationX>
        <locationY>223</locationY>
        <assignmentItems>
            <assignToReference>varTask.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetRecordTypeID.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.WhoId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>indexId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Subject</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Subject</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Direction__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Direction</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Comments</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Reason_for_Contact__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Reason_For_Contact</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Channel</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.TaskSubtype</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Call</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.ActivityDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Date_Time</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Log_Method__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Manual Bulk</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varTask.Temp_Date_Time__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Screen1Date_Time</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>AddTaskToCollection</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Email</name>
        <choiceText>Email</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Email</stringValue>
        </value>
    </choices>
    <choices>
        <name>SMS</name>
        <choiceText>SMS</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>SMS</stringValue>
        </value>
    </choices>
    <decisions>
        <name>Check_Object_Type</name>
        <label>Check Object Type</label>
        <locationX>399</locationX>
        <locationY>263</locationY>
        <defaultConnector>
            <targetReference>LoopAllAccountIds</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Lead</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>indexId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>00Q</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignTaskValue_0</targetReference>
            </connector>
            <label>Is Lead</label>
        </rules>
        <rules>
            <name>Is_Account</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>indexId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>001</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignTaskValue</targetReference>
            </connector>
            <label>Is Account</label>
        </rules>
    </decisions>
    <dynamicChoiceSets>
        <name>channelChoice</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Channel__c</picklistField>
        <picklistObject>Task</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>directionChoice</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Direction__c</picklistField>
        <picklistObject>Task</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>ReasonforContactChoice</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Reason_for_Contact__c</picklistField>
        <picklistObject>Task</picklistObject>
    </dynamicChoiceSets>
    <interviewLabel>Bulk Create Interactions {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Bulk Create Interactions</label>
    <loops>
        <name>LoopAllAccountIds</name>
        <label>LoopAllAccountIds</label>
        <locationX>441</locationX>
        <locationY>50</locationY>
        <assignNextValueToReference>indexId</assignNextValueToReference>
        <collectionReference>ids</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_Object_Type</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Screen2</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Interactions</name>
        <label>Create Interactions</label>
        <locationX>1178</locationX>
        <locationY>36</locationY>
        <inputReference>varTaskCollection</inputReference>
    </recordCreates>
    <recordLookups>
        <name>GetRecordTypeID</name>
        <label>GetRecordTypeID</label>
        <locationX>149</locationX>
        <locationY>50</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Screen1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Interaction</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Screen1</name>
        <label>Bulk Create Interactions</label>
        <locationX>303</locationX>
        <locationY>51</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>LoopAllAccountIds</targetReference>
        </connector>
        <fields>
            <name>Screen1Subject</name>
            <dataType>String</dataType>
            <defaultValue>
                <stringValue>Interaction - </stringValue>
            </defaultValue>
            <fieldText>Subject</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Screen1Channel</name>
            <choiceReferences>Email</choiceReferences>
            <choiceReferences>SMS</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>Email</defaultSelectedChoiceReference>
            <fieldText>Channel</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Screen1Direction</name>
            <choiceReferences>directionChoice</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Direction</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Screen1Reason_For_Contact</name>
            <choiceReferences>ReasonforContactChoice</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Reason for Contact</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Screen1Comments</name>
            <fieldText>Comments</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Screen1Date_Time</name>
            <dataType>DateTime</dataType>
            <defaultValue>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </defaultValue>
            <fieldText>Date &amp; Time</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Screen2</name>
        <label>Screen2</label>
        <locationX>953</locationX>
        <locationY>36</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Create_Interactions</targetReference>
        </connector>
        <fields>
            <name>DisplayConfirmation</name>
            <fieldText>&lt;p&gt;Are you sure to perform this action to create &lt;b&gt;{!counter}&lt;/b&gt; interactions?&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>GetRecordTypeID</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>counter</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>indexId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varTask</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>varTaskCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
</Flow>
