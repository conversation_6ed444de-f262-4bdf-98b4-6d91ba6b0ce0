<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>DL_Record_Found</name>
        <label>DL Record Found</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>No_Related_Deposit_Limit</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Deposit_Limit_Related_to_Customer</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Terminated</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Deposit_Limit_Information</targetReference>
            </connector>
            <label>Yes and Status != Terminated</label>
        </rules>
        <rules>
            <name>Yes_but_Status_Terminated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Terminated</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Deposit_Limit_Related_to_Customer</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Terminated_Deposit_Limit_Information</targetReference>
            </connector>
            <label>Yes but Status = Terminated</label>
        </rules>
    </decisions>
    <description>Screen flow to display the Deposit Limit Information on the customer record
Added the new Deposit Limit Type Field</description>
    <environments>Default</environments>
    <interviewLabel>Customer Deposit Limit Display {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer Deposit Limit Display</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Deposit_Limit_Related_to_Customer</name>
        <label>Get Deposit Limit Related to Customer</label>
        <locationX>314</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>DL_Record_Found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Customer__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Deposit_Limit__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Deposit_Limit_Information</name>
        <label>Deposit Limit Information</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Deposit_Limit_Information_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Deposit_Limit_Information_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Column1</name>
                    <fieldText>&lt;p&gt;Deposit Limit Created Date&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Created_Date__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Status&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Status__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Value&lt;/p&gt;&lt;p&gt;${!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Value__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Utilisation&lt;/p&gt;&lt;p&gt;${!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Utilisation__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Anniversary&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Anniversary__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Frequency&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Frequency__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Type&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.Deposit_Limit_Type__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Deposit_Limit_Information_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Column2</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Deposit Limit SG Enforced&lt;/span&gt;&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_SG_Enforced__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Deposit Limit Enforcement Reason&lt;/span&gt;&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Enforcement_Reason__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Deposit Limit Change Date&lt;/span&gt;&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Change_Date__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Cooling off End Date&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;{!Get_Deposit_Limit_Related_to_Customer.Cooling_Off_End_Date__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;New Amount (After Cooling Off)&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;${!Get_Deposit_Limit_Related_to_Customer.API_New_DL_Amount__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;New Period (after Cooling off)&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.API_New_DL_Period__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>No_Related_Deposit_Limit</name>
        <label>No Related Deposit Limit</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>display1</name>
            <fieldText>&lt;p&gt;No related deposit limit&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Terminated_Deposit_Limit_Information</name>
        <label>Terminated Deposit Limit Information</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Terminated_Deposit_Limit_Information_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Terminated_Deposit_Limit_Information_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_Column1</name>
                    <fieldText>&lt;p&gt;Deposit Limit Created Date&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Status&lt;/p&gt;&lt;p&gt;{!Get_Deposit_Limit_Related_to_Customer.API_Deposit_Limit_Status__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Value&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Utilisation&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Anniversary&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Deposit Limit Frequency&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Terminated_Deposit_Limit_Information_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_Column2</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;Deposit Limit SG Enforced&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;Deposit Limit Enforcement Reason&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;Deposit Limit Change Date&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;Cooling off End Date&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;New Amount (After Cooling Off)&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;New Period (after Cooling off)&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Deposit_Limit_Related_to_Customer</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
