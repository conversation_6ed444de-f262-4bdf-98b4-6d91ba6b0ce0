<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Which fields were updated</description>
        <name>Which_field_changed</name>
        <label>Which field changed</label>
        <locationX>446</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Email_SMS</name>
            <conditionLogic>(1 AND 2) OR 3</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>formulaIsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Email_SMS_Opt_Out</targetReference>
            </connector>
            <label>Email &amp; SMS</label>
        </rules>
        <rules>
            <name>Email_Only</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Email_Opt_Out_Only</targetReference>
            </connector>
            <label>Email Only</label>
        </rules>
        <rules>
            <name>SMS_Only</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Communications_via_RM_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_SMS_Opt_Out_Only</targetReference>
            </connector>
            <label>SMS Only</label>
        </rules>
    </decisions>
    <description>Used to update the equivalent fields on Customer record when changes are made to fields on the Customer Consent record</description>
    <environments>Default</environments>
    <formulas>
        <name>formulaIsNew</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <interviewLabel>Customer Consent: Update Customer Record Opt Out Fields {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer Consent: Update Customer Record Opt Out Fields</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Updates the Customer record with the updated Customer Consent opt-out for Email only</description>
        <name>Update_Email_Opt_Out_Only</name>
        <label>Update Email Opt Out Only</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Communications_via_RM_Email__c</field>
            <value>
                <elementReference>$Record.Communications_via_RM_Email__c</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the Customer record with the updated Customer Consent opt-out for Email &amp; SMS</description>
        <name>Update_Email_SMS_Opt_Out</name>
        <label>Update Email &amp; SMS Opt Out</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Communications_via_RM_Email__c</field>
            <value>
                <elementReference>$Record.Communications_via_RM_Email__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Communications_via_RM_SMS__c</field>
            <value>
                <elementReference>$Record.Communications_via_RM_SMS__c</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <description>Updates the Customer record with the updated Customer Consent opt-out for SMS only</description>
        <name>Update_SMS_Opt_Out_Only</name>
        <label>Update SMS Opt Out Only</label>
        <locationX>578</locationX>
        <locationY>431</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Customer__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Communications_via_RM_SMS__c</field>
            <value>
                <elementReference>$Record.Communications_via_RM_SMS__c</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Which_field_changed</targetReference>
        </connector>
        <filterFormula>OR(
ISNEW(),
ISCHANGED({!$Record.Communications_via_RM_Email__c}),
ISCHANGED({!$Record.Communications_via_RM_SMS__c})
)</filterFormula>
        <object>Customer_Consent__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
