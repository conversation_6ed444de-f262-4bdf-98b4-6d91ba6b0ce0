<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>get_transacript</name>
        <label>get transacript</label>
        <locationX>176</locationX>
        <locationY>647</locationY>
        <actionName>getConversationTranscripts</actionName>
        <actionType>getConversationTranscripts</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>engagementRecordDetailsList</name>
            <value>
                <elementReference>MS</elementReference>
            </value>
        </inputParameters>
        <nameSegment>getConversationTranscripts</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assignment_To_List</name>
        <label>Assignment To List</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <assignmentItems>
            <assignToReference>messagingsessiondetails.recordId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>messagingsessiondetails.conversationIdentifier</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.ConversationId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>messagingsessiondetails.startDateTime</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.StartTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>messagingsessiondetails.endDateTime</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.EndTime</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>MS_Assignment</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assingLst</name>
        <label>assingLst</label>
        <locationX>176</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>lstMS</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <targetReference>get_transacript</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>MS_Assignment</name>
        <label>MS_Assignment</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <assignmentItems>
            <assignToReference>MS.engagementRecordDetails</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>messagingsessiondetails</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assingLst</targetReference>
        </connector>
    </assignments>
    <environments>Default</environments>
    <interviewLabel>test {!$Flow.CurrentDateTime}</interviewLabel>
    <label>test</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assignment_To_List</targetReference>
        </connector>
        <object>MessagingSession</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Draft</status>
    <variables>
        <name>lstMessagingSesssion</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>MessagingSession</objectType>
    </variables>
    <variables>
        <name>lstMS</name>
        <apexClass>DataRetrieval__EngagementRecordDetailsList</apexClass>
        <dataType>Apex</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>messagingsessiondetails</name>
        <apexClass>DataRetrieval__EngagementRecordDetails</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>MS</name>
        <apexClass>DataRetrieval__EngagementRecordDetailsList</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
