<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Account_Status_Changes_Last_12_Months__c</fullName>
    <description>This field is used to indicate the Total Number of times the Customer&apos;s Account changed Status in the Last 12 Months. It is derived field based on Information in OpenBet and populated via Integration.</description>
    <externalId>false</externalId>
    <inlineHelpText>The Total Number of times there was a change in Status of Customer&apos;s Account in the Last 12 Months (e.g. Active &gt; Suspended &gt; Active = 2). Refer to OpenBet for more detail.</inlineHelpText>
    <label>Account Status Changes Last 12 Months</label>
    <precision>18</precision>
    <required>false</required>
    <scale>0</scale>
    <trackTrending>false</trackTrending>
    <type>Number</type>
    <unique>false</unique>
</CustomField>
