<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Notify_CM_Queue</name>
        <label>Notify CM Queue</label>
        <locationX>1898</locationX>
        <locationY>2748</locationY>
        <actionName>Case.Notidy_CM_Queue_on_escalation_alert</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Notidy_CM_Queue_on_escalation_alert</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Notify_ECDD_analyst</name>
        <label>Notify ECDD analyst</label>
        <locationX>2910</locationX>
        <locationY>1932</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddressesArray</name>
            <value>
                <elementReference>RecepientAddressList</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>New LER case requires ECDD</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>EmailBody</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_receipient</name>
        <label>Assign receipient</label>
        <locationX>2910</locationX>
        <locationY>1824</locationY>
        <assignmentItems>
            <assignToReference>RecepientAddressList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>ECDDRecepient</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Notify_ECDD_analyst</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Checking Case Record Type</description>
        <name>Case_Type</name>
        <label>Case Type</label>
        <locationX>578</locationX>
        <locationY>492</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isECDD</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.FastTrack_Required__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Queue</targetReference>
            </connector>
            <label>IsECDD</label>
        </rules>
        <rules>
            <name>Is_QA_QC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>QA_QC</stringValue>
                </rightValue>
            </conditions>
            <label>Is QA/QC</label>
        </rules>
        <rules>
            <name>Is_SMR</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_if_status_changes</targetReference>
            </connector>
            <label>Is SMR</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check Triggering Case Record Types</description>
        <name>Case_Type0</name>
        <label>Case Type</label>
        <locationX>3020</locationX>
        <locationY>684</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>SMR0</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Standard_SMR</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fast_Track_SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_SMR_fields0</targetReference>
            </connector>
            <label>SMR</label>
        </rules>
        <rules>
            <name>ECDD</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Risk_Mitigation_Measures_contains_Re_review</targetReference>
            </connector>
            <label>ECDD</label>
        </rules>
        <rules>
            <name>NTM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non_Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_case_Origin</targetReference>
            </connector>
            <label>NTM</label>
        </rules>
        <rules>
            <name>TM_or_CRA</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer_Risk_Assessment</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>TM_or_CRA_Status_Check</targetReference>
            </connector>
            <label>TM or CRA</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Analyst_Risk_Rating</name>
        <label>Check Analyst Risk Rating</label>
        <locationX>2162</locationX>
        <locationY>1908</locationY>
        <defaultConnector>
            <targetReference>ECDD_Case_Status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Low_or_Medium</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Analyst_Risk_Rating__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>High</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Analyst_Risk_Rating__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Get_ECDD_Case_Milestones</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Low or Medium</label>
        </rules>
        <rules>
            <name>High</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Analyst_Risk_Rating__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>High</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_ECDD_Case_Milestones</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>High</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_case_Origin</name>
        <label>Check case Origin</label>
        <locationX>3306</locationX>
        <locationY>792</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>LER</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>LER</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_if_ifast_track_required</targetReference>
            </connector>
            <label>LER</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check CRA fields: Open_Source_Search_High_Risk_Occupation__c, Are_Commercial_Cards_Funds_Being_Used__c, Manual_Google_Search_for_Adverse_Media__c.
If any CRA fields are &quot;Yes&quot; then ECDD Required should be set to &quot;Yes&quot;</description>
        <name>Check_CRA_fields</name>
        <label>Check CRA fields</label>
        <locationX>4098</locationX>
        <locationY>1200</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>CRA_fields_Yes</name>
            <conditionLogic>(1 OR 2) AND 3</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Manual_Google_Search_for_Adverse_Media__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Open_Source_Search_High_Risk_Occupation__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer_Risk_Assessment</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_ECDD_Required_to_Yes</targetReference>
            </connector>
            <label>CRA fields - Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_any_active_ECDD_is_present</name>
        <label>Check if any active ECDD is present</label>
        <locationX>3042</locationX>
        <locationY>1716</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_active_ECDD_records_for_the_customer.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ECDD_Required_Picklist__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_in_progress</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ECDD_Required_Picklist__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_receipient</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_ifast_track_required</name>
        <label>Check if ifast-track required</label>
        <locationX>3042</locationX>
        <locationY>900</locationY>
        <defaultConnector>
            <targetReference>Check_if_Investigation_steps_are_complete_for_non_fasttrack</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Required</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.FastTrack_Required__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_if_Investigation_steps_are_complete_for_fasttrack</targetReference>
            </connector>
            <label>Required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Investigation_steps_are_complete_for_fasttrack</name>
        <label>Check if Investigation steps are complete for fasttrack</label>
        <locationX>2822</locationX>
        <locationY>1008</locationY>
        <defaultConnector>
            <targetReference>Get_ECDD_recordtype</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Complete</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Offence_listed__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Triage_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ECDD_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AML_Analyst_Comments_Long_Text__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.FastTrack_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.TF_or_Non_TF_related__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_LER_case_milsetone</targetReference>
            </connector>
            <label>Complete</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Investigation_steps_are_complete_for_non_fasttrack</name>
        <label>Check if Investigation steps are complete for non fasttrack</label>
        <locationX>3262</locationX>
        <locationY>1008</locationY>
        <defaultConnector>
            <targetReference>Get_ECDD_recordtype</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Complete0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Offence_listed__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Triage_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ECDD_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AML_Analyst_Comments_Long_Text__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.FastTrack_Required__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Get_LER_case_milsetone</targetReference>
            </connector>
            <label>Complete</label>
        </rules>
    </decisions>
    <decisions>
        <description>If SMR status changes to Closed</description>
        <name>Check_if_status_changes</name>
        <label>Check if status changes</label>
        <locationX>710</locationX>
        <locationY>600</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_SMR_fields</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Closed?</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check Case Sub-Status</description>
        <name>Check_if_the_sub_status_is_pending_with_crc1</name>
        <label>Check Sub-Status</label>
        <locationX>2162</locationX>
        <locationY>1392</locationY>
        <defaultConnector>
            <targetReference>Check_Analyst_Risk_Rating</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Pending_with_CRC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Sub_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending with Customer Risk Committee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Queue1</targetReference>
            </connector>
            <label>Pending with CRC</label>
        </rules>
        <rules>
            <name>Documents_under_review_with_CM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Sub_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Documents under review with CM</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Case_Milestones_for_Document_Request</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Documents under review with CM</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check if a Case is new or not</description>
        <name>Check_Status</name>
        <label>Check Status</label>
        <locationX>1799</locationX>
        <locationY>276</locationY>
        <defaultConnector>
            <targetReference>If_Customer_is_changed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>New</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Sportsbet_Account_Number_text_field</targetReference>
            </connector>
            <label>New</label>
        </rules>
    </decisions>
    <decisions>
        <description>IF ECDD Case is in Escalated Status</description>
        <name>ECDD_Case_Status</name>
        <label>ECDD Case Status</label>
        <locationX>2162</locationX>
        <locationY>2532</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Escalated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Escalated</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Escalated_Date_Time_field</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Escalated</label>
        </rules>
        <rules>
            <name>Closed1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Close_Case_Milestones2</targetReference>
            </connector>
            <label>Closed</label>
        </rules>
    </decisions>
    <decisions>
        <name>If_Customer_is_changed</name>
        <label>If Customer is changed</label>
        <locationX>3020</locationX>
        <locationY>384</locationY>
        <defaultConnector>
            <targetReference>Case_Type0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Update_Sportsbet_Account_Number</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.AccountId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Update_Sportsbet_Account_Number_text_field</targetReference>
            </connector>
            <label>Update Sportsbet Account Number</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check conditions for re-review: Risk Mitigation measures should contain Re-review, Date of Re-review should not be null and RecordType should be ECDD_Case.</description>
        <name>Is_ECDD_Re_review_required</name>
        <label>Is ECDD Re-review required?</label>
        <locationX>4934</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_Re_review_ECDD</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Risk_Mitigation_Measures__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Re-review</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Date_of_Re_review__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_ECDD_Unassigned_Queue</targetReference>
            </connector>
            <label>Yes, Re-review ECDD</label>
        </rules>
    </decisions>
    <decisions>
        <description>If Risk_Mitigation_Measures__c multi-select picklist contains Other then Contains_Other_checkbox__c should be true.
This is used to apply conditional visibility on Risk_Mitigation_Other__c field on ECDD Lightning page.</description>
        <name>Risk_Mitigation_Measures_contains_Other</name>
        <label>Risk Mitigation Measures contains Other</label>
        <locationX>2162</locationX>
        <locationY>1092</locationY>
        <defaultConnector>
            <targetReference>Set_Contains_Other_checkbox_to_False</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Contains_Other</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Risk_Mitigation_Measures__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Other</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Contains_Other_checkbox_to_True</targetReference>
            </connector>
            <label>Contains Other</label>
        </rules>
    </decisions>
    <decisions>
        <description>If Risk_Mitigation_Measures__c multi-select picklist contains Re-review then Contains_Re_review_checkbox__c should be true.
This is used to apply conditional visibility on Date_of_Re_review__c field on ECDD Lightning page.</description>
        <name>Risk_Mitigation_Measures_contains_Re_review</name>
        <label>Risk Mitigation Measures contains Re-review</label>
        <locationX>2162</locationX>
        <locationY>792</locationY>
        <defaultConnector>
            <targetReference>Set_Contains_Re_review_checkbox_to_False</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Contains_Re_review</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Risk_Mitigation_Measures__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Re-review</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Contains_Re_review_checkbox_to_True</targetReference>
            </connector>
            <label>Contains Re-review</label>
        </rules>
    </decisions>
    <decisions>
        <description>If SMR status is closed</description>
        <name>SMR_Status_Closed</name>
        <label>SMR Status Closed?</label>
        <locationX>1502</locationX>
        <locationY>900</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes_SMR_Closed</name>
            <conditionLogic>1 AND (2 OR 3)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Standard_SMR</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fast_Track_SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Update_Case_Initial_Completion_Date_Time</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Yes SMR Closed</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check the status of Transactional Monitoring or Customer Risk Assessment Case.</description>
        <name>TM_or_CRA_Status_Check</name>
        <label>TM or CRA Status Check</label>
        <locationX>4098</locationX>
        <locationY>792</locationY>
        <defaultConnector>
            <targetReference>Check_CRA_fields</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>TM_CRA_Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Case_Initial_Completion_Date_Time</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>TM/CRA Closed</label>
        </rules>
        <rules>
            <name>TM_CRA_In_Progress</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In Progress</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.TM_Alert_Review_Start_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_TM_Alert_Review_Start_Date</targetReference>
            </connector>
            <label>TM/CRA In Progress</label>
        </rules>
    </decisions>
    <description>This flow is primarily being used by Sportsbet AML.

This Case After-Save Flow automates various processes for Sportsbet AML cases, including ECDD, SMR, Transaction Monitoring, and Non-Transaction Monitoring. It is triggered when a Case is created or updated, performing key actions such as entitlement assignment, queue notifications, milestone tracking, and email alerts.

Key Functionalities:
	•	Entitlement Assignment: Determines the entitlement based on the Case Record Type.
	•	Queue Assignments: Routes Cases to CM, CRC, or ECDD Analyst queues based on status and sub-status changes.
	•	Milestone Tracking: Closes Case Milestones and updates SLA completion dates.
	•	Risk-Based Actions: Evaluates Analyst Risk Ratings and triggers corresponding processes.
	•	Case Ownership Updates: Assigns Cases to the appropriate queue when escalated or re-reviewed.
	•	Email Notifications: Alerts CM Queues and ECDD Analysts when critical changes occur.
	•	Re-review Processing: Creates child ECDD Cases for re-reviews when required.
	•	SMR Status Handling: Updates Case completion date when an SMR case is closed.
	•	Field Updates: Ensures correct values for SLA target dates, escalation timestamps, and customer account numbers.

Edit 24 Jan 2025
- Added logic in TM or CRA decision element to include new Record Type: Customer Risk Assessment.

Edit 19 Feb 2025
- Added logic for Risk Mitigation Other
- Added logic for Customer Risk Assessment Record type - ECDD Required Picklist should be Yes if any CRA fields are Yes.

Edit 7 April 2025
- Removed Are_Commercial_Cards_Funds_Being_Used__c in Check CRA fields decision element.</description>
    <environments>Default</environments>
    <formulas>
        <description>Date/Time Right Now</description>
        <name>DateTimeNow</name>
        <dataType>DateTime</dataType>
        <expression>NOW()</expression>
    </formulas>
    <formulas>
        <name>DateToday</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <name>ECDDRecepient</name>
        <dataType>String</dataType>
        <expression>{!Get_active_ECDD_records_for_the_customer.Owner:User.Email}</expression>
    </formulas>
    <formulas>
        <name>FTSMRStartDate</name>
        <dataType>DateTime</dataType>
        <expression>DATETIMEVALUE(TEXT(TODAY())+&quot;12:00AM&quot;)</expression>
    </formulas>
    <formulas>
        <name>GFSDocumentVariable</name>
        <dataType>String</dataType>
        <expression>&apos;GFS Summary&apos;+BR()+{!$Record.GFS_Summary__c}+BR()+&apos;-------------------------------------------------------&apos;+BR()+
&apos;Red Flags&apos;+BR()+{!$Record.Red_Flags__c}+BR()+&apos;-------------------------------------------------------&apos;+BR()+
&apos;Customer Profile&apos;+BR()+{!$Record.Customer_Profile__c}+BR()+&apos;-------------------------------------------------------&apos;+BR()+
&apos;Details of Suspicious matter Key details&apos;+BR()+{!$Record.DetailsofSuspiciousmatterKeydetails__c}+BR()+&apos;-------------------------------------------------------&apos;+BR()+
&apos;Explanation of what specifically raised&apos;+BR()+{!$Record.Explanation_of_what_specifically_raised__c}+BR()+&apos;-------------------------------------------------------&apos;+BR()+
&apos;Any relevant supporting documentation&apos;+BR()+{!$Record.Any_relevant_supporting_documentation__c}</expression>
    </formulas>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <interviewLabel>Case After save Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case After save Flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>Create ECDD Re-review Case as a child of triggering Case.</description>
        <name>Create_ECDD_Case_for_Re_review</name>
        <label>Create ECDD Case for Re-review</label>
        <locationX>4802</locationX>
        <locationY>600</locationY>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>texttemplateRereviewECDDDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FastTrack_Required__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <stringValue>Re-review</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_ECDD_Unassigned_Queue.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_ECDD_RecordType_Id.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <description>Create SMR from Data</description>
        <name>Create_SMR</name>
        <label>Create SMR</label>
        <locationX>50</locationX>
        <locationY>816</locationY>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>$Record.ContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FastTrack_Required__c</field>
            <value>
                <elementReference>$Record.FastTrack_Required__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <elementReference>$Record.Origin</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Queue.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Fasttrack_Recordtype.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TF_or_Non_TF_related__c</field>
            <value>
                <elementReference>$Record.TF_or_Non_TF_related__c</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Copy_1_of_Get_ECDD_Case_Milestones</name>
        <label>Get ECDD Case Milestones</label>
        <locationX>1898</locationX>
        <locationY>2016</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_1_of_Close_ECDD_Case_Milestones</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCompleted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_active_ECDD_records_for_the_customer</name>
        <label>Get active ECDD records for the customer</label>
        <locationX>3042</locationX>
        <locationY>1608</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_if_any_active_ECDD_is_present</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_ECDD_recordtype.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Document Request Case Milestone</description>
        <name>Get_Case_Milestones_for_Document_Request</name>
        <label>Get Case Milestones for Document Request</label>
        <locationX>2162</locationX>
        <locationY>1500</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Close_Request_Document_Case_Milestone</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCompleted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>TargetResponseInMins</field>
            <operator>EqualTo</operator>
            <value>
                <numberValue>10080.0</numberValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ECDD_Case_Milestones</name>
        <label>Get ECDD Case Milestones</label>
        <locationX>2162</locationX>
        <locationY>2016</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Close_ECDD_Case_Milestones</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCompleted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ECDD_recordtype</name>
        <label>Get ECDD recordtype</label>
        <locationX>3042</locationX>
        <locationY>1500</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_active_ECDD_records_for_the_customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get ECDD RecordType Id</description>
        <name>Get_ECDD_RecordType_Id</name>
        <label>Get ECDD RecordType Id</label>
        <locationX>4802</locationX>
        <locationY>492</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_ECDD_Case_for_Re_review</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the ECDD Unassigned Queue in order to be assigned as the owner of the Re-review ECDD case.</description>
        <name>Get_ECDD_Unassigned_Queue</name>
        <label>Get ECDD Unassigned Queue</label>
        <locationX>4802</locationX>
        <locationY>384</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_ECDD_RecordType_Id</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Unassigned_Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Fast Track SMR Recordtype</description>
        <name>Get_Fasttrack_Recordtype</name>
        <label>Get Fasttrack Recordtype</label>
        <locationX>50</locationX>
        <locationY>708</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_SMR</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Fast_Track_SMR</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_LER_case_milsetone</name>
        <label>Get LER case milsetone</label>
        <locationX>2690</locationX>
        <locationY>1116</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Close_LER_Case_Milestone</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>MilestoneTypeId</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the Mitigate High-Risk ECDD Case Milestone Target Date.</description>
        <name>Get_Mitigate_High_Risk_ECDD_Case_Milestone</name>
        <label>Get Mitigate High Risk ECDD Case Milestone</label>
        <locationX>2162</locationX>
        <locationY>2232</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_SLA_Target_Date_for_Mitigate_High_Risk_ECDD</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCompleted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>TargetResponseInMins</field>
            <operator>EqualTo</operator>
            <value>
                <numberValue>40320.0</numberValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get ECDD_Unassigned_Queue</description>
        <name>Get_Queue</name>
        <label>Get Queue</label>
        <locationX>50</locationX>
        <locationY>600</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Fasttrack_Recordtype</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Unassigned_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get CRC_Queue</description>
        <name>Get_Queue1</name>
        <label>Get Queue</label>
        <locationX>1898</locationX>
        <locationY>1500</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Case_Owner_to_CRC_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CRC_Queue</stringValue>
            </value>
        </filters>
        <object>Group</object>
        <outputAssignments>
            <assignToReference>CRCQueue</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordUpdates>
        <name>Close_ECDD_Case_Milestones</name>
        <label>Close ECDD Case Milestones</label>
        <locationX>2162</locationX>
        <locationY>2124</locationY>
        <connector>
            <targetReference>Get_Mitigate_High_Risk_ECDD_Case_Milestone</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_ECDD_Case_Milestones.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>CompletionDate</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>CompletionDate</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <object>CaseMilestone</object>
    </recordUpdates>
    <recordUpdates>
        <name>Close_LER_Case_Milestone</name>
        <label>Close LER Case Milestone</label>
        <locationX>2690</locationX>
        <locationY>1224</locationY>
        <connector>
            <targetReference>Get_ECDD_recordtype</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_LER_case_milsetone.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>CompletionDate</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <object>CaseMilestone</object>
    </recordUpdates>
    <recordUpdates>
        <description>Close Request Document Milestones when customer responds with documents.</description>
        <name>Close_Request_Document_Case_Milestone</name>
        <label>Close Request Document Case Milestone</label>
        <locationX>2162</locationX>
        <locationY>1608</locationY>
        <connector>
            <targetReference>Update_Request_Doc_SLA_Completed_Date</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCompleted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case_Milestones_for_Document_Request.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>CompletionDate</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>CompletionDate</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <object>CaseMilestone</object>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_1_of_Close_ECDD_Case_Milestones</name>
        <label>Close ECDD Case Milestones</label>
        <locationX>1898</locationX>
        <locationY>2124</locationY>
        <connector>
            <targetReference>Copy_1_of_Update_SLA_Completed_Date</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Copy_1_of_Get_ECDD_Case_Milestones.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>CompletionDate</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>CompletionDate</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <object>CaseMilestone</object>
    </recordUpdates>
    <recordUpdates>
        <description>Case Initial Completion Date/Time is used for reporting in Power BI
Field not visible on any page layouts.</description>
        <name>Copy_1_of_Update_Case_Initial_Completion_Date_Time</name>
        <label>Update Case Initial Completion Date/Time</label>
        <locationX>1370</locationX>
        <locationY>1008</locationY>
        <connector>
            <targetReference>Close_Case_Milestones</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Case_Initial_Completion_Date_Time__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Case_Initial_Completion_Date_Time__c</field>
            <value>
                <elementReference>DateTimeNow</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_1_of_Update_SLA_Completed_Date</name>
        <label>Update SLA Completed Date</label>
        <locationX>1898</locationX>
        <locationY>2232</locationY>
        <connector>
            <targetReference>ECDD_Case_Status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SLA_Completed_Date__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>SLA_Completed_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Assign the value from the SportsBet_Account_Number__c formula field on the Case to the ECDD_Sportsbet_Account_Number__c text field. This ensures agents can use Global Search to find cases by a specific SB account number, as formula fields like SportsBet_Account_Number__c are not searchable in Global Search.</description>
        <name>Copy_1_of_Update_Sportsbet_Account_Number_text_field</name>
        <label>Update Sportsbet Account Number text field</label>
        <locationX>2888</locationX>
        <locationY>492</locationY>
        <connector>
            <targetReference>Case_Type0</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>ECDD_SportsBet_Account_Number__c</field>
            <value>
                <elementReference>$Record.Account.Sports_Bet_Account_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update the Contains_Other_checkbox__c to FALSE.

If Risk_Mitigation_Measures__c multi-select picklist contains Other then Contains_Other_checkbox__c should be false.
This is used to apply conditional visibility on Risk_Mitigation_Other__c field on ECDD Lightning page.</description>
        <name>Set_Contains_Other_checkbox_to_False</name>
        <label>Set Contains Other checkbox to False</label>
        <locationX>2294</locationX>
        <locationY>1200</locationY>
        <connector>
            <targetReference>Check_if_the_sub_status_is_pending_with_crc1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Contains_Other_checkbox__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Contains_Other_checkbox__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update the Contains_Other_checkbox__c to TRUE.

If Risk_Mitigation_Measures__c multi-select picklist contains Other then Contains_Other_checkbox__c should be true.
This is used to apply conditional visibility on Risk_Mitigation_Other__c field on ECDD Lightning page.</description>
        <name>Set_Contains_Other_checkbox_to_True</name>
        <label>Set Contains Other checkbox to True</label>
        <locationX>2030</locationX>
        <locationY>1200</locationY>
        <connector>
            <targetReference>Check_if_the_sub_status_is_pending_with_crc1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Contains_Other_checkbox__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Contains_Other_checkbox__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update the Contains_Re_review_checkbox__c to FALSE.

If Risk_Mitigation_Measures__c multi-select picklist contains Re-review then Contains_Re_review_checkbox__c should be true.
This is used to apply conditional visibility on Date_of_Re_review__c field on ECDD Lightning page.</description>
        <name>Set_Contains_Re_review_checkbox_to_False</name>
        <label>Set Contains Re-review checkbox to False</label>
        <locationX>2294</locationX>
        <locationY>900</locationY>
        <connector>
            <targetReference>Risk_Mitigation_Measures_contains_Other</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Contains_Re_review_checkbox__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Contains_Re_review_checkbox__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update the Contains_Re_review_checkbox__c to TRUE.

If Risk_Mitigation_Measures__c multi-select picklist contains Re-review then Contains_Re_review_checkbox__c should be true.
This is used to apply conditional visibility on Date_of_Re_review__c field on ECDD Lightning page.</description>
        <name>Set_Contains_Re_review_checkbox_to_True</name>
        <label>Set Contains Re-review checkbox to True</label>
        <locationX>2030</locationX>
        <locationY>900</locationY>
        <connector>
            <targetReference>Risk_Mitigation_Measures_contains_Other</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Contains_Re_review_checkbox__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Contains_Re_review_checkbox__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Case Initial Completion Date/Time is used for reporting in Power BI
Field not visible on any page layouts.</description>
        <name>Update_Case_Initial_Completion_Date_Time</name>
        <label>Update Case Initial Completion Date/Time</label>
        <locationX>3834</locationX>
        <locationY>900</locationY>
        <connector>
            <targetReference>Close_Case_Milestones1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Case_Initial_Completion_Date_Time__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Case_Initial_Completion_Date_Time__c</field>
            <value>
                <elementReference>DateTimeNow</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>If sub-status is changed to &quot;Pending with Customer Risk Committee&quot; then change the Case Owner to CRC queue</description>
        <name>Update_Case_Owner_to_CRC_Queue</name>
        <label>Update Case Owner to CRC Queue</label>
        <locationX>1898</locationX>
        <locationY>1608</locationY>
        <connector>
            <targetReference>Check_Analyst_Risk_Rating</targetReference>
        </connector>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>CRCQueue</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update ECDD_Required_Picklist__c to Yes if ECDD_Required_Picklist__c != Yes.</description>
        <name>Update_ECDD_Required_to_Yes</name>
        <label>Update ECDD Required to Yes</label>
        <locationX>3966</locationX>
        <locationY>1308</locationY>
        <filterLogic>or</filterLogic>
        <filters>
            <field>ECDD_Required_Picklist__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>ECDD_Required_Picklist__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>This field is being used for Reporting.</description>
        <name>Update_Escalated_Date_Time_field</name>
        <label>Update Escalated Date/Time field</label>
        <locationX>1898</locationX>
        <locationY>2640</locationY>
        <connector>
            <targetReference>Notify_CM_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Escalation_Date_Time__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Escalation_Date_Time__c</field>
            <value>
                <elementReference>DateTimeNow</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update Request Doc SLA Completed Date__c when milestone is completed.</description>
        <name>Update_Request_Doc_SLA_Completed_Date</name>
        <label>Update Request Doc SLA Completed Date</label>
        <locationX>2162</locationX>
        <locationY>1716</locationY>
        <connector>
            <targetReference>Check_Analyst_Risk_Rating</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Request_Doc_SLA_Completed_Date__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Request_Doc_SLA_Completed_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update the target date of the Mitigate High Risk ECDD case milestone.</description>
        <name>Update_SLA_Target_Date_for_Mitigate_High_Risk_ECDD</name>
        <label>Update SLA Target Date for Mitigate High Risk ECDD</label>
        <locationX>2162</locationX>
        <locationY>2340</locationY>
        <connector>
            <targetReference>ECDD_Case_Status</targetReference>
        </connector>
        <inputAssignments>
            <field>SLA_Target_Date__c</field>
            <value>
                <elementReference>Get_Mitigate_High_Risk_ECDD_Case_Milestone.TargetDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update SMR_Completed_Date__c field</description>
        <name>Update_SMR_fields</name>
        <label>Update SMR fields</label>
        <locationX>578</locationX>
        <locationY>708</locationY>
        <inputAssignments>
            <field>SMR_Completed_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update GFS_Document__c field</description>
        <name>Update_SMR_fields0</name>
        <label>Update SMR fields</label>
        <locationX>1502</locationX>
        <locationY>792</locationY>
        <connector>
            <targetReference>SMR_Status_Closed</targetReference>
        </connector>
        <inputAssignments>
            <field>GFS_Document__c</field>
            <value>
                <elementReference>GFSDocumentVariable</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Assign the value from the SportsBet_Account_Number__c formula field on the Case to the ECDD_Sportsbet_Account_Number__c text field. This ensures agents can use Global Search to find cases by a specific SB account number, as formula fields like SportsBet_Account_Number__c are not searchable in Global Search.</description>
        <name>Update_Sportsbet_Account_Number_text_field</name>
        <label>Update Sportsbet Account Number text field</label>
        <locationX>578</locationX>
        <locationY>384</locationY>
        <connector>
            <targetReference>Case_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>ECDD_SportsBet_Account_Number__c</field>
            <value>
                <elementReference>$Record.Account.Sports_Bet_Account_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updte TM_Alert_Review_Start_Date__c with current date when a TM case status is updated to In Progress</description>
        <name>Update_TM_Alert_Review_Start_Date</name>
        <label>Update TM Alert Review Start Date</label>
        <locationX>4098</locationX>
        <locationY>900</locationY>
        <connector>
            <targetReference>Check_CRA_fields</targetReference>
        </connector>
        <inputAssignments>
            <field>TM_Alert_Review_Start_Date__c</field>
            <value>
                <elementReference>DateToday</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>3240</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Status</targetReference>
        </connector>
        <filterFormula>OR(
{!$Record.RecordType.DeveloperName} = &apos;ECDD_Case&apos;,
{!$Record.RecordType.DeveloperName} = &apos;Fast_Track_SMR&apos;,
{!$Record.RecordType.DeveloperName} = &apos;Standard_SMR&apos;,
{!$Record.RecordType.DeveloperName} = &apos;QA_QC&apos;,
{!$Record.RecordType.DeveloperName} = &apos;Transaction_Monitoring&apos;,
{!$Record.RecordType.DeveloperName} = &apos;Customer_Risk_Assessment&apos;,
{!$Record.RecordType.DeveloperName} = &apos;Non_Transaction_Monitoring&apos;
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <scheduledPaths>
            <name>Create_ECDD_Case_on_Date_of_Re_review</name>
            <connector>
                <targetReference>Is_ECDD_Re_review_required</targetReference>
            </connector>
            <label>Create ECDD Case on Date of Re-review</label>
            <offsetNumber>1</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <recordField>Date_of_Re_review__c</recordField>
            <timeSource>RecordField</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <description>Subflow to loop through case milestones on triggering Case and close all of them.</description>
        <name>Close_Case_Milestones</name>
        <label>Close Case Milestones</label>
        <locationX>1370</locationX>
        <locationY>1116</locationY>
        <flowName>AML_Subflow_Close_Case_Milestones</flowName>
        <inputAssignments>
            <name>caseId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <subflows>
        <description>Subflow to loop through case milestones on triggering Case and close all of them.</description>
        <name>Close_Case_Milestones1</name>
        <label>Close Case Milestones</label>
        <locationX>3834</locationX>
        <locationY>1008</locationY>
        <connector>
            <targetReference>Check_CRA_fields</targetReference>
        </connector>
        <flowName>AML_Subflow_Close_Case_Milestones</flowName>
        <inputAssignments>
            <name>caseId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <subflows>
        <description>Subflow to loop through case milestones on triggering Case and close all of them.</description>
        <name>Close_Case_Milestones2</name>
        <label>Close Case Milestones</label>
        <locationX>2162</locationX>
        <locationY>2640</locationY>
        <flowName>AML_Subflow_Close_Case_Milestones</flowName>
        <inputAssignments>
            <name>caseId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <textTemplates>
        <name>EmailBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>There has been a new LER case created-{!$Record.CaseNumber} for the ECDD customer- {!$Record.Customer_Name__c}, which requires ECDD and an existing active ECDD is present under your name. 
Please have a look at the new case have been raised.</text>
    </textTemplates>
    <textTemplates>
        <description>ECDD Case Description for Re-review Case.</description>
        <name>texttemplateRereviewECDDDescription</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Re-review from Case: {!$Record.CaseNumber}. Refer to this case for more information.</text>
    </textTemplates>
    <variables>
        <name>CaseMilestoneTypeId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CaseMilestoneTypeIDs</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>CRCQueue</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Recepient</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>RecepientAddressList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>varCase</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>varCaseCollection</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>varMilestoneTypeId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
