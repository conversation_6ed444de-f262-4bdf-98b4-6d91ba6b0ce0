<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>closureComment</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Customer has updated their mass contact channels – resub task closed as no action required</stringValue>
        </value>
    </constants>
    <constants>
        <name>IntegrationId</name>
        <dataType>String</dataType>
        <value>
            <stringValue>00528000004OfqPAAS</stringValue>
        </value>
    </constants>
    <constants>
        <name>PremiumServiceId</name>
        <dataType>String</dataType>
        <value>
            <stringValue>0052y000000He8UAAS</stringValue>
        </value>
    </constants>
    <constants>
        <name>taskDescription</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Contact customer with a service message to confirm their RM Contact Channel preferences. Update the relevant RM Opt In Email and/or SMS fields with the outcome of your interaction.   Ensure you log an interaction - Reason for Contact = Contact Preferences and ensure you update comments within the interaction.  FYI - Service Message: “So I can continue communicating with you, we’ll just need to make sure your marketing preferences are updated – are you happy for me to send you offers and other information over SMS or email to the details listed on your account?”</stringValue>
        </value>
    </constants>
    <constants>
        <name>taskSubject</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Resub Task - Contact customer to confirm their RM Contact Channels</stringValue>
        </value>
    </constants>
    <constants>
        <name>varEmail</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Email</stringValue>
        </value>
    </constants>
    <constants>
        <name>varEmailSMS</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Email &amp; SMS</stringValue>
        </value>
    </constants>
    <constants>
        <name>varSMS</name>
        <dataType>String</dataType>
        <value>
            <stringValue>SMS</stringValue>
        </value>
    </constants>
    <constants>
        <name>varTaskRecordId</name>
        <dataType>String</dataType>
        <value>
            <stringValue>0122y0000004HSpAAM</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Checks if RM Opt in SMS/Email field is updated to No and has any open Task</description>
        <name>Check_RM_Opt_in_and_Open_Tasks</name>
        <label>Check RM Opt in and Open Tasks</label>
        <locationX>3614</locationX>
        <locationY>1247</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RM_Opt_in_Email_or_SMS_is_No</name>
            <conditionLogic>(1 AND 2 AND 5) OR (1 AND 3 AND 4)</conditionLogic>
            <conditions>
                <leftValueReference>Get_RM_Resub_Open_Tasks.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_RM_Contact_Channel_Task</targetReference>
            </connector>
            <label>RM Opt in Email or SMS is No</label>
        </rules>
        <rules>
            <name>Auto_Close_RM_Resub_Task</name>
            <conditionLogic>(1 AND 2 AND 3 AND 4) OR (1 AND 5 AND 6 AND 7)</conditionLogic>
            <conditions>
                <leftValueReference>Get_RM_Resub_Open_Tasks.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_SMS__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_open_Task_to_Closure</targetReference>
            </connector>
            <label>Auto Close RM Resub Task</label>
        </rules>
    </decisions>
    <decisions>
        <description>checking if Unsub Status Mass Comms field or Mass Comms Contact Channel field is changed</description>
        <name>Is_Mass_Comms_Contact_Channel_Changed</name>
        <label>Is Mass Comms Contact Channel Changed?</label>
        <locationX>3614</locationX>
        <locationY>335</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_Email_has_SMS_Prior_has_Both_1</name>
            <conditionLogic>1 AND 2 AND 3 AND NOT(4) AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_Date</targetReference>
            </connector>
            <label>No Email has SMS Prior has Both 1</label>
        </rules>
        <rules>
            <name>No_SMS_has_Email_Prior_has_Both_2</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND NOT(5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_SMS_to_No_Only_Date_2</targetReference>
            </connector>
            <label>No SMS has Email Prior has Both 2</label>
        </rules>
        <rules>
            <name>No_Email_SMS_But_Priror_has_Both_3</name>
            <conditionLogic>1 AND 2 AND 3 AND NOT(4) AND NOT(5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_SMS_to_No_3</targetReference>
            </connector>
            <label>No Email SMS But Priror has Both 3</label>
        </rules>
        <rules>
            <name>Add_Email_No_SMS_Prior_has_Both_Blanks_4</name>
            <conditionLogic>1 AND NOT(2) AND NOT(3) AND 4 AND NOT(5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>update_RM_Opt_in_EMail_to_Yes_Only_4</targetReference>
            </connector>
            <label>Add Email No SMS Prior has Both Blanks 4</label>
        </rules>
        <rules>
            <name>Add_SMS_No_Email_Prior_has_Both_Blanks_5</name>
            <conditionLogic>1 AND NOT(2) AND NOT(3) AND NOT(4) AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_SMS_to_Yes_only_5</targetReference>
            </connector>
            <label>Add SMS No Email Prior has Both Blanks 5</label>
        </rules>
        <rules>
            <name>Add_Email_SMS_Prior_has_Both_Blanks_6</name>
            <conditionLogic>1 AND NOT(2) AND NOT(3) AND 4 AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RM_Opt_in_Email_SMS_to_Yes_6</targetReference>
            </connector>
            <label>Add Email SMS Prior has Both Blanks 6</label>
        </rules>
        <rules>
            <name>Remove_Email_Only_No_SMS_Prior_has_Email_Only_No_SMS_7</name>
            <conditionLogic>1 AND 2 AND NOT(3) AND NOT(4) and NOT(5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_to_NO_only_7</targetReference>
            </connector>
            <label>Remove Email Only No SMS Prior has Email Only No SMS 7</label>
        </rules>
        <rules>
            <name>Add_SMS_No_Email_Prior_has_Email_Only_No_SMS_8</name>
            <conditionLogic>1 AND 2 AND NOT(3) AND 4 AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_SMS_to_Yes_and_Date_only_8</targetReference>
            </connector>
            <label>Add SMS No Email Prior has Email Only No SMS 8</label>
        </rules>
        <rules>
            <name>Remove_SMS_No_Email_Prior_has_SMS_Only_No_Email_9</name>
            <conditionLogic>1 AND NOT(2) AND 3 AND NOT(4) AND NOT(5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_SMS_to_No_Only_9</targetReference>
            </connector>
            <label>Remove SMS No Email Prior has SMS Only No Email 9</label>
        </rules>
        <rules>
            <name>Add_Email_No_SMS_Prior_has_SMS_Only_No_Email_10</name>
            <conditionLogic>1 AND NOT(2) AND 3 AND 4 AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_opt_in_Email_to_Yes_Only_10</targetReference>
            </connector>
            <label>Add Email No SMS Prior has SMS Only No Email 10</label>
        </rules>
        <rules>
            <name>Remove_SMS_Add_Email_Prior_has_SMS_No_Email_11</name>
            <conditionLogic>1 AND NOT(2) AND 3 AND 4 AND NOT(5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_to_yes_SMS_to_No_11</targetReference>
            </connector>
            <label>Remove SMS Add Email Prior has SMS No Email 11</label>
        </rules>
        <rules>
            <name>Remove_Email_Add_SMS_Prior_has_Email_No_SMS_12</name>
            <conditionLogic>1 AND 2 AND NOT(3) AND NOT(4) AND 5</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_to_No_SMS_to_Yes_and_date_12</targetReference>
            </connector>
            <label>Remove Email Add SMS Prior has Email No SMS 12</label>
        </rules>
        <rules>
            <name>NO_Email_NO_SMS_Prior_has_Both_NO_13</name>
            <conditionLogic>1 AND NOT(2) AND NOT(3) AND NOT(4) AND NOT(5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Email_SMS_to_NO_13</targetReference>
            </connector>
            <label>NO Email NO SMS Prior has Both NO 13</label>
        </rules>
        <rules>
            <name>Has_Email_SMS_Prior_has_Both_14</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Email_SMS_to_Yes_14</targetReference>
            </connector>
            <label>Has Email SMS Prior has Both 14</label>
        </rules>
        <rules>
            <name>Has_Email_Prior_has_Email_15</name>
            <conditionLogic>1 AND 2 AND NOT(3) AND 4 AND NOT(5) AND 6</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Email_to_Yes_and_SMS_to_NO_15</targetReference>
            </connector>
            <label>Has Email Prior has Email 15</label>
        </rules>
        <rules>
            <name>Has_SMS_Prior_has_SMS_only_16</name>
            <conditionLogic>1 AND NOT(2) AND 3 AND NOT(4) AND 5 AND 6</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Email_to_No_SMS_to_Yes_16</targetReference>
            </connector>
            <label>Has SMS Prior has SMS only 16</label>
        </rules>
        <rules>
            <name>Has_Email_Only_New_Record_17</name>
            <conditionLogic>1 AND 2 AND NOT(3)</conditionLogic>
            <conditions>
                <leftValueReference>IsNewRecord</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_yes_17</targetReference>
            </connector>
            <label>Has Email Only New Record 17</label>
        </rules>
        <rules>
            <name>Has_SMS_Only_New_Record_18</name>
            <conditionLogic>1 AND NOT(2) AND 3</conditionLogic>
            <conditions>
                <leftValueReference>IsNewRecord</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_SMS_yes_Only_18</targetReference>
            </connector>
            <label>Has SMS Only New Record 18</label>
        </rules>
        <rules>
            <name>Has_Email_SMS_New_Record_19</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNewRecord</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_SMS_Yes_19</targetReference>
            </connector>
            <label>Has Email SMS New Record 19</label>
        </rules>
        <rules>
            <name>No_Email_SMS_New_Record_20</name>
            <conditionLogic>1 AND NOT(2) AND NOT(3)</conditionLogic>
            <conditions>
                <leftValueReference>IsNewRecord</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Email_SMS_to_NO_20</targetReference>
            </connector>
            <label>No Email SMS New Record 20</label>
        </rules>
        <rules>
            <name>Owner_Change_Has_Email_Only_21</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND NOT(8)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>PremiumServiceId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_to_yes_only_21</targetReference>
            </connector>
            <label>Owner Change Has Email Only 21</label>
        </rules>
        <rules>
            <name>Owner_Change_Has_SMS_only_22</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND NOT(7) AND 8</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>PremiumServiceId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_SMS_to_No_22</targetReference>
            </connector>
            <label>Owner Change Has SMS only 22</label>
        </rules>
        <rules>
            <name>Owner_Change_Has_Email_SMS_23</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>PremiumServiceId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_SMS_to_Yes_23</targetReference>
            </connector>
            <label>Owner Change Has Email SMS 23</label>
        </rules>
        <rules>
            <name>Owner_Change_No_Email_SMS_24</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND NOT(7) AND NOT(8)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>IntegrationId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>PremiumServiceId</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Preferred_Method_Of_Contact__c</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>varSMS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_SMS_to_NO_24</targetReference>
            </connector>
            <label>Owner Change No Email SMS 24</label>
        </rules>
        <rules>
            <name>RM_Opt_In_Email_Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email</targetReference>
            </connector>
            <label>RM Opt In Email Updated</label>
        </rules>
        <rules>
            <name>RM_Opt_In_SMS_Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_SMS_Date_field</targetReference>
            </connector>
            <label>RM Opt In SMS Updated</label>
        </rules>
        <rules>
            <name>RM_Opt_In_Email_SMS_Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_SMS__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RM_Opt_in_Email_SMS_Date_filed</targetReference>
            </connector>
            <label>RM Opt In Email &amp; SMS Updated</label>
        </rules>
    </decisions>
    <decisions>
        <name>RM_Opt_In_Email</name>
        <label>RM Opt In Email</label>
        <locationX>3614</locationX>
        <locationY>671</locationY>
        <defaultConnector>
            <targetReference>Yes</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>RM Opt In Email = Yes</defaultConnectorLabel>
        <rules>
            <name>RM_Opt_In_Email_No</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RM_Opt_In_Email__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>No</targetReference>
            </connector>
            <label>RM Opt In Email = No</label>
        </rules>
    </decisions>
    <description>This Flow updates RM Opt in Email, RM Opt in SMS, and corresponding Date fields</description>
    <environments>Default</environments>
    <formulas>
        <name>IsNewRecord</name>
        <dataType>Boolean</dataType>
        <expression>IsNew()</expression>
    </formulas>
    <formulas>
        <name>taskActivityDate</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate} + 7</expression>
    </formulas>
    <interviewLabel>RM Opt in Email SMS and Date fields update comparing Prior Values {!$Flow.CurrentDateTime}</interviewLabel>
    <label>RM Opt in Email SMS and Date fields update comparing Prior Values</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>Create Resub Task - RM Contact Channel Task and assign to Customer Owner</description>
        <name>Create_RM_Contact_Channel_Task</name>
        <label>Create RM Contact Channel Task</label>
        <locationX>3350</locationX>
        <locationY>1367</locationY>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>taskActivityDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>taskDescription</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Current_Customer_Details.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>varTaskRecordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>taskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>Current_Customer_Details.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <description>Get Current Customer Details</description>
        <name>Current_Customer_Details</name>
        <label>Current Customer Details</label>
        <locationX>3614</locationX>
        <locationY>1007</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_RM_Resub_Open_Tasks</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get RM Resub Open tasks</description>
        <name>Get_RM_Resub_Open_Tasks</name>
        <label>Get RM Resub Open Tasks</label>
        <locationX>3614</locationX>
        <locationY>1127</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_RM_Opt_in_and_Open_Tasks</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varTaskRecordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Subject</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>taskSubject</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Current_Customer_Details.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Email_SMS_to_NO_13</name>
        <label>Email SMS to NO 13</label>
        <locationX>3218</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Email_SMS_to_Yes_14</name>
        <label>Email SMS to Yes 14</label>
        <locationX>3482</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Email_to_No_SMS_to_Yes_16</name>
        <label>Email to No SMS to Yes 16</label>
        <locationX>4010</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Email_to_Yes_and_SMS_to_NO_15</name>
        <label>Email to Yes and SMS to NO 15</label>
        <locationX>3746</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>No</name>
        <label>No</label>
        <locationX>3482</locationX>
        <locationY>791</locationY>
        <connector>
            <targetReference>Current_Customer_Details</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in Email, SMS to Yes and corresponding date field with todays date</description>
        <name>RM_Opt_in_Email_SMS_to_Yes_6</name>
        <label>RM Opt in Email SMS to Yes 6</label>
        <locationX>1370</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in EMail, SMS to No</description>
        <name>Update_Email_SMS_to_NO_20</name>
        <label>Update Email SMS to NO 20</label>
        <locationX>5066</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Auto close the open task to closure</description>
        <name>Update_open_Task_to_Closure</name>
        <label>Update open Task to Closure</label>
        <locationX>3614</locationX>
        <locationY>1367</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_RM_Resub_Open_Tasks.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>closureComment</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Closed by System</stringValue>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in EMail field when RM Opt in Email field is updated</description>
        <name>Update_RM_Opt_in_Email</name>
        <label>Update RM Opt in Email</label>
        <locationX>6386</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates RM Opt in Email to No and Date to Today</description>
        <name>Update_RM_Opt_in_Email_Date</name>
        <label>Update RM Opt in Email &amp; Date</label>
        <locationX>50</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update both RM Opt in Email Date and RM Opt in SMS Date fields when both are changed</description>
        <name>Update_RM_Opt_in_Email_SMS_Date_filed</name>
        <label>Update RM Opt in Email SMS Date filed</label>
        <locationX>6914</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_Opt_in_Email_SMS_to_NO_24</name>
        <label>Update RM Opt in Email SMS to NO 24</label>
        <locationX>6122</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in Email and SMS to No and RM opt in Email and SMS date to today</description>
        <name>Update_RM_Opt_in_Email_SMS_to_No_3</name>
        <label>Update RM Opt in Email &amp; SMS to No 3</label>
        <locationX>578</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_Opt_in_Email_SMS_to_Yes_23</name>
        <label>Update RM Opt in Email SMS to Yes 23</label>
        <locationX>5858</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_Opt_in_Email_SMS_Yes_19</name>
        <label>Update RM Opt in Email SMS Yes 19</label>
        <locationX>4802</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in Email  to No and Date to today</description>
        <name>Update_RM_Opt_in_Email_to_NO_only_7</name>
        <label>Update RM Opt in Email to NO only 7</label>
        <locationX>1634</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in Email to No and RM Opt in SMS to Yes and date fields</description>
        <name>Update_RM_Opt_in_Email_to_No_SMS_to_Yes_and_date_12</name>
        <label>Update RM Opt in Email to No SMS to Yes and date 12</label>
        <locationX>2954</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in EMail to Yes and Date to todays date</description>
        <name>Update_RM_opt_in_Email_to_Yes_Only_10</name>
        <label>Update RM opt in Email to Yes Only 10</label>
        <locationX>2426</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_Opt_in_Email_to_yes_only_21</name>
        <label>Update RM Opt in Email to yes only 21</label>
        <locationX>5330</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>updates RM Opt in Email to Yes only</description>
        <name>update_RM_Opt_in_EMail_to_Yes_Only_4</name>
        <label>update RM Opt in EMail to Yes Only 4</label>
        <locationX>842</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in Email to Yes and RM Opt in SMS to No and date fields</description>
        <name>Update_RM_Opt_in_Email_to_yes_SMS_to_No_11</name>
        <label>Update RM Opt in Email to yes SMS to No 11</label>
        <locationX>2690</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_Opt_in_Email_yes_17</name>
        <label>Update RM Opt in Email yes 17</label>
        <locationX>4274</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>RM Opt in SMS date field is set to today when RM Opt in SMS field is changed</description>
        <name>Update_RM_Opt_in_SMS_Date_field</name>
        <label>Update RM Opt in SMS Date field</label>
        <locationX>6650</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_Opt_in_SMS_to_No_22</name>
        <label>Update RM Opt in SMS to Yes 22</label>
        <locationX>5594</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Updates RM Opt in SMS to No and date to Today only</description>
        <name>Update_RM_Opt_in_SMS_to_No_Only_9</name>
        <label>Update RM Opt in SMS to No Only 9</label>
        <locationX>2162</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in SMS to No and Date to today</description>
        <name>Update_RM_Opt_in_SMS_to_No_Only_Date_2</name>
        <label>Update RM Opt in SMS to No Only &amp; Date 2</label>
        <locationX>314</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>TodaysDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update RM Opt in SMS to Yes and Date to today&apos;s date</description>
        <name>Update_RM_Opt_in_SMS_to_Yes_and_Date_only_8</name>
        <label>Update RM Opt in SMS to Yes and Date only 8</label>
        <locationX>1898</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>RM opt in SMS update to yes and date to Today</description>
        <name>Update_RM_Opt_in_SMS_to_Yes_only_5</name>
        <label>Update RM Opt in SMS to Yes only 5</label>
        <locationX>1106</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RM_Opt_in_SMS_yes_Only_18</name>
        <label>Update RM Opt in SMS yes Only 18</label>
        <locationX>4538</locationX>
        <locationY>455</locationY>
        <connector>
            <targetReference>RM_Opt_In_Email</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_Email__c</field>
            <value>
                <stringValue>No</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RM_Opt_In_SMS__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Yes</name>
        <label>Yes</label>
        <locationX>3746</locationX>
        <locationY>791</locationY>
        <connector>
            <targetReference>Current_Customer_Details</targetReference>
        </connector>
        <inputAssignments>
            <field>PersonHasOptedOutOfEmail</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>3488</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_Mass_Comms_Contact_Channel_Changed</targetReference>
        </connector>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
    <variables>
        <name>TodaysDate</name>
        <dataType>Date</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Flow.CurrentDate</elementReference>
        </value>
    </variables>
    <variables>
        <description>stores the Open Task id</description>
        <name>varOpenTaskId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
