<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>When a Interaction task that is inbound and log method Omni is created it updates Migrated to Omni field on customer record = TRUE</description>
    <environments>Default</environments>
    <interviewLabel>Task: After Save - Update Customer Migrated to Omni {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Task: After Save - Update Customer Migrated to Omni</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Updates Migrated to Omni = TRUE</description>
        <name>Update_Customer_Record</name>
        <label>Update Customer Record</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Migrated_to_Omni_Date__c</field>
            <value>
                <elementReference>$Record.CreatedDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Migrated_to_Omni__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Customer_Record</targetReference>
        </connector>
        <filterFormula>AND(
{!$Record.RecordType.Name} = &apos;Interaction&apos;,
ISPICKVAL({!$Record.Direction__c}, &apos;Inbound&apos;),
ISPICKVAL({!$Record.Log_Method__c}, &apos;Omni&apos;),
NOT({!$Record.Account.Migrated_to_Omni__c})
)</filterFormula>
        <object>Task</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
