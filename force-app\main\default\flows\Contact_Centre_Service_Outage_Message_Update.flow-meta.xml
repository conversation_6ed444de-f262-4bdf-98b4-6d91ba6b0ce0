<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Found_an_existing_Outage_message</name>
        <label>Found an existing Outage message?</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Message_Screen_to_Create</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No current outage message</defaultConnectorLabel>
        <rules>
            <name>Existing_outage_message</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Active_Service_Outage_Message.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Message_Screen</targetReference>
            </connector>
            <label>Existing outage message</label>
        </rules>
    </decisions>
    <description>Used to change the Service Outage Message that is referenced in the Amazon Connect contact flow</description>
    <environments>Default</environments>
    <interviewLabel>Contact Centre Service Outage Message Update {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Contact Centre Service Outage Message Update</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Service_Outage_Message</name>
        <label>Create Service Outage Message</label>
        <locationX>578</locationX>
        <locationY>458</locationY>
        <connector>
            <targetReference>Message_Create_Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Roll_Back_Record</targetReference>
        </faultConnector>
        <inputReference>NewServiceOutageMessage</inputReference>
    </recordCreates>
    <recordLookups>
        <description>Gets the Service Outage Message that is active</description>
        <name>Get_Active_Service_Outage_Message</name>
        <label>Get Active Service Outage Message</label>
        <locationX>314</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Found_an_existing_Outage_message</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Active__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Message_End_Date__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Service_Outage_Message__c</object>
        <sortField>Message_End_Date__c</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordRollbacks>
        <description>Rolls back any changes when there is a fault</description>
        <name>Roll_Back_Record</name>
        <label>Roll Back Record</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <connector>
            <targetReference>Flow_Error_Screen</targetReference>
        </connector>
    </recordRollbacks>
    <recordUpdates>
        <description>Updates the Service Outage Message</description>
        <name>Update_Service_Outage_Message</name>
        <label>Update Service Outage Message</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <connector>
            <targetReference>Message_Update_Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Roll_Back_Record</targetReference>
        </faultConnector>
        <inputReference>Get_Active_Service_Outage_Message</inputReference>
    </recordUpdates>
    <screens>
        <description>Screen used to display error message to user when a fault occurs</description>
        <name>Flow_Error_Screen</name>
        <label>Flow Error Screen</label>
        <locationX>314</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FlowErrorSLDS</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Flow Error Occurred&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>FlowErrorMessage</name>
            <fieldText>&lt;p&gt;An error occurred during this screen flow, please try again or send the below details to your Salesforce admin&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Flow Error Message: {!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Flow Start Time: {!$Flow.InterviewStartTime}&lt;/p&gt;&lt;p&gt;Flow ID: {!$Flow.InterviewGuid}&lt;/p&gt;&lt;p&gt;Flow User: {!$User.Username}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Displays success message to the user</description>
        <name>Message_Create_Success_Screen</name>
        <label>Message Create Success Screen</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Message_Create_Success_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Message_Create_Success_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Message_Create_Success_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_displaySuccessMessageSLDS</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;Success&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Copy_1_of_displaySuccessMessage</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;img src=&quot;https://sb-premium--pstspring2.sandbox.file.force.com/sfc/servlet.shepherd/version/download/0688s0000081nF2?asPdf=false&amp;amp;operationContext=CHATTER&quot; alt=&quot;consent-final.jpeg&quot;&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;Service Outage Message &lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;Successfully Created&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Message_Create_Success_Screen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Used to create or update Service Outage Message</description>
        <name>Message_Screen</name>
        <label>Message Screen</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Update_Service_Outage_Message</targetReference>
        </connector>
        <fields>
            <name>Message_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Message_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Message_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>displayCurrentMessage</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;Service Outage Message for Customers during Peak hours or Emergency period&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>Get_Active_Service_Outage_Message.Message__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Message_Screen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>Get_Active_Service_Outage_Message.Message_Start_Date__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>Get_Active_Service_Outage_Message.Message_End_Date__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <nextOrFinishButtonLabel>Update Message</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Used to create or update Service Outage Message</description>
        <name>Message_Screen_to_Create</name>
        <label>Message Screen to Create</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Create_Service_Outage_Message</targetReference>
        </connector>
        <fields>
            <name>Message_Screen_to_Create_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Message_Screen_to_Create_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>2</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Message_Screen_to_Create_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>CreateNewMessage</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;Create New Service Outage Message for Customers during Peak hours or Emergency period&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>NewServiceOutageMessage.Message__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>7</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Message_Screen_to_Create_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>NewServiceOutageMessage.Message_Start_Date__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>NewServiceOutageMessage.Message_End_Date__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <nextOrFinishButtonLabel>Create Message</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Displays success message to the user</description>
        <name>Message_Update_Success_Screen</name>
        <label>Message Update Success Screen</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Message_Update_Success_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Message_Update_Success_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Message_Update_Success_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>displaySuccessMessageSLDS</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;Success&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>displaySuccessMessage</name>
                    <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;img src=&quot;https://sb-premium--pstspring2.sandbox.file.force.com/sfc/servlet.shepherd/version/download/0688s0000081nF2?asPdf=false&amp;amp;operationContext=CHATTER&quot; alt=&quot;consent-final.jpeg&quot;&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;Service Outage Message Updated&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Message_Update_Success_Screen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Active_Service_Outage_Message</targetReference>
        </connector>
    </start>
    <status>Draft</status>
    <variables>
        <name>NewServiceOutageMessage</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Service_Outage_Message__c</objectType>
    </variables>
</Flow>
