<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_pmetdec</targetReference>
            </connector>
            <label>Attendee is Created</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_1_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>100</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_1_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_1_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_1_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1_pmetrule</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <description>This Process Builder is used to populate Event Check-In Information</description>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>&quot;Number of Seats: &quot; +&quot; &quot;+ TEXT([Attendee__c].Number_Of_Seats__c)+&quot;, &quot;+
&quot;Seat/Table Number: &quot;+&quot; &quot;+TEXT([Attendee__c].Seat_Table_Number__c)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_2_myRule_1_A1_6277097925</name>
        <dataType>String</dataType>
        <expression>&quot;Number of Seats: &quot; +&quot; &quot;+ TEXT({!myVariable_current.Number_Of_Seats__c})+&quot;, &quot;+
&quot;Seat/Table Number: &quot;+&quot; &quot;+TEXT({!myVariable_current.Seat_Table_Number__c})</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!ISBLANK([Attendee__c].Account__c )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1</name>
        <dataType>Boolean</dataType>
        <expression>!ISBLANK({!myVariable_current.Account__c} )</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!ISBLANK([Attendee__c].Account__c )</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1_pmetrule</name>
        <dataType>Boolean</dataType>
        <expression>!ISBLANK({!myVariable_old.Account__c} )</expression>
    </formulas>
    <interviewLabel>Attendee_Process_Populate_Check_In_Information-2_InterviewLabel</interviewLabel>
    <label>Attendee Process - Populate Check-In Information</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Attendee__c</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>ObjectVariable</name>
        <value>
            <elementReference>myVariable_current</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OldObjectVariable</name>
        <value>
            <elementReference>myVariable_old</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>TriggerType</name>
        <value>
            <stringValue>onAllChanges</stringValue>
        </value>
    </processMetadataValues>
    <processType>Workflow</processType>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Attendee__c]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_1_A1</name>
        <label>Update Check-In Information</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Attendee Check-in Information</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>Attendee_Check_in_Information__c</field>
            <value>
                <elementReference>formula_2_myRule_1_A1_6277097925</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>First Name (Text)</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>First_Name_Text__c</field>
            <value>
                <elementReference>myVariable_current.Account__r.FirstName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Last Name (Text)</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>Last_Name_Text__c</field>
            <value>
                <elementReference>myVariable_current.Account__r.LastName</elementReference>
            </value>
        </inputAssignments>
        <object>Attendee__c</object>
    </recordUpdates>
    <startElementReference>myDecision</startElementReference>
    <status>Obsolete</status>
    <variables>
        <name>myVariable_current</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
    <variables>
        <name>myVariable_old</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
</Flow>
