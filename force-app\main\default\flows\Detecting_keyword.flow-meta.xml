<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Action_1</name>
        <label>Action 1</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <actionName>nullNotFound</actionName>
        <connector>
            <targetReference>Invoke_Prompt_Builder</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Invoke_Prompt_Builder</name>
        <label>Invoke Prompt Builder</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <actionName>0hf8s000000cCubNotFound</actionName>
        <actionType>generatePromptResponse</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>Input:case_obj</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputParameters>
        <offset>0</offset>
        <outputParameters>
            <assignToReference>Response</assignToReference>
            <name>promptResponse</name>
        </outputParameters>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <environments>Default</environments>
    <interviewLabel>Detecting keyword {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Detecting keyword</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Action_1</targetReference>
        </connector>
        <object>Case</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>InvalidDraft</status>
    <variables>
        <name>Response</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
