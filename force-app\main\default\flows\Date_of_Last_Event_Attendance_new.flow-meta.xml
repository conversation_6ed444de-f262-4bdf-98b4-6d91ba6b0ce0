<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>53.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Check_Customer_Attendee</name>
        <label>Check Customer Attendee</label>
        <locationX>879</locationX>
        <locationY>105</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Record_is_Customer_Attendee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Customer_Attendee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Record_Type_Id</targetReference>
            </connector>
            <label>Record is Customer Attendee</label>
        </rules>
    </decisions>
    <description>Date of Last Event Attendance for the customer</description>
    <interviewLabel>Date of Last Event Attendance {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Date of Last Event Attendance</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Attendee_Details</name>
        <label>Get Attendee Details</label>
        <locationX>1233</locationX>
        <locationY>113</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Account</targetReference>
        </connector>
        <filterLogic>((1 OR 2 OR 3) AND 4 AND 5 AND (6 OR 7) AND 8)</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attended</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>No Show</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Record_Type_Id.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Event_Date__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </filters>
        <filters>
            <field>Campaign_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>Campaign_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Attendee__c</object>
        <sortField>Event_Date__c</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Record_Type_Id</name>
        <label>Get Record Type Id</label>
        <locationX>1111</locationX>
        <locationY>113</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Attendee_Details</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Customer_Attendee</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attendee__c</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Account</name>
        <label>Update Account</label>
        <locationX>1363</locationX>
        <locationY>114</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Date_of_Last_Event_Attendance__c</field>
            <value>
                <elementReference>Get_Attendee_Details.Event_Date__c</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>407</locationX>
        <locationY>13</locationY>
        <connector>
            <targetReference>Check_Customer_Attendee</targetReference>
        </connector>
        <filterLogic>1 OR 2 OR 3</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>No Show</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Attended</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <object>Attendee__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>Counter</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>LastEventDate</name>
        <dataType>Date</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
