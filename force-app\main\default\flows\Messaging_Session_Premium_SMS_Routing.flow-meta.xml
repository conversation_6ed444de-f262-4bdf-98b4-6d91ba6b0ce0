<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Checks if the customer owner as the primary relationship manager is available for Grow  customer</description>
        <name>Check_Grow_Primary_Relationship_Manager_Available1</name>
        <label>Check Grow Primary Relationship Manager Available</label>
        <locationX>4054</locationX>
        <locationY>1646</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_Grow_Primary_Relationship_Manager_Available</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Automated_Process_User1</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if a team member from the National Protect is available</description>
        <name>Check_National_Protect_Member_available</name>
        <label>Check National Protect Member available</label>
        <locationX>6342</locationX>
        <locationY>2186</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_a_National_Protect_Member_Available</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Protect_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if the customer owner as the primary relationship manager is available for Protect  customer</description>
        <name>Check_Protect_Primary_Relationship_Manager_Available</name>
        <label>Check Protect Primary Relationship Manager Available</label>
        <locationX>1150</locationX>
        <locationY>1322</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_Protect_Primary_Relationship_Manager_Available</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Automated_Process_User</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if a team member from the primary relationship managers state is available</description>
        <name>Check_Relationship_Manager_State_Available</name>
        <label>Check Relationship Manager State Available</label>
        <locationX>1722</locationX>
        <locationY>2570</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_a_State_Member_Available</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if a team member from the primary relationship managers state is available</description>
        <name>Check_Relationship_Manager_State_Available1</name>
        <label>Check Relationship Manager State Available</label>
        <locationX>4626</locationX>
        <locationY>2894</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_a_State_Member_Available1</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Queue1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if a team member from the primary relationship managers state is available</description>
        <name>Check_Relationship_Manager_State_Available2</name>
        <label>Check Relationship Manager State Available</label>
        <locationX>6144</locationX>
        <locationY>1970</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_a_State_Member_Available2</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Protect_Queue1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Check_RM_Last_24_Hours</name>
        <label>Check RM Last 24 Hours</label>
        <locationX>1370</locationX>
        <locationY>1862</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_RM_Last_24_Hours_Available</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_State_Members_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Messaging_Sessions.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Check_RM_Last_24_Hours1</name>
        <label>Check RM Last 24 Hours</label>
        <locationX>4274</locationX>
        <locationY>2186</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_RM_Last_24_Hours_Available1</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_State_Members_Queue1</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Messaging_Sessions1.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined Business Hours</description>
        <name>Check_Within_Grow_Business_Hours</name>
        <label>Check Within Grow Business Hours</label>
        <locationX>5275</locationX>
        <locationY>1322</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Update_Grow_Business_Hours_and_Portfolio_on_Messaging_Session_Record</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Grow_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined OOH AM Business Hours</description>
        <name>Check_Within_Grow_OOH_AM_Business_Hours</name>
        <label>Check Within Grow OOH AM Business Hours</label>
        <locationX>6848</locationX>
        <locationY>1970</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Grow_OOH_AM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Grow_OOH_AM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined OOH PM Business Hours</description>
        <name>Check_Within_Grow_OOH_PM_Business_Hours</name>
        <label>Check Within Grow OOH PM Business Hours</label>
        <locationX>7046</locationX>
        <locationY>2294</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Grow_OOH_PM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Grow_OOH_PM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined Protect Business Hours</description>
        <name>Check_Within_Protect_Business_Hours1</name>
        <label>Check Within Protect Business Hours</label>
        <locationX>7396</locationX>
        <locationY>350</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Get_National_Protect_Queue</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Protect_Business_Hours1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined OOH AM Business Hours</description>
        <name>Check_Within_Protect_OOH_AM_Business_Hours</name>
        <label>Check Within Protect OOH AM Business Hours</label>
        <locationX>2976</locationX>
        <locationY>1538</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Protect_OOH_AM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Protect_OOH_AM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined OOH PM Business Hours</description>
        <name>Check_Within_Protect_OOH_PM_Business_Hours</name>
        <label>Check Within Protect OOH PM Business Hours</label>
        <locationX>3174</locationX>
        <locationY>1862</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Protect_OOH_PM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Protect_OOH_PM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Route_to_Blocked_Queue</name>
        <label>Route to Blocked Queue</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>input_record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_Premium_Managed_SMS_Blocked.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Route_to_Event_Experience_Queue</name>
        <label>Route to Event Experience Queue</label>
        <locationX>7794</locationX>
        <locationY>1322</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>input_record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_Events_Experience_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to primary relationship manager for grow customer</description>
        <name>Route_to_Grow_Primary_Relationship_Manager</name>
        <label>Route to Grow Primary Relationship Manager</label>
        <locationX>3482</locationX>
        <locationY>1862</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Automated_Process_User1</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to the wider national queue for any relationship manager to action</description>
        <name>Route_to_National_Grow_Queue</name>
        <label>Route to National Grow Queue</label>
        <locationX>5275</locationX>
        <locationY>3578</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to the wider national queue for any relationship manager to action</description>
        <name>Route_to_National_Protect_Queue</name>
        <label>Route to National Protect Queue</label>
        <locationX>7396</locationX>
        <locationY>4046</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Protect_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to primary relationship manager for protect customer</description>
        <name>Route_to_Protect_Primary_Relationship_Manager</name>
        <label>Route to Protect Primary Relationship Manager</label>
        <locationX>578</locationX>
        <locationY>1538</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Automated_Process_User</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Protect_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to a state member</description>
        <name>Route_to_Protect_State</name>
        <label>Route to Protect State</label>
        <locationX>2778</locationX>
        <locationY>1862</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Protect_Queue2.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Route_to_RM_Last_24_Hours</name>
        <label>Route to RM Last 24 Hours</label>
        <locationX>1106</locationX>
        <locationY>2078</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_State_Members_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Protect_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Messaging_Sessions.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentRequired</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Route_to_RM_Last_24_Hours1</name>
        <label>Route to RM Last 24 Hours</label>
        <locationX>4010</locationX>
        <locationY>2402</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_State_Members_Queue1</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Messaging_Sessions1.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to a state member</description>
        <name>Route_to_State</name>
        <label>Route to State</label>
        <locationX>1458</locationX>
        <locationY>2786</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to a state member</description>
        <name>Route_to_State1</name>
        <label>Route to State</label>
        <locationX>4362</locationX>
        <locationY>3110</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Grow_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Members_Queue1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to a state member</description>
        <name>Route_to_State2</name>
        <label>Route to State</label>
        <locationX>5946</locationX>
        <locationY>2186</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Protect_Queue1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to a state member</description>
        <name>Route_to_State3</name>
        <label>Route to State</label>
        <locationX>6650</locationX>
        <locationY>2294</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Grow_Queue2.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Route_to_Unmanaged_Queue</name>
        <label>Route to Unmanaged Queue</label>
        <locationX>8058</locationX>
        <locationY>1322</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>input_record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_Unmanaged_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Sends customer SMS component advising they messaged out of hours</description>
        <name>Send_Out_Of_Hours_SMS_Component</name>
        <label>Send Out Of Hours SMS Component</label>
        <locationX>2976</locationX>
        <locationY>1322</locationY>
        <actionName>sendConversationMessages</actionName>
        <actionType>sendConversationMessages</actionType>
        <connector>
            <targetReference>Get_Protect_OOH_AM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>messageDefinitionName</name>
            <value>
                <stringValue>Premium_Managed_Out_of_Hours_Message</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingDefinitionInputParameters</name>
        </inputParameters>
        <inputParameters>
            <name>requestType</name>
            <value>
                <stringValue>SendNotificationMessages</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isEnforceMessagingChannelConsent</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>allowedSessionStatus</name>
            <value>
                <stringValue>Any</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingEndUserIds</name>
            <value>
                <elementReference>varMessagingUsersIds</elementReference>
            </value>
        </inputParameters>
        <nameSegment>sendConversationMessages</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Sends customer SMS component advising they messaged out of hours</description>
        <name>Send_Out_Of_Hours_SMS_Component1</name>
        <label>Send Out Of Hours SMS Component</label>
        <locationX>6496</locationX>
        <locationY>1646</locationY>
        <actionName>sendConversationMessages</actionName>
        <actionType>sendConversationMessages</actionType>
        <connector>
            <targetReference>Is_Event_Attendee</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Check_Grow_Primary_Relationship_Manager_Available1</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>messageDefinitionName</name>
            <value>
                <stringValue>Premium_Managed_Out_of_Hours_Message</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingDefinitionInputParameters</name>
        </inputParameters>
        <inputParameters>
            <name>requestType</name>
            <value>
                <stringValue>SendNotificationMessages</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isEnforceMessagingChannelConsent</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>allowedSessionStatus</name>
            <value>
                <stringValue>Any</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingEndUserIds</name>
            <value>
                <elementReference>varMessagingUsersIds</elementReference>
            </value>
        </inputParameters>
        <nameSegment>sendConversationMessages</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Sends customer SMS component advising they messaged out of hours</description>
        <name>Send_Out_Of_Hours_SMS_No_Customer_Match</name>
        <label>Send Out Of Hours SMS No Customer Match</label>
        <locationX>8498</locationX>
        <locationY>998</locationY>
        <actionName>sendConversationMessages</actionName>
        <actionType>sendConversationMessages</actionType>
        <connector>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>messageDefinitionName</name>
            <value>
                <stringValue>Premium_Managed_Out_of_Hours_Message</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingDefinitionInputParameters</name>
        </inputParameters>
        <inputParameters>
            <name>requestType</name>
            <value>
                <stringValue>SendNotificationMessages</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isEnforceMessagingChannelConsent</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>allowedSessionStatus</name>
            <value>
                <stringValue>Any</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingEndUserIds</name>
            <value>
                <elementReference>varMessagingUsersIds</elementReference>
            </value>
        </inputParameters>
        <nameSegment>sendConversationMessages</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Sends customer SMS advising them they are not managed customer</description>
        <name>Send_Unmanaged_Messaging_Component</name>
        <label>Send Unmanaged Messaging Component</label>
        <locationX>7926</locationX>
        <locationY>998</locationY>
        <actionName>sendConversationMessages</actionName>
        <actionType>sendConversationMessages</actionType>
        <connector>
            <targetReference>Is_Unmanaged_Attendee</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>messageDefinitionName</name>
            <value>
                <stringValue>Premium_Managed_Unmanaged_Customer_SMS</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingDefinitionInputParameters</name>
        </inputParameters>
        <inputParameters>
            <name>requestType</name>
            <value>
                <stringValue>SendNotificationMessages</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isEnforceMessagingChannelConsent</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>allowedSessionStatus</name>
            <value>
                <stringValue>Any</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingEndUserIds</name>
            <value>
                <elementReference>varMessagingUsersIds</elementReference>
            </value>
        </inputParameters>
        <nameSegment>sendConversationMessages</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assign Messaging User Id to text collection variable to use in Messaging Component if needed</description>
        <name>Assign_Messaging_User_Id</name>
        <label>Assign Messaging User Id</label>
        <locationX>7396</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>varMessagingUsersIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>input_record.MessagingEndUser.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Customer_Match</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Is there a customer match?</description>
        <name>Customer_Match</name>
        <label>Customer Match</label>
        <locationX>7396</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Within_Business_Hours_No_Customer_Match</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Match</defaultConnectorLabel>
        <rules>
            <name>Customer_Matched</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_Customer_Managed</targetReference>
            </connector>
            <label>Customer Matched</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is a National protect Member Available</description>
        <name>Is_a_National_Protect_Member_Available</name>
        <label>Is a National Protect Member Available</label>
        <locationX>6342</locationX>
        <locationY>2294</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Grow_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>National_Protect_Member_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_National_Protect_Member_available.onlineAgentsCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Route_to_National_Protect_Queue</targetReference>
            </connector>
            <label>National Protect Member Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is a State Member Available</description>
        <name>Is_a_State_Member_Available</name>
        <label>Is a State Member Available</label>
        <locationX>1722</locationX>
        <locationY>2678</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>State_Member_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Relationship_Manager_State_Available.onlineAgentsCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_State</targetReference>
            </connector>
            <label>State Member Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is a State Member Available</description>
        <name>Is_a_State_Member_Available1</name>
        <label>Is a State Member Available</label>
        <locationX>4626</locationX>
        <locationY>3002</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Grow_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>State_Member_Available1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Relationship_Manager_State_Available1.onlineAgentsCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_State1</targetReference>
            </connector>
            <label>State Member Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is a State Member Available</description>
        <name>Is_a_State_Member_Available2</name>
        <label>Is a State Member Available</label>
        <locationX>6144</locationX>
        <locationY>2078</locationY>
        <defaultConnector>
            <targetReference>Check_National_Protect_Member_available</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>State_Member_Available2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Relationship_Manager_State_Available2.onlineAgentsCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_State2</targetReference>
            </connector>
            <label>State Member Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is this customer a Premium managed Customer</description>
        <name>Is_Customer_Managed</name>
        <label>Is Customer Managed</label>
        <locationX>6383</locationX>
        <locationY>890</locationY>
        <defaultConnector>
            <targetReference>Send_Unmanaged_Messaging_Component</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Managed_Customer</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Elite_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Elite</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Protect_or_Grow</targetReference>
            </connector>
            <label>Managed Customer</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Event_Attendee</name>
        <label>Is Event Attendee?</label>
        <locationX>6496</locationX>
        <locationY>1754</locationY>
        <defaultConnector>
            <targetReference>Get_Grow_OOH_AM_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not an Event Attendee</defaultConnectorLabel>
        <rules>
            <name>Event_Attendee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Next_Event_Attendance__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_State_Protect_Queue1</targetReference>
            </connector>
            <label>Event Attendee?</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is the contact owner of the record available for grow customer</description>
        <name>Is_Grow_Primary_Relationship_Manager_Available</name>
        <label>Is Grow Primary Relationship Manager Available</label>
        <locationX>4054</locationX>
        <locationY>1754</locationY>
        <defaultConnector>
            <targetReference>Get_Automated_Process_User1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>Grow_Relationship_Manager_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Grow_Primary_Relationship_Manager_Available1.onlineAgentsCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_Grow_Primary_Relationship_Manager</targetReference>
            </connector>
            <label>Grow Relationship Manager Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is the contact owner of the record available for protect customer</description>
        <name>Is_Protect_Primary_Relationship_Manager_Available</name>
        <label>Is Protect Primary Relationship Manager Available</label>
        <locationX>1150</locationX>
        <locationY>1430</locationY>
        <defaultConnector>
            <targetReference>Get_Automated_Process_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Unavailable</defaultConnectorLabel>
        <rules>
            <name>Protect_Relationship_Manager_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Protect_Primary_Relationship_Manager_Available.onlineAgentsCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_Protect_Primary_Relationship_Manager</targetReference>
            </connector>
            <label>Protect Relationship Manager Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Is_RM_Last_24_Hours_Available</name>
        <label>Is RM Last 24 Hours Available?</label>
        <locationX>1370</locationX>
        <locationY>1970</locationY>
        <defaultConnector>
            <targetReference>Get_State_Members_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>IsAvailable</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_RM_Last_24_Hours.onlineAgentsCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_RM_Last_24_Hours</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Is_RM_Last_24_Hours_Available1</name>
        <label>Is RM Last 24 Hours Available?</label>
        <locationX>4274</locationX>
        <locationY>2294</locationY>
        <defaultConnector>
            <targetReference>Get_State_Members_Queue1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_RM_Last_24_Hours1.onlineAgentsCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_RM_Last_24_Hours1</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Unmanaged_Attendee</name>
        <label>Is Unmanaged Attendee?</label>
        <locationX>7926</locationX>
        <locationY>1106</locationY>
        <defaultConnector>
            <targetReference>Get_Unmanaged_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Unmanaged_Attendee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Customer_Attendee__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Events_Experience_Queue</targetReference>
            </connector>
            <label>Unmanaged Attendee?</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_User_Blocked</name>
        <label>Is User Blocked</label>
        <locationX>3723</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>Get_Protect_Business_Hours1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Allowed</defaultConnectorLabel>
        <rules>
            <name>Blocked</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>input_record.MessagingEndUser.Blocked__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Premium_Managed_SMS_Blocked</targetReference>
            </connector>
            <label>Blocked</label>
        </rules>
    </decisions>
    <decisions>
        <description>What is the Portfolio of the customer</description>
        <name>Protect_or_Grow</name>
        <label>Protect or Grow?</label>
        <locationX>4840</locationX>
        <locationY>998</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Match</defaultConnectorLabel>
        <rules>
            <name>Protect</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Protect</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Protect_Business_Hours_and_Portfolio_on_Messaging_Session_Record</targetReference>
            </connector>
            <label>Protect</label>
        </rules>
        <rules>
            <name>Grow</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Grow</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Grow_Business_Hours</targetReference>
            </connector>
            <label>Grow</label>
        </rules>
    </decisions>
    <decisions>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Was_Messaging_Session_within_last_24_hours</name>
        <label>Was Messaging Session within last 24 hours?</label>
        <locationX>1722</locationX>
        <locationY>1754</locationY>
        <defaultConnector>
            <targetReference>Get_State_Members_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Messaging_Sessions.CreatedDate</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>formulaYesterdayDateTime</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>UserIdCheck</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_RM_Last_24_Hours</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Was_Messaging_Session_within_last_24_hours1</name>
        <label>Was Messaging Session within last 24 hours?</label>
        <locationX>4626</locationX>
        <locationY>2078</locationY>
        <defaultConnector>
            <targetReference>Get_State_Members_Queue1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Messaging_Sessions1.CreatedDate</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>formulaYesterdayDateTime</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>UserIdCheck</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_RM_Last_24_Hours1</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this email sent inside business hours?</description>
        <name>Within_Business_Hours</name>
        <label>Within Business Hours</label>
        <locationX>2063</locationX>
        <locationY>1214</locationY>
        <defaultConnector>
            <targetReference>Send_Out_Of_Hours_SMS_Component</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Protect_Business_Hours1.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Protect_Primary_Relationship_Manager_Available</targetReference>
            </connector>
            <label>Inside Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this email sent inside business hours?</description>
        <name>Within_Business_Hours1</name>
        <label>Within Business Hours</label>
        <locationX>5275</locationX>
        <locationY>1538</locationY>
        <defaultConnector>
            <targetReference>Send_Out_Of_Hours_SMS_Component1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Business_Hours1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Grow_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Grow_Primary_Relationship_Manager_Available1</targetReference>
            </connector>
            <label>Inside Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this email sent inside business hours?</description>
        <name>Within_Business_Hours_No_Customer_Match</name>
        <label>Within Business Hours No Customer Match</label>
        <locationX>8410</locationX>
        <locationY>890</locationY>
        <defaultConnector>
            <targetReference>Send_Out_Of_Hours_SMS_No_Customer_Match</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Business_Hours_No_Customer_Match</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Protect_Business_Hours1.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_National_Protect_Queue</targetReference>
            </connector>
            <label>Inside Business Hours No Customer Match</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this SMS sent inside OOH AM business hours?</description>
        <name>Within_Grow_OOH_AM_Business_Hours</name>
        <label>Within Grow OOH AM Business Hours</label>
        <locationX>6848</locationX>
        <locationY>2078</locationY>
        <defaultConnector>
            <targetReference>Get_Grow_OOH_PM_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Grow OOH AM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Grow_OOH_AM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Grow_OOH_AM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_State_Grow_Queue2</targetReference>
            </connector>
            <label>Inside Grow OOH AM Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this SMS sent inside OOH PM business hours?</description>
        <name>Within_Grow_OOH_PM_Business_Hours</name>
        <label>Within Grow OOH PM Business Hours</label>
        <locationX>7046</locationX>
        <locationY>2402</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Grow_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Grow OOH PM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Grow_OOH_PM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Grow_OOH_PM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Get_State_Grow_Queue2</targetReference>
            </connector>
            <label>Inside Grow OOH PM Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this SMS sent inside OOH AM business hours?</description>
        <name>Within_Protect_OOH_AM_Business_Hours</name>
        <label>Within Protect OOH AM Business Hours</label>
        <locationX>2976</locationX>
        <locationY>1646</locationY>
        <defaultConnector>
            <targetReference>Get_Protect_OOH_PM_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Protect OOH AM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Protect_OOH_AM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Protect_OOH_AM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_State_Protect_Queue2</targetReference>
            </connector>
            <label>Inside Protect OOH AM Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this SMS sent inside OOH PM business hours?</description>
        <name>Within_Protect_OOH_PM_Business_Hours</name>
        <label>Within Protect OOH PM Business Hours</label>
        <locationX>3174</locationX>
        <locationY>1970</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Protect OOH PM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Protect_OOH_PM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Protect_OOH_PM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Get_State_Protect_Queue2</targetReference>
            </connector>
            <label>Inside Protect OOH PM Business Hours</label>
        </rules>
    </decisions>
    <description>Omni-Channel Flow with logic for routing SMS to the appropriate relationship manager
Reverting V9 changes to have Enhanced Omni changes and the E&amp;E routing stuff without mobile.
Adding Grow &amp; Protect changes</description>
    <environments>Default</environments>
    <formulas>
        <description>Formula outputs the DeveloperName of the Queue based on the Role of customers relationship manager to assign to the Grow state queue</description>
        <name>formulaCalculateStateGrowQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,    
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A&apos;,    
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B&apos;,
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;National_Grow_Queue&apos;
)</expression>
    </formulas>
    <formulas>
        <description>Formula outputs the DeveloperName of the Queue based on the Role of customers relationship manager to assign to the state queue</description>
        <name>formulaCalculateStateQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,    
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A&apos;,    
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B&apos;,
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;National_Protect_Queue&apos;
)</expression>
    </formulas>
    <formulas>
        <description>Formula outputs the DeveloperName of the Queue based on the Role of customers relationship manager</description>
        <name>formulaCalculateTeamQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},
    &apos;Premium_State_Manager_NSW&apos;, &apos;Premium_Managed_NSW&apos;,
    &apos;Premium_Team_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Premium_Team_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,
    &apos;Premium_State_Manager_VIC&apos;, &apos;Premium_Managed_VIC&apos;,
    &apos;Premium_Team_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Premium_Team_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,
    &apos;Premium_State_Manager_QLD&apos;, &apos;Premium_Managed_QLD&apos;,
    &apos;Premium_Team_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD&apos;,
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD&apos;,
    &apos;Premium_Team_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;Premium_Managed_General&apos;
)</expression>
    </formulas>
    <formulas>
        <description>Calculates tomorrows date from Current Date</description>
        <name>formulaDateTomorrow</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate} + 1</expression>
    </formulas>
    <formulas>
        <description>Calculates the day of the week from the Next Business Day output of the Check Within Business Hours Apex Action</description>
        <name>formulaDayOfWeek</name>
        <dataType>String</dataType>
        <expression>CASE(
  MOD(DATEVALUE({!Check_Within_Protect_Business_Hours1.nextStartDate}) - DATE(1900, 1, 8), 7),
  0, &quot;Monday&quot;,
  1, &quot;Tuesday&quot;,
  2, &quot;Wednesday&quot;,
  3, &quot;Thursday&quot;,
  4, &quot;Friday&quot;,
  5, &quot;Saturday&quot;,
  &quot;Sunday&quot;
)</expression>
    </formulas>
    <formulas>
        <name>formulaGetStateGrowQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,    
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A&apos;,    
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B&apos;,
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;National_Grow_Queue&apos;
)</expression>
    </formulas>
    <formulas>
        <name>formulaGetStateProtectQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,    
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A&apos;,    
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B&apos;,
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;National_Protect_Queue&apos;
)</expression>
    </formulas>
    <formulas>
        <name>formulaYesterdayDateTime</name>
        <dataType>DateTime</dataType>
        <expression>{!$Flow.CurrentDateTime} - 1</expression>
    </formulas>
    <formulas>
        <name>UserIdCheck</name>
        <dataType>Boolean</dataType>
        <expression>BEGINS({!Get_Messaging_Sessions.OwnerId} , &apos;005&apos;)</expression>
    </formulas>
    <interviewLabel>Messaging Session: Premium SMS Routing {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Messaging Session: Premium SMS Routing</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>RoutingFlow</processType>
    <recordLookups>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Get_Automated_Process_User</name>
        <label>Get Automated Process User</label>
        <locationX>1722</locationX>
        <locationY>1538</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Messaging_Sessions</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Alias</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>autoproc</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Get_Automated_Process_User1</name>
        <label>Get Automated Process User</label>
        <locationX>4626</locationX>
        <locationY>1862</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Messaging_Sessions1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Alias</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>autoproc</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record related to the Messaging Session</description>
        <name>Get_Customer</name>
        <label>Get Customer</label>
        <locationX>7396</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Messaging_User_Id</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>input_record.Customer__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Events_Experience_Queue</name>
        <label>Get Events &amp; Experience Queue</label>
        <locationX>7794</locationX>
        <locationY>1214</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_Event_Experience_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Events_Experience_General</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Grow Business Hours record</description>
        <name>Get_Grow_Business_Hours</name>
        <label>Get Grow Business Hours</label>
        <locationX>5275</locationX>
        <locationY>1106</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_National_Grow_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National Grow</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Grow OOH AM Business Hours record</description>
        <name>Get_Grow_OOH_AM_Business_Hours</name>
        <label>Get Grow OOH AM Business Hours</label>
        <locationX>6848</locationX>
        <locationY>1862</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Grow_OOH_AM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Grow Out of hours AM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Grow OOH PM Business Hours record</description>
        <name>Get_Grow_OOH_PM_Business_Hours</name>
        <label>Get Grow OOH PM Business Hours</label>
        <locationX>7046</locationX>
        <locationY>2186</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Grow_OOH_PM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Grow Out of hours PM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Get_Messaging_Sessions</name>
        <label>Get Messaging Sessions</label>
        <locationX>1722</locationX>
        <locationY>1646</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Was_Messaging_Session_within_last_24_hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Customer__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>input_record.Customer__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Ended</stringValue>
            </value>
        </filters>
        <filters>
            <field>OwnerId</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>Get_Automated_Process_User.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingSession</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>SBET-1024 SMS - Route to recent contact if RM is not available</description>
        <name>Get_Messaging_Sessions1</name>
        <label>Get Messaging Sessions</label>
        <locationX>4626</locationX>
        <locationY>1970</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Was_Messaging_Session_within_last_24_hours1</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_State_Members_Queue1</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Customer__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>input_record.Customer__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Ended</stringValue>
            </value>
        </filters>
        <filters>
            <field>OwnerId</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>Get_Automated_Process_User1.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingSession</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_National_Grow_Queue</name>
        <label>Get National Grow Queue</label>
        <locationX>5275</locationX>
        <locationY>1214</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Grow_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National_Grow_Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the general Queue for all the premium team</description>
        <name>Get_National_Protect_Queue</name>
        <label>Get National Protect Queue</label>
        <locationX>7396</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National_Protect_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Premium_Managed_SMS_Blocked</name>
        <label>Get Premium Managed SMS Blocked</label>
        <locationX>50</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_Blocked_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_SMS_Blocked</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Protect Business Hours record</description>
        <name>Get_Protect_Business_Hours1</name>
        <label>Get Protect Business Hours</label>
        <locationX>7396</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Protect_Business_Hours1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National Protect</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Protect OOH AM Business Hours record</description>
        <name>Get_Protect_OOH_AM_Business_Hours</name>
        <label>Get Protect OOH AM Business Hours</label>
        <locationX>2976</locationX>
        <locationY>1430</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Protect_OOH_AM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Protect Out of hours AM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Protect OOH PM Business Hours record</description>
        <name>Get_Protect_OOH_PM_Business_Hours</name>
        <label>Get Protect OOH PM Business Hours</label>
        <locationX>3174</locationX>
        <locationY>1754</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Protect_OOH_PM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Protect Out of hours PM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Queue for the relationship manager&apos;s state</description>
        <name>Get_State_Grow_Queue2</name>
        <label>Get State Grow Queue</label>
        <locationX>6650</locationX>
        <locationY>2186</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_State3</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaGetStateGrowQueue</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Queue for the relationship manager&apos;s state</description>
        <name>Get_State_Members_Queue</name>
        <label>Get State Protect Queue</label>
        <locationX>1722</locationX>
        <locationY>2462</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Relationship_Manager_State_Available</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaGetStateProtectQueue</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Queue for the relationship manager&apos;s state</description>
        <name>Get_State_Members_Queue1</name>
        <label>Get State Grow Queue</label>
        <locationX>4626</locationX>
        <locationY>2786</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Relationship_Manager_State_Available1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaGetStateGrowQueue</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Queue for the relationship manager&apos;s state</description>
        <name>Get_State_Protect_Queue1</name>
        <label>Get State Protect Queue</label>
        <locationX>6144</locationX>
        <locationY>1862</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Relationship_Manager_State_Available2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaGetStateProtectQueue</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Queue for the relationship manager&apos;s state</description>
        <name>Get_State_Protect_Queue2</name>
        <label>Get State Protect Queue</label>
        <locationX>2778</locationX>
        <locationY>1754</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_Protect_State</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaGetStateProtectQueue</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Unmanaged_Queue</name>
        <label>Get Unmanaged Queue</label>
        <locationX>8058</locationX>
        <locationY>1214</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_Unmanaged_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Unmanaged</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Grow_Business_Hours_and_Portfolio_on_Messaging_Session_Record</name>
        <label>Update Grow Business Hours and Portfolio on Messaging Session Record</label>
        <locationX>5275</locationX>
        <locationY>1430</locationY>
        <connector>
            <targetReference>Within_Business_Hours1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>input_record.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>Get_Customer.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>Check_Within_Grow_Business_Hours.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>MessagingSession</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Protect_Business_Hours_and_Portfolio_on_Messaging_Session_Record</name>
        <label>Update Protect Business Hours and Portfolio on Messaging Session Record</label>
        <locationX>2063</locationX>
        <locationY>1106</locationY>
        <connector>
            <targetReference>Within_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>input_record.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>Get_Customer.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>Check_Within_Protect_Business_Hours1.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>MessagingSession</object>
    </recordUpdates>
    <start>
        <locationX>3597</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_User_Blocked</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>input_record</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>MessagingSession</objectType>
    </variables>
    <variables>
        <description>Stores the ID that uniquely identifies the work record being routed</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores users Ids of the event Hosts</description>
        <name>varHostUserIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value to determine if the customer is managed</description>
        <name>varManagedCustomer</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores Messaging User Id, used in the Send Conversation SMS Action</description>
        <name>varMessagingUsersIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the Developer Name of the Queue to route work to</description>
        <name>varQueueDeveloperName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores whether email was sent within business TRUE or FALSE</description>
        <name>varWithinBusinessHours</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
