<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Milestone_Found</name>
        <label>Milestone Found?</label>
        <locationX>374</locationX>
        <locationY>384</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Case_MIlestone</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_SLA_Target_Date</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>AML case Priority Updated {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML case Priority Updated</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Case_MIlestone</name>
        <label>Get Case MIlestone</label>
        <locationX>374</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Milestone_Found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_SLA_Target_Date</name>
        <label>Update SLA Target Date</label>
        <locationX>242</locationX>
        <locationY>492</locationY>
        <inputAssignments>
            <field>SLA_Target_Date__c</field>
            <value>
                <elementReference>Get_Case_MIlestone.TargetDate</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterFormula>OR(
{!$Record.RecordType.DeveloperName} = &quot;ECDD_Case&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Transaction_Monitoring&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Non_Transaction_Monitoring&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Standard_SMR&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Fast_Track_SMR&quot;
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <name>Trigger_Directly_After_Update</name>
            <connector>
                <targetReference>Get_Case_MIlestone</targetReference>
            </connector>
            <label>Trigger Directly After Update</label>
            <offsetNumber>0</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <timeSource>RecordTriggerEvent</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>ChatterPost</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;This case has just been escalated to {!$Record.Priority}. Please action this case as soon as possible&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>Recipient</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Record.OwnerId</elementReference>
        </value>
    </variables>
    <variables>
        <name>recipients</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
