<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>add field Automatically_Reallocated__c</description>
    <environments>Default</environments>
    <interviewLabel>Load Balance Premium Messaging Sessions {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Load Balance Premium Messaging Sessions</label>
    <loops>
        <name>loopAW</name>
        <label>loopAW</label>
        <locationX>176</locationX>
        <locationY>1538</locationY>
        <collectionReference>Get_AgentWork</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Re_Route_SMS</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_AgentWork</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Active_UserPresences</name>
        <label>Get Active UserPresences</label>
        <locationX>176</locationX>
        <locationY>998</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_PSRs</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsCurrentState</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>ServicePresenceStatusId</field>
            <operator>In</operator>
            <value>
                <elementReference>ServicePresenceStatusIds</elementReference>
            </value>
        </filters>
        <filters>
            <field>OwnerId</field>
            <operator>In</operator>
            <value>
                <elementReference>PremiumStaffIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>UserServicePresence</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_AgentWork</name>
        <label>Get AgentWork</label>
        <locationX>176</locationX>
        <locationY>1322</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>AgentWorkIds</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WorkItemId</field>
            <operator>In</operator>
            <value>
                <elementReference>PSRWorkItemIds</elementReference>
            </value>
        </filters>
        <filters>
            <field>AcceptDateTime</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Assigned</stringValue>
            </value>
        </filters>
        <filters>
            <field>OwnerId</field>
            <operator>In</operator>
            <value>
                <elementReference>PremiumStaffIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>AgentWork</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_PremiumStaff</name>
        <label>Get PremiumStaff</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>PremiumStaffIds</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>GroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetPremiumGroup.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GroupMember</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_PSRs</name>
        <label>Get PSRs</label>
        <locationX>176</locationX>
        <locationY>1106</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>PSRWorkItemIds</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsTransfer</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>PreferredUserId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </filters>
        <filters>
            <field>ServiceChannelId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_ServiceChannel.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>PendingServiceRouting</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ServiceChannel</name>
        <label>Get ServiceChannel</label>
        <locationX>176</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_ServiceChannelStatus_for_Messaging</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ServiceChannel</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ServiceChannelStatus_for_Messaging</name>
        <label>Get ServiceChannelStatus for Messaging</label>
        <locationX>176</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ServiceChannelStatusIds</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ServiceChannelId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_ServiceChannel.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ServiceChannelStatus</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ServicePresenceStatus</name>
        <label>Get ServicePresenceStatus</label>
        <locationX>176</locationX>
        <locationY>890</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Active_UserPresences</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>ServicePresenceStatusIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ServicePresenceStatus</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetPremiumGroup</name>
        <label>GetPremiumGroup</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_PremiumStaff</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>SportsBet_Customers</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Regular</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_AgentWork</name>
        <label>Update AgentWork</label>
        <locationX>176</locationX>
        <locationY>1838</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>AgentWorkIds</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Automatically_Reallocated__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>AgentWork</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetPremiumGroup</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Re_Route_SMS</name>
        <label>Re-Route SMS</label>
        <locationX>264</locationX>
        <locationY>1646</locationY>
        <connector>
            <targetReference>loopAW</targetReference>
        </connector>
        <flowName>Messaging_Session_Premium_SMS_Routing</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>loopAW.WorkItemId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <transforms>
        <name>AgentWorkIds</name>
        <label>AgentWorkIds</label>
        <locationX>176</locationX>
        <locationY>1430</locationY>
        <connector>
            <targetReference>loopAW</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_AgentWork[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>PremiumStaffIds</name>
        <label>PremiumStaffIds</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>Get_ServiceChannel</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_PremiumStaff[$EachItem].UserOrGroupId</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>PSRWorkItemIds</name>
        <label>PSRWorkItemIds</label>
        <locationX>176</locationX>
        <locationY>1214</locationY>
        <connector>
            <targetReference>Get_AgentWork</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_PSRs[$EachItem].WorkItemId</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>ServiceChannelStatusIds</name>
        <label>ServiceChannelStatusIds</label>
        <locationX>176</locationX>
        <locationY>674</locationY>
        <connector>
            <targetReference>ServicePresenceStatusIds</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_ServiceChannelStatus_for_Messaging[$EachItem].Id</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <name>ServicePresenceStatusIds</name>
        <label>ServicePresenceStatusIds</label>
        <locationX>176</locationX>
        <locationY>782</locationY>
        <connector>
            <targetReference>Get_ServicePresenceStatus</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_ServiceChannelStatus_for_Messaging[$EachItem].ServicePresenceStatusId</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
</Flow>
