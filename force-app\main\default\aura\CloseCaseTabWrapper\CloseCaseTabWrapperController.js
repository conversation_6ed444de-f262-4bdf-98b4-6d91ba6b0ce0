/**
    * @description      : 
    * <AUTHOR> 
    * @group            : 
    * @created          : 08/04/2025 - 06:52:13
    * 
    * MODIFICATION LOG
    * - Version         : 1.0.0
    * - Date            : 08/04/2025
    * - Author          : 
    * - Modification    : 
**/
({
  handleCloseTab: function (component, event, helper) {
    const workspaceAPI = component.find('workspace')
    workspaceAPI
      .getFocusedTabInfo()
      .then(function (tabInfo) {
        workspaceAPI.closeTab({ tabId: tabInfo.tabId })
      })
      .catch(function (error) {
        console.error('Error closing tab:', error)
      })
  },
})