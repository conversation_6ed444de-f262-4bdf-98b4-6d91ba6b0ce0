<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>58.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Values</name>
        <label>Assign Values</label>
        <locationX>182</locationX>
        <locationY>1124</locationY>
        <assignmentItems>
            <assignToReference>GetCase.Text_Replacement__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Text_Replacement_Setup</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>GetCase.Inconsistence_Reason__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Betstop_Inconsistence_Reason</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>GetCase.Betstop_Promoted__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Betstop_Promoted</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>GetCase.No_SMS_to_Customers__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>I_do_not_send_SMS_messages_to_customers</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>DocumentUploadedBoolean</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Validation_Passed</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Confirm_File_Upload</name>
        <label>Confirm File Upload</label>
        <locationX>204</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>GetCase.Compliance_Documents_Provided__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Values</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Bool</name>
        <label>Update Bool</label>
        <locationX>468</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>DocumentUploadedBoolean</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Close_Case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Boolean</name>
        <label>Update Boolean</label>
        <locationX>50</locationX>
        <locationY>1340</locationY>
        <assignmentItems>
            <assignToReference>ValidationBool</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Close_Case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Validation_Boolean_False</name>
        <label>Update Validation Boolean False</label>
        <locationX>314</locationX>
        <locationY>1340</locationY>
        <assignmentItems>
            <assignToReference>ValidationBool</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>GetCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>UpdateCase</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Case_Origin</name>
        <label>Case Origin</label>
        <locationX>457</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Betstop</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCase.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Betstop</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Close_Case</targetReference>
            </connector>
            <label>Betstop</label>
        </rules>
    </decisions>
    <decisions>
        <name>Files_Upload_Skipped</name>
        <label>Files Upload Skipped</label>
        <locationX>336</locationX>
        <locationY>674</locationY>
        <defaultConnector>
            <targetReference>Update_Bool</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Content_Doc_Link.LinkedEntityId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Confirm_File_Upload</targetReference>
            </connector>
            <label>Not Null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Validation_Passed</name>
        <label>Validation Passed</label>
        <locationX>182</locationX>
        <locationY>1232</locationY>
        <defaultConnector>
            <targetReference>Update_Validation_Boolean_False</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No</name>
            <conditionLogic>1 AND (2 OR 3)</conditionLogic>
            <conditions>
                <leftValueReference>Betstop_Inconsistence_Reason</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Betstop_Promoted</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Text_Replacement_Setup</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Boolean</targetReference>
            </connector>
            <label>No</label>
        </rules>
    </decisions>
    <decisions>
        <name>Was_No_SMS_Selected</name>
        <label>Was No SMS Selected</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Get_Content_Doc_Link</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Was_Selected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>I_do_not_send_SMS_messages_to_customers</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Values</targetReference>
            </connector>
            <label>Was Selected</label>
        </rules>
    </decisions>
    <description>SBET-497 - Modify Content Document Provided Save</description>
    <environments>Default</environments>
    <interviewLabel>Case Closure Widget {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Compliance Case Closure Widget</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Content_Doc_Link</name>
        <label>Get Content Doc Link</label>
        <locationX>336</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Files_Upload_Skipped</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>LinkedEntityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContentDocumentLink</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetCase</name>
        <label>GetCase</label>
        <locationX>457</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Case_Origin</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>UpdateCase</name>
        <label>UpdateCase</label>
        <locationX>314</locationX>
        <locationY>1448</locationY>
        <connector>
            <targetReference>Case_Closed</targetReference>
        </connector>
        <inputReference>GetCase</inputReference>
    </recordUpdates>
    <screens>
        <name>Case_Closed</name>
        <label>Case Closed</label>
        <locationX>314</locationX>
        <locationY>1556</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>CaseClosed</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(34, 139, 34);&quot;&gt;You have uploaded all required documents and evidence&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close Case</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Close_Case</name>
        <label>Close Case</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Was_No_SMS_Selected</targetReference>
        </connector>
        <fields>
            <name>Close_Case_label</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Close Case&lt;span class=&quot;ql-cursor&quot;&gt;﻿&lt;/span&gt;&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Validation</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(237, 33, 33); background-color: rgb(255, 255, 255);&quot;&gt;If either Betstop Promoted or Text Replacement Setup is not selected, please enter a Betstop Inconsistence Reason.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ValidationBool</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Betstop_Promoted</name>
            <dataType>Boolean</dataType>
            <fieldText>Betstop promoted in all emails</fieldText>
            <fieldType>InputField</fieldType>
            <helpText>&lt;p&gt;You have promoted BetStop at the end of each customer SMS interaction.&lt;/p&gt;</helpText>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Text_Replacement_Setup</name>
            <dataType>Boolean</dataType>
            <fieldText>Text replacement has been set up</fieldText>
            <fieldType>InputField</fieldType>
            <helpText>&lt;p&gt;You have set up the &apos;text replacement&apos; on your Sportsbet mobile device.&lt;/p&gt;</helpText>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Betstop_Inconsistence_Reason</name>
            <fieldText>Betstop Inconsistency Reason</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>FileError</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(237, 33, 33);&quot;&gt;Provide monthly screenshot (for previous month) to show promotion of BetStop on an outbound SMS.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(237, 33, 33);&quot;&gt;This is required to close the case.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DocumentUploadedBoolean</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Close_Case_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Close_Case_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>UploadDocuments</name>
                    <extensionName>forceContent:fileUpload</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>multiple</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>recordId</name>
                        <value>
                            <elementReference>recordId</elementReference>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Upload Documents</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>I_do_not_send_SMS_messages_to_customers</name>
            <dataType>Boolean</dataType>
            <fieldText>I do not send SMS messages to customers</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Next</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>331</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCase</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>caseUpdates</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>ConfirmFileUpload</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>DocumentCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>ContentDocument</objectType>
    </variables>
    <variables>
        <name>DocumentsUploaded</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>DocumentUploadedBoolean</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>NewCreatedId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ValidationBool</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
</Flow>
