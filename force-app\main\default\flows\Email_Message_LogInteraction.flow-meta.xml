<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Check_is_within_Business_Hours</name>
        <label>Check is within Business Hours</label>
        <locationX>534</locationX>
        <locationY>984</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>GetInteractionTaskRecordtype</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Completed_Status_to_Task</name>
        <label>Assign Completed Status to Task</label>
        <locationX>1722</locationX>
        <locationY>384</locationY>
        <assignmentItems>
            <assignToReference>newTaskId.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Change_task_status_to_Closed</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Grow_Variable_for_Business_Hours_Check</name>
        <label>Assign Protect Variable for Business Hours Check</label>
        <locationX>314</locationX>
        <locationY>600</locationY>
        <assignmentItems>
            <assignToReference>varPremiumCustomerBusinessHoursId</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>National Grow</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Business_Hours</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Protect_Variable_for_Business_Hours_Check</name>
        <label>Assign Protect Variable for Business Hours Check</label>
        <locationX>50</locationX>
        <locationY>600</locationY>
        <assignmentItems>
            <assignToReference>varPremiumCustomerBusinessHoursId</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>National Protect</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Business_Hours</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Get_Default_Premium_Managed_Business_Hours</name>
        <label>Get Default Premium Managed Business Hours</label>
        <locationX>754</locationX>
        <locationY>492</locationY>
        <assignmentItems>
            <assignToReference>varPremiumCustomerBusinessHoursId</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Premium Managed Business Hours</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Business_Hours</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_If_Email_Message_Related_to_Customer_Portfolio</name>
        <label>Check If Email Message Related to Customer Portfolio</label>
        <locationX>534</locationX>
        <locationY>384</locationY>
        <defaultConnector>
            <targetReference>Get_Default_Premium_Managed_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Has_Customer_Portfolio</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Parent.Customer_Portfolio_At_Time__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Portfolio_of_Customer</targetReference>
            </connector>
            <label>Has Customer Portfolio</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Portfolio_of_Customer</name>
        <label>Check Portfolio of Customer</label>
        <locationX>314</locationX>
        <locationY>492</locationY>
        <defaultConnector>
            <targetReference>Get_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Protect</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Parent.Customer_Portfolio_At_Time__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Protect</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Protect_Variable_for_Business_Hours_Check</targetReference>
            </connector>
            <label>Is Protect</label>
        </rules>
        <rules>
            <name>Is_Grow</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Parent.Customer_Portfolio_At_Time__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Grow_Variable_for_Business_Hours_Check</targetReference>
            </connector>
            <label>Is Grow</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_Is_error_for_Locked_Row</name>
        <label>Is error for Locked Row</label>
        <locationX>1326</locationX>
        <locationY>1524</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_Is_locked_row_error</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isLockedRowError</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Create_Interaction_Task_with_Default_Owner</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_2</name>
        <label>Status of New Task</label>
        <locationX>1854</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Outcome_1_of_Decision_2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>newTaskId.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>newTaskId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Completed_Status_to_Task</targetReference>
            </connector>
            <label>Status is not Completed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_error_for_Locked_Row</name>
        <label>Is error for Locked Row</label>
        <locationX>534</locationX>
        <locationY>1416</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Is_locked_row_error</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isLockedRowError</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_CreateInteractionTask</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Owner_of_Case_A_user</name>
        <label>Is Owner of Case A user?</label>
        <locationX>534</locationX>
        <locationY>1200</locationY>
        <defaultConnector>
            <targetReference>Get_System_Admin_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is queue</defaultConnectorLabel>
        <rules>
            <name>Is_User</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Parent.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>005</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CreateInteractionTask</targetReference>
            </connector>
            <label>Is User</label>
        </rules>
    </decisions>
    <description>SBET-1181 - Populate Current Customer Owner on Interaction Tasks
SNSM-10: Include bulk interaction checkbox</description>
    <environments>Default</environments>
    <formulas>
        <name>DirectionFormula</name>
        <dataType>String</dataType>
        <expression>IF({!$Record.Incoming}, &quot;Inbound&quot;,&quot;Outbound&quot;)</expression>
    </formulas>
    <formulas>
        <name>isLockedRowError</name>
        <dataType>Boolean</dataType>
        <expression>CONTAINS({!$Flow.FaultMessage}, &quot;UNABLE_TO_LOCK_ROW&quot;)</expression>
    </formulas>
    <formulas>
        <name>SubjectFormula</name>
        <dataType>String</dataType>
        <expression>&quot;Email - &quot; + {!DirectionFormula} + &quot; - &quot; + TEXT({!$Record.Parent.Closed_Reason__c})</expression>
    </formulas>
    <interviewLabel>Email Message - LogInteraction {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Email Message - LogInteraction</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Copy_1_of_CreateInteractionTask</name>
        <label>CreateInteractionTask (Open)</label>
        <locationX>402</locationX>
        <locationY>1524</locationY>
        <assignRecordIdToReference>newTaskId.Id</assignRecordIdToReference>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bulk_Interactions__c</field>
            <value>
                <elementReference>$Record.Parent.Bulk_Interactions__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Case__c</field>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Parent.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Parent.Case_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <elementReference>DirectionFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner_Role__c</field>
            <value>
                <elementReference>$Record.Parent.Account.Owner.UserRole.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner__c</field>
            <value>
                <elementReference>$Record.Parent.Customer_Owner__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.Parent.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Parent.Reason</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Parent.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>$Record.Parent.Account.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>Check_is_within_Business_Hours.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordCreates>
    <recordCreates>
        <name>Copy_2_of_Create_Interaction_Task_with_Default_Owner</name>
        <label>Create Interaction Task with Default Owner (Open)</label>
        <locationX>1194</locationX>
        <locationY>1632</locationY>
        <assignRecordIdToReference>newTaskId.Id</assignRecordIdToReference>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bulk_Interactions__c</field>
            <value>
                <elementReference>$Record.Parent.Bulk_Interactions__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Case__c</field>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Parent.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Parent.Case_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <elementReference>DirectionFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner_Role__c</field>
            <value>
                <elementReference>$Record.Parent.Account.Owner.UserRole.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner__c</field>
            <value>
                <elementReference>$Record.Parent.Customer_Owner__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_System_Admin_User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Parent.Reason</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Parent.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>$Record.Parent.Account.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>Check_is_within_Business_Hours.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordCreates>
    <recordCreates>
        <name>CreateInteractionTask</name>
        <label>CreateInteractionTask</label>
        <locationX>138</locationX>
        <locationY>1308</locationY>
        <faultConnector>
            <targetReference>Is_error_for_Locked_Row</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bulk_Interactions__c</field>
            <value>
                <elementReference>$Record.Parent.Bulk_Interactions__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Case__c</field>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Parent.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Parent.Case_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <elementReference>DirectionFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner_Role__c</field>
            <value>
                <elementReference>$Record.Parent.Account.Owner.UserRole.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner__c</field>
            <value>
                <elementReference>$Record.Parent.Customer_Owner__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.Parent.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Parent.Reason</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Parent.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>$Record.Parent.Account.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>Check_is_within_Business_Hours.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>CreateInteractionTaskwithDefaultOwner</name>
        <label>Create Interaction Task with Default Owner</label>
        <locationX>930</locationX>
        <locationY>1416</locationY>
        <faultConnector>
            <targetReference>Copy_1_of_Is_error_for_Locked_Row</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bulk_Interactions__c</field>
            <value>
                <elementReference>$Record.Parent.Bulk_Interactions__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Case__c</field>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>$Record.Parent.Customer_Portfolio_At_Time__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>$Record.Parent.Case_Summary__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <elementReference>DirectionFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner_Role__c</field>
            <value>
                <elementReference>$Record.Parent.Account.Owner.UserRole.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner__c</field>
            <value>
                <elementReference>$Record.Parent.Customer_Owner__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_System_Admin_User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>$Record.Parent.Reason</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>SubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>$Record.Parent.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>$Record.Parent.Account.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>Check_is_within_Business_Hours.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Business_Hours</name>
        <label>Get Business Hours</label>
        <locationX>534</locationX>
        <locationY>876</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_is_within_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varPremiumCustomerBusinessHoursId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_System_Admin_User</name>
        <label>Get System Admin User</label>
        <locationX>930</locationX>
        <locationY>1308</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CreateInteractionTaskwithDefaultOwner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>FirstName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>System</stringValue>
            </value>
        </filters>
        <filters>
            <field>LastName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Admin</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetInteractionTaskRecordtype</name>
        <label>GetInteractionTaskRecordtype</label>
        <locationX>534</locationX>
        <locationY>1092</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Owner_of_Case_A_user</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Interaction</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Change_task_status_to_Closed</name>
        <label>Change task status to Closed</label>
        <locationX>1722</locationX>
        <locationY>492</locationY>
        <inputReference>newTaskId</inputReference>
    </recordUpdates>
    <start>
        <locationX>1068</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_If_Customer_has_Opted_Out_of_Emails</targetReference>
        </connector>
        <filterFormula>AND(
NOT(ISNULL({!$Record.ParentId})),
{!$Record.Parent.RecordType.DeveloperName} = &quot;Premium_Managed_Case&quot;
)</filterFormula>
        <object>EmailMessage</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <name>Check_if_task_created_as_open</name>
            <connector>
                <targetReference>Decision_2</targetReference>
            </connector>
            <label>Check if task created as open</label>
            <maxBatchSize>1</maxBatchSize>
            <offsetNumber>30</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <timeSource>RecordTriggerEvent</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Check_If_Customer_has_Opted_Out_of_Emails</name>
        <label>Check If Customer has Opted Out of Emails</label>
        <locationX>534</locationX>
        <locationY>276</locationY>
        <connector>
            <targetReference>Check_If_Email_Message_Related_to_Customer_Portfolio</targetReference>
        </connector>
        <flowName>Customer_Opted_Out_Channel_Check</flowName>
        <inputAssignments>
            <name>EmailMessage</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Parent.AccountId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <triggerOrder>500</triggerOrder>
    <variables>
        <name>newTaskId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Task</objectType>
    </variables>
    <variables>
        <name>varPremiumCustomerBusinessHoursId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
