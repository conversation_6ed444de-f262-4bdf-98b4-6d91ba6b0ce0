<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Redirects user to Case record</description>
        <name>Redirect_to_Case</name>
        <label>Redirect to Case</label>
        <locationX>176</locationX>
        <locationY>458</locationY>
        <actionName>c:navigate</actionName>
        <actionType>component</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>destinationName</name>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>destinationRecordId</name>
            <value>
                <elementReference>Create_New_Case</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>destinationType</name>
            <value>
                <stringValue>record</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>destinationAction</name>
            <value>
                <stringValue>view</stringValue>
            </value>
        </inputParameters>
        <nameSegment>c:navigate</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Flow launched from a button on existing Premium Managed Case to create a New Case with the Case details a new thread can be started</description>
    <environments>Default</environments>
    <interviewLabel>Case: Screen - Create New Premium Managed Case {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case: Screen - Create New Premium Managed Case</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Creates new Case with details from old Case</description>
        <name>Create_New_Case</name>
        <label>Create New Case</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>Redirect_to_Case</targetReference>
        </connector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>Get_Case.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Case_Created_During_Business_Hours__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>Get_Case.Account.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>Get_Case.Description</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Case_Record_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>In Progress</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>Get_Case.Subject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Warning_Message_Acknowledged__c</field>
            <value>
                <elementReference>Get_Case.Warning_Message_Acknowledged__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Warning_Message_Not_Shown__c</field>
            <value>
                <elementReference>Get_Case.Warning_Message_Not_Shown__c</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <description>Gets the Case record the flow was launched from</description>
        <name>Get_Case</name>
        <label>Get Case</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Case_Record_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Id for Premium Managed Case Record Type</description>
        <name>Get_Case_Record_Type</name>
        <label>Get Case Record Type</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_New_Case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithSharing</runInMode>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>Stores recordid pass from the button</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
