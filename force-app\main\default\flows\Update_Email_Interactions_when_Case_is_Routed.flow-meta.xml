<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_New_Owner</name>
        <label>Assign New Owner</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>get_Task.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.UserId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Task.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Case.AccountId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>UpdateTasks</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Are_there_any_tasks</name>
        <label>Are there any tasks?</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Case</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_New_Owner</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Update Email Interactions when Case is Routed {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Email Interactions when Case is Routed</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Case</name>
        <label>Get Case</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_Task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WorkItemId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_Task</name>
        <label>get Task</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Are_there_any_tasks</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Case__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>UpdateTasks</name>
        <label>UpdateTasks</label>
        <locationX>50</locationX>
        <locationY>755</locationY>
        <inputReference>get_Task</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Opened</stringValue>
            </value>
        </filters>
        <filters>
            <field>WorkItemId</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>500</stringValue>
            </value>
        </filters>
        <object>AgentWork</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
