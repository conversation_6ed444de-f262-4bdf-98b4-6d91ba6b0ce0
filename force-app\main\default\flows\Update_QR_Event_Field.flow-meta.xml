<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>This is for SBET 432</description>
    <formulas>
        <name>QREvent</name>
        <dataType>String</dataType>
        <expression>{!$Record.QR_Event__c}</expression>
    </formulas>
    <interviewLabel>Update QR Event Field {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update QR Event Field</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_QR_Event</name>
        <label>Update QR Event</label>
        <locationX>176</locationX>
        <locationY>335</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Sports_Bet_Account_Number__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Sportsbet_Account_Number__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>QR_Event__c</field>
            <value>
                <elementReference>$Record.QR_Event__c</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_QR_Event</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Converted</stringValue>
            </value>
        </filters>
        <object>Lead</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>CustomerQREvent</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
</Flow>
