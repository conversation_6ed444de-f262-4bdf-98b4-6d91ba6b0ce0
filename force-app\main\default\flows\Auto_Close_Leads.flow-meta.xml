<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Closed_Status</name>
        <label>Closed Status</label>
        <locationX>738</locationX>
        <locationY>484</locationY>
        <defaultConnectorLabel>Is Created</defaultConnectorLabel>
        <rules>
            <name>is_Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.QR_Event__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.QR_Opt_In_to_Comms__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_status_auto_closed</targetReference>
            </connector>
            <label>is Closed</label>
        </rules>
    </decisions>
    <description>Where QR_Event__c = is not blank
QR_Opt_In_to_Comms__c = No

Lead should Auto Close with the following fields updated:
Status = Not Converted
Closed Reason - Uncontactable</description>
    <interviewLabel>Auto Close Leads {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Auto Close Leads</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_status_auto_closed</name>
        <label>Update status auto closed</label>
        <locationX>498</locationX>
        <locationY>707</locationY>
        <inputAssignments>
            <field>Not_Converted_Reason__c</field>
            <value>
                <stringValue>Uncontactable</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Not_Converted</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>650</locationX>
        <locationY>48</locationY>
        <connector>
            <targetReference>Closed_Status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>QR_Event__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>QR_Opt_In_to_Comms__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>No</stringValue>
            </value>
        </filters>
        <object>Lead</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
