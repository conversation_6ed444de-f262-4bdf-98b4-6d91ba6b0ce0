<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>57.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>IsTheTaskAnIntroductionTask</name>
        <label>Is the task an Introduction Task</label>
        <locationX>182</locationX>
        <locationY>455</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>decIntroductionTask</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Subject</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Alert: Introduction to Customer</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetCustomerAccount</targetReference>
            </connector>
            <label>Introduction Task</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Update Customer on Task Completion {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Customer on Task Completion</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetCustomerAccount</name>
        <label>Get Customer Account</label>
        <locationX>50</locationX>
        <locationY>575</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Customer_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Populate_Task_Completed_By_field</name>
        <label>Populate Task Completed By field</label>
        <locationX>182</locationX>
        <locationY>335</locationY>
        <connector>
            <targetReference>IsTheTaskAnIntroductionTask</targetReference>
        </connector>
        <inputAssignments>
            <field>Task_Completed_By_User_ID__c</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Customer_Record</name>
        <label>Update Customer Record</label>
        <locationX>50</locationX>
        <locationY>695</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCustomerAccount.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Intro_Task_Completed_By__c</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Populate_Task_Completed_By_field</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </filters>
        <object>Task</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>customerID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>GetCustomerAccount.Id</elementReference>
        </value>
    </variables>
</Flow>
