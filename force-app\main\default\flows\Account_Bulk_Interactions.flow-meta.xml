<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Set_ShowFailures_as_true</name>
        <label>Set ShowFailures as true</label>
        <locationX>50</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>ShowFailures</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Results_Screen</targetReference>
        </connector>
    </assignments>
    <constants>
        <name>BULK_EMAIL_TEMPLATES_FOLDER_NAME</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BulkEmailTemplates</stringValue>
        </value>
    </constants>
    <constants>
        <name>SENDER_EMAIL_ADDRESS</name>
        <dataType>String</dataType>
        <value>
            <stringValue><EMAIL></stringValue>
        </value>
    </constants>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Any_Failures</name>
        <label>Any Failures?</label>
        <locationX>182</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Results_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Launch_Bulk_Interactions_Flow.RecipientsFailOutput</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Launch_Bulk_Interactions_Flow.RecipientsFailOutput</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_ShowFailures_as_true</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Any_Success</name>
        <label>Any Success?</label>
        <locationX>336</locationX>
        <locationY>674</locationY>
        <defaultConnector>
            <targetReference>All_Failures_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No, All Failures</defaultConnectorLabel>
        <rules>
            <name>Yes_Messages_Sent</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Launch_Bulk_Interactions_Flow.RecipientsSuccessOutput</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Launch_Bulk_Interactions_Flow.RecipientsSuccessOutput</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Any_Failures</targetReference>
            </connector>
            <label>Yes, Messages Sent</label>
        </rules>
    </decisions>
    <decisions>
        <name>Found_Account_Records</name>
        <label>Found Account Records?</label>
        <locationX>677</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>No_Customers_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No, none returned</defaultConnectorLabel>
        <rules>
            <name>Yes_found_records</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Selected_Account_Records</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_PersonContactId_List</targetReference>
            </connector>
            <label>Yes, found records</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>BulkInteractionsMessageLimit</name>
        <dataType>Number</dataType>
        <expression>MAX({!$Setup.System_Limits__c.Bulk_Interactions_Limit__c}, 2)</expression>
        <scale>0</scale>
    </formulas>
    <interviewLabel>Customer: Bulk Interactions {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer: Bulk Interactions</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Contacts</name>
        <label>Get Contacts</label>
        <locationX>336</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Launch_Bulk_Interactions_Flow</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>No_Customers_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>Get_PersonContactId_List</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <limit>
            <elementReference>BulkInteractionsMessageLimit</elementReference>
        </limit>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Selected_Account_Records</name>
        <label>Get Selected Account Records</label>
        <locationX>677</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Found_Account_Records</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>No_Customers_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>ids</elementReference>
            </value>
        </filters>
        <filters>
            <field>PersonContactId</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <limit>
            <elementReference>BulkInteractionsMessageLimit</elementReference>
        </limit>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>All_Failures_Screen</name>
        <label>All Failures Screen</label>
        <locationX>490</locationX>
        <locationY>782</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>NoSuccessDisplay</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;There was no message sent.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_Customers_Screen</name>
        <label>No Customers Screen</label>
        <locationX>1018</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>NoRecordsDisplay</name>
            <fieldText>&lt;p&gt;No Customers were received as input. &lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Please select Customer records that are Person Accounts to proceed.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Results_Screen</name>
        <label>Results Screen</label>
        <locationX>182</locationX>
        <locationY>1082</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessResultsDisplay</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;Your {!Launch_Bulk_Interactions_Flow.MessageTypeOutput} messages have been successfully queued to send to the following recipients!:&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>SuccessfulRecipientsDataTableEmail</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Launch_Bulk_Interactions_Flow.RecipientsSuccessOutput</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-27b1&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Email&quot;,&quot;guid&quot;:&quot;column-31ca&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Standard Email&quot;,&quot;type&quot;:&quot;email&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Launch_Bulk_Interactions_Flow.MessageTypeOutput</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Email</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>SuccessfulRecipientsDataTableSMS</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Launch_Bulk_Interactions_Flow.RecipientsSuccessOutput</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-4be4&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;MobilePhone&quot;,&quot;guid&quot;:&quot;column-82a6&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Standard Mobile&quot;,&quot;type&quot;:&quot;phone&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Launch_Bulk_Interactions_Flow.MessageTypeOutput</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>SMS</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>FailuresDisplay</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(255, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;Please note&lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;: {!Launch_Bulk_Interactions_Flow.MessageTypeOutput} messages were &lt;/span&gt;&lt;strong style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;NOT &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;sent to the following recipients:&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowFailures</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>FailuresDataTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Contact</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Launch_Bulk_Interactions_Flow.RecipientsFailOutput</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-cfbf&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Full Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowFailures</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>551</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Selected_Account_Records</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Launch_Bulk_Interactions_Flow</name>
        <label>Launch Bulk Interactions Flow</label>
        <locationX>336</locationX>
        <locationY>566</locationY>
        <connector>
            <targetReference>Any_Success</targetReference>
        </connector>
        <flowName>Bulk_Interactions_Send_Bulk_Email_SMS_Screen_Flow</flowName>
        <inputAssignments>
            <name>RecipientsInput</name>
            <value>
                <elementReference>Get_Contacts</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <transforms>
        <name>Get_PersonContactId_List</name>
        <label>Get PersonContactId List</label>
        <locationX>336</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>Get_Contacts</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>Get_Selected_Account_Records[$EachItem].PersonContactId</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <variables>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ShowFailures</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
</Flow>
