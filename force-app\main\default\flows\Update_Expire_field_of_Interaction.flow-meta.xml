<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>53.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>TaskRecordName</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Task</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Checks for the conditions to set Expired Field to True</description>
        <name>Expired_Field_Conditions_Satisfied</name>
        <label>Expired Field Conditions Satisfied?</label>
        <locationX>182</locationX>
        <locationY>311</locationY>
        <defaultConnector>
            <targetReference>Update_Expire_Field_False</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Expired_TRUE</name>
            <conditionLogic>(1 AND 2) OR  (1 AND 3 AND 4)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>TaskRecordName</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Expired</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Expired__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Expire_field_True</targetReference>
            </connector>
            <label>Expired TRUE</label>
        </rules>
    </decisions>
    <description>Flow updates Expire field of Tasks</description>
    <interviewLabel>Update Expire field of Interaction {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Expire field of Interaction</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Expire_Field_False</name>
        <label>Update Expire Field False</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>Expired__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Expire_field_True</name>
        <label>Update Expire field True</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>Expired__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Expired_Field_Conditions_Satisfied</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
