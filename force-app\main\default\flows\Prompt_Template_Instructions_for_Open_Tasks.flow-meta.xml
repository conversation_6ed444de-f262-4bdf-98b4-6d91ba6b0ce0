<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Sends message that there was a fault in the flow and it couldn&apos;t find the records</description>
        <name>Flow_Fault_Prompt_Instructions</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Flow Fault Prompt Instructions</label>
        <locationX>578</locationX>
        <locationY>242</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>There was a fault in the flow when retrieving records
</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sends message that no Tasks were found to Prompt Template</description>
        <name>No_Open_Tasks</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>No Open Tasks</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Customers had no interactions in the last 90 days
</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <description>Sends a summary of the customers open Tasks to Prompt Template</description>
        <name>Send_Open_Tasks_to_Prompt_Template</name>
        <elementSubtype>AddPromptInstructions</elementSubtype>
        <label>Send Open Tasks to Prompt Template</label>
        <locationX>138</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>$Output.Prompt</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Status: {!Loop_Open_Tasks.Status}
Subject: {!Loop_Open_Tasks.Subject}
</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Open_Tasks</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Are there any open tasks?</description>
        <name>Open_Tasks_Found</name>
        <label>Open Tasks Found?</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>No_Open_Tasks</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Tasks</defaultConnectorLabel>
        <rules>
            <name>Open_Tasks</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer_Open_Tasks</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Customer_Open_Tasks</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Open_Tasks</targetReference>
            </connector>
            <label>Open Tasks</label>
        </rules>
    </decisions>
    <description>Used by Customer Summary Prompt Template to generate additional prompt instructions related to the customers Open Tasks</description>
    <environments>Default</environments>
    <formulas>
        <description>Stores date 14 days ago</description>
        <name>formulaDateLast14Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 14</expression>
    </formulas>
    <formulas>
        <name>formulaDateLast30Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 30</expression>
    </formulas>
    <formulas>
        <description>Stores date 365 days ago</description>
        <name>formulaDateLast365Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() - 365</expression>
    </formulas>
    <formulas>
        <description>Stores date 14 days in future</description>
        <name>formulaDateNext14Days</name>
        <dataType>Date</dataType>
        <expression>TODAY() + 14</expression>
    </formulas>
    <formulas>
        <description>Stores value for today&apos;s date</description>
        <name>formulaDateToday</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <interviewLabel>Prompt Template: Instructions for Open Tasks {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Prompt Template: Instructions for Open Tasks</label>
    <loops>
        <description>Loop through all Open customers Task records</description>
        <name>Loop_Open_Tasks</name>
        <label>Loop Open Tasks</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_Customer_Open_Tasks</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Send_Open_Tasks_to_Prompt_Template</targetReference>
        </nextValueConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>PromptFlow</processType>
    <recordLookups>
        <description>Gets any task for the Customer that has status of Open</description>
        <name>Get_Customer_Open_Tasks</name>
        <label>Get Customer Open Tasks</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Open_Tasks_Found</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Flow_Fault_Prompt_Instructions</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhoId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Input.objectToSummarize.PersonContactId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </filters>
        <filters>
            <field>CreatedDate</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>formulaDateLast30Days</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <capabilityTypes>
            <name>PromptTemplateType://einstein_gpt__recordSummary</name>
            <capabilityName>PromptTemplateType://einstein_gpt__recordSummary</capabilityName>
            <inputs>
                <name>objectToSummarize</name>
                <capabilityInputName>objectToSummarize</capabilityInputName>
                <dataType>SOBJECT://Account</dataType>
                <isCollection>false</isCollection>
            </inputs>
        </capabilityTypes>
        <connector>
            <targetReference>Get_Customer_Open_Tasks</targetReference>
        </connector>
        <triggerType>Capability</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>currentItem_Filter_Attendee_Last_2_Weeks</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Attendee__c</objectType>
    </variables>
</Flow>
