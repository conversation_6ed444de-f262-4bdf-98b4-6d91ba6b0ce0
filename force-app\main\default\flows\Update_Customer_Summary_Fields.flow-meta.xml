<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Is_value_new</name>
        <label>Is value new</label>
        <locationX>182</locationX>
        <locationY>287</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Value_is_different</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Customer_Summary__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>$Record__Prior.Customer_Summary__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Date_and_User</targetReference>
            </connector>
            <label>Value is different</label>
        </rules>
    </decisions>
    <description>Flow that is triggered by changes tot he Customer Summary field and populates the Updated By name and date</description>
    <environments>Default</environments>
    <formulas>
        <name>today</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <name>userFullName</name>
        <dataType>String</dataType>
        <expression>{!$User.FirstName}+ &quot; &quot;+{!$User.LastName}</expression>
    </formulas>
    <interviewLabel>Update Customer Summary Fields {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Customer Summary Fields</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Date_and_User</name>
        <label>Update Date and User</label>
        <locationX>50</locationX>
        <locationY>395</locationY>
        <inputAssignments>
            <field>Date_of_Summary_Update__c</field>
            <value>
                <elementReference>today</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Summary_Updated_By__c</field>
            <value>
                <elementReference>userFullName</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_value_new</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Customer_Summary__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Account</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
