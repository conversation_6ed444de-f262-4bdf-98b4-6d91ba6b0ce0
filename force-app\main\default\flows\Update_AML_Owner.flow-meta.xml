<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assign Running User Id to varOwnerId</description>
        <name>Assign_Owner</name>
        <label>Assign Owner</label>
        <locationX>182</locationX>
        <locationY>287</locationY>
        <assignmentItems>
            <assignToReference>varOwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>SMR_Case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign Running User FirstName and LastName to varOwnerName</description>
        <name>Assign_OwnerName</name>
        <label>Assign OwnerName</label>
        <locationX>50</locationX>
        <locationY>503</locationY>
        <assignmentItems>
            <assignToReference>varOwnerName</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$User.FirstName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varOwnerName</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>varSpace</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varOwnerName</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$User.LastName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Case</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Check if Case RecordType is SMR</description>
        <name>SMR_Case</name>
        <label>SMR Case?</label>
        <locationX>182</locationX>
        <locationY>395</locationY>
        <defaultConnector>
            <targetReference>Update_Case</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes_SMR</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Standard_SMR</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fast_Track_SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_OwnerName</targetReference>
            </connector>
            <label>Yes SMR</label>
        </rules>
    </decisions>
    <description>- Update Case Owner when an analyst changes the status of AML cases.
- Added SMR Prepared By field update
  - Populates the name of the user who moves the status of SMR from New to In Progress

11/1/2025
- Added Customer_Risk_Assessment Record type in the entry criteria.</description>
    <environments>Default</environments>
    <interviewLabel>Update AML Owner {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Update Owner</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Update Case
SMR_Prepared_By__c field is used for reporting and is not visible on page layouts.</description>
        <name>Update_Case</name>
        <label>Update Case</label>
        <locationX>182</locationX>
        <locationY>695</locationY>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>varOwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>SMR_Prepared_By__c</field>
            <value>
                <elementReference>varOwnerName</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assign_Owner</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterFormula>AND(
OR(
{!$Record.RecordType.DeveloperName} = &quot;ECDD_Case&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Transaction_Monitoring&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Non_Transaction_Monitoring&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Customer_Risk_Assessment&quot;,
{!$Record.RecordType.DeveloperName} = &quot;QA_QC&quot;,
CONTAINS({!$Record.RecordType.DeveloperName}, &quot;SMR&quot;)
),
OR(
TEXT({!$Record.Status}) = &quot;In Progress&quot;,
TEXT({!$Record.Status}) = &quot;Retrieving ECDD data&quot;,
TEXT({!$Record.Status}) = &quot;QA in progress&quot;
)
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>varOwnerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varOwnerName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varSpace</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue> </stringValue>
        </value>
    </variables>
</Flow>
