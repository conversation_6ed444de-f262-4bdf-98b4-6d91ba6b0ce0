<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>AssignRTid</name>
        <label>AssignRTid</label>
        <locationX>264</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>ECDDRTId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Through_Recordtypes.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_Recordtypes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignRTidFstSMR</name>
        <label>AssignRTid</label>
        <locationX>1320</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>FstSMRTId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Through_Recordtypes.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_Recordtypes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignRTidNonTM</name>
        <label>AssignRTid</label>
        <locationX>792</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>NonTMRTId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Through_Recordtypes.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_Recordtypes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignRTidStdSMR</name>
        <label>AssignRTid</label>
        <locationX>1056</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>StdSMRTId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Through_Recordtypes.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_Recordtypes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignRTidTM</name>
        <label>AssignRTid</label>
        <locationX>528</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>TMRTid</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Through_Recordtypes.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_Recordtypes</targetReference>
        </connector>
    </assignments>
    <collectionProcessors>
        <name>ECCD_cases_Filter</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>ECCD cases Filter</label>
        <locationX>176</locationX>
        <locationY>842</locationY>
        <assignNextValueToReference>currentItem_ECCD_cases_Filter</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_Cases</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_ECCD_cases_Filter.RecordTypeId</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>ECDDRTId</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>TM_Cases_Fitler</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>FastTrack_SMR_Cases_Filter</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>FastTrack SMR Cases Filter</label>
        <locationX>176</locationX>
        <locationY>1274</locationY>
        <assignNextValueToReference>currentItem_FastTrack_SMR_Cases_Filter</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_Cases</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_FastTrack_SMR_Cases_Filter.RecordTypeId</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>FstSMRTId</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Display_Case_Lists</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>Non_TM_Cases_Fitler</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Non TM Cases Fitler</label>
        <locationX>176</locationX>
        <locationY>1058</locationY>
        <assignNextValueToReference>currentItem_FastTrack_SMR_Cases_Filter</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_Cases</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_FastTrack_SMR_Cases_Filter.RecordTypeId</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>NonTMRTId</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Standard_SMR_Cases_Filter</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>Standard_SMR_Cases_Filter</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Standard SMR Cases Filter</label>
        <locationX>176</locationX>
        <locationY>1166</locationY>
        <assignNextValueToReference>currentItem_FastTrack_SMR_Cases_Filter</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_Cases</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_FastTrack_SMR_Cases_Filter.RecordTypeId</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>StdSMRTId</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>FastTrack_SMR_Cases_Filter</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>TM_Cases_Fitler</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>TM Cases Fitler</label>
        <locationX>176</locationX>
        <locationY>950</locationY>
        <assignNextValueToReference>currentItem_FastTrack_SMR_Cases_Filter</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_Cases</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_FastTrack_SMR_Cases_Filter.RecordTypeId</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>TMRTid</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Non_TM_Cases_Fitler</targetReference>
        </connector>
    </collectionProcessors>
    <decisions>
        <name>What_Recordtype</name>
        <label>What Recordtype?</label>
        <locationX>924</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Loop_Through_Recordtypes</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default</defaultConnectorLabel>
        <rules>
            <name>IsECDD</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Through_Recordtypes.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignRTid</targetReference>
            </connector>
            <label>IsECDD</label>
        </rules>
        <rules>
            <name>Is_TM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Through_Recordtypes.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignRTidTM</targetReference>
            </connector>
            <label>Is TM</label>
        </rules>
        <rules>
            <name>Is_NTM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Through_Recordtypes.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non_Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignRTidNonTM</targetReference>
            </connector>
            <label>Is NTM</label>
        </rules>
        <rules>
            <name>Standard_SMR</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Through_Recordtypes.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Standard_SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignRTidStdSMR</targetReference>
            </connector>
            <label>Standard SMR</label>
        </rules>
        <rules>
            <name>Fast_Track_SMR</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Through_Recordtypes.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fast_Track_SMR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignRTidFstSMR</targetReference>
            </connector>
            <label>Fast Track SMR</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Get AML RecordTypes {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML cases List</label>
    <loops>
        <name>Loop_Through_Recordtypes</name>
        <label>Loop Through Recordtypes</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_Recordtypes</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>What_Recordtype</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>ECCD_cases_Filter</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Cases</name>
        <label>Get Cases</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Recordtypes</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Recordtypes</name>
        <label>Get Recordtypes</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Through_Recordtypes</targetReference>
        </connector>
        <filterLogic>(1 OR 2 OR 4 OR 5) AND 3</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>Contains</operator>
            <value>
                <stringValue>Transaction_Monitoring</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Fast_Track_SMR</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Standard_SMR</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Display_Case_Lists</name>
        <label>Display Case Lists</label>
        <locationX>176</locationX>
        <locationY>1382</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>TMCasesTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Non Transaction Monitoring Cases</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Non_TM_Cases_Fitler</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Case_Number_Hyperlink__c&quot;,&quot;guid&quot;:&quot;column-0342&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Case Number Hyperlink&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Status&quot;,&quot;guid&quot;:&quot;column-b273&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Origin&quot;,&quot;guid&quot;:&quot;column-b146&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Case Origin&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;CreatedDate&quot;,&quot;guid&quot;:&quot;column-ec9d&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Created Date&quot;,&quot;type&quot;:&quot;customDateTime&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>TMCaseTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Transaction Monitoring Cases</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>TM_Cases_Fitler</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Case_Number_Hyperlink__c&quot;,&quot;guid&quot;:&quot;column-276a&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Case Number Hyperlink&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Status&quot;,&quot;guid&quot;:&quot;column-3311&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Origin&quot;,&quot;guid&quot;:&quot;column-cfc4&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Case Origin&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Insert_Date__c&quot;,&quot;guid&quot;:&quot;column-ad8f&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Insert Date&quot;,&quot;type&quot;:&quot;date-local&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>ECDDCasesTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>ECDD Cases</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>ECCD_cases_Filter</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Case_Number_Hyperlink__c&quot;,&quot;guid&quot;:&quot;column-39ba&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Case Number Hyperlink&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Status&quot;,&quot;guid&quot;:&quot;column-b0c2&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Origin&quot;,&quot;guid&quot;:&quot;column-2382&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Origin&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Case Origin&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;ClosedDate&quot;,&quot;guid&quot;:&quot;column-db1f&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Closed Date&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Closed Date&quot;,&quot;type&quot;:&quot;customDateTime&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>StandardSMRCases</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Standard SMR Cases</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Standard_SMR_Cases_Filter</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Case_Number_Hyperlink__c&quot;,&quot;guid&quot;:&quot;column-f408&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Case Number Hyperlink&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Status&quot;,&quot;guid&quot;:&quot;column-c4bc&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;CreatedDate&quot;,&quot;guid&quot;:&quot;column-309d&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Created Date&quot;,&quot;type&quot;:&quot;customDateTime&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>FastTrackSMRCases</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>FastTrack SMR Cases</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>FastTrack_SMR_Cases_Filter</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Case_Number_Hyperlink__c&quot;,&quot;guid&quot;:&quot;column-f479&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Case Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Case Number Hyperlink&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Status&quot;,&quot;guid&quot;:&quot;column-84bc&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;CreatedDate&quot;,&quot;guid&quot;:&quot;column-a636&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Created Date&quot;,&quot;type&quot;:&quot;customDateTime&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Cases</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>currentItem_ECCD_cases_Filter</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>currentItem_ECDD_Cases_0</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>currentItem_FastTrack_SMR_Cases_Filter</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>ECDDRTId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>FstSMRTId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>NonTMRTId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>StdSMRTId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>TMRTid</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
