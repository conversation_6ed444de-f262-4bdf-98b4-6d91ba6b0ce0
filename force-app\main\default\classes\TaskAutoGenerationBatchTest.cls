/**
 * @description       : Test class for TaskAutoGenerationBatch
 * <AUTHOR> <PERSON> (8Squad)
 * @group             : 
 * @last modified on  : 05-23-2025
 * @last modified by  : jam<PERSON><PERSON><PERSON>@gmail.com
**/
@isTest
private class TaskAutoGenerationBatchTest {
    @testSetup
    static void setupTestData() {
        // Create custom setting with generosity limit
        Process_Automation_Control_Panel__c settings = new Process_Automation_Control_Panel__c(
            Task_Batch_Customer_Generosity_Limit__c = 25.0
        );
        insert settings;
        
        // Create a test user to own the accounts
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            Username = 'test.user.' + DateTime.now().getTime() + '@example.com',
            Alias = 'tuser',
            ProfileId = p.Id,
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            TimeZoneSidKey = 'America/Los_Angeles'
        );
        insert testUser;
        
        // Get Person Account RecordTypeId
        Id personAccountRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId();
        
        System.runAs(testUser) {
            // Create test accounts
            List<Account> testAccounts = new List<Account>();
            
            // 10 accounts with low generosity
            for (Integer i = 0; i < 10; i++) {
                Account acc = new Account(
                    FirstName = 'Test',
                    LastName = 'Generosity' + i,
                    RecordTypeId = personAccountRecordTypeId,
                    Elite_Status__c = 'Elite',
                    Portfolio__c = 'Protect',
                    Generosity_TPV_Last_12_months__c = 15,
                    Sports_Bet_Account_Number__c = 'GEN' + i
                );
                testAccounts.add(acc);
            }
            
            // 10 accounts with Ladbrokes as main bookie
            for (Integer i = 0; i < 10; i++) {
                Account acc = new Account(
                    FirstName = 'Test',
                    LastName = 'MainBookie' + i,
                    RecordTypeId = personAccountRecordTypeId,
                    Elite_Status__c = 'Elite',
                    Portfolio__c = 'Grow',
                    Main_Bookie__c = 'Ladbrokes',
                    Sports_Bet_Account_Number__c = 'MB' + i
                );
                testAccounts.add(acc);
            }
            
            // 10 accounts that bet with Ladbrokes
            for (Integer i = 0; i < 10; i++) {
                Account acc = new Account(
                    FirstName = 'Test',
                    LastName = 'Competitor' + i,
                    RecordTypeId = personAccountRecordTypeId,
                    Elite_Status__c = 'Elite',
                    Portfolio__c = 'Protect',
                    Bets_With_Competitor__c = 'Ladbrokes',
                    Sports_Bet_Account_Number__c = 'COMP' + i
                );
                testAccounts.add(acc);
            }
            
            insert testAccounts;
        }
    }
    
    @isTest
    static void testBatchJobCreatesCorrectTasks() {
        // Execute the batch job
        Test.startTest();
        TaskAutoGenerationBatch batchJob = new TaskAutoGenerationBatch();
        Database.executeBatch(batchJob);
        Test.stopTest();
        
        // Verify tasks were created correctly
        List<Task> allTasks = [SELECT Id, Subject, Description FROM Task];
        
        // Count tasks by type
        Integer generosityTasks = 0;
        Integer mainBookieTasks = 0;
        Integer competitorTasks = 0;
        
        for (Task t : allTasks) {
            if (t.Description != null && t.Description.contains('generosity percent of 15% is below the 25% limit')) {
                generosityTasks++;
            } else if (t.Description != null && t.Description == 'Customer\'s main bookie is Ladbrokes') {
                mainBookieTasks++;
            } else if (t.Description != null && t.Description == 'Customer bets with competitors: Ladbrokes.') {
                competitorTasks++;
            }
        }
        
        System.assertEquals(10, generosityTasks, 'Should have created 10 tasks for low generosity');
        System.assertEquals(10, mainBookieTasks, 'Should have created 10 tasks for main bookie');
        System.assertEquals(10, competitorTasks, 'Should have created 10 tasks for competitor betting');
        
        // Verify total number of tasks
        System.assertEquals(30, allTasks.size(), 'Should have created 30 tasks in total');
    }
    
    @isTest
    static void testScheduler() {
        // Test the scheduler
        Test.startTest();
        TaskAutoGenerationScheduler scheduler = new TaskAutoGenerationScheduler();
        String jobId = System.schedule('Test Job', '0 0 0 * * ?', scheduler);
        Test.stopTest();
        
        // Verify the job was scheduled
        CronTrigger ct = [SELECT Id, CronExpression FROM CronTrigger WHERE Id = :jobId];
        System.assertEquals('0 0 0 * * ?', ct.CronExpression, 'Job should be scheduled to run at midnight');
    }
}