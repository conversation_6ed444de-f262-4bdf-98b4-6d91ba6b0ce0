<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>update_capacity</name>
        <label>update capacity</label>
        <locationX>308</locationX>
        <locationY>503</locationY>
        <assignmentItems>
            <assignToReference>$Record.CapacityPercentage</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>60.0</numberValue>
            </value>
        </assignmentItems>
    </assignments>
    <decisions>
        <name>Voicemail</name>
        <label>Voicemail</label>
        <locationX>176</locationX>
        <locationY>395</locationY>
        <defaultConnector>
            <targetReference>update_capacity</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsVoicemail</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetVoicecall.CallOrigin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Voicemail</stringValue>
                </rightValue>
            </conditions>
            <label>Voicemail</label>
        </rules>
    </decisions>
    <description>Overwrites the 100% capacity for voicecall with 60% instead</description>
    <environments>Default</environments>
    <interviewLabel>AgentWork Before Modify Voicecall {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AgentWork Before Update Voicecall Capacity</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetVoicecall</name>
        <label>GetVoicecall</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Voicemail</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WorkItemId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetVoicecall</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WorkItemId</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>0LQ</stringValue>
            </value>
        </filters>
        <object>AgentWork</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
