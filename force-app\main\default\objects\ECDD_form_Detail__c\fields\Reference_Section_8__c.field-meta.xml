<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Reference_Section_8__c</fullName>
    <label>Reference Section 8</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Adverse Media</fullName>
                <default>false</default>
                <label>Adverse Media</label>
            </value>
            <value>
                <fullName>Jurisdiction Risk</fullName>
                <default>false</default>
                <label>Jurisdiction Risk</label>
            </value>
            <value>
                <fullName>PEP Status</fullName>
                <default>false</default>
                <label>PEP Status</label>
            </value>
            <value>
                <fullName>Profession Risk</fullName>
                <default>false</default>
                <label>Profession Risk</label>
            </value>
            <value>
                <fullName>SoW/SoF</fullName>
                <default>false</default>
                <label>SoW/SoF</label>
            </value>
            <value>
                <fullName>Business Information</fullName>
                <default>false</default>
                <label>Business Information</label>
            </value>
            <value>
                <fullName>Profession Information</fullName>
                <default>false</default>
                <label>Profession Information</label>
            </value>
            <value>
                <fullName>Property Information</fullName>
                <default>false</default>
                <label>Property Information</label>
            </value>
            <value>
                <fullName>Other</fullName>
                <default>false</default>
                <label>Other</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
