/**
 * @description       : <PERSON><PERSON><PERSON> to delete tasks created by the current user today
 * <AUTHOR> <PERSON> (8Squad)
 * @last modified on  : 05-23-2025
**/

// Get the current user's ID
Id currentUserId = UserInfo.getUserId();
System.debug('Current user ID: ' + currentUserId);

// Query for task IDs created by the current user today
List<Id> taskIds = new List<Id>();
for (Task t : [
    SELECT Id 
    FROM Task 
    WHERE CreatedById = :currentUserId 
    AND CreatedDate = TODAY
    AND Subject LIKE 'Customer Eligible for Value:%'
]) {
    taskIds.add(t.Id);
}

System.debug('Found ' + taskIds.size() + ' tasks to delete');

// Delete the tasks in batches of 9000 to avoid governor limits
Integer batchSize = 9000;
Integer totalDeleted = 0;
Integer totalBatches = (taskIds.size() / batchSize) + (Math.mod(taskIds.size(), batchSize) > 0 ? 1 : 0);

System.debug('Will process deletion in ' + totalBatches + ' batches');

for (Integer i = 0; i < totalBatches; i++) {
    Integer startIndex = i * batchSize;
    Integer endIndex = Math.min((i + 1) * batchSize, taskIds.size());
    
    if (startIndex >= taskIds.size()) {
        break;
    }
    
    List<Id> batchIds = new List<Id>();
    for (Integer j = startIndex; j < endIndex; j++) {
        batchIds.add(taskIds[j]);
    }
    
    System.debug('Processing batch ' + (i + 1) + ' of ' + totalBatches + ' (' + batchIds.size() + ' tasks)');
    
    try {
        delete [SELECT Id FROM Task WHERE Id IN :batchIds];
        totalDeleted += batchIds.size();
        System.debug('Successfully deleted batch ' + (i + 1));
    } catch (Exception e) {
        System.debug('Error deleting batch ' + (i + 1) + ': ' + e.getMessage());
    }
}

System.debug('Total tasks deleted: ' + totalDeleted);