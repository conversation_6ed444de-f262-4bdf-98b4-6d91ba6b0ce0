<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>SendEmail</name>
        <label>SendEmail</label>
        <locationX>1579</locationX>
        <locationY>168</locationY>
        <actionName>Case.Event_and_Hospitality_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>CreateCase</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Event_and_Hospitality_Case_Email_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>SendEmail1</name>
        <label>SendEmail1</label>
        <locationX>1577</locationX>
        <locationY>332</locationY>
        <actionName>Case.Event_and_Hospitality_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>CreateCase_0</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Event_and_Hospitality_Case_Email_Notification</nameSegment>
    </actionCalls>
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>GetRecordCount</name>
        <label>GetRecordCount</label>
        <locationX>827</locationX>
        <locationY>69</locationY>
        <assignmentItems>
            <assignToReference>OfferAcceptedRecordCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>GetOfferAceptedAttendees</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OfferPendingRecordCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>GetOfferPendingAttendees</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>CheckAttendeesCollection</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>CheckAttendeesCollection</name>
        <label>CheckAttendeesCollection</label>
        <locationX>1003</locationX>
        <locationY>71</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>If_not_null</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>OfferAcceptedRecordCount</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>OfferPendingRecordCount</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetRecordTypeId</targetReference>
            </connector>
            <label>If not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Owner_on_Delegation</name>
        <label>Is Owner on Delegation</label>
        <locationX>1270</locationX>
        <locationY>257</locationY>
        <defaultConnector>
            <targetReference>CreateCase</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is Not On Delegation</defaultConnectorLabel>
        <rules>
            <name>Is_On_Delegation</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetUser.Delegate_Assignment_Start_Date__c</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetUser.Delegate_Assignment_End_Date__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetUser.Delegate_Assignment_User_ID__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CreateCase_0</targetReference>
            </connector>
            <label>Is On Delegation</label>
        </rules>
    </decisions>
    <description>Updates made for SBET-180 - input parameter SObjectID</description>
    <environments>Default</environments>
    <interviewLabel>Create Case to RM if Customer is self Excluded and has future event {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Create Case to RM if Customer is self Excluded and has future event</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>CreateCase</name>
        <label>CreateCase</label>
        <locationX>1465</locationX>
        <locationY>168</locationY>
        <connector>
            <targetReference>SendEmail</targetReference>
        </connector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>inputAccount.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>inputAccount.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <stringValue>Self Exclusion Alert</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>inputAccount.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>GetRecordTypeId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Follow up Event and Hospitality Offered for Customer is self excluded</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <stringValue>Event and Hospitality</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>CreateCase_0</name>
        <label>CreateCase</label>
        <locationX>1463</locationX>
        <locationY>332</locationY>
        <connector>
            <targetReference>SendEmail1</targetReference>
        </connector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>inputAccount.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>inputAccount.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin</field>
            <value>
                <stringValue>Self Exclusion Alert</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>GetUser.Delegate_Assignment_User_ID__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Priority</field>
            <value>
                <stringValue>High</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>GetRecordTypeId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <stringValue>Follow up Event and Hospitality Offered for Customer is self excluded</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <stringValue>Event and Hospitality</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>GetOfferAceptedAttendees</name>
        <label>GetOfferAceptedAttendees</label>
        <locationX>497</locationX>
        <locationY>69</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetOfferPendingAttendees</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputAccount.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Is_Future_Open_Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Accepted</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetOfferPendingAttendees</name>
        <label>GetOfferPendingAttendees</label>
        <locationX>649</locationX>
        <locationY>69</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetRecordCount</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputAccount.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Is_Future_Open_Campaign__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Offer Pending</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attendee__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetRecordTypeId</name>
        <label>GetRecordTypeId</label>
        <locationX>1170</locationX>
        <locationY>70</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetUser</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Generic_Case</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetUser</name>
        <label>GetUser</label>
        <locationX>1170</locationX>
        <locationY>257</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Owner_on_Delegation</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputAccount.OwnerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>GetOfferAceptedAttendees</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>CaseDescription</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;Customer is self excluded. Please contact the customer for the below event and hospitality offered&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>inputAccount</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>OfferAcceptedRecordCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>OfferPendingRecordCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
</Flow>
