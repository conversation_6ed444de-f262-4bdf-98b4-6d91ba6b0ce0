<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Updates the Owner of the record to the user of the Assigned To field when changed that field is changed</description>
        <name>Assign_Owner_to_Assigned_To</name>
        <label>Assign Owner to Assigned To</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <assignmentItems>
            <assignToReference>$Record.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Assigned_To__c</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <description>Used to update the Owner of the record to the user in the Assigned To field when that field is updated</description>
    <environments>Default</environments>
    <interviewLabel>Rebase Review: Update Owner to Assigned To {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rebase Review: Update Owner to Assigned To</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assign_Owner_to_Assigned_To</targetReference>
        </connector>
        <filterFormula>ISCHANGED({!$Record.Assigned_To__c})</filterFormula>
        <object>Rebase_Review__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
