<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Job_Title__c</fullName>
    <label>Job Title</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Business Owner</fullName>
                <default>false</default>
                <label>Business Owner</label>
            </value>
            <value>
                <fullName>CEO</fullName>
                <default>false</default>
                <label>CEO</label>
            </value>
            <value>
                <fullName>Clerical &amp; Administrative</fullName>
                <default>false</default>
                <label>Clerical &amp; Administrative</label>
            </value>
            <value>
                <fullName>Dentist</fullName>
                <default>false</default>
                <label>Dentist</label>
            </value>
            <value>
                <fullName>Director</fullName>
                <default>false</default>
                <label>Director</label>
            </value>
            <value>
                <fullName>Doctor</fullName>
                <default>false</default>
                <label>Doctor</label>
            </value>
            <value>
                <fullName>Tradesman</fullName>
                <default>false</default>
                <label>Tradesman</label>
            </value>
            <value>
                <fullName>Manager</fullName>
                <default>false</default>
                <label>Manager</label>
            </value>
            <value>
                <fullName>Other</fullName>
                <default>false</default>
                <label>Other</label>
            </value>
            <value>
                <fullName>Sales</fullName>
                <default>false</default>
                <label>Sales</label>
            </value>
            <value>
                <fullName>Technician</fullName>
                <default>false</default>
                <label>Technician</label>
            </value>
            <value>
                <fullName>Unknown</fullName>
                <default>false</default>
                <label>Unknown</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
