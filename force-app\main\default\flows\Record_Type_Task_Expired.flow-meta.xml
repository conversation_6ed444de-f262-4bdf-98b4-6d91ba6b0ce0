<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>54.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Expired_or_Not</name>
        <label>Expired or Not</label>
        <locationX>519</locationX>
        <locationY>346</locationY>
        <defaultConnector>
            <targetReference>Not_Expired</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Expired</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Expired</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Expired_Status</targetReference>
            </connector>
            <label>Expired</label>
        </rules>
    </decisions>
    <description>Users can&apos;t change the Status to any if its already Expired. Only System Admin can change the Status.</description>
    <interviewLabel>Record Type: Task (Expired) {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Record Type: Task (Expired)</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Expired_Status</name>
        <label>Expired Status</label>
        <locationX>738</locationX>
        <locationY>347</locationY>
        <inputAssignments>
            <field>Expired_Status__c</field>
            <value>
                <stringValue>Expired</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Not_Expired</name>
        <label>Not Expired</label>
        <locationX>292</locationX>
        <locationY>343</locationY>
        <inputAssignments>
            <field>Expired_Status__c</field>
            <value>
                <stringValue>Not Expired</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>401</locationX>
        <locationY>48</locationY>
        <connector>
            <targetReference>Expired_or_Not</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
