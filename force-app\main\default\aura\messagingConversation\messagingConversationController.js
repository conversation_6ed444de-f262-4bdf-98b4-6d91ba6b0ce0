({
    doInit : function(cmp, evt, hlp) {
        cmp.set('v.showSpinner', true);
        let forHighlight = false, componentInitializing = true;
        hlp.getTabInfoDelayed(cmp, hlp, forHighlight, componentInitializing);
    },

    handleLoadFinished : function(cmp, evt, hlp) {
        console.log('AURA STOPPING SPINNER AS WE HAVE RECIEVED THE the event for handleLoadFinished');
                                        
        if (cmp.isValid())
            cmp.set('v.showSpinner', false);
        else
            console.error('[AURA]messagingConversationController.handleLoadFinished() cmp is invalid');
    },

    onAgentSend : function(cmp, evt, hlp) {
        if (cmp.isValid()) {
            var recordId = evt.getParam('recordId');
            var content = evt.getParam('content');
            var name = evt.getParam('name');
            var type = evt.getParam('type');
            var timestamp = evt.getParam('timestamp');

            var recId = cmp.get('v.recordId');
            
            if (cmp.isValid())
                cmp.find('lwc').conversationAgentSend(recordId, type, name, content, timestamp);
        } else {
            console.error('[AURA]messagingConversationController.onAgentSend() cmp is invalid');
        }
    },

    onNewMessage : function(cmp, evt, hlp) {
        if (cmp.isValid()) {
            var recordId = evt.getParam('recordId');
            var content = evt.getParam('content');
            var name = evt.getParam('name');
            var type = evt.getParam('type');
            var timestamp = evt.getParam('timestamp');

            // console.error(
            //     'ENDUSER NEW MESSAGE AURA onNewMessage(lightning:conversationNewMessage) =>\r\n',
            //     'FOR RECORDID:', recordId, '\r\n',
            //     'CONTENT:', content, '\r\n',
            //     'NAME:', name,'\r\n',
            //     'TYPE:', type,'\r\n',
            //     'TIMESTAMP:', timestamp, '\r\n',
            //     '\r\n\r\nCOMPONENT DESTROYED? ', cmp.isValid()
            // );

        
            cmp.find('lwc').conversationNewMessage(recordId, type, name, content, timestamp);
            
            var recId = cmp.get('v.recordId');
            if (recId && recId.substring(0, 15) === recordId) {
                hlp.doTabHighlight(cmp, hlp);
            }
        } else {
            console.error('[AURA]messagingConversationController.onNewMessage() cmp is invalid');
        }
    },

    onCustomEvent : function(cmp, evt, hlp) {
        if (cmp.isValid()) {
            var recordId = evt.getParam('recordId');
            var type = evt.getParam('type');
            var data = evt.getParam('data');
            var name = evt.getParam('name');

            // console.log(
            //     'AURA onCustomEvent(lightning:conversationCustomEvent) =>',
            //     'recordId:', recordId,
            //     'type:', type,
            //     'data:', data
            // );
            // console.log('name'+name);
            cmp.find('lwc').conversationCustomEvent(recordId, type, data);
        } else {
            console.error('[AURA]messagingConversationController.onCustomEvent() cmp is invalid');
        }
    },

    onChatEnded : function(cmp, evt, hlp) {
        if (cmp.isValid()) {
            var recordId = evt.getParam('recordId');

        // console.log(
        //     'AURA onChatEnded(lightning:conversationChatEnded) =>',
        //     'recordId:', recordId
        // );
       
            cmp.find('lwc').conversationChatEnded(recordId);
        } else {
            console.error('[AURA]messagingConversationController.onChatEnded() cmp is invalid');
        }
    },

    onTabFocused : function(cmp, evt, hlp) {
        if (cmp.isValid()) {
            var tabId = cmp.get('v.tabId');
            var parentTabId = cmp.get('v.parentTabId');

            if (tabId) {
                var focusedTabId = evt.getParam('currentTabId');

                if (tabId === focusedTabId || parentTabId === focusedTabId) {
                    hlp.unhighlightTab(cmp, focusedTabId);
                }
            }
        } else {
            console.error('[AURA]messagingConversationController.onTabFocused() cmp is invalid');
        }
    },

    //Event which is dispatched from conversationMessageLWC - seems to be mostly intervalic
    //Ends up invoking conversationLog() on conversationMessageLWC which adds messages on the window via _chatLogMessages avenue
    handleChatLogFired : function(cmp, evt, hlp) {
        if (cmp.isValid()) {
            var newconversationAPI = cmp.find('newconversationAPI');
            var recordId = evt.getParam('recordId');
            // console.log('recordId'+recordId);
            if (newconversationAPI && recordId)
            {
            // console.log('AURA handleChatLogFired() recordId', recordId);
            hlp.getChatLog(cmp, recordId);
            }
        } else {
            console.error('[AURA]messagingConversationController.handleChatLogFired() cmp is invalid');
        }
    },

    handleMsgSendFired : function(cmp, evt, hlp) {
        if (cmp.isValid()) {
            var newconversationAPI = cmp.find('newconversationAPI');
            var recordId = evt.getParam('recordId');
        
            if (newconversationAPI && recordId)
            {
            var recordId = evt.getParam('recordId');
            var message = evt.getParam('message');
            // console.log('AURA handleMsgSendFired() recordId', recordId, 'message', message);
            hlp.sendChatMessage(cmp, recordId, message);
            }
        } else {
            console.error('[AURA]messagingConversationController.handleMsgSendFired() cmp is invalid');
        }
    },

    handleEndChatFired : function(cmp, evt, hlp) {
        if (cmp.isValid()) {
            var recordId = evt.getParam('recordId');

            // console.log('AURA handleEndChatFired() recordId', recordId);

            hlp.endChat(cmp, recordId);
        } else {
            console.error('[AURA]messagingConversationController.handleEndChatFired() cmp is invalid');
        }
    },

    onTabClosed : function(cmp, evt, hlp) {
        if (cmp.isValid()) {
            let closedTabId = evt.getParam('tabId');
        
            let thisCmpTabId = cmp.get('v.tabId');
            let thisCmpParentTabId = cmp.get('v.parentTabId');
            let matchd = (closedTabId == thisCmpTabId || closedTabId == thisCmpParentTabId);// || true);
            console.error('AURA messagingConversation[onTabClosed] closedTabId', closedTabId, 'thisCmpTabId', thisCmpTabId, 'thisCmpParentTabId', thisCmpParentTabId, 'matched tab/parent?', matchd);
            if (matchd) {
                let msRecId = cmp.get('v.recordId');
                console.error('MY TAB ' + closedTabId + ' IS CLOSING!!! CALLING ENDCHAT FOR recordId', msRecId);

                hlp.endChat(cmp, msRecId);
            }
        } else {
            console.error('[AURA]messagingConversationController.onTabClosed() cmp is invalid');
        }
    }
})