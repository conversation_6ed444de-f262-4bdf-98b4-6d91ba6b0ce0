<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Set_old_Service_message_to_Inactive</name>
        <label>Set old Service message to Inactive</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>Get_Active_Service_Outage_Message.Active__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>De_activate_old_record</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>There are active Service Outage Messages</description>
        <name>Active_Messages</name>
        <label>Active Messages?</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>Not Active</defaultConnectorLabel>
        <rules>
            <name>Active</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Active_Service_Outage_Message</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Active_Service_Outage_Message</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_old_Service_message_to_Inactive</targetReference>
            </connector>
            <label>Active</label>
        </rules>
    </decisions>
    <description>Used to provide custom validation to ensure only one Service Outage Message is Active at any given time. Display custom error message to the user</description>
    <environments>Default</environments>
    <interviewLabel>Contact Centre Service Outage Message Active Validation {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Contact Centre Service Outage Message Active Validation</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the Service Outage Message that is active</description>
        <name>Get_Active_Service_Outage_Message</name>
        <label>Get Active Service Outage Message</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Active_Messages</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Active__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Message_End_Date__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Service_Outage_Message__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>De_activate_old_record</name>
        <label>De-activate old record</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <inputReference>Get_Active_Service_Outage_Message</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Active_Service_Outage_Message</targetReference>
        </connector>
        <filterFormula>OR(
    AND(
        ISNEW(),
        {!$Record.Active__c}
    ),
    AND(
        ISCHANGED({!$Record.Active__c}),
        {!$Record.Active__c}
    ),
    AND(
        ISCHANGED({!$Record.Message_End_Date__c}),
        {!$Record.Active__c}
    )
)</filterFormula>
        <object>Service_Outage_Message__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
