<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Check_Availability_for_Case_Owner_Last_24_Hours</name>
        <label>Check Availability for Case Owner Last 24 Hours</label>
        <locationX>3482</locationX>
        <locationY>4010</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Agent_Last_24_Hr_Available</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Check_Relationship_Manager_Team_Member_Available</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_related_Cases_Last_24_Hours.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Check_National_Grow_Availability</name>
        <label>Check National Grow Availability</label>
        <locationX>1370</locationX>
        <locationY>3578</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_National_Grow_Team_Member_Available</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue2</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Checks if the customer owner as the primary relationship manager is available</description>
        <name>Check_Relationship_Manager_Available</name>
        <label>Check Relationship Manager Available</label>
        <locationX>3504</locationX>
        <locationY>3254</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_Relationship_Manager_Available</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Check_Relationship_Manager_Team_Member_Available</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Check_Relationship_Manager_Team_Member_Available</name>
        <label>Check Relationship Manager Team Member Available</label>
        <locationX>4318</locationX>
        <locationY>4778</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_a_Team_Member_Available</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_Protect_Grow_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Protect_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Check_State_Team_Member_Available</name>
        <label>Check State Team Member Available</label>
        <locationX>974</locationX>
        <locationY>3254</locationY>
        <actionName>checkAvailabilityForRouting</actionName>
        <actionType>checkAvailabilityForRouting</actionType>
        <connector>
            <targetReference>Is_a_State_Team_Member_available</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_National_Grow_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>selectedOutputs</name>
            <value>
                <stringValue>GET_ALL</stringValue>
            </value>
        </inputParameters>
        <nameSegment>checkAvailabilityForRouting</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined Business Hours</description>
        <name>Check_Within_Business_Hours</name>
        <label>Check Within Business Hours</label>
        <locationX>3075</locationX>
        <locationY>974</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Update_Customer_Business_Hours_and_Portfolio_on_Case_Record</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>BusinessHours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Apex action to check if the current date/time is within defined Premium Business Hours</description>
        <name>Check_Within_Business_Hours_No_Customer</name>
        <label>Check Within Business Hours No Customer</label>
        <locationX>7420</locationX>
        <locationY>1664</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Business_Hours_No_Customer</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Premium_Business_Hours_No_Customer.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Check_Within_Grow_OOH_AM_Business_Hours</name>
        <label>Check Within Grow OOH AM Business Hours</label>
        <locationX>2915</locationX>
        <locationY>1730</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Grow_OOH_AM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Grow_OOH_AM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Check_Within_Grow_OOH_PM_Business_Hours</name>
        <label>Check Within Grow OOH PM Business Hours</label>
        <locationX>3113</locationX>
        <locationY>2054</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Grow_OOH_PM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Grow_OOH_PM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Check_Within_Protect_OOH_AM_Business_Hours</name>
        <label>Check Within Protect OOH AM Business Hours</label>
        <locationX>3707</locationX>
        <locationY>1730</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Protect_OOH_AM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Protect_OOH_AM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Check_Within_Protect_OOH_PM_Business_Hours</name>
        <label>Check Within Protect OOH PM Business Hours</label>
        <locationX>3905</locationX>
        <locationY>2054</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Within_Protect_OOH_PM_Business_Hours</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Protect_OOH_PM_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to primary relationship manager for customer</description>
        <name>Copy_1_of_Route_to_Relationship_Manager</name>
        <label>Route to Relationship Manager</label>
        <locationX>50</locationX>
        <locationY>3146</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_State_Grow_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Protect_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Find_Customer</name>
        <label>Find Customer</label>
        <locationX>7420</locationX>
        <locationY>566</locationY>
        <actionName>findMatchingIndividuals</actionName>
        <actionType>findMatchingIndividuals</actionType>
        <connector>
            <targetReference>Get_Customer_from_Email</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>searchTerm</name>
            <value>
                <stringValue>{!varSuppliedEmail}</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>searchFields</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>searchObject</name>
            <value>
                <stringValue>Contact</stringValue>
            </value>
        </inputParameters>
        <nameSegment>findMatchingIndividuals</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Sends email to customer when they are not a managed customer advising the to contact customer service</description>
        <name>Not_Managed_Contact_Customer_Service_Email</name>
        <label>Not Managed Contact Customer Service Email</label>
        <locationX>6342</locationX>
        <locationY>782</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Get_Premium_Unmanaged_Queue</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>Get_Customer.PersonContactId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailTemplateId</name>
            <value>
                <elementReference>Get_Unmanaged_Email_Template.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Route_to_Events_Experience_Queue</name>
        <label>Route to Events &amp; Experience Queue</label>
        <locationX>5814</locationX>
        <locationY>782</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Unmanaged_Email_Template</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_Events_Experience_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Route_to_National_Grow_Queue</name>
        <label>Route to National Grow Queue</label>
        <locationX>1106</locationX>
        <locationY>3794</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue2</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to either Protect or Grow National Queue, based on Portfolio of customer</description>
        <name>Route_to_National_Protect_Grow_Queue</name>
        <label>Route to National Protect/Grow Queue</label>
        <locationX>3245</locationX>
        <locationY>2378</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Protect_Grow_Queue1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to the national protect queue for any relationship manager to action</description>
        <name>Route_to_National_Protect_Queue</name>
        <label>Route to National Protect Queue</label>
        <locationX>5998</locationX>
        <locationY>5630</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Protect_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to the national protect queue for any relationship manager to action</description>
        <name>Route_to_National_Protect_Queue2</name>
        <label>Route to National Protect Queue</label>
        <locationX>1634</locationX>
        <locationY>3794</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Protect_Grow_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_National_Protect_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to either Protect or Grow National Queue, based on Portfolio of customer</description>
        <name>Route_to_Protect_Grow_Queue</name>
        <label>Route to National Protect/Grow Queue</label>
        <locationX>4582</locationX>
        <locationY>4994</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_Protect_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Case is routed to primary relationship manager for customer</description>
        <name>Route_to_Relationship_Manager</name>
        <label>Route to Relationship Manager</label>
        <locationX>2690</locationX>
        <locationY>3470</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Email_Message</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_Protect_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_Customer.Owner.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Route_to_RM_Last_24_Hours</name>
        <label>Route to RM Last 24 Hours</label>
        <locationX>3218</locationX>
        <locationY>4226</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Check_Relationship_Manager_Team_Member_Available</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Protect_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>Get_related_Cases_Last_24_Hours.OwnerId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentRequired</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Route_to_State_Grow</name>
        <label>Route to State Grow</label>
        <locationX>578</locationX>
        <locationY>3470</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_National_Grow_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Route_to_State_Grow_Protect_Queue</name>
        <label>Route to State Queue</label>
        <locationX>4054</locationX>
        <locationY>4994</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_Protect_Grow_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Protect_Grow_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Route_to_State_Queue</name>
        <label>Route to State Queue</label>
        <locationX>2717</locationX>
        <locationY>2054</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_State_Protect_Grow_Queue1.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Route_to_Unmanaged_Queue</name>
        <label>Route to Unmanaged Queue</label>
        <locationX>6342</locationX>
        <locationY>998</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>input_record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>QueueBased</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueId</name>
            <value>
                <elementReference>Get_Premium_Unmanaged_Queue.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>queueLabel</name>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>copilotId</name>
        </inputParameters>
        <inputParameters>
            <name>copilotLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isQueueVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Sends an email to the customer to advise they have emailed outside of business hours</description>
        <name>Send_Customer_Outside_Business_Hours_Email</name>
        <label>Send Customer Outside Business Hours Email</label>
        <locationX>3608</locationX>
        <locationY>1406</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Is_Customer_Protect_or_Grow1</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Is_Customer_Protect_Grow_and_or_Event_Attendee</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>Get_Customer.PersonContactId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailTemplateId</name>
            <value>
                <elementReference>Get_OOH_Email_Template.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Sends an email to the customer to advise they have emailed outside of business hours</description>
        <name>Send_Customer_Outside_Business_Hours_Email_No_Customer</name>
        <label>Send Customer Outside Business Hours Email No Customer</label>
        <locationX>7508</locationX>
        <locationY>1988</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>input_record.SuppliedEmail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Auto Reply - Sportsbet Account Management Team</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <stringValue>Hi,  Thanks for contacting the Sportsbet Account Management team, we are unable to respond to your email at this time.  Our contact center service team is available via phone on 1800 990 907 or live chat on the sportsbet.com.au website from 6am till midnight AEST.  Thank you</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Business_Hours</name>
        <label>Assign Business Hours</label>
        <locationX>2811</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>BusinessHours</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_National_Grow_Business_Hours</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Within_Business_Hours</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Assign_Business_Hours</name>
        <label>Assign Business Hours</label>
        <locationX>3075</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>BusinessHours</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_National_Protect_Business_Hours</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Within_Business_Hours</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Count_of_Customer_Records</name>
        <label>Count of Customer Records</label>
        <locationX>7420</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>numberCustomerRecords</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Customer_from_Email</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Count_Check</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_the_email_address</name>
        <label>Set the email address</label>
        <locationX>7420</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>varSuppliedEmail</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input_record.SuppliedEmail</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Find_Customer</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_varAccountId</name>
        <label>Set varAccountId</label>
        <locationX>7266</locationX>
        <locationY>1106</locationY>
        <assignmentItems>
            <assignToReference>varAccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Call_Get_Customer_Subflow.varAccountId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_AccountId</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Agent_Last_24_Hr_Available</name>
        <label>Agent Last 24 Hr Available?</label>
        <locationX>3482</locationX>
        <locationY>4118</locationY>
        <defaultConnector>
            <targetReference>Check_Relationship_Manager_Team_Member_Available</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No, Agent unavailable</defaultConnectorLabel>
        <rules>
            <name>Yes_Agent_available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Availability_for_Case_Owner_Last_24_Hours.onlineAgentsCount</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_RM_Last_24_Hours</targetReference>
            </connector>
            <label>Yes, Agent available</label>
        </rules>
    </decisions>
    <decisions>
        <name>Case_is_Assigned_to_User_or_Queue</name>
        <label>Case is Assigned to User or Queue?</label>
        <locationX>3834</locationX>
        <locationY>3902</locationY>
        <defaultConnector>
            <targetReference>Check_Relationship_Manager_Team_Member_Available</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Assigned to Queue</defaultConnectorLabel>
        <rules>
            <name>Assigned_to_User</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_related_Cases_Last_24_Hours.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>005</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Availability_for_Case_Owner_Last_24_Hours</targetReference>
            </connector>
            <label>Assigned to User</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_AccountId</name>
        <label>Check AccountId</label>
        <locationX>7266</locationX>
        <locationY>1214</locationY>
        <defaultConnector>
            <targetReference>Get_Premium_Business_Hours_No_Customer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No AccountId</defaultConnectorLabel>
        <rules>
            <name>Account_Id_present</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>varAccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>varAccountId</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Get_Customer</targetReference>
            </connector>
            <label>Account Id present?</label>
        </rules>
    </decisions>
    <decisions>
        <name>Count_Check</name>
        <label>Count Check?</label>
        <locationX>7420</locationX>
        <locationY>890</locationY>
        <defaultConnector>
            <targetReference>Get_Premium_Business_Hours_No_Customer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Only 1</defaultConnectorLabel>
        <rules>
            <name>Greater_than_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>numberCustomerRecords</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Call_Get_Customer_Subflow</targetReference>
            </connector>
            <label>Greater than 1</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is there a customer match?</description>
        <name>Customer_Match</name>
        <label>Customer Match</label>
        <locationX>5998</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Set_the_email_address</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Match</defaultConnectorLabel>
        <rules>
            <name>Customer_Matched</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_Customer_Managed</targetReference>
            </connector>
            <label>Customer Matched</label>
        </rules>
    </decisions>
    <decisions>
        <name>Did_Customer_Email_in_Last_24_Hours</name>
        <label>Did Customer Email in Last 24 Hours?</label>
        <locationX>4318</locationX>
        <locationY>3578</locationY>
        <defaultConnector>
            <targetReference>Check_Relationship_Manager_Team_Member_Available</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Over 24 Hours</defaultConnectorLabel>
        <rules>
            <name>Last_24_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Email_Message.CreatedDate</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>formulaYesterdayDateTime</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_related_Cases_Last_24_Hours</targetReference>
            </connector>
            <label>Last 24 Hours</label>
        </rules>
    </decisions>
    <decisions>
        <name>Found_Case_Last_24_Hours</name>
        <label>Found Case Last 24 Hours?</label>
        <locationX>4098</locationX>
        <locationY>3794</locationY>
        <defaultConnector>
            <targetReference>Check_Relationship_Manager_Team_Member_Available</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Cases found</defaultConnectorLabel>
        <rules>
            <name>Yes_Case_in_last_24_Hrs</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_related_Cases_Last_24_Hours</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Case_is_Assigned_to_User_or_Queue</targetReference>
            </connector>
            <label>Yes, Case in last 24 Hrs</label>
        </rules>
    </decisions>
    <decisions>
        <name>Inside_Business_Hours1</name>
        <label>Inside Business Hours?</label>
        <locationX>512</locationX>
        <locationY>3038</locationY>
        <defaultConnector>
            <targetReference>Get_State_Grow_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No, Outside Business Hours</defaultConnectorLabel>
        <rules>
            <name>Yes_Inside_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Route_to_Relationship_Manager</targetReference>
            </connector>
            <label>Yes, Inside Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_a_State_Team_Member_available</name>
        <label>Is a State Team Member available?</label>
        <locationX>974</locationX>
        <locationY>3362</locationY>
        <defaultConnector>
            <targetReference>Get_National_Grow_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes_State_available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_State_Team_Member_Available.onlineAgentsCount</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_State_Grow</targetReference>
            </connector>
            <label>Yes, State available</label>
        </rules>
    </decisions>
    <decisions>
        <description>If a Team Member is available in State Protect/Grow, route there; otherwise, route to National Protect/Grow</description>
        <name>Is_a_Team_Member_Available</name>
        <label>Is a Team Member Available?</label>
        <locationX>4318</locationX>
        <locationY>4886</locationY>
        <defaultConnector>
            <targetReference>Route_to_Protect_Grow_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No, None Available</defaultConnectorLabel>
        <rules>
            <name>Team_Member_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Relationship_Manager_Team_Member_Available.onlineAgentsCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_State_Grow_Protect_Queue</targetReference>
            </connector>
            <label>Yes, Team Member Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is this customer a Premium managed Customer</description>
        <name>Is_Customer_Managed</name>
        <label>Is Customer Managed</label>
        <locationX>4576</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Is_Event_Attendee_Today</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Managed_Customer</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Elite_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Elite</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Customer.Next_Event_Attendance__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_Customer_Protect_or_Grow</targetReference>
            </connector>
            <label>Managed Customer</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check customer portfolio for protect/grow, and if customer is an event attendee for today</description>
        <name>Is_Customer_Protect_Grow_and_or_Event_Attendee</name>
        <label>Is Customer Protect/Grow and/or Event Attendee?</label>
        <locationX>3075</locationX>
        <locationY>2930</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>None</defaultConnectorLabel>
        <rules>
            <name>Grow_Event_Attendee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Grow</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Customer.Next_Event_Attendance__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Inside_Business_Hours1</targetReference>
            </connector>
            <label>Grow &amp; Event Attendee</label>
        </rules>
        <rules>
            <name>Protect_Grow</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Protect</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Grow</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Protect_Grow_Queue</targetReference>
            </connector>
            <label>Protect/Grow</label>
        </rules>
    </decisions>
    <decisions>
        <description>If customer has portfolio as null, treat as unmanaged</description>
        <name>Is_Customer_Protect_or_Grow</name>
        <label>Is Customer Protect or Grow?</label>
        <locationX>3075</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Unmanaged_Email_Template</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>None</defaultConnectorLabel>
        <rules>
            <name>Grow</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Grow</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_National_Grow_Business_Hours</targetReference>
            </connector>
            <label>Grow</label>
        </rules>
        <rules>
            <name>Protect</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Protect</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_National_Protect_Business_Hours</targetReference>
            </connector>
            <label>Protect</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Customer_Protect_or_Grow1</name>
        <label>Is Customer Protect or Grow?</label>
        <locationX>3608</locationX>
        <locationY>1514</locationY>
        <defaultConnector>
            <targetReference>Is_Customer_Protect_Grow_and_or_Event_Attendee</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Grow1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Grow</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Grow_OOH_AM_Business_Hours</targetReference>
            </connector>
            <label>Grow</label>
        </rules>
        <rules>
            <name>Protect1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Protect</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Protect_OOH_AM_Business_Hours</targetReference>
            </connector>
            <label>Protect</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Event_Attendee_Today</name>
        <label>Is Event Attendee Today?</label>
        <locationX>6078</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>Get_Unmanaged_Email_Template</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer.Customer_Attendee__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Events_Experience_Queue</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_National_Grow_Team_Member_Available</name>
        <label>Is National Grow Team Member Available?</label>
        <locationX>1370</locationX>
        <locationY>3686</locationY>
        <defaultConnector>
            <targetReference>Route_to_National_Protect_Queue2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes_Team_Member_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_National_Grow_Availability.onlineAgentsCount</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_National_Grow_Queue</targetReference>
            </connector>
            <label>Yes, Team Member Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is the contact owner of the record available for customer</description>
        <name>Is_Relationship_Manager_Available</name>
        <label>Is Relationship Manager Available?</label>
        <locationX>3504</locationX>
        <locationY>3362</locationY>
        <defaultConnector>
            <targetReference>Get_Email_Message</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No, RM Unavailable</defaultConnectorLabel>
        <rules>
            <name>Yes_RM_Available</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Relationship_Manager_Available.onlineAgentsCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_Relationship_Manager</targetReference>
            </connector>
            <label>Yes, RM Available</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this email sent inside business hours?</description>
        <name>Within_Business_Hours</name>
        <label>Within Business Hours</label>
        <locationX>3075</locationX>
        <locationY>1190</locationY>
        <defaultConnector>
            <targetReference>Get_OOH_Email_Template</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_Customer_Protect_Grow_and_or_Event_Attendee</targetReference>
            </connector>
            <label>Inside Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this email sent inside business hours?</description>
        <name>Within_Business_Hours_No_Customer</name>
        <label>Within Business Hours No Customer</label>
        <locationX>7420</locationX>
        <locationY>1772</locationY>
        <defaultConnector>
            <targetReference>Get_OOH_Email_Template_No_Customer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Business_Hours_No_Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Business_Hours_No_Customer.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_to_National_Protect_Queue</targetReference>
            </connector>
            <label>Inside Business Hours No Customer</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this SMS sent inside OOH AM business hours?</description>
        <name>Within_Grow_OOH_AM_Business_Hours</name>
        <label>Within Grow OOH AM Business Hours</label>
        <locationX>2915</locationX>
        <locationY>1838</locationY>
        <defaultConnector>
            <targetReference>Get_Grow_OOH_PM_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Grow OOH AM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Grow_OOH_AM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Grow_OOH_AM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_State_Protect_Grow_Queue1</targetReference>
            </connector>
            <label>Inside Grow OOH AM Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this sent inside OOH PM business hours?</description>
        <name>Within_Grow_OOH_PM_Business_Hours</name>
        <label>Within Grow OOH PM Business Hours</label>
        <locationX>3113</locationX>
        <locationY>2162</locationY>
        <defaultConnector>
            <targetReference>Get_National_Protect_Grow_Queue1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Grow OOH PM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Grow_OOH_PM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Grow_OOH_PM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Get_State_Protect_Grow_Queue1</targetReference>
            </connector>
            <label>Inside Grow OOH PM Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this sent inside OOH AM business hours?</description>
        <name>Within_Protect_OOH_AM_Business_Hours</name>
        <label>Within Protect OOH AM Business Hours</label>
        <locationX>3707</locationX>
        <locationY>1838</locationY>
        <defaultConnector>
            <targetReference>Get_Protect_OOH_PM_Business_Hours</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Protect OOH AM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Protect_OOH_AM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Protect_OOH_AM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Get_State_Protect_Grow_Queue1</targetReference>
            </connector>
            <label>Inside Protect OOH AM Business Hours</label>
        </rules>
    </decisions>
    <decisions>
        <description>Was this sent inside OOH PM business hours?</description>
        <name>Within_Protect_OOH_PM_Business_Hours</name>
        <label>Within Protect OOH PM Business Hours</label>
        <locationX>3905</locationX>
        <locationY>2162</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_National_Protect_Grow_Queue1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Outside Protect OOH PM Business Hours</defaultConnectorLabel>
        <rules>
            <name>Inside_Protect_OOH_PM_Business_Hours</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_Within_Protect_OOH_PM_Business_Hours.isWithin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Get_State_Protect_Grow_Queue1</targetReference>
            </connector>
            <label>Inside Protect OOH PM Business Hours</label>
        </rules>
    </decisions>
    <description>Omni-Channel Flow with logic for routing Email-to-Case to the appropriate relationship manager
SBET-1250 - Route Unmanaged Customers to E&amp;E Queue
Updated for Protect and Grow changes
Added logic to call subflow - Get Customer Subflow
Added business hours for grow and protect and customer portfolio</description>
    <environments>Default</environments>
    <formulas>
        <description>Formula outputs the DeveloperName of the Queue based on the Role of customers relationship manager to assign to the state queue</description>
        <name>formulaCalculateStateQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Portfolio__c},
    &apos;Grow&apos;, {!GetStateGrowQueue},
    &apos;Protect&apos;, {!GetStateProtectQueue},
    &apos;National_Protect_Queue&apos;
)</expression>
    </formulas>
    <formulas>
        <description>*NOTE: Deprecated* Formula outputs the DeveloperName of the Queue based on the Role of customers relationship manager</description>
        <name>formulaCalculateTeamQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},
    &apos;Premium_State_Manager_NSW&apos;, &apos;Premium_Managed_NSW&apos;,
    &apos;Premium_Team_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Premium_Team_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,
    &apos;Premium_State_Manager_VIC&apos;, &apos;Premium_Managed_VIC&apos;,
    &apos;Premium_Team_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Premium_Team_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,
    &apos;Premium_State_Manager_QLD&apos;, &apos;Premium_Managed_QLD&apos;,
    &apos;Premium_Team_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD&apos;,
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD&apos;,
    &apos;Premium_Team_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;Premium_Managed_General&apos;
)</expression>
    </formulas>
    <formulas>
        <name>formulaCustomerId</name>
        <dataType>String</dataType>
        <expression>IF(
    ISBLANK({!input_record.AccountId}),
    {!varAccountId},
    {!input_record.AccountId}
)</expression>
    </formulas>
    <formulas>
        <description>Calculates the day of the week from the Next Business Day output of the Check Within Business Hours Apex Action</description>
        <name>formulaDayOfWeek</name>
        <dataType>String</dataType>
        <expression>CASE(
  MOD(DATEVALUE({!Check_Within_Business_Hours.nextStartDate}) - DATE(1900, 1, 8), 7),
  0, &quot;Monday&quot;,
  1, &quot;Tuesday&quot;,
  2, &quot;Wednesday&quot;,
  3, &quot;Thursday&quot;,
  4, &quot;Friday&quot;,
  5, &quot;Saturday&quot;,
  &quot;Sunday&quot;
)</expression>
    </formulas>
    <formulas>
        <name>formulaYesterdayDateTime</name>
        <dataType>DateTime</dataType>
        <expression>{!$Flow.CurrentDateTime} - 1</expression>
    </formulas>
    <formulas>
        <name>GetStateGrowQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,    
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A&apos;,    
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B&apos;,
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;National_Grow_Queue&apos;
)</expression>
    </formulas>
    <formulas>
        <name>GetStateProtectQueue</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!Get_Customer.Owner.UserRole.DeveloperName},   
    &apos;Relationship_Manager_NSW_A&apos;, &apos;Premium_Managed_NSW_A&apos;,
    &apos;Relationship_Manager_NSW_B&apos;, &apos;Premium_Managed_NSW_B&apos;,    
    &apos;Relationship_Manager_VIC_A&apos;, &apos;Premium_Managed_VIC_A&apos;,
    &apos;Relationship_Manager_VIC_B&apos;, &apos;Premium_Managed_VIC_B&apos;,    
    &apos;Relationship_Manager_QLD_A&apos;, &apos;Premium_Managed_QLD_A&apos;,    
    &apos;Relationship_Manager_QLD_B&apos;, &apos;Premium_Managed_QLD_B&apos;,
    &apos;Relationship_Manager_WA&apos;, &apos;Premium_Managed_WA&apos;,
    &apos;National_Protect_Queue&apos;
)</expression>
    </formulas>
    <formulas>
        <name>NationalProtectGrowQueueName</name>
        <dataType>String</dataType>
        <expression>CASE({!Get_Customer.Portfolio__c},
&apos;Protect&apos;, &apos;National_Protect_Queue&apos;,
&apos;Grow&apos;, &apos;National_Grow_Queue&apos;,
&apos;National_Protect_Queue&apos;)</expression>
    </formulas>
    <interviewLabel>Case: Premium Email-to-Case Routing {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case: Premium Email-to-Case Routing</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>RoutingFlow</processType>
    <recordLookups>
        <description>Gets the Customer record related to the Case</description>
        <name>Get_Customer</name>
        <label>Get Customer</label>
        <locationX>5998</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Customer_Match</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Route_to_National_Protect_Queue</targetReference>
        </faultConnector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaCustomerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Customer record related to the Case using Email</description>
        <name>Get_Customer_from_Email</name>
        <label>Get Customer from Email</label>
        <locationX>7420</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Count_of_Customer_Records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>PersonContactId</field>
            <operator>In</operator>
            <value>
                <elementReference>Find_Customer.contactIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Email_Message</name>
        <label>Get Email Message</label>
        <locationX>4318</locationX>
        <locationY>3470</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Did_Customer_Email_in_Last_24_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ParentId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>input_record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Incoming</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailMessage</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Events_Experience_Queue</name>
        <label>Get Events &amp; Experience Queue</label>
        <locationX>5814</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_Events_Experience_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Events_Experience_General</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Grow_OOH_AM_Business_Hours</name>
        <label>Get Grow OOH AM Business Hours</label>
        <locationX>2915</locationX>
        <locationY>1622</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Grow_OOH_AM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Grow Out of hours AM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Grow_OOH_PM_Business_Hours</name>
        <label>Get Grow OOH PM Business Hours</label>
        <locationX>3113</locationX>
        <locationY>1946</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Grow_OOH_PM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Grow Out of hours PM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the National Grow Business Hours record</description>
        <name>Get_National_Grow_Business_Hours</name>
        <label>Get National Grow Business Hours</label>
        <locationX>2811</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National Grow</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_National_Grow_Queue</name>
        <label>Get National Grow Queue</label>
        <locationX>1370</locationX>
        <locationY>3470</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_National_Grow_Availability</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National_Grow_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the National Protect Business Hours record</description>
        <name>Get_National_Protect_Business_Hours</name>
        <label>Get National Protect Business Hours</label>
        <locationX>3075</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Copy_1_of_Assign_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National Protect</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets either Protect or Grow National Queue, based on Customer Portfolio</description>
        <name>Get_National_Protect_Grow_Queue1</name>
        <label>Get National Protect/Grow Queue</label>
        <locationX>3245</locationX>
        <locationY>2270</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_National_Protect_Grow_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>NationalProtectGrowQueueName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>This queue will act as the fallback queue for the premium team</description>
        <name>Get_National_Protect_Queue</name>
        <label>Get National Protect Queue</label>
        <locationX>5998</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>National_Protect_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_OOH_Email_Template</name>
        <label>Get OOH Email Template</label>
        <locationX>3608</locationX>
        <locationY>1298</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Send_Customer_Outside_Business_Hours_Email</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium Out of Hours Response Email</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_OOH_Email_Template_No_Customer</name>
        <label>Get OOH Email Template No Customer</label>
        <locationX>7508</locationX>
        <locationY>1880</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Send_Customer_Outside_Business_Hours_Email_No_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium Out of Hours Response Email</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Premium Business Hours record</description>
        <name>Get_Premium_Business_Hours_No_Customer</name>
        <label>Get Premium Business Hours No Customer</label>
        <locationX>7420</locationX>
        <locationY>1556</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Business_Hours_No_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium Managed Business Hours</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Premium_Unmanaged_Queue</name>
        <label>Get Premium Unmanaged Queue</label>
        <locationX>6342</locationX>
        <locationY>890</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_Unmanaged_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Unmanaged</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets either Protect or Grow National Queue, based on Customer Portfolio</description>
        <name>Get_Protect_Grow_Queue</name>
        <label>Get National Protect/Grow Queue</label>
        <locationX>3504</locationX>
        <locationY>3038</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_State_Protect_Grow_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>NationalProtectGrowQueueName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Protect_OOH_AM_Business_Hours</name>
        <label>Get Protect OOH AM Business Hours</label>
        <locationX>3707</locationX>
        <locationY>1622</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Protect_OOH_AM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Protect Out of hours AM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Protect_OOH_PM_Business_Hours</name>
        <label>Get Protect OOH PM Business Hours</label>
        <locationX>3905</locationX>
        <locationY>1946</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Within_Protect_OOH_PM_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>State Protect Out of hours PM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_related_Cases_Last_24_Hours</name>
        <label>Get related Cases Last 24 Hours</label>
        <locationX>4098</locationX>
        <locationY>3686</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Found_Case_Last_24_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Customer.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>CreatedDate</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>formulaYesterdayDateTime</elementReference>
            </value>
        </filters>
        <filters>
            <field>OwnerId</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>Get_Customer.OwnerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_State_Grow_Queue</name>
        <label>Get State Grow Queue</label>
        <locationX>974</locationX>
        <locationY>3146</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_State_Team_Member_Available</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetStateGrowQueue</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_State_Protect_Grow_Queue</name>
        <label>Get State Protect/Grow Queue</label>
        <locationX>3504</locationX>
        <locationY>3146</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Relationship_Manager_Available</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaCalculateStateQueue</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_State_Protect_Grow_Queue1</name>
        <label>Get State Protect/Grow Queue</label>
        <locationX>2717</locationX>
        <locationY>1946</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Route_to_State_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>formulaCalculateStateQueue</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Unmanaged_Email_Template</name>
        <label>Get Unmanaged Email Template</label>
        <locationX>6342</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Not_Managed_Contact_Customer_Service_Email</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Unmanaged Customer Contact</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Customer_Business_Hours_and_Portfolio_on_Case_Record</name>
        <label>Update Customer Business Hours and Portfolio on Case Record</label>
        <locationX>3075</locationX>
        <locationY>1082</locationY>
        <connector>
            <targetReference>Within_Business_Hours</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>input_record.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>Get_Customer.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Premium_Managed_Portfolio_Hours__c</field>
            <value>
                <elementReference>Check_Within_Business_Hours.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <start>
        <locationX>5872</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_National_Protect_Queue</targetReference>
        </connector>
    </start>
    <status>InvalidDraft</status>
    <subflows>
        <description>Call Get Customer Subflow to resolve the duplicate customer matching issue</description>
        <name>Call_Get_Customer_Subflow</name>
        <label>Call Get Customer Subflow</label>
        <locationX>7266</locationX>
        <locationY>998</locationY>
        <connector>
            <targetReference>Set_varAccountId</targetReference>
        </connector>
        <flowName>Get_Customer_Subflow1</flowName>
        <inputAssignments>
            <name>varCase</name>
            <value>
                <elementReference>input_record</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <variables>
        <name>BusinessHours</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>BusinessHours</objectType>
    </variables>
    <variables>
        <description>Stores the work record that’s being routed, including all fields and input values</description>
        <name>input_record</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>numberCustomerRecords</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <description>Stores the ID that uniquely identifies the work record being routed</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varAccountId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores value to determine if the customer is managed</description>
        <name>varManagedCustomer</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the Developer Name of the Queue to route work to</description>
        <name>varQueueDeveloperName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varSuppliedEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <description>Stores whether email was sent within business TRUE or FALSE</description>
        <name>varWithinBusinessHours</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
