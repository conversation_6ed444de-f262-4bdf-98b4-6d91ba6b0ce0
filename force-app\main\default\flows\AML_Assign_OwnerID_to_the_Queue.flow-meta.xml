<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>check_the_record_type</name>
        <label>check the record type</label>
        <locationX>314</locationX>
        <locationY>287</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ECDD</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ECDD_Unassigned_Queue</targetReference>
            </connector>
            <label>ECDD</label>
        </rules>
        <rules>
            <name>TM_or_Non_TM</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Non_Transaction_Monitoring</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AML_Unassigned_Queue</targetReference>
            </connector>
            <label>TM or Non TM</label>
        </rules>
    </decisions>
    <description>AML Assign OwnerID to the Queue</description>
    <environments>Default</environments>
    <interviewLabel>AML Assign OwnerID to the Queue {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Assign OwnerID to the Queue</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>AML_Unassigned_Queue</name>
        <label>AML Unassigned Queue</label>
        <locationX>314</locationX>
        <locationY>395</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>update_owner_to_queue_TM_and_Non_TM</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>AML_Unassigned_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>ECDD_Unassigned_Queue</name>
        <label>ECDD Unassigned Queue</label>
        <locationX>50</locationX>
        <locationY>395</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>update_owner_to_queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ECDD_Unassigned_Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>update_owner_to_queue</name>
        <label>update owner to queue</label>
        <locationX>50</locationX>
        <locationY>503</locationY>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>ECDD_Unassigned_Queue.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>update_owner_to_queue_TM_and_Non_TM</name>
        <label>update  owner to queue TM and Non TM</label>
        <locationX>314</locationX>
        <locationY>503</locationY>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>AML_Unassigned_Queue.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>check_the_record_type</targetReference>
        </connector>
        <filterFormula>AND(
OR(
{!$Record.RecordType.DeveloperName} = &quot;ECDD_Case&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Transaction_Monitoring&quot;,
{!$Record.RecordType.DeveloperName} = &quot;Non_Transaction_Monitoring&quot;
)
,
TEXT({!$Record.Status}) = &quot;New&quot;
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
