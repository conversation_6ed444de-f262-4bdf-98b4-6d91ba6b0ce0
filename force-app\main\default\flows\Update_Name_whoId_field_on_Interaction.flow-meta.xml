<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Name_WhoId_is_Null</name>
        <label>Name WhoId is Null</label>
        <locationX>182</locationX>
        <locationY>311</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Interaction</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WhoId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WhatId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>001</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Related_To</targetReference>
            </connector>
            <label>Null</label>
        </rules>
    </decisions>
    <description>When removing Name field, it still should auto populate the field.</description>
    <environments>Default</environments>
    <interviewLabel>Update Name(whoId) field on Interaction {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Name(whoId) field on Interaction</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Related_To</name>
        <label>Get Related To</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Name_WhoId</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Name_WhoId</name>
        <label>Update Name WhoId</label>
        <locationX>50</locationX>
        <locationY>551</locationY>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>Get_Related_To.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Name_WhoId_is_Null</targetReference>
        </connector>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
