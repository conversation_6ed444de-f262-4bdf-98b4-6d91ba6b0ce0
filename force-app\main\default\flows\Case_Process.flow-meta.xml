<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>invoke RG Case Send Notification to Owner</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_1_A1</name>
        <label>Assign Case Owner And Send Notification</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <actionName>invoke_RG_Case_Send_Notification_to_Owner</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision2</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>SObject</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>inputCase</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue>Case</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>SObject</stringValue>
                </value>
            </processMetadataValues>
            <name>inputCase</name>
            <value>
                <elementReference>myVariable_current</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>inputUserId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>inputUserId</name>
            <value>
                <elementReference>myVariable_current.Account.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>invoke_RG_Case_Send_Notification_to_Owner</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>emailAlertSelection</name>
            <value>
                <stringValue>Time_Out_Self_Exclusion_Alert_Case_Email_Notification</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_21_A1</name>
        <label>Send Email Notification</label>
        <locationX>2100</locationX>
        <locationY>200</locationY>
        <actionName>Case.Time_Out_Self_Exclusion_Alert_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>myDecision22</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Time_Out_Self_Exclusion_Alert_Case_Email_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>emailAlertSelection</name>
            <value>
                <stringValue>Self_Exclusion_Alert_Case_Email_Notification</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_26_A1</name>
        <label>Send Email to Case Owner-Self excl</label>
        <locationX>2600</locationX>
        <locationY>200</locationY>
        <actionName>Case.Self_Exclusion_Alert_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>myDecision27</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Self_Exclusion_Alert_Case_Email_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>emailAlertSelection</name>
            <value>
                <stringValue>Closed_Account_Customer_Self_Exclusion_Alert_Case_Email_Notification</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_28_A1</name>
        <label>Send Email Notification</label>
        <locationX>2800</locationX>
        <locationY>200</locationY>
        <actionName>Case.Closed_Account_Customer_Self_Exclusion_Alert_Case_Email_Notification</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>myDecision29</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Closed_Account_Customer_Self_Exclusion_Alert_Case_Email_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>RG Escalated to RG Team Queue</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_30_A2</name>
        <label>RG Queue</label>
        <locationX>3000</locationX>
        <locationY>300</locationY>
        <actionName>RG_Escalated_to_RG_Team_Queue</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision33</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>VarCaseId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>VarCaseId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>RG_Escalated_to_RG_Team_Queue</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>invoke RG Case Send Notification to Owner</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_3_A1</name>
        <label>Assign Case Owner to Delegated User and Send Notification</label>
        <locationX>300</locationX>
        <locationY>200</locationY>
        <actionName>invoke_RG_Case_Send_Notification_to_Owner</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myDecision4</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>SObject</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>inputCase</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue>Case</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>SObject</stringValue>
                </value>
            </processMetadataValues>
            <name>inputCase</name>
            <value>
                <elementReference>myVariable_current</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>inputUserId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>inputUserId</name>
            <value>
                <elementReference>myVariable_current.Account.Owner.Delegate_Assignment_User_ID__c</elementReference>
            </value>
        </inputParameters>
        <nameSegment>invoke_RG_Case_Send_Notification_to_Owner</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>customNotifTypeName</name>
            <value>
                <stringValue>RG_Case_Notification</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>recipientCategory</name>
            <value>
                <stringValue>queue</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>recipientSelection</name>
            <value>
                <stringValue>Unassigned_Cases</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>recipientType</name>
            <value>
                <stringValue>record</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_8_A1</name>
        <label>Send Custom Notification to Jenny</label>
        <locationX>800</locationX>
        <locationY>200</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <connector>
            <targetReference>myRule_8_A2</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <stringValue>000000000000000</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>customNotifTypeName</name>
            <value>
                <stringValue>RG_Case_Notification</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <stringValue>{!myVariable_current.Priority} Responsible Gambling Alert is in Unassigned Queue</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <stringValue>
Case Number: {!myVariable_current.CaseNumber}

Subject: {!myVariable_current.Subject}

Alert Date/Time: {!myVariable_current.Alert_Date_Time__c}

Alert Level: {!myVariable_current.Priority}

Case Type: {!myVariable_current.Type}</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <stringValue>{!myVariable_current.Id}</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>recipientIds</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>500.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <name>recipientIds</name>
            <value>
                <elementReference>myCollection_myRule_8_A1recipientIds</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>emailAlertSelection</name>
            <value>
                <stringValue>RG_Alert_Unassigned_Case_Notification</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_8_A2</name>
        <label>Send Email Notification</label>
        <locationX>800</locationX>
        <locationY>300</locationY>
        <actionName>Case.RG_Alert_Unassigned_Case_Notification</actionName>
        <actionType>emailAlert</actionType>
        <connector>
            <targetReference>myDecision9</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.RG_Alert_Unassigned_Case_Notification</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Invoke_Case_Escalation_Notification</stringValue>
            </value>
        </processMetadataValues>
        <name>myWaitEvent_myWait_myRule_49_event_0_SA1</name>
        <label>Send Escalation Notification</label>
        <locationX>4900</locationX>
        <locationY>200</locationY>
        <actionName>Invoke_Case_Escalation_Notification</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myWaitEvent_myWait_myRule_49_event_0_postWaitExecutionAssignment</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>SObject</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>inputCase</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue>Case</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>SObject</stringValue>
                </value>
            </processMetadataValues>
            <name>inputCase</name>
            <value>
                <elementReference>myVariable_current</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>inputUserId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>inputUserId</name>
            <value>
                <elementReference>myVariable_current.Account.OwnerId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Invoke_Case_Escalation_Notification</nameSegment>
    </actionCalls>
    <apiVersion>53.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>myAssignment_myRule_8_A1</name>
        <label>myAssignment_myRule_8_A1</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>myCollection_myRule_8_A1recipientIds</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>00G0w000001npjcEAA</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>myRule_8_A1</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>myWaitAssignment_myWait_myRule_49</name>
        <label>myWaitAssignment_myWait_myRule_49</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>cancelWaits</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>myWait_myRule_49</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>myDecision</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>myWaitEvent_myWait_myRule_49_event_0_postWaitExecutionAssignment</name>
        <label>myWaitEvent_myWait_myRule_49_event_0_postWaitExecutionAssignment</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>myWaitEvent_myWait_myRule_49_event_0_postActionExecutionVariable</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>myWait_myRule_49</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>isChangedDecision15_myRule_14_ECDD_First_Name_c</name>
        <label>isChangedDecision15_myRule_14_ECDD_First_Name_c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>isChangedDecision16_myRule_14_ECDD_Last_Name_c</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>isChangedRule_15_myRule_14_ECDD_First_Name_c</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>myVariable_old.ECDD_First_Name__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>myVariable_current.ECDD_First_Name__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>isChangedDecision16_myRule_14_ECDD_Last_Name_c</targetReference>
            </connector>
            <label>isChangedRule_15_myRule_14_ECDD_First_Name_c</label>
        </rules>
    </decisions>
    <decisions>
        <name>isChangedDecision16_myRule_14_ECDD_Last_Name_c</name>
        <label>isChangedDecision16_myRule_14_ECDD_Last_Name_c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>isChangedDecision31_myRule_30_ECDD_RG_Escalation_c</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>isChangedRule_16_myRule_14_ECDD_Last_Name_c</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>myVariable_old.ECDD_Last_Name__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>myVariable_current.ECDD_Last_Name__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>isChangedDecision31_myRule_30_ECDD_RG_Escalation_c</targetReference>
            </connector>
            <label>isChangedRule_16_myRule_14_ECDD_Last_Name_c</label>
        </rules>
    </decisions>
    <decisions>
        <name>isChangedDecision31_myRule_30_ECDD_RG_Escalation_c</name>
        <label>isChangedDecision31_myRule_30_ECDD_RG_Escalation_c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>isChangedDecision35_myRule_34_ECCD_EC_Status_c</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>isChangedRule_31_myRule_30_ECDD_RG_Escalation_c</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>myVariable_old.ECDD_RG_Escalation__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>myVariable_current.ECDD_RG_Escalation__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>isChangedDecision35_myRule_34_ECCD_EC_Status_c</targetReference>
            </connector>
            <label>isChangedRule_31_myRule_30_ECDD_RG_Escalation_c</label>
        </rules>
    </decisions>
    <decisions>
        <name>isChangedDecision35_myRule_34_ECCD_EC_Status_c</name>
        <label>isChangedDecision35_myRule_34_ECCD_EC_Status_c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>isChangedDecision39_myRule_38_ECDD_EC_Escalation_c</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>isChangedRule_35_myRule_34_ECCD_EC_Status_c</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>myVariable_old.ECCD_EC_Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>myVariable_current.ECCD_EC_Status__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>isChangedDecision39_myRule_38_ECDD_EC_Escalation_c</targetReference>
            </connector>
            <label>isChangedRule_35_myRule_34_ECCD_EC_Status_c</label>
        </rules>
    </decisions>
    <decisions>
        <name>isChangedDecision39_myRule_38_ECDD_EC_Escalation_c</name>
        <label>isChangedDecision39_myRule_38_ECDD_EC_Escalation_c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>isChangedDecision42_myRule_41_OwnerId</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>isChangedRule_39_myRule_38_ECDD_EC_Escalation_c</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>myVariable_old.ECDD_EC_Escalation__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>myVariable_current.ECDD_EC_Escalation__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>isChangedDecision42_myRule_41_OwnerId</targetReference>
            </connector>
            <label>isChangedRule_39_myRule_38_ECDD_EC_Escalation_c</label>
        </rules>
    </decisions>
    <decisions>
        <name>isChangedDecision42_myRule_41_OwnerId</name>
        <label>isChangedDecision42_myRule_41_OwnerId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>isChangedDecision46_myRule_45_Status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>isChangedRule_42_myRule_41_OwnerId</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>myVariable_old.OwnerId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>myVariable_current.OwnerId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>isChangedDecision46_myRule_45_Status</targetReference>
            </connector>
            <label>isChangedRule_42_myRule_41_OwnerId</label>
        </rules>
    </decisions>
    <decisions>
        <name>isChangedDecision46_myRule_45_Status</name>
        <label>isChangedDecision46_myRule_45_Status</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myPreWaitDecision_myWait_myRule_49</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>isChangedRule_46_myRule_45_Status</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>myVariable_old.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>myVariable_current.Status</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myPreWaitDecision_myWait_myRule_49</targetReference>
            </connector>
            <label>isChangedRule_46_myRule_45_Status</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_A1</targetReference>
            </connector>
            <label>Assign RG Alert Case to Customer Owner</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>5.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision11</name>
        <label>myDecision11</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision13</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_12</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_12</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_12_A1</targetReference>
            </connector>
            <label>If New Self Excl case with Closed Operator and Queue Owner</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>6.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision13</name>
        <label>myDecision13</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision18</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_14</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>isChangedRule_15_myRule_14_ECDD_First_Name_c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>isChangedRule_16_myRule_14_ECDD_Last_Name_c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_14_A1</targetReference>
            </connector>
            <label>ECDD case subject</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>7.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision18</name>
        <label>myDecision18</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision20</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_19</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_19</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_19_A1</targetReference>
            </connector>
            <label>Limit Type = time_out OR self_excl</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision2</name>
        <label>myDecision2</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision4</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_3</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_3_A1</targetReference>
            </connector>
            <label>Assign RG Alert Case to Customer Owner Delegate User</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>8.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision20</name>
        <label>myDecision20</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision22</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_21</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_21</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_21_pmetdec</targetReference>
            </connector>
            <label>Email Notification for Self Excl Time Out</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>9.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision22</name>
        <label>myDecision22</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision25</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_23</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_23</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_23_A1</targetReference>
            </connector>
            <label>Limit Type = Closed Mobile</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>10.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision25</name>
        <label>myDecision25</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision27</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_26</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_26</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_26_pmetdec</targetReference>
            </connector>
            <label>Email Notification for New Self Excl 2</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>11.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision27</name>
        <label>myDecision27</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision29</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_28</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_28</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_28_pmetdec</targetReference>
            </connector>
            <label>Email Notification for New Self Excl 3</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>12.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision29</name>
        <label>myDecision29</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision33</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_30</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>isChangedRule_31_myRule_30_ECDD_RG_Escalation_c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.ECDD_RG_Escalation__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Y</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_30_A1</targetReference>
            </connector>
            <label>RG Escalated (ECDD)</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>13.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision33</name>
        <label>myDecision33</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision37</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_34</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.ECCD_EC_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>isChangedRule_35_myRule_34_ECCD_EC_Status_c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_34_A1</targetReference>
            </connector>
            <label>EC Status Closed</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>14.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision37</name>
        <label>myDecision37</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision40</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_38</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.ECDD_EC_Escalation__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Y</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>isChangedRule_39_myRule_38_ECDD_EC_Escalation_c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_38_A1</targetReference>
            </connector>
            <label>EC Escalated (ECDD)</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>2.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision4</name>
        <label>myDecision4</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision7</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_5</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_5</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_5_A1</targetReference>
            </connector>
            <label>RG Alert Case Assigned to Unmanaged Queue</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>15.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision40</name>
        <label>myDecision40</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision44</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_41</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideReferenceTo</name>
                    <value>
                        <stringValue>Group;User</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>ID</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>isChangedRule_42_myRule_41_OwnerId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>ID</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideReferenceTo</name>
                    <value>
                        <stringValue>Group;User</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>ID</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>ID</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>005</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_41_A1</targetReference>
            </connector>
            <label>ECDD Queue change</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>16.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision44</name>
        <label>myDecision44</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision48</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_45</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ECDD_Case</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>isChangedRule_46_myRule_45_Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Picklist</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>myVariable_current.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_45_A1</targetReference>
            </connector>
            <label>ECDD Close Date</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>17.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision48</name>
        <label>myDecision48</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_49</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_49</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_49_pmetdec</targetReference>
            </connector>
            <label>If RG Case and Status is NEW</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>3.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision7</name>
        <label>myDecision7</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision9</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_8</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_8</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myAssignment_myRule_8_A1</targetReference>
            </connector>
            <label>If RG Case is not New and Remain in Unassigned Queue</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>4.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision9</name>
        <label>myDecision9</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision11</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_10</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_10</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_10_A1</targetReference>
            </connector>
            <label>Assign Self Excl Case to Account Owner</label>
        </rules>
    </decisions>
    <decisions>
        <name>myPostWaitDecision_myWaitEvent_myWait_myRule_49_event_0</name>
        <label>myPostWaitDecision_myWaitEvent_myWait_myRule_49_event_0</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myWaitEvent_myWait_myRule_49_event_0_postWaitExecutionAssignment</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myPostWaitRule_myWaitEvent_myWait_myRule_49_event_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_current.CreatedDate</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myWaitEvent_myWait_myRule_49_event_0_SA1</targetReference>
            </connector>
            <label>myPostWaitRule_myWaitEvent_myWait_myRule_49_event_0</label>
        </rules>
    </decisions>
    <decisions>
        <name>myPreWaitDecision_myWait_myRule_49</name>
        <label>myPreWaitDecision_myWait_myRule_49</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myWaitAssignment_myWait_myRule_49</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myPreWaitRule_myWait_myRule_49</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_49</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision</targetReference>
            </connector>
            <label>myPreWaitRule_myWait_myRule_49</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_21_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>2100</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_21_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_21_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_21_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_21_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_21_pmetrule</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision22</targetReference>
            </connector>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_26_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>2600</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_26_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_26_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_26_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_26_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_26_pmetrule</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision27</targetReference>
            </connector>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_28_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>2800</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myRule_28_A1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_28_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_28_A1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_28_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_28_pmetrule</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision29</targetReference>
            </connector>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_49_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>4900</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myWait_myRule_49</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_49_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myWait_myRule_49</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_49_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_49_pmetrule</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <description>This process is used for triggered from Case object *Changes in the self exclusion process</description>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Case].ECDD_SportsBet_Account_Number__c+&quot; - &quot;+[Case].ECDD_First_Name__c+&quot; &quot;+[Case].ECDD_Last_Name__c+&quot; - &quot;+ Text(Datevalue([Case].CreatedDate ))</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_17_myRule_14_A1_0963057483</name>
        <dataType>String</dataType>
        <expression>{!myVariable_current.ECDD_SportsBet_Account_Number__c}+&quot; - &quot;+{!myVariable_current.ECDD_First_Name__c}+&quot; &quot;+{!myVariable_current.ECDD_Last_Name__c}+&quot; - &quot;+ Text(Datevalue({!myVariable_current.CreatedDate} ))</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>&quot;DMA&quot;</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_24_myRule_23_A2_8217762599</name>
        <dataType>String</dataType>
        <expression>&quot;DMA&quot;</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>Today()</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_32_myRule_30_A1_1428925935</name>
        <dataType>Date</dataType>
        <expression>Today()</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>Today()</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_36_myRule_34_A1_9079889189</name>
        <dataType>Date</dataType>
        <expression>Today()</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>[Case].Owner:User.FirstName+&quot; &quot;+[Case].Owner:User.LastName  </stringValue>
            </value>
        </processMetadataValues>
        <name>formula_43_myRule_41_A1_7707740165</name>
        <dataType>String</dataType>
        <expression>{!myVariable_current.Owner:User.FirstName}+&quot; &quot;+{!myVariable_current.Owner:User.LastName}</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>Today()</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_47_myRule_45_A1_7586456864</name>
        <dataType>Date</dataType>
        <expression>Today()</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>&quot;System Auto Closed&quot;</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_6_myRule_5_A1_7584818034</name>
        <dataType>String</dataType>
        <expression>&quot;System Auto Closed&quot;</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_1__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;RG_Alert_Case&apos;
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)
&amp;&amp;
(
([Case].Account.Owner.Delegate_Assignment_Start_Date__c &gt; TODAY()  || ISBLANK([Case].Account.Owner.Delegate_Assignment_Start_Date__c))
||
([Case].Account.Owner.Delegate_Assignment_End_Date__c &lt; TODAY() || ISBLANK([Case].Account.Owner.Delegate_Assignment_End_Date__c))
)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_1__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;RG_Alert_Case&apos;
&amp;&amp;
!ISBLANK({!myVariable_current.AccountId})
&amp;&amp;
IF(LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_current.Owner:Queue.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)
&amp;&amp;
(
({!myVariable_current.Account.Owner.Delegate_Assignment_Start_Date__c} &gt; TODAY()  || ISBLANK({!myVariable_current.Account.Owner.Delegate_Assignment_Start_Date__c}))
||
({!myVariable_current.Account.Owner.Delegate_Assignment_End_Date__c} &lt; TODAY() || ISBLANK({!myVariable_current.Account.Owner.Delegate_Assignment_End_Date__c}))
)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_10</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
!ISBLANK({!myVariable_current.AccountId})
&amp;&amp;
IF(LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_current.Owner:Queue.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL([Case].Status, &apos;New&apos;)
&amp;&amp;
ISPICKVAL([Case].Limit_Type__c ,&apos;Closed Operator&apos;)
&amp;&amp;
LEFT([Case].OwnerId, 3) =&quot;00G&quot; 
&amp;&amp;
([Case].Owner:Queue.DeveloperName =&apos;Premium_Team_Managed&apos; || [Case].Owner:Queue.DeveloperName =&apos;Premium_Service&apos;)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_12</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Status}, &apos;New&apos;)
&amp;&amp;
ISPICKVAL({!myVariable_current.Limit_Type__c} ,&apos;Closed Operator&apos;)
&amp;&amp;
LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot; 
&amp;&amp;
({!myVariable_current.Owner:Queue.DeveloperName} =&apos;Premium_Team_Managed&apos; || {!myVariable_current.Owner:Queue.DeveloperName} =&apos;Premium_Service&apos;)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL([Case].Status, &apos;New&apos;)
&amp;&amp;
(ISPICKVAL([Case].Limit_Type__c ,&apos;time_out&apos;) || ISPICKVAL([Case].Limit_Type__c ,&apos;self_excl&apos;))
&amp;&amp;
LEFT([Case].OwnerId, 3) =&quot;00G&quot; 
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_19</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Status}, &apos;New&apos;)
&amp;&amp;
(ISPICKVAL({!myVariable_current.Limit_Type__c} ,&apos;time_out&apos;) || ISPICKVAL({!myVariable_current.Limit_Type__c} ,&apos;self_excl&apos;))
&amp;&amp;
LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL([Case].Closed_Reason__c , &apos;Time Out&apos;)
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
ISPICKVAL([Case].Status,&apos;New&apos;)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_21</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;Time Out&apos;)
&amp;&amp;
!ISBLANK({!myVariable_current.AccountId})
&amp;&amp;
ISPICKVAL({!myVariable_current.Status},&apos;New&apos;)
&amp;&amp;
IF(LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_current.Owner:Queue.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL([Case].Closed_Reason__c , &apos;Time Out&apos;)
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
ISPICKVAL([Case].Status,&apos;New&apos;)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_21_pmetrule</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c} 
&amp;&amp;
!ISBLANK({!myVariable_old.RecordTypeId}) 
&amp;&amp;
{!myVariable_old.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL({!myVariable_old.Closed_Reason__c} , &apos;Time Out&apos;)
&amp;&amp;
!ISBLANK({!myVariable_old.AccountId})
&amp;&amp;
ISPICKVAL({!myVariable_old.Status},&apos;New&apos;)
&amp;&amp;
IF(LEFT({!myVariable_old.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_old.Owner:Queue.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL([Case].Status, &apos;New&apos;)
&amp;&amp;
ISPICKVAL([Case].Limit_Type__c ,&apos;Closed Mobile&apos;)
&amp;&amp;
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - No longer interested&apos;) ||
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - Employment Restrictions&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;DMA - Prefer not to specify&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;DMA - Time management&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;DMA - Personal reasons&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;Permanent Self Exclusion&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;Self Exclusion&apos;)
&amp;&amp;
LEFT([Case].OwnerId, 3) =&quot;00G&quot; 
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_23</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_7__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
ISPICKVAL({!myVariable_current.Status}, &apos;New&apos;)
&amp;&amp;
ISPICKVAL({!myVariable_current.Limit_Type__c} ,&apos;Closed Mobile&apos;)
&amp;&amp;
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;DMA - No longer interested&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;DMA - Employment Restrictions&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;DMA - Prefer not to specify&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;DMA - Time management&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;DMA - Personal reasons&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;Permanent Self Exclusion&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;Self Exclusion&apos;)
&amp;&amp;
LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
((ISPICKVAL([Case].Limit_Type__c ,&apos;Closed Mobile&apos;)
&amp;&amp;
(
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - No reason specified&apos;) ||
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - Unhappy with service/product&apos;) ||
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - Unhappy with your offers&apos;) ||
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - Using another bookmaker&apos;) ||	
ISPICKVAL([Case].Closed_Reason__c , &apos;Closed Operator - Other&apos;)
)) ||
ISPICKVAL([Case].Limit_Type__c ,&apos;Closed Operator&apos;) &amp;&amp; 
[Case].Owner:Queue.DeveloperName &lt;&gt; &apos;Premium_Service&apos; )
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
ISPICKVAL([Case].Status,&apos;New&apos;)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_26</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
((ISPICKVAL({!myVariable_current.Limit_Type__c} ,&apos;Closed Mobile&apos;)
&amp;&amp;
(
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;DMA - No reason specified&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;DMA - Unhappy with service/product&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;DMA - Unhappy with your offers&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;DMA - Using another bookmaker&apos;) ||	
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;Closed Operator - Other&apos;)
)) ||
ISPICKVAL({!myVariable_current.Limit_Type__c} ,&apos;Closed Operator&apos;) &amp;&amp; 
{!myVariable_current.Owner:Queue.DeveloperName} &lt;&gt; &apos;Premium_Service&apos; )
&amp;&amp;
!ISBLANK({!myVariable_current.AccountId})
&amp;&amp;
ISPICKVAL({!myVariable_current.Status},&apos;New&apos;)
&amp;&amp;
IF(LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_current.Owner:Queue.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
((ISPICKVAL([Case].Limit_Type__c ,&apos;Closed Mobile&apos;)
&amp;&amp;
(
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - No reason specified&apos;) ||
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - Unhappy with service/product&apos;) ||
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - Unhappy with your offers&apos;) ||
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - Using another bookmaker&apos;) ||	
ISPICKVAL([Case].Closed_Reason__c , &apos;Closed Operator - Other&apos;)
)) ||
ISPICKVAL([Case].Limit_Type__c ,&apos;Closed Operator&apos;) &amp;&amp; 
[Case].Owner:Queue.DeveloperName &lt;&gt; &apos;Premium_Service&apos; )
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
ISPICKVAL([Case].Status,&apos;New&apos;)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_26_pmetrule</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c} 
&amp;&amp;
!ISBLANK({!myVariable_old.RecordTypeId}) 
&amp;&amp;
{!myVariable_old.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
((ISPICKVAL({!myVariable_old.Limit_Type__c} ,&apos;Closed Mobile&apos;)
&amp;&amp;
(
ISPICKVAL({!myVariable_old.Closed_Reason__c} , &apos;DMA - No reason specified&apos;) ||
ISPICKVAL({!myVariable_old.Closed_Reason__c} , &apos;DMA - Unhappy with service/product&apos;) ||
ISPICKVAL({!myVariable_old.Closed_Reason__c} , &apos;DMA - Unhappy with your offers&apos;) ||
ISPICKVAL({!myVariable_old.Closed_Reason__c} , &apos;DMA - Using another bookmaker&apos;) ||	
ISPICKVAL({!myVariable_old.Closed_Reason__c} , &apos;Closed Operator - Other&apos;)
)) ||
ISPICKVAL({!myVariable_old.Limit_Type__c} ,&apos;Closed Operator&apos;) &amp;&amp; 
{!myVariable_old.Owner:Queue.DeveloperName} &lt;&gt; &apos;Premium_Service&apos; )
&amp;&amp;
!ISBLANK({!myVariable_old.AccountId})
&amp;&amp;
ISPICKVAL({!myVariable_old.Status},&apos;New&apos;)
&amp;&amp;
IF(LEFT({!myVariable_old.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_old.Owner:Queue.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
(
(ISPICKVAL([Case].Limit_Type__c ,&apos;Closed Mobile&apos;)
&amp;&amp;
(
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - No longer interested&apos;) ||
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - Employment Restrictions&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;DMA - Prefer not to specify&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;DMA - Time management&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;DMA - Personal reasons&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;Permanent Self Exclusion&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;Self Exclusion&apos;)
))
||
(ISPICKVAL([Case].Limit_Type__c ,&apos;self_excl&apos;) &amp;&amp; ISPICKVAL([Case].Closed_Reason__c ,&apos;Self Exclusion&apos;))
)
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_28</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
(
(ISPICKVAL({!myVariable_current.Limit_Type__c} ,&apos;Closed Mobile&apos;)
&amp;&amp;
(
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;DMA - No longer interested&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} , &apos;DMA - Employment Restrictions&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;DMA - Prefer not to specify&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;DMA - Time management&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;DMA - Personal reasons&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;Permanent Self Exclusion&apos;) ||
ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;Self Exclusion&apos;)
))
||
(ISPICKVAL({!myVariable_current.Limit_Type__c} ,&apos;self_excl&apos;) &amp;&amp; ISPICKVAL({!myVariable_current.Closed_Reason__c} ,&apos;Self Exclusion&apos;))
)
&amp;&amp;
!ISBLANK({!myVariable_current.AccountId})
&amp;&amp;
IF(LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_current.Owner:Queue.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
(
(ISPICKVAL([Case].Limit_Type__c ,&apos;Closed Mobile&apos;)
&amp;&amp;
(
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - No longer interested&apos;) ||
ISPICKVAL([Case].Closed_Reason__c , &apos;DMA - Employment Restrictions&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;DMA - Prefer not to specify&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;DMA - Time management&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;DMA - Personal reasons&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;Permanent Self Exclusion&apos;) ||
ISPICKVAL([Case].Closed_Reason__c ,&apos;Self Exclusion&apos;)
))
||
(ISPICKVAL([Case].Limit_Type__c ,&apos;self_excl&apos;) &amp;&amp; ISPICKVAL([Case].Closed_Reason__c ,&apos;Self Exclusion&apos;))
)
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_28_pmetrule</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_8__c} 
&amp;&amp;
!ISBLANK({!myVariable_old.RecordTypeId}) 
&amp;&amp;
{!myVariable_old.RecordType.DeveloperName} == &apos;Self_Exclusion_Alert_Case&apos;
&amp;&amp;
(
(ISPICKVAL({!myVariable_old.Limit_Type__c} ,&apos;Closed Mobile&apos;)
&amp;&amp;
(
ISPICKVAL({!myVariable_old.Closed_Reason__c} , &apos;DMA - No longer interested&apos;) ||
ISPICKVAL({!myVariable_old.Closed_Reason__c} , &apos;DMA - Employment Restrictions&apos;) ||
ISPICKVAL({!myVariable_old.Closed_Reason__c} ,&apos;DMA - Prefer not to specify&apos;) ||
ISPICKVAL({!myVariable_old.Closed_Reason__c} ,&apos;DMA - Time management&apos;) ||
ISPICKVAL({!myVariable_old.Closed_Reason__c} ,&apos;DMA - Personal reasons&apos;) ||
ISPICKVAL({!myVariable_old.Closed_Reason__c} ,&apos;Permanent Self Exclusion&apos;) ||
ISPICKVAL({!myVariable_old.Closed_Reason__c} ,&apos;Self Exclusion&apos;)
))
||
(ISPICKVAL({!myVariable_old.Limit_Type__c} ,&apos;self_excl&apos;) &amp;&amp; ISPICKVAL({!myVariable_old.Closed_Reason__c} ,&apos;Self Exclusion&apos;))
)
&amp;&amp;
!ISBLANK({!myVariable_old.AccountId})
&amp;&amp;
IF(LEFT({!myVariable_old.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_old.Owner:Queue.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;
, False)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_2__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;RG_Alert_Case&apos;
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
 [Case].Owner:Queue.DeveloperName == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;,
 False
)
&amp;&amp;
!ISBLANK([Case].Account.Owner.Delegate_Assignment_User_Id__c)
&amp;&amp;
[Case].Account.Owner.Delegate_Assignment_Start_Date__c &lt;= TODAY()
&amp;&amp;
[Case].Account.Owner.Delegate_Assignment_End_Date__c &gt;= TODAY()</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_3</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_2__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;RG_Alert_Case&apos;
&amp;&amp;
!ISBLANK({!myVariable_current.AccountId})
&amp;&amp;
IF(LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;, 
 {!myVariable_current.Owner:Queue.DeveloperName} == &apos;Temp_Queue_to_Assign_Case_to_RM&apos;,
 False
)
&amp;&amp;
!ISBLANK({!myVariable_current.Account.Owner.Delegate_Assignment_User_ID__c})
&amp;&amp;
{!myVariable_current.Account.Owner.Delegate_Assignment_Start_Date__c} &lt;= TODAY()
&amp;&amp;
{!myVariable_current.Account.Owner.Delegate_Assignment_End_Date__c} &gt;= TODAY()</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_5__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;RG_Alert_Case&apos;
&amp;&amp; 
ISPICKVAL([Case].Case_Name__c, &apos;Customer Deposit over Avg Threshold&apos;) 
&amp;&amp;
ISPICKVAL([Case].Status, &apos;New&apos;)
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
ISBLANK([Case].ClosedDate)
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_49</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_5__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;RG_Alert_Case&apos;
&amp;&amp; 
ISPICKVAL({!myVariable_current.Case_Name__c}, &apos;Customer Deposit over Avg Threshold&apos;) 
&amp;&amp;
ISPICKVAL({!myVariable_current.Status}, &apos;New&apos;)
&amp;&amp;
!ISBLANK({!myVariable_current.AccountId})
&amp;&amp;
ISBLANK({!myVariable_current.ClosedDate})</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_5__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;RG_Alert_Case&apos;
&amp;&amp; 
ISPICKVAL([Case].Case_Name__c, &apos;Customer Deposit over Avg Threshold&apos;) 
&amp;&amp;
ISPICKVAL([Case].Status, &apos;New&apos;)
&amp;&amp;
!ISBLANK([Case].AccountId)
&amp;&amp;
ISBLANK([Case].ClosedDate)
</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_49_pmetrule</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_5__c} 
&amp;&amp;
!ISBLANK({!myVariable_old.RecordTypeId}) 
&amp;&amp;
{!myVariable_old.RecordType.DeveloperName} == &apos;RG_Alert_Case&apos;
&amp;&amp; 
ISPICKVAL({!myVariable_old.Case_Name__c}, &apos;Customer Deposit over Avg Threshold&apos;) 
&amp;&amp;
ISPICKVAL({!myVariable_old.Status}, &apos;New&apos;)
&amp;&amp;
!ISBLANK({!myVariable_old.AccountId})
&amp;&amp;
ISBLANK({!myVariable_old.ClosedDate})</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_3__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;RG_Alert_Case&apos;
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Unmanaged_Queue&apos;
, False)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_5</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_3__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;RG_Alert_Case&apos;
&amp;&amp;
IF(LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_current.Owner:Queue.DeveloperName} == &apos;Unmanaged_Queue&apos;
, False)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_4__c 
&amp;&amp;
!ISBLANK([Case].RecordTypeId) 
&amp;&amp;
[Case].RecordType.DeveloperName == &apos;RG_Alert_Case&apos;
&amp;&amp;
!ISNEW()
&amp;&amp;
ISBLANK([Case].AccountId)
&amp;&amp;
IF(LEFT([Case].OwnerId, 3) =&quot;00G&quot;, 
[Case].Owner:Queue.DeveloperName == &apos;Unassigned_Cases&apos;
, False)
&amp;&amp;
[Case].LastModifiedDate - [Case].CreatedDate &lt; 0.01</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_8</name>
        <dataType>Boolean</dataType>
        <expression>!{!$Setup.Process_Automation_Control_Panel__c.Case_PB_Entry_4__c} 
&amp;&amp;
!ISBLANK({!myVariable_current.RecordTypeId}) 
&amp;&amp;
{!myVariable_current.RecordType.DeveloperName} == &apos;RG_Alert_Case&apos;
&amp;&amp;
!ISNEW()
&amp;&amp;
ISBLANK({!myVariable_current.AccountId})
&amp;&amp;
IF(LEFT({!myVariable_current.OwnerId}, 3) =&quot;00G&quot;, 
{!myVariable_current.Owner:Queue.DeveloperName} == &apos;Unassigned_Cases&apos;
, False)
&amp;&amp;
{!myVariable_current.LastModifiedDate} - {!myVariable_current.CreatedDate} &lt; 0.01</expression>
    </formulas>
    <interviewLabel>Case_Process-14_InterviewLabel</interviewLabel>
    <label>Case Process</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Case</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>ObjectVariable</name>
        <value>
            <elementReference>myVariable_current</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OldObjectVariable</name>
        <value>
            <elementReference>myVariable_old</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>TriggerType</name>
        <value>
            <stringValue>onAllChanges</stringValue>
        </value>
    </processMetadataValues>
    <processType>Workflow</processType>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_10_A1</name>
        <label>Assign Case Owner as Account Owner</label>
        <locationX>1000</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision11</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Owner ID</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue>Group;User</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <field>OwnerId</field>
            <value>
                <elementReference>myVariable_current.Account.Owner.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_12_A1</name>
        <label>Close the Case</label>
        <locationX>1200</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision13</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closure Comments</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <field>Closure_Comments__c</field>
            <value>
                <stringValue>System Automatically Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Outcome</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_14_A1</name>
        <label>Update Subject</label>
        <locationX>1400</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision18</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Subject</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>Subject</field>
            <value>
                <elementReference>formula_17_myRule_14_A1_0963057483</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>criteria</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_19_A1</name>
        <label>Close Time Out Self Exclusion</label>
        <locationX>1900</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myRule_19_A2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>inputDataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Limit Type</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>operatorDataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Limit_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>time_out</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closed Reason</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Closed_Reason__c</field>
            <value>
                <stringValue>Time Out</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closure Comments</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <field>Closure_Comments__c</field>
            <value>
                <stringValue>System Automatically Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Outcome</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Case Type</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Type</field>
            <value>
                <stringValue>Closure</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>criteria</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_19_A2</name>
        <label>Close Self Excl Self Exclusion</label>
        <locationX>1900</locationX>
        <locationY>300</locationY>
        <connector>
            <targetReference>myDecision20</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>inputDataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Limit Type</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>operatorDataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Limit_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>self_excl</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closed Reason</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Closed_Reason__c</field>
            <value>
                <stringValue>Self Exclusion</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closure Comments</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <field>Closure_Comments__c</field>
            <value>
                <stringValue>System Automatically Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Outcome</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Case Type</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Type</field>
            <value>
                <stringValue>Closure</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>criteria</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_23_A1</name>
        <label>Close Permanent and Self Exclusion</label>
        <locationX>2300</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myRule_23_A2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>inputDataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closed Reason</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>operatorDataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Closed_Reason__c</field>
            <operator>Contains</operator>
            <value>
                <stringValue>Self Exclusion</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closure Comments</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <field>Closure_Comments__c</field>
            <value>
                <stringValue>System Automatically Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Outcome</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Case Type</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Type</field>
            <value>
                <stringValue>Closure</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>criteria</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_23_A2</name>
        <label>Close Deactivation</label>
        <locationX>2300</locationX>
        <locationY>300</locationY>
        <connector>
            <targetReference>myDecision25</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>inputDataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closed Reason</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>operatorDataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>Closed_Reason__c</field>
            <operator>StartsWith</operator>
            <value>
                <elementReference>formula_24_myRule_23_A2_8217762599</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closure Comments</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <field>Closure_Comments__c</field>
            <value>
                <stringValue>System Automatically Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Outcome</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Action Taken</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Case Type</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Type</field>
            <value>
                <stringValue>Deactivation</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_30_A1</name>
        <label>Update RG Case</label>
        <locationX>3000</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myRule_30_A2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>ECDD Date referred to RG Team</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>ECDD_Date_referred_to_RG_Team__c</field>
            <value>
                <elementReference>formula_32_myRule_30_A1_1428925935</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>RG Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>ECDD_RG_Status__c</field>
            <value>
                <stringValue>Not started</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>Escalated</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_34_A1</name>
        <label>Update EC Closed Date</label>
        <locationX>3400</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision37</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>EC Close Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>ECDD_EC_Closed_Date__c</field>
            <value>
                <elementReference>formula_36_myRule_34_A1_9079889189</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_38_A1</name>
        <label>Update status</label>
        <locationX>3800</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision40</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>Escalated</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_41_A1</name>
        <label>Update status</label>
        <locationX>4100</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision44</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Customer Security Analyst</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>ECDD_Customer_Security_Analyst__c</field>
            <value>
                <elementReference>formula_43_myRule_41_A1_7707740165</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>In Progress</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_45_A1</name>
        <label>Update ECDD Closed Date</label>
        <locationX>4500</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision48</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>ECDD Closed Date</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>ECDD_Closed_Date__c</field>
            <value>
                <elementReference>formula_47_myRule_45_A1_7586456864</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <processMetadataValues>
            <name>evaluationType</name>
            <value>
                <stringValue>always</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>extraTypeInfo</name>
        </processMetadataValues>
        <processMetadataValues>
            <name>isChildRelationship</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>reference</name>
            <value>
                <stringValue>[Case]</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>referenceTargetField</name>
        </processMetadataValues>
        <name>myRule_5_A1</name>
        <label>Close Case</label>
        <locationX>500</locationX>
        <locationY>200</locationY>
        <connector>
            <targetReference>myDecision7</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <processMetadataValues>
                <name>implicit</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </processMetadataValues>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Closure Comments</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Formula</stringValue>
                </value>
            </processMetadataValues>
            <field>Closure_Comments__c</field>
            <value>
                <elementReference>formula_6_myRule_5_A1_7584818034</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Outcome</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Outcome__c</field>
            <value>
                <stringValue>No Further Action</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>MultiPicklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>RG Tools Accepted</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>MultiPicklist</stringValue>
                </value>
            </processMetadataValues>
            <field>RG_Tools_Accepted__c</field>
            <value>
                <stringValue>Not Offered</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>MultiPicklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>RG Tools Offered</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>MultiPicklist</stringValue>
                </value>
            </processMetadataValues>
            <field>RG_Tools_Offered__c</field>
            <value>
                <stringValue>Not Offered</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>Status</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideReferenceTo</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Picklist</stringValue>
                </value>
            </processMetadataValues>
            <field>Status</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <startElementReference>isChangedDecision15_myRule_14_ECDD_First_Name_c</startElementReference>
    <status>Obsolete</status>
    <variables>
        <name>cancelWaits</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>myCollection_myRule_8_A1recipientIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>myVariable_current</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>myVariable_old</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>myWaitEvent_myWait_myRule_49_event_0_postActionExecutionVariable</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <waits>
        <name>myWait_myRule_49</name>
        <label>myWait_myRule_49</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>defaultLabel</defaultConnectorLabel>
        <waitEvents>
            <processMetadataValues>
                <name>inputParameterType</name>
                <value>
                    <stringValue>referenced</stringValue>
                </value>
            </processMetadataValues>
            <name>myWaitEvent_myWait_myRule_49_event_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myWaitEvent_myWait_myRule_49_event_0_postActionExecutionVariable</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myPostWaitDecision_myWaitEvent_myWait_myRule_49_event_0</targetReference>
            </connector>
            <eventType>DateRefAlarmEvent</eventType>
            <inputParameters>
                <name>TimeTableColumnEnumOrId</name>
                <value>
                    <stringValue>Case</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>EntityObjectId</name>
                <value>
                    <elementReference>myVariable_current.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>TimeOffsetUnit</name>
                <value>
                    <stringValue>Hours</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>TimeOffset</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>TimeFieldColumnEnumOrId</name>
                <value>
                    <stringValue>CreatedDate</stringValue>
                </value>
            </inputParameters>
            <label>myWaitEvent_myWait_myRule_49_event_0</label>
        </waitEvents>
    </waits>
</Flow>
