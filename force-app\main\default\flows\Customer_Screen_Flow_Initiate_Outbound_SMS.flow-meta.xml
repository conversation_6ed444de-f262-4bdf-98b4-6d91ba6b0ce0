<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionCalls>
        <name>Send_New_Outbound_SMS</name>
        <label>Send New Outbound SMS</label>
        <locationX>314</locationX>
        <locationY>1298</locationY>
        <actionName>sendConversationMessages</actionName>
        <actionType>sendConversationMessages</actionType>
        <connector>
            <targetReference>Loop_Message_Identifiers</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>messageDefinitionName</name>
            <value>
                <stringValue>Premium_Managed_Outbound_SMS</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingDefinitionInputParameters</name>
            <value>
                <apexValue>[
  {
    &quot;name&quot;: &quot;Flow_SMS_Message_Body&quot;,
    &quot;textValue&quot;: &quot;{!SMS_Message}&quot;
  }
]</apexValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>requestType</name>
            <value>
                <stringValue>SendNotificationMessages</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isEnforceMessagingChannelConsent</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>allowedSessionStatus</name>
            <value>
                <stringValue>Any</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messagingEndUserIds</name>
            <value>
                <elementReference>varMessagingUsersIds</elementReference>
            </value>
        </inputParameters>
        <nameSegment>sendConversationMessages</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <description>Calls out to Queuable class that will update the Messaging Session that is created by the Send Conversation Action but with a 1 minute delay to allow enough time for the Messaging Session to be created</description>
        <name>Update_Messaging_Session_Action</name>
        <label>Update Messaging Session Action</label>
        <locationX>314</locationX>
        <locationY>2486</locationY>
        <actionName>MessagingSessionUpdaterAction</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Send_Another_SMS</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customerId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>delayInMinutes</name>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messageIdentifier</name>
            <value>
                <elementReference>varMessageIdentifier</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>ownerId</name>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>portfolioAtTime</name>
            <value>
                <elementReference>recordId.PersonContact.Account.Portfolio__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sessionReason</name>
            <value>
                <elementReference>varMessagingSessionReason</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sessionSummary</name>
            <value>
                <elementReference>varMessagingSessionSummary</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>withinBusinessHours</name>
            <value>
                <elementReference>Within_Business_Hours_Action.isWithin</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MessagingSessionUpdaterAction</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Within_Business_Hours_Action</name>
        <label>Within Business Hours Action</label>
        <locationX>314</locationX>
        <locationY>974</locationY>
        <actionName>InvocableCheckBusinessHours</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Assign_Messaging_User_Id</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>businessHoursId</name>
            <value>
                <elementReference>Get_Business_Hours.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetDT</name>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InvocableCheckBusinessHours</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Grow_Business_Hours</name>
        <label>Assign Grow Business Hours</label>
        <locationX>314</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>varBusinessHoursName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>National Grow</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Business_Hours</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Updates the message count variable</description>
        <name>Assign_Message_Count</name>
        <label>Assign Message Count</label>
        <locationX>314</locationX>
        <locationY>1706</locationY>
        <assignmentItems>
            <assignToReference>varMessageCount</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>How_Many_SMS_Sent</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Message Identifier from collection to add the single item to a single variable</description>
        <name>Assign_Message_Identifier</name>
        <label>Assign Message Identifier</label>
        <locationX>402</locationX>
        <locationY>1514</locationY>
        <assignmentItems>
            <assignToReference>varMessageIdentifier</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Message_Identifiers</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Message_Identifiers</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign Messaging User Id to text collection variable to be used in Send SMS Action</description>
        <name>Assign_Messaging_User_Id</name>
        <label>Assign Messaging User Id</label>
        <locationX>314</locationX>
        <locationY>1082</locationY>
        <assignmentItems>
            <assignToReference>varMessagingUsersIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_Messaging_End_User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Initiate_Outbound_SMS_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Premium_Managed</name>
        <label>Assign Premium Managed</label>
        <locationX>578</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>varBusinessHoursName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Premium Managed Business Hours</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Business_Hours</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Protect_Business_Hours</name>
        <label>Assign Protect Business Hours</label>
        <locationX>50</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>varBusinessHoursName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>National Protect</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Business_Hours</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Adds message to Task Summary for each message being sent</description>
        <name>Assign_Summary</name>
        <label>Assign Summary</label>
        <locationX>182</locationX>
        <locationY>1922</locationY>
        <assignmentItems>
            <assignToReference>varMessagingSessionSummaryForTask</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>SMS_Message</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varMessagingSessionSummary</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>SMS_Message</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Another_Outbound_SMS_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns values from only the 1st Messaging to be used in Task creation</description>
        <name>Assign_Values_for_Task</name>
        <label>Assign Values for Task</label>
        <locationX>446</locationX>
        <locationY>1922</locationY>
        <assignmentItems>
            <assignToReference>varMessagingSessionReason</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Message_Reason</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varMessagingSessionSummaryForTask</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>SMS_Message</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varMessageIdentifierForTask</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>varMessageIdentifier</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varMessagingSessionSummary</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>SMS_Message</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Interaction_Task_Record_Type</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Clear_Collection_Variables</name>
        <label>Clear Collection Variables</label>
        <locationX>182</locationX>
        <locationY>2702</locationY>
        <assignmentItems>
            <assignToReference>varMessageIdentifier</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Initiate_Outbound_SMS_Screen</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_Customer_Portfolio</name>
        <label>Check Customer Portfolio</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>Assign_Premium_Managed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Protect</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.PersonContact.Account.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Protect</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Protect_Business_Hours</targetReference>
            </connector>
            <label>Is Protect</label>
        </rules>
        <rules>
            <name>Is_Grow</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.PersonContact.Account.Portfolio__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Grow</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Grow_Business_Hours</targetReference>
            </connector>
            <label>Is Grow</label>
        </rules>
    </decisions>
    <decisions>
        <description>Does Messaging End User exist</description>
        <name>Found_MEU</name>
        <label>Found MEU</label>
        <locationX>908</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>No_Messaging_End_User</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No User</defaultConnectorLabel>
        <rules>
            <name>Found_User</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Messaging_End_User</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Messaging_End_User</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Customer_Portfolio</targetReference>
            </connector>
            <label>Found User</label>
        </rules>
    </decisions>
    <decisions>
        <description>How many SMS have been sent. 1st one creates Task, all subsequent don&apos;t create additional Tasks</description>
        <name>How_Many_SMS_Sent</name>
        <label>How Many SMS Sent</label>
        <locationX>314</locationX>
        <locationY>1814</locationY>
        <defaultConnector>
            <targetReference>Assign_Values_for_Task</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>First</defaultConnectorLabel>
        <rules>
            <name>Second_or_More</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varMessageCount</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>2.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Summary</targetReference>
            </connector>
            <label>Second or More</label>
        </rules>
    </decisions>
    <decisions>
        <description>Is the current User online in Omnichannel?</description>
        <name>Is_User_Online</name>
        <label>Is User Online?</label>
        <locationX>1469</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>User_Offline</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Offline</defaultConnectorLabel>
        <rules>
            <name>Online</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_User_Presence</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_User_Presence</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Messaging_End_User</targetReference>
            </connector>
            <label>Online</label>
        </rules>
    </decisions>
    <decisions>
        <description>Does the user want to send another SMS?</description>
        <name>Send_Another_SMS</name>
        <label>Send Another SMS</label>
        <locationX>314</locationX>
        <locationY>2594</locationY>
        <defaultConnector>
            <targetReference>Update_Task_With_Summary</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Send_SMS</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>toggleSMS.value</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Clear_Collection_Variables</targetReference>
            </connector>
            <label>Send SMS</label>
        </rules>
    </decisions>
    <description>Used by Relationship Manager to initiate Outbound SMS to a Customer, launched from Action on Customer page.</description>
    <dynamicChoiceSets>
        <description>Stores values from Message Reason picklist field</description>
        <name>picklistMessageReason</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Message_Reason__c</picklistField>
        <picklistObject>MessagingSession</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <description>Used to reactively count characters remaining in SMS Message</description>
        <name>formulaCharacterCount</name>
        <dataType>Number</dataType>
        <expression>IF(ISBLANK({!SMS_Message}), 0, LEN(TRIM({!SMS_Message})))</expression>
        <scale>0</scale>
    </formulas>
    <formulas>
        <name>formulaCurrentDateAEST</name>
        <dataType>Date</dataType>
        <expression>{!$Flow.CurrentDate}</expression>
    </formulas>
    <formulas>
        <description>First and Last names of Customer Owner</description>
        <name>formulaOwnerFirstLast</name>
        <dataType>String</dataType>
        <expression>{!recordId.Owner.FirstName} + &apos; &apos; + {!recordId.Owner.LastName}</expression>
    </formulas>
    <formulas>
        <description>Stores the format and text for the Task subject</description>
        <name>formulaTaskSubject</name>
        <dataType>String</dataType>
        <expression>&quot;SMS  - &quot; + &quot;Outbound&quot;  +  &quot; - &quot; + {!varMessagingSessionReason}</expression>
    </formulas>
    <interviewLabel>Customer: Screen Flow - Initiate Outbound SMS {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Customer: Screen Flow - Initiate Outbound SMS</label>
    <loops>
        <description>Loops through Message Identifiers collection to add the single item to a single variable</description>
        <name>Loop_Message_Identifiers</name>
        <label>Loop Message Identifiers</label>
        <locationX>314</locationX>
        <locationY>1406</locationY>
        <collectionReference>Send_New_Outbound_SMS.messageIdentifiers</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Message_Identifier</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Assign_Message_Count</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Creates single interaction task all outbound SMS sent as part of Flow</description>
        <name>Create_Interaction_Task</name>
        <label>Create Interaction Task</label>
        <locationX>446</locationX>
        <locationY>2138</locationY>
        <connector>
            <targetReference>Another_Outbound_SMS_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>ActivityDate</field>
            <value>
                <elementReference>formulaCurrentDateAEST</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Channel__c</field>
            <value>
                <stringValue>SMS</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer_Portfolio_At_Time__c</field>
            <value>
                <elementReference>recordId.PersonContact.Account.Portfolio__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>varMessagingSessionSummaryForTask</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Direction__c</field>
            <value>
                <stringValue>Outbound</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner_Role__c</field>
            <value>
                <elementReference>recordId.Owner.UserRole.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Interaction_Customer_Owner__c</field>
            <value>
                <elementReference>formulaOwnerFirstLast</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Log_Method__c</field>
            <value>
                <stringValue>Omni</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Message_Identifier__c</field>
            <value>
                <elementReference>varMessageIdentifierForTask</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Reason_for_Contact__c</field>
            <value>
                <elementReference>varMessagingSessionReason</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Interaction_Task_Record_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Subject</field>
            <value>
                <elementReference>formulaTaskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhatId</field>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WhoId</field>
            <value>
                <elementReference>recordId.PersonContact.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Within_Business_Hours__c</field>
            <value>
                <elementReference>Within_Business_Hours_Action.isWithin</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Business_Hours</name>
        <label>Get Business Hours</label>
        <locationX>314</locationX>
        <locationY>866</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Within_Business_Hours_Action</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>varBusinessHoursName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>BusinessHours</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the record type details for interaction Tasks</description>
        <name>Get_Interaction_Task_Record_Type</name>
        <label>Get Interaction Task Record Type</label>
        <locationX>446</locationX>
        <locationY>2030</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Interaction_Task</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Task</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Interaction</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the Messaging End User that matches the Customer Account mobile phone number</description>
        <name>Get_Messaging_End_User</name>
        <label>Get Messaging End User</label>
        <locationX>908</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Found_MEU</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MessagingPlatformKey</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.PersonMobilePhone</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>MessagingEndUser</object>
        <sortField>LastModifiedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets current Omnichannel status of User</description>
        <name>Get_User_Presence</name>
        <label>Get User Presence</label>
        <locationX>1469</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_User_Online</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>UserId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCurrentState</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>IsAway</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>UserServicePresence</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Updates the created Task with Summary when additional messages are sent</description>
        <name>Update_Task_With_Summary</name>
        <label>Update Task With Summary</label>
        <locationX>446</locationX>
        <locationY>2702</locationY>
        <connector>
            <targetReference>Chat_History_End_Screen</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Flow_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Create_Interaction_Task</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>varMessagingSessionSummaryForTask</elementReference>
            </value>
        </inputAssignments>
        <object>Task</object>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <description>Screen for relationship manager choose if they want to send another SMS</description>
        <name>Another_Outbound_SMS_Screen</name>
        <label>Another Outbound SMS Screen</label>
        <locationX>314</locationX>
        <locationY>2378</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Update_Messaging_Session_Action</targetReference>
        </connector>
        <fields>
            <name>Another_Outbound_SMS_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Another_Outbound_SMS_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>displayAnotherSMS</name>
                    <fieldText>&lt;p&gt;Would you like to send another SMS to this customer?&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Another_Outbound_SMS_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>toggleSMS</name>
                    <extensionName>flowruntime:toggle</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Send SMS</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>messageToggleActive</name>
                        <value>
                            <stringValue>Yes</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>messageToggleInactive</name>
                        <value>
                            <stringValue>No</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <booleanValue>false</booleanValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <nextOrFinishButtonLabel>Next</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen for relationship manager to see chat history again</description>
        <name>Chat_History_End_Screen</name>
        <label>Chat History End Screen</label>
        <locationX>446</locationX>
        <locationY>2810</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ConversationAfterSend</name>
            <extensionName>c:lastSMSConversationEntries</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>Get_Messaging_End_User.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>beforeDT</name>
                <value>
                    <elementReference>$Flow.CurrentDateTime</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>pollInterval</name>
                <value>
                    <numberValue>30.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>pollForMsgs</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>numMessages</name>
                <value>
                    <numberValue>1000.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Finish</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Used to show user Flow Error when there is a fault or error in this Screen Flow</description>
        <name>Flow_Error_Screen</name>
        <label>Flow Error Screen</label>
        <locationX>710</locationX>
        <locationY>2810</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displayFlowError</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;{!texttemplateErrorSLDS}Flow Error Occurred&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>displayFlowErrorMessage</name>
            <fieldText>&lt;p&gt;An error has occurred. Please try again or send the below details to your Salesforce admin&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Flow Start Time: {!$Flow.InterviewStartTime}&lt;/p&gt;&lt;p&gt;Flow Name: Customer: Screen Flow - Initiate Outbound SMS&lt;/p&gt;&lt;p&gt;Flow Error Message: {!$Flow.FaultMessage}&lt;/p&gt;&lt;p&gt;Flow User: {!$User.Username}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen for relationship manager to type SMS message to be sent to customer</description>
        <name>Initiate_Outbound_SMS_Screen</name>
        <label>Initiate Outbound SMS Screen</label>
        <locationX>314</locationX>
        <locationY>1190</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Send_New_Outbound_SMS</targetReference>
        </connector>
        <fields>
            <name>Conversation</name>
            <extensionName>c:lastSMSConversationEntries</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>Get_Messaging_End_User.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>beforeDT</name>
                <value>
                    <elementReference>$Flow.CurrentDateTime</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>pollInterval</name>
                <value>
                    <numberValue>30.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>pollForMsgs</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>numMessages</name>
                <value>
                    <numberValue>1000.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>displayCharacterCount</name>
            <fieldText>&lt;p&gt;{!formulaCharacterCount} / 320&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>SMS_Message</name>
            <fieldText>SMS Message</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;SMS character count must be 320 or below for a single message&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>LEN({!SMS_Message}) &lt;= 320</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>Message_Reason</name>
            <choiceReferences>picklistMessageReason</choiceReferences>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>varMessagingSessionReason</elementReference>
            </defaultValue>
            <fieldText>Message Reason</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Send SMS</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Screen displays error if there is no existing Messaging User for this Customer</description>
        <name>No_Messaging_End_User</name>
        <label>No Messaging End User</label>
        <locationX>1502</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>screenNoNEUError</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;{!texttemplateErrorSLDS}Flow Error Occurred&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>screenNoNEU</name>
            <fieldText>&lt;p&gt;This customer does not have a Messaging End User record to be able to send an SMS to. Please Contact your Salesforce Admin to have one created&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Screen to tell user to go online for SMS</description>
        <name>User_Offline</name>
        <label>User Offline</label>
        <locationX>2030</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_User_Presence</targetReference>
        </connector>
        <fields>
            <name>displayOfflineError</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;{!texttemplateErrorSLDS}Offline&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>displayUserOffline</name>
            <fieldText>&lt;p&gt;You must be online in Omnichannel to send SMS, please update your status  to Available SMS and try again&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Try Again</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>1343</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_User_Presence</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>Stores the SLDS div class to render error notifications in display text</description>
        <name>texttemplateErrorSLDS</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;div class=&quot;slds-scoped-notification slds-theme_error&quot; role=&quot;status&quot;&gt;</text>
    </textTemplates>
    <variables>
        <description>Used to store Account Record from where this Screen Flow was launched from</description>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>varBusinessHoursName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores the number of SMS sent as part of Flow</description>
        <name>varMessageCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <description>Stores Message Identifier from Send Conversation Message action to be used in Apex Action</description>
        <name>varMessageIdentifier</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores Message Identifier from Send Conversation Message to be used in Task</description>
        <name>varMessageIdentifierForTask</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores Id of first Messaging Session from 1st SMS send</description>
        <name>varMessagingSessionId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores Reason for Contact of first Messaging Session from 1st SMS send</description>
        <name>varMessagingSessionReason</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores Messaging Session Summary of first Messaging Session from 1st SMS send</description>
        <name>varMessagingSessionSummary</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores Messaging Session Summary of first Messaging Session from 1st SMS send</description>
        <name>varMessagingSessionSummaryForTask</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Stores Messaging User Id, used in the Send Conversation SMS Action</description>
        <name>varMessagingUsersIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
