<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <choices>
        <name>Remove</name>
        <choiceText>Remove Current Delegate Setting</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Remove</stringValue>
        </value>
    </choices>
    <choices>
        <name>Update</name>
        <choiceText>Update Delegate Setting</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Update</stringValue>
        </value>
    </choices>
    <decisions>
        <name>IsUserInDelegatePeriod</name>
        <label>IsUserInDelegatePeriod</label>
        <locationX>394</locationX>
        <locationY>52</locationY>
        <defaultConnector>
            <targetReference>Screen2SetDelegateAssignment</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not in period</defaultConnectorLabel>
        <rules>
            <name>Is_in_period</name>
            <conditionLogic>((1 AND 4) OR (2 AND 5)) AND 3</conditionLogic>
            <conditions>
                <leftValueReference>GetUserRecord.Delegate_Assignment_Start_Date__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetUserRecord.Delegate_Assignment_End_Date__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>$Flow.CurrentDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetUserRecord.Delegate_Assignment_User_ID__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetUserRecord.Delegate_Assignment_Start_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetUserRecord.Delegate_Assignment_End_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetDelegateUserRecord</targetReference>
            </connector>
            <label>Is in period</label>
        </rules>
    </decisions>
    <decisions>
        <name>Remove_Update</name>
        <label>Remove/Update</label>
        <locationX>810</locationX>
        <locationY>178</locationY>
        <defaultConnector>
            <targetReference>Screen2SetDelegateAssignment</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>IsUpdate</defaultConnectorLabel>
        <rules>
            <name>IsRemove</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Screen1_Update_Remove</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Remove</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RemoveDeletageSetting</targetReference>
            </connector>
            <label>IsRemove</label>
        </rules>
    </decisions>
    <decisions>
        <name>UserIdCheck</name>
        <label>UserIdCheck</label>
        <locationX>810</locationX>
        <locationY>530</locationY>
        <defaultConnector>
            <targetReference>UpdateUserRecord</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>False</defaultConnectorLabel>
        <rules>
            <name>True</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>DelegateUserCheck</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Screen2SetDelegateAssignment</targetReference>
            </connector>
            <label>True</label>
        </rules>
    </decisions>
    <formulas>
        <name>CurrentUserId</name>
        <dataType>String</dataType>
        <expression>{!$User.Id}</expression>
    </formulas>
    <formulas>
        <name>DelegateUserCheck</name>
        <dataType>Boolean</dataType>
        <expression>CASESAFEID({!CurrentUserId})== CASESAFEID({!screen2inputUserId.recordId})</expression>
    </formulas>
    <interviewLabel>Delegate Assignment Setting Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Delegate Assignment Setting Flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>GetDelegateUserRecord</name>
        <label>GetDelegateUserRecord</label>
        <locationX>664</locationX>
        <locationY>42</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Show_Current_Delegated_Assignment</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetUserRecord.Delegate_Assignment_User_ID__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Name</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetUserRecord</name>
        <label>GetUserRecord</label>
        <locationX>209</locationX>
        <locationY>50</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>IsUserInDelegatePeriod</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CurrentUserId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>User</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Delegate_Assignment_User_ID__c</queriedFields>
        <queriedFields>Delegate_Assignment_End_Date__c</queriedFields>
        <queriedFields>Delegate_Assignment_Start_Date__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>RemoveDeletageSetting</name>
        <label>RemoveDeletageSetting</label>
        <locationX>509</locationX>
        <locationY>393</locationY>
        <connector>
            <targetReference>Screen3</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CurrentUserId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Delegate_Assignment_End_Date__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>Delegate_Assignment_Start_Date__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>Delegate_Assignment_User_ID__c</field>
        </inputAssignments>
        <object>User</object>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateUserRecord</name>
        <label>UpdateUserRecord</label>
        <locationX>201</locationX>
        <locationY>522</locationY>
        <connector>
            <targetReference>GetUserRecord</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CurrentUserId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Delegate_Assignment_End_Date__c</field>
            <value>
                <elementReference>Screen2inputEndDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Delegate_Assignment_Start_Date__c</field>
            <value>
                <elementReference>Screen2inputStartDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Delegate_Assignment_User_ID__c</field>
            <value>
                <elementReference>screen2inputUserId.recordId</elementReference>
            </value>
        </inputAssignments>
        <object>User</object>
    </recordUpdates>
    <screens>
        <name>Screen2SetDelegateAssignment</name>
        <label>Set Delegate Assignment</label>
        <locationX>818</locationX>
        <locationY>311</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>UserIdCheck</targetReference>
        </connector>
        <fields>
            <name>Screen2Display</name>
            <fieldText>&lt;p&gt;&lt;b&gt;My Salesforcee&lt;/b&gt; &lt;b&gt;Delegation:&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Screen2inputStartDate</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>$Flow.CurrentDate</elementReference>
            </defaultValue>
            <fieldText>Start Date</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;Start Date must be future date&lt;/p&gt;</errorMessage>
                <formulaExpression>{!Screen2inputStartDate}&gt;={!$Flow.CurrentDate}</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>Screen2inputEndDate</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>$Flow.CurrentDate</elementReference>
            </defaultValue>
            <fieldText>End Date</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;End Date must be a future date, and greater than or equal to Start Date&lt;/p&gt;</errorMessage>
                <formulaExpression>{!Screen2inputEndDate}&gt;={!$Flow.CurrentDate} &amp;&amp; {!Screen2inputEndDate}&gt;={!Screen2inputStartDate}</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>screen2inputUserId</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>ManagerId</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Delegate Assignment User</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>User</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Screen2ErrorMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Delegate User must be other user&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DelegateUserCheck</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Screen3</name>
        <label>Cleared Success Message</label>
        <locationX>350</locationX>
        <locationY>275</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>GetUserRecord</targetReference>
        </connector>
        <fields>
            <name>Screen3Display</name>
            <fieldText>&lt;p&gt;Setting Successfully Cleared.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Show_Current_Delegated_Assignment</name>
        <label>Show Current Delegated Assignment</label>
        <locationX>817</locationX>
        <locationY>42</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Remove_Update</targetReference>
        </connector>
        <fields>
            <name>DisplayCurrentDelegateAssignment</name>
            <fieldText>&lt;p&gt;&lt;b&gt;My Salesforce &lt;/b&gt;&lt;b style=&quot;background-color: rgb(255, 255, 255); color: rgb(62, 62, 60);&quot;&gt;Delegation:&lt;/b&gt;&lt;/p&gt;&lt;p&gt;&lt;b&gt;Start Date:&lt;/b&gt; {!GetUserRecord.Delegate_Assignment_Start_Date__c}&lt;/p&gt;&lt;p&gt;&lt;b&gt;End Date: &lt;/b&gt;{!GetUserRecord.Delegate_Assignment_End_Date__c}&lt;/p&gt;&lt;p&gt;&lt;b&gt;Delegate User: &lt;/b&gt;{!GetDelegateUserRecord.Name}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Screen1_Update_Remove</name>
            <choiceReferences>Remove</choiceReferences>
            <choiceReferences>Update</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>Update</defaultSelectedChoiceReference>
            <fieldText>Select the action below then &quot;Next&quot;</fieldText>
            <fieldType>RadioButtons</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>50</locationY>
        <connector>
            <targetReference>GetUserRecord</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>DelegateUser</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
</Flow>
