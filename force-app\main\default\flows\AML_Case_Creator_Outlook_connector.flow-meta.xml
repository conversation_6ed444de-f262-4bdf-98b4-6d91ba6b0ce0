<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Case_Number</name>
        <label>Assign Case Number</label>
        <locationX>1106</locationX>
        <locationY>1538</locationY>
        <assignmentItems>
            <assignToReference>CaseNumber</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Copy_1_of_Get_Case.CaseNumber</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Case_Success</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>The assignment element sets the variable **case_origin_not_filled** to **True** if the case origin is not entered.</description>
        <name>assign_case_origin_not_filled_to_true</name>
        <label>assign case origin not filled to true</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>case_origin_not_filled</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Create_AML_Case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Store created cases.</description>
        <name>Assign_Cases_to_List</name>
        <label>Assign Cases to List</label>
        <locationX>644</locationX>
        <locationY>2054</locationY>
        <assignmentItems>
            <assignToReference>CaseList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_Case</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_1_of_Assign_Case_Number</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Customer_to_Case</name>
        <label>Assign Customer to Case</label>
        <locationX>1106</locationX>
        <locationY>1214</locationY>
        <assignmentItems>
            <assignToReference>CaseRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Customer.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Case_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Link parent and child case.</description>
        <name>Assign_Customer_to_child_case</name>
        <label>Assign Customer to child case and link case</label>
        <locationX>644</locationX>
        <locationY>1730</locationY>
        <assignmentItems>
            <assignToReference>CaseRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Customer.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>CaseRecord.ParentId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ParentCaseId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Case_Loop_Records</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ParentCase</name>
        <label>Assign ParentCase</label>
        <locationX>512</locationX>
        <locationY>1538</locationY>
        <assignmentItems>
            <assignToReference>ParentCaseId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>CaseRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Customer_to_child_case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignAccount_number_back_to_null</name>
        <label>AssignAccount number back to null</label>
        <locationX>512</locationX>
        <locationY>1322</locationY>
        <assignmentItems>
            <assignToReference>CaseRecord.ECDD_SportsBet_Account_Number__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <targetReference>Create_Parent_Case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Assign_Case_Number</name>
        <label>Assign Case Number</label>
        <locationX>644</locationX>
        <locationY>2162</locationY>
        <assignmentItems>
            <assignToReference>CaseNumber</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Case.CaseNumber</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Counter</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Case_Success_Loop</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>NoMore</name>
        <choiceText>No</choiceText>
        <dataType>String</dataType>
    </choices>
    <choices>
        <name>YesMore</name>
        <choiceText>Yes</choiceText>
        <dataType>String</dataType>
    </choices>
    <decisions>
        <description>The decision element checks if the Case Origin field is empty, and if true, routes the flow through the “Case Origin Not Entered” outcome.</description>
        <name>check_if_the_user_has_entered_case_origin_or_not</name>
        <label>check if the user has entered case origin or not</label>
        <locationX>586</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Get_Queue</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Case Origin Entered</defaultConnectorLabel>
        <rules>
            <name>Case_Origin_Not_Entered</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>CaseRecord.Origin</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assign_case_origin_not_filled_to_true</targetReference>
            </connector>
            <label>Case Origin Not Entered</label>
        </rules>
    </decisions>
    <decisions>
        <name>Counter_greater_than_1</name>
        <label>Counter greater than 1</label>
        <locationX>644</locationX>
        <locationY>2378</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>AccountNumber_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>YesGreater</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Counter</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>More_Customers_Screen_Loop</targetReference>
            </connector>
            <label>YesGreater</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check if customer exists.</description>
        <name>Has_customer_been_found</name>
        <label>Has customer been found?</label>
        <locationX>1122</locationX>
        <locationY>890</locationY>
        <defaultConnector>
            <targetReference>Cant_find_account_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Customer</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Display_Customer_Details</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Loop through Customers</description>
        <name>Is_first_loop</name>
        <label>Is first loop?</label>
        <locationX>644</locationX>
        <locationY>1214</locationY>
        <defaultConnector>
            <targetReference>Assign_Customer_to_child_case</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_first_loop</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Counter</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignAccount_number_back_to_null</targetReference>
            </connector>
            <label>Yes first loop</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks if More Customers should be added.</description>
        <name>MoreCustomers</name>
        <label>MoreCustomers?</label>
        <locationX>875</locationX>
        <locationY>1106</locationY>
        <defaultConnector>
            <targetReference>Assign_Customer_to_Case</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>YesMoreCustomers</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>YesMore</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_first_loop</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>MoreCustomersLoop</name>
        <label>MoreCustomers?</label>
        <locationX>446</locationX>
        <locationY>2594</locationY>
        <defaultConnector>
            <targetReference>Cases_Success</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes_More_Customers</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>YesMore</leftValueReference>
                <operator>WasSelected</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>AccountNumber_Screen</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>This flow is used in the Salesforce Outlook connector to create LER and UAR cases directly from Outlook.

16/9/2024
- Updated Get Queue element to get AML_Unassigned_Queue.</description>
    <dynamicChoiceSets>
        <name>CaseOrigin</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Origin</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>AML Case Creator - Outlook connector {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML Case Creator - Outlook connector</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Create Cases</description>
        <name>Create_Case_Loop_Records</name>
        <label>Create Case Loop Records</label>
        <locationX>644</locationX>
        <locationY>1838</locationY>
        <connector>
            <targetReference>Get_Case</targetReference>
        </connector>
        <inputReference>CaseRecord</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Create_Case_Record</name>
        <label>Create Case Record</label>
        <locationX>1106</locationX>
        <locationY>1322</locationY>
        <connector>
            <targetReference>Copy_1_of_Get_Case</targetReference>
        </connector>
        <inputReference>CaseRecord</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Create_Parent_Case</name>
        <label>Create Parent Case</label>
        <locationX>512</locationX>
        <locationY>1430</locationY>
        <connector>
            <targetReference>Assign_ParentCase</targetReference>
        </connector>
        <inputReference>CaseRecord</inputReference>
    </recordCreates>
    <recordLookups>
        <description>Get Created Case</description>
        <name>Copy_1_of_Get_Case</name>
        <label>Get Case</label>
        <locationX>1106</locationX>
        <locationY>1430</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Case_Number</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CaseRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get created case.</description>
        <name>Get_Case</name>
        <label>Get Case</label>
        <locationX>644</locationX>
        <locationY>1946</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Cases_to_List</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CaseRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get Triggering Case - SB Account Number.</description>
        <name>Get_Customer</name>
        <label>Get Customer</label>
        <locationX>1122</locationX>
        <locationY>782</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Has_customer_been_found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Sports_Bet_Account_Number__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CaseRecord.ECDD_SportsBet_Account_Number__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>The element retrieves the Record Type ID for Non-Transaction Monitoring cases.</description>
        <name>Get_Non_TM_RT</name>
        <label>Get Non TM RT</label>
        <locationX>586</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_AML_Case</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Non_Transaction_Monitoring</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>CaseRecord.RecordTypeId</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Retrieves “AML_Unassigned_Queue,” storing the Id of the queue in the CaseRecord &gt; Owner ID variable.</description>
        <name>Get_Queue</name>
        <label>Get Queue</label>
        <locationX>1122</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>More_Customers_Screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>AML_Unassigned_Queue</stringValue>
            </value>
        </filters>
        <object>Group</object>
        <outputAssignments>
            <assignToReference>CaseRecord.OwnerId</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <description>Submit SB Account Number</description>
        <name>AccountNumber_Screen</name>
        <label>AccountNumber Screen</label>
        <locationX>1122</locationX>
        <locationY>674</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Get_Customer</targetReference>
        </connector>
        <fields>
            <name>accountnumber_displayText</name>
            <fieldText>&lt;p&gt;Please add the customers account number. Salesforce will then try and pull up the customer details.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <fieldType>ObjectProvided</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <objectFieldReference>CaseRecord.ECDD_SportsBet_Account_Number__c</objectFieldReference>
        </fields>
        <nextOrFinishButtonLabel>Find Customer</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>The screen allows users to input or update the SportsBet Account Number when a customer record could not be retrieved, providing an option to retry by clicking &quot;Find Customer.&quot;</description>
        <name>Cant_find_account_Screen</name>
        <label>Cant find account Screen</label>
        <locationX>1370</locationX>
        <locationY>998</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Get_Customer</targetReference>
        </connector>
        <fields>
            <name>NoMatchFound</name>
            <fieldText>&lt;p&gt;Unfortunately we were unable to retrieve a customer record.  Please review and update the below account number.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <fieldType>ObjectProvided</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <objectFieldReference>CaseRecord.ECDD_SportsBet_Account_Number__c</objectFieldReference>
        </fields>
        <nextOrFinishButtonLabel>Find Customer</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Case_Success</name>
        <label>Case Success</label>
        <locationX>1106</locationX>
        <locationY>1646</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>CaseText</name>
            <fieldText>&lt;p&gt;Case {!CaseNumber} has been created successfully. Please log all email interactions against this case&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Successful case creation message.</description>
        <name>Case_Success_Loop</name>
        <label>Case Success Loop</label>
        <locationX>644</locationX>
        <locationY>2270</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Counter_greater_than_1</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_CaseText</name>
            <fieldText>&lt;p&gt;Case {!CaseNumber} has been created successfully. Please log all email interactions against this case&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Cases_Success</name>
        <label>Cases Success</label>
        <locationX>578</locationX>
        <locationY>2702</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_2_of_CaseText</name>
            <fieldText>&lt;p&gt;All below cases have been successfully created!&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Case_Table</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Data Table</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>CaseList</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;CaseNumber&quot;,&quot;guid&quot;:&quot;column-fdf0&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Case Number&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Customer_Name__c&quot;,&quot;guid&quot;:&quot;column-faa3&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Customer Name&quot;,&quot;type&quot;:&quot;customRichText&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>The screen component allows users to input the Case Origin, Description, and Requester Details (Name and Email) for creating an AML case.</description>
        <name>Create_AML_Case</name>
        <label>Create AML Case</label>
        <locationX>586</locationX>
        <locationY>242</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>check_if_the_user_has_entered_case_origin_or_not</targetReference>
        </connector>
        <fields>
            <name>Case_Details</name>
            <fieldText>Case Details</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Case_Details_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>CaseRecord.Origin</objectFieldReference>
                </fields>
                <fields>
                    <name>ErrorMsg</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(253, 2, 2);&quot;&gt;Please select the Case Origin.&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>case_origin_not_filled</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>CaseRecord.Description</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Requester_Details</name>
            <fieldText>Requester Details</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Requester_Details_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>CaseRecord.Requester_Name__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Requester_Details_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>CaseRecord.Requester_Email__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>The screen displays customer details in a table (DetailsTable) for the user to review and confirm before proceeding to create a case, with an option to go back and re-enter the customer account number if needed.</description>
        <name>Display_Customer_Details</name>
        <label>Display Customer Details</label>
        <locationX>875</locationX>
        <locationY>998</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>MoreCustomers</targetReference>
        </connector>
        <fields>
            <name>CustomerDetailsScreen</name>
            <fieldText>&lt;p&gt;A customer record was found. Please review below table to confirm the correct customer has been retrieved.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;{!DetailsTable}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Select create case if customer information is correct. Otherwise go to previous screen to re-enter customer account number.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Create Case</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>More customers question.</description>
        <name>More_Customers_Screen</name>
        <label>More Customers Screen</label>
        <locationX>1122</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>AccountNumber_Screen</targetReference>
        </connector>
        <fields>
            <name>test</name>
            <fieldText>&lt;p&gt;{!CaseRecord.Origin}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Are_there_more_customers</name>
            <choiceReferences>YesMore</choiceReferences>
            <choiceReferences>NoMore</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Are there multiple customers that need a case created for this?</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>More_Customers_Screen_Loop</name>
        <label>More Customers Screen</label>
        <locationX>446</locationX>
        <locationY>2486</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>MoreCustomersLoop</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_Are_there_more_customers</name>
            <choiceReferences>YesMore</choiceReferences>
            <choiceReferences>NoMore</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Are there more customers?</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>460</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Non_TM_RT</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>DetailsTable</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;table class=&quot;ql-table-blob&quot; border=&quot;1&quot; style=&quot;border: none;&quot;&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td width=&quot;301&quot; valign=&quot;top&quot; style=&quot;width: 225.4pt; border: 1pt solid windowtext; padding: 0cm 5.4pt;&quot;&gt;&lt;p class=&quot;MsoNormal&quot; style=&quot;margin: 0cm; font-size: 12pt; font-family: Calibri, sans-serif;&quot;&gt;&lt;b&gt;&lt;span style=&quot;font-size: 10pt;&quot;&gt;Account Number&lt;o:p&gt;&lt;/o:p&gt;&lt;/span&gt;&lt;/b&gt;&lt;/p&gt;&lt;/td&gt;&lt;td width=&quot;301&quot; valign=&quot;top&quot; style=&quot;width: 225.4pt; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-image: initial; border-left: none; padding: 0cm 5.4pt;&quot;&gt;&lt;p class=&quot;MsoNormal&quot; style=&quot;margin: 0cm; font-size: 12pt; font-family: Calibri, sans-serif;&quot;&gt;&lt;span style=&quot;font-size: 10pt; color: rgb(68, 68, 68); background: white;&quot;&gt;{!Get_Customer.Sports_Bet_Account_Number__c}&lt;/span&gt;&lt;span style=&quot;font-size: 10pt;&quot;&gt;&lt;o:p&gt;&lt;/o:p&gt;&lt;/span&gt;&lt;/p&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td width=&quot;301&quot; valign=&quot;top&quot; style=&quot;width: 225.4pt; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt;&quot;&gt;&lt;p class=&quot;MsoNormal&quot; style=&quot;margin: 0cm; font-size: 12pt; font-family: Calibri, sans-serif;&quot;&gt;&lt;b&gt;&lt;span style=&quot;font-size: 10pt;&quot;&gt;First Name&lt;o:p&gt;&lt;/o:p&gt;&lt;/span&gt;&lt;/b&gt;&lt;/p&gt;&lt;/td&gt;&lt;td width=&quot;301&quot; valign=&quot;top&quot; style=&quot;width: 225.4pt; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt;&quot;&gt;&lt;p class=&quot;MsoNormal&quot; style=&quot;margin: 0cm; font-size: 12pt; font-family: Calibri, sans-serif;&quot;&gt;&lt;span style=&quot;font-size: 10pt; color: rgb(68, 68, 68); background: white;&quot;&gt;{!Get_Customer.FirstName}&lt;/span&gt;&lt;span style=&quot;font-size: 10pt;&quot;&gt;&lt;o:p&gt;&lt;/o:p&gt;&lt;/span&gt;&lt;/p&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td width=&quot;301&quot; valign=&quot;top&quot; style=&quot;width: 225.4pt; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt;&quot;&gt;&lt;p class=&quot;MsoNormal&quot; style=&quot;margin: 0cm; font-size: 12pt; font-family: Calibri, sans-serif;&quot;&gt;&lt;b&gt;&lt;span style=&quot;font-size: 10pt;&quot;&gt;Last Name&lt;o:p&gt;&lt;/o:p&gt;&lt;/span&gt;&lt;/b&gt;&lt;/p&gt;&lt;/td&gt;&lt;td width=&quot;301&quot; valign=&quot;top&quot; style=&quot;width: 225.4pt; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt;&quot;&gt;&lt;p class=&quot;MsoNormal&quot; style=&quot;margin: 0cm; font-size: 12pt; font-family: Calibri, sans-serif;&quot;&gt;&lt;span style=&quot;font-size: 10pt; color: rgb(68, 68, 68); background: white;&quot;&gt;{!Get_Customer.LastName}&lt;/span&gt;&lt;span style=&quot;font-size: 10pt;&quot;&gt;&lt;o:p&gt;&lt;/o:p&gt;&lt;/span&gt;&lt;/p&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>case_origin_not_filled</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>CaseList</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>CaseNumber</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CaseRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>Counter</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>ECDDCase</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>ECDDCases</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>ParentCaseId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
