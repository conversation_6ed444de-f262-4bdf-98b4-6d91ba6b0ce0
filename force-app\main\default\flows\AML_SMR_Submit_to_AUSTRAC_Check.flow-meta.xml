<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>This flow is designed to asynchronously update the SMR Case to trigger a milestone. The SMR Approval process changes the SMR Case status to &quot;Submit to AUSTRAC,&quot; but this status update doesn&apos;t trigger the SMR milestone. Therefore, an asynchronous update on the SMR Case is employed to ensure the milestone is triggered.</description>
    <environments>Default</environments>
    <interviewLabel>AML SMR Submit to Austrac Check {!$Flow.CurrentDateTime}</interviewLabel>
    <label>AML SMR Submit to AUSTRAC Check</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>This custom field is updated asynchronously to prompt an update on the SMR case, which then triggers the Close SMR case milestone.</description>
        <name>Update_Submit_to_AUSTRAC_checkbox</name>
        <label>Update Submit to AUSTRAC checkbox</label>
        <locationX>308</locationX>
        <locationY>276</locationY>
        <inputAssignments>
            <field>Submit_to_Austrac_check__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Submit to AUSTRAC</stringValue>
            </value>
        </filters>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Update_Submit_to_AUSTRAC_checkbox</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
