<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Hours_per_Message__c</fullName>
    <defaultValue>48</defaultValue>
    <externalId>false</externalId>
    <inlineHelpText>Sends a Single Message for a Unique Customer in this number of hours. Limiting time window, where a Customer has received a message, at least this amount of time must pass before they will receive another/second message. Value is in hours.</inlineHelpText>
    <label>Hours per Message</label>
    <precision>18</precision>
    <required>true</required>
    <scale>0</scale>
    <trackTrending>false</trackTrending>
    <type>Number</type>
    <unique>false</unique>
</CustomField>
