<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Attendees: Update Customer Status at the Event Field</stringValue>
            </value>
        </processMetadataValues>
        <name>myWaitEvent_myWait_myRule_1_event_0_SA1</name>
        <label>Call Flow</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <actionName>Attendees_Update_Customer_Status_at_the_Event_Field</actionName>
        <actionType>flow</actionType>
        <connector>
            <targetReference>myWaitEvent_myWait_myRule_1_event_0_postWaitExecutionAssignment</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>CampaignId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>CampaignId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Attendees_Update_Customer_Status_at_the_Event_Field</nameSegment>
    </actionCalls>
    <apiVersion>52.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>myWaitAssignment_myWait_myRule_1</name>
        <label>myWaitAssignment_myWait_myRule_1</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>cancelWaits</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>myWait_myRule_1</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>myDecision</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>myWaitEvent_myWait_myRule_1_event_0_postWaitExecutionAssignment</name>
        <label>myWaitEvent_myWait_myRule_1_event_0_postWaitExecutionAssignment</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>myWaitEvent_myWait_myRule_1_event_0_postActionExecutionVariable</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>myWait_myRule_1</targetReference>
        </connector>
    </assignments>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_pmetdec</targetReference>
            </connector>
            <label>Campaign Date is NOW()</label>
        </rules>
    </decisions>
    <decisions>
        <name>myPostWaitDecision_myWaitEvent_myWait_myRule_1_event_0</name>
        <label>myPostWaitDecision_myWaitEvent_myWait_myRule_1_event_0</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myWaitEvent_myWait_myRule_1_event_0_postWaitExecutionAssignment</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myPostWaitRule_myWaitEvent_myWait_myRule_1_event_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_current.Start_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myWaitEvent_myWait_myRule_1_event_0_SA1</targetReference>
            </connector>
            <label>myPostWaitRule_myWaitEvent_myWait_myRule_1_event_0</label>
        </rules>
    </decisions>
    <decisions>
        <name>myPreWaitDecision_myWait_myRule_1</name>
        <label>myPreWaitDecision_myWait_myRule_1</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myWaitAssignment_myWait_myRule_1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myPreWaitRule_myWait_myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision</targetReference>
            </connector>
            <label>myPreWaitRule_myWait_myRule_1</label>
        </rules>
    </decisions>
    <decisions>
        <name>myRule_1_pmetdec</name>
        <label>Previously Met Decision</label>
        <locationX>100</locationX>
        <locationY>100</locationY>
        <defaultConnector>
            <targetReference>myWait_myRule_1</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Previously Met</defaultConnectorLabel>
        <rules>
            <name>myRule_1_pmetnullrule</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>myVariable_old</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myWait_myRule_1</targetReference>
            </connector>
            <label>Previously Met - Null</label>
        </rules>
        <rules>
            <name>myRule_1_pmetrule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1_pmetrule</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Previously Met - Prev</label>
        </rules>
    </decisions>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>OR(
AND(
ISNEW(),
NOT(ISBLANK([Campaign].Start_Date__c)),
TEXT([Campaign].Status) &lt;&gt; &quot;Cancelled&quot;
),

AND(
NOT(ISNEW()),
NOT(ISBLANK([Campaign].Start_Date__c)),
TEXT([Campaign].Status) &lt;&gt; &quot;Cancelled&quot;
)
)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1</name>
        <dataType>Boolean</dataType>
        <expression>OR(
AND(
ISNEW(),
NOT(ISBLANK({!myVariable_current.Start_Date__c})),
TEXT({!myVariable_current.Status}) &lt;&gt; &quot;Cancelled&quot;
),

AND(
NOT(ISNEW()),
NOT(ISBLANK({!myVariable_current.Start_Date__c})),
TEXT({!myVariable_current.Status}) &lt;&gt; &quot;Cancelled&quot;
)
)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>OR(
AND(
ISNEW(),
NOT(ISBLANK([Campaign].Start_Date__c)),
TEXT([Campaign].Status) &lt;&gt; &quot;Cancelled&quot;
),

AND(
NOT(ISNEW()),
NOT(ISBLANK([Campaign].Start_Date__c)),
TEXT([Campaign].Status) &lt;&gt; &quot;Cancelled&quot;
)
)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1_pmetrule</name>
        <dataType>Boolean</dataType>
        <expression>OR(
AND(
ISNEW(),
NOT(ISBLANK({!myVariable_old.Start_Date__c})),
TEXT({!myVariable_old.Status}) &lt;&gt; &quot;Cancelled&quot;
),

AND(
NOT(ISNEW()),
NOT(ISBLANK({!myVariable_old.Start_Date__c})),
TEXT({!myVariable_old.Status}) &lt;&gt; &quot;Cancelled&quot;
)
)</expression>
    </formulas>
    <interviewLabel>Campaign_Update_Attendees-2_InterviewLabel</interviewLabel>
    <label>Campaign: Update Attendees</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Campaign</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>ObjectVariable</name>
        <value>
            <elementReference>myVariable_current</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OldObjectVariable</name>
        <value>
            <elementReference>myVariable_old</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>TriggerType</name>
        <value>
            <stringValue>onAllChanges</stringValue>
        </value>
    </processMetadataValues>
    <processType>Workflow</processType>
    <startElementReference>myPreWaitDecision_myWait_myRule_1</startElementReference>
    <status>Active</status>
    <variables>
        <name>cancelWaits</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>myVariable_current</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Campaign</objectType>
    </variables>
    <variables>
        <name>myVariable_old</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Campaign</objectType>
    </variables>
    <variables>
        <name>myWaitEvent_myWait_myRule_1_event_0_postActionExecutionVariable</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <waits>
        <name>myWait_myRule_1</name>
        <label>myWait_myRule_1</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>defaultLabel</defaultConnectorLabel>
        <waitEvents>
            <processMetadataValues>
                <name>inputParameterType</name>
                <value>
                    <stringValue>referenced</stringValue>
                </value>
            </processMetadataValues>
            <name>myWaitEvent_myWait_myRule_1_event_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>myWaitEvent_myWait_myRule_1_event_0_postActionExecutionVariable</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myPostWaitDecision_myWaitEvent_myWait_myRule_1_event_0</targetReference>
            </connector>
            <eventType>DateRefAlarmEvent</eventType>
            <inputParameters>
                <name>TimeTableColumnEnumOrId</name>
                <value>
                    <stringValue>Campaign</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>EntityObjectId</name>
                <value>
                    <elementReference>myVariable_current.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>TimeOffsetUnit</name>
                <value>
                    <stringValue>Hours</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>TimeOffset</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>TimeFieldColumnEnumOrId</name>
                <value>
                    <stringValue>Start_Date__c</stringValue>
                </value>
            </inputParameters>
            <label>myWaitEvent_myWait_myRule_1_event_0</label>
        </waitEvents>
    </waits>
</Flow>
