<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Route_Cases_to_Agent</name>
        <label>Route Cases to Agent</label>
        <locationX>50</locationX>
        <locationY>2606</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <connector>
            <targetReference>Selected_Messaging_Sessions</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZOfCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>Email</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
            <value>
                <stringValue>0K92y000000XZHrCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
            <value>
                <stringValue>Premium Managed Routing Configuration</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>varCurrentUser</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentRequired</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>recordsToRouteType</name>
            <value>
                <stringValue>multiple</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>varCaseIds</elementReference>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Route_Messaging_Sessions_to_Agent</name>
        <label>Route Messaging Sessions to Agent</label>
        <locationX>50</locationX>
        <locationY>2906</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <connector>
            <targetReference>Selected_Voice_Calls</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNNCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Messaging</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_livemessage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
            <value>
                <stringValue>0K92y000000XZHrCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
            <value>
                <stringValue>Premium Managed Routing Configuration</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>varCurrentUser</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentRequired</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>recordsToRouteType</name>
            <value>
                <stringValue>multiple</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>varMessagingSessionIds</elementReference>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Route_Voice_Calls_to_Agent</name>
        <label>Route Voice Calls to Agent</label>
        <locationX>50</locationX>
        <locationY>3206</locationY>
        <actionName>routeWork</actionName>
        <actionType>routeWork</actionType>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenUsersWorkItems</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>serviceChannelId</name>
            <value>
                <stringValue>0N92y000000XZNOCA4</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelLabel</name>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>serviceChannelDevName</name>
            <value>
                <stringValue>sfdc_phone</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingType</name>
            <value>
                <stringValue>Agent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigId</name>
            <value>
                <stringValue>0K92y000000XZHsCAO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>routingConfigLabel</name>
            <value>
                <stringValue>Premium Managed Voicemail Routing</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentId</name>
            <value>
                <elementReference>varCurrentUser</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>agentLabel</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentRequired</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>skillOption</name>
        </inputParameters>
        <inputParameters>
            <name>skillRequirementsResourceItem</name>
        </inputParameters>
        <inputParameters>
            <name>isAgentVariable</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>botId</name>
        </inputParameters>
        <inputParameters>
            <name>botLabel</name>
        </inputParameters>
        <inputParameters>
            <name>recordsToRouteType</name>
            <value>
                <stringValue>multiple</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>varVoiceCallIds</elementReference>
            </value>
        </inputParameters>
        <nameSegment>routeWork</nameSegment>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Queue_Ids</name>
        <label>Assign Queue Ids</label>
        <locationX>270</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>varQueueIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Queue_Records.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Queue_Records</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Selected_Case_Ids</name>
        <label>Assign Selected Case Ids</label>
        <locationX>270</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>varCaseIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>datatableUserCases.firstSelectedRow.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Selected_Cases</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Selected_Messaging_Session_Ids</name>
        <label>Assign Selected Messaging Session Ids</label>
        <locationX>270</locationX>
        <locationY>1898</locationY>
        <assignmentItems>
            <assignToReference>varMessagingSessionIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>datatableUserMessagingSessions.firstSelectedRow.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Selected_Messaging_Sessions</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Selected_Voice_Calls_Ids</name>
        <label>Assign Selected Voice Calls Ids</label>
        <locationX>270</locationX>
        <locationY>2198</locationY>
        <assignmentItems>
            <assignToReference>varVoiceCallIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>datatableUserVoiceCalls.firstSelectedRow.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Selected_Voice_Calls</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Users_Customer_Ids</name>
        <label>Assign Users Customer Ids</label>
        <locationX>270</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>varUserCustomerIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_User_Customers.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_User_Customers</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Number_of_Work_Items</name>
        <label>Set Number of Work Items</label>
        <locationX>182</locationX>
        <locationY>2390</locationY>
        <assignmentItems>
            <assignToReference>varNumberOfCases</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>varCaseIds</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varNumberOfMessagingSessions</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>varMessagingSessionIds</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varNumberOfVoiceCalls</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>varVoiceCallIds</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>varCurrentUser</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Selected_Cases</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Selected_Cases</name>
        <label>Selected Cases</label>
        <locationX>182</locationX>
        <locationY>2498</locationY>
        <defaultConnector>
            <targetReference>Selected_Messaging_Sessions</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Cases Selected</defaultConnectorLabel>
        <rules>
            <name>Cases_Selected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varNumberOfCases</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_Cases_to_Agent</targetReference>
            </connector>
            <label>Cases Selected</label>
        </rules>
    </decisions>
    <decisions>
        <name>Selected_Messaging_Sessions</name>
        <label>Selected Messaging Sessions</label>
        <locationX>182</locationX>
        <locationY>2798</locationY>
        <defaultConnector>
            <targetReference>Selected_Voice_Calls</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Messaging Sessions Selected</defaultConnectorLabel>
        <rules>
            <name>Messaging_Sessions_Selected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varNumberOfMessagingSessions</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_Messaging_Sessions_to_Agent</targetReference>
            </connector>
            <label>Messaging Sessions Selected</label>
        </rules>
    </decisions>
    <decisions>
        <name>Selected_Voice_Calls</name>
        <label>Selected Voice Calls</label>
        <locationX>182</locationX>
        <locationY>3098</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenUsersWorkItems</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Voice Calls Selected</defaultConnectorLabel>
        <rules>
            <name>Voice_Calls_Selected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varNumberOfVoiceCalls</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Route_Voice_Calls_to_Agent</targetReference>
            </connector>
            <label>Voice Calls Selected</label>
        </rules>
    </decisions>
    <description>Used to display an &apos;inbox&apos; view for logged in user that will show any Cases, Messaging Sessions or Voice Calls that are currently in the queue and they are the customer owner for</description>
    <environments>Default</environments>
    <interviewLabel>Screen: Relationship Managers Queue Items {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Screen: Relationship Managers Queue Items</label>
    <loops>
        <name>Loop_Queue_Records</name>
        <label>Loop Queue Records</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_Queues</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Queue_Ids</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Users_Customer_Records</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Selected_Cases</name>
        <label>Loop Selected Cases</label>
        <locationX>182</locationX>
        <locationY>1490</locationY>
        <collectionReference>datatableUserCases.selectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Selected_Case_Ids</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Loop_Selected_Messaging_Sessions</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Selected_Messaging_Sessions</name>
        <label>Loop Selected Messaging Sessions</label>
        <locationX>182</locationX>
        <locationY>1790</locationY>
        <collectionReference>datatableUserMessagingSessions.selectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Selected_Messaging_Session_Ids</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Loop_Selected_Voice_Calls</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Selected_Voice_Calls</name>
        <label>Loop Selected Voice Calls</label>
        <locationX>182</locationX>
        <locationY>2090</locationY>
        <collectionReference>datatableUserVoiceCalls.selectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Selected_Voice_Calls_Ids</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Set_Number_of_Work_Items</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_User_Customers</name>
        <label>Loop User Customers</label>
        <locationX>182</locationX>
        <locationY>758</locationY>
        <collectionReference>Get_Users_Customer_Records</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Users_Customer_Ids</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Case_Records</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Case_Records</name>
        <label>Get Case Records</label>
        <locationX>182</locationX>
        <locationY>1058</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Messaging_Sessions</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OwnerId</field>
            <operator>In</operator>
            <value>
                <elementReference>varQueueIds</elementReference>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>In</operator>
            <value>
                <elementReference>varUserCustomerIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Messaging_Sessions</name>
        <label>Get Messaging Sessions</label>
        <locationX>182</locationX>
        <locationY>1166</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Voice_Calls</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OwnerId</field>
            <operator>In</operator>
            <value>
                <elementReference>varQueueIds</elementReference>
            </value>
        </filters>
        <filters>
            <field>EndUserAccountId</field>
            <operator>In</operator>
            <value>
                <elementReference>varUserCustomerIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>MessagingSession</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Queues</name>
        <label>Get Queues</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Queue_Records</targetReference>
        </connector>
        <filterLogic>1 AND (2 OR 3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10)</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_NSW</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_NSW_A</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_NSW_B</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_VIC</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_VIC_A</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_VIC_B</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_QLD</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_WA</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Premium_Managed_General</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Users_Customer_Records</name>
        <label>Get Users Customer Records</label>
        <locationX>182</locationX>
        <locationY>650</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_User_Customers</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OwnerId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Voice_Calls</name>
        <label>Get Voice Calls</label>
        <locationX>182</locationX>
        <locationY>1274</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>My_Customers_In_Queue</targetReference>
        </connector>
        <filterLogic>1 AND 2 AND (3 OR 4)</filterLogic>
        <filters>
            <field>OwnerId</field>
            <operator>In</operator>
            <value>
                <elementReference>varQueueIds</elementReference>
            </value>
        </filters>
        <filters>
            <field>RelatedRecordId</field>
            <operator>In</operator>
            <value>
                <elementReference>varUserCustomerIds</elementReference>
            </value>
        </filters>
        <filters>
            <field>CallDisposition</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>completed</stringValue>
            </value>
        </filters>
        <filters>
            <field>CallOrigin</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Voicemail</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <name>My_Customers_In_Queue</name>
        <label>My Customers In Queue</label>
        <locationX>182</locationX>
        <locationY>1382</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Loop_Selected_Cases</targetReference>
        </connector>
        <fields>
            <name>datatableUserCases</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Case</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>My Emails</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Case_Records</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Customer_Name__c&quot;,&quot;guid&quot;:&quot;column-633a&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Customer Name&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;CaseNumber&quot;,&quot;guid&quot;:&quot;column-1b83&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Case Number&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Subject&quot;,&quot;guid&quot;:&quot;column-72a6&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Subject&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>datatableUserMessagingSessions</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>MessagingSession</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>My SMS</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Messaging_Sessions</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Customer_Name__c&quot;,&quot;guid&quot;:&quot;column-f714&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Customer Name&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Customer_Phone_Number__c&quot;,&quot;guid&quot;:&quot;column-3fe7&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Phone Number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Customer Phone Number&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Status&quot;,&quot;guid&quot;:&quot;column-f235&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>datatableUserVoiceCalls</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>VoiceCall</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>My Phone Calls</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Voice_Calls</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Customer_Name__c&quot;,&quot;guid&quot;:&quot;column-8bf1&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Customer Name&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;FromPhoneNumber&quot;,&quot;guid&quot;:&quot;column-28b7&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Caller Number&quot;,&quot;type&quot;:&quot;phone&quot;},{&quot;apiName&quot;:&quot;CallOrigin&quot;,&quot;guid&quot;:&quot;column-082f&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Call Origin&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Assign To Me</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ScreenUsersWorkItems</name>
        <label>Screen Users Work Items</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Get_Queues</targetReference>
        </connector>
        <fields>
            <name>displaytextQueueItems</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;{!$User.FirstName} {!$User.LastName}&apos;s Inbox&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255); font-size: 12px;&quot;&gt;Click &lt;/span&gt;&lt;strong style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255); font-size: 12px;&quot;&gt;Refresh&lt;/strong&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255); font-size: 12px;&quot;&gt; to get your c&lt;/span&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;urrent work items in the queue&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Refresh</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>ScreenUsersWorkItems</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>Stores collection of Case Ids</description>
        <name>varCaseIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varCurrentUser</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varMessagingSessionIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varNumberOfCases</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>varNumberOfMessagingSessions</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>varNumberOfVoiceCalls</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>varQueueIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varUserCustomerIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varVoiceCallIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
